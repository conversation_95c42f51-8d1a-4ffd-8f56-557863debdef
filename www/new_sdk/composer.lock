{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "7c59ba98ad366211910477f05a2165e0", "packages": [{"name": "guzzlehttp/guzzle", "version": "6.3.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/guzzlehttp/guzzle/6.3.3/guzzlehttp-guzzle-6.3.3.zip", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2018-04-22T15:46:56+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/guzzlehttp/promises/v1.3.1/guzzlehttp-promises-v1.3.1.zip", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/guzzlehttp/psr7/1.4.2/guzzlehttp-psr7-1.4.2.zip", "reference": "f5b8a8512e2b58b0071a7280e39f14f72e05d87c", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"], "time": "2017-03-20T17:10:46+00:00"}, {"name": "hashids/hashids", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/vinkla/hashids.git", "reference": "8cab111f78e0bd9c76953b082919fc9e251761be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vinkla/hashids/zipball/8cab111f78e0bd9c76953b082919fc9e251761be", "reference": "8cab111f78e0bd9c76953b082919fc9e251761be", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.0 || ^9.4", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-bcmath": "Required to use BC Math arbitrary precision mathematics (*).", "ext-gmp": "Required to use GNU multiple precision mathematics (*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"psr-4": {"Hashids\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generate short, unique, non-sequential ids (like YouTube and Bitly) from numbers", "homepage": "https://hashids.org/php", "keywords": ["bitly", "decode", "encode", "hash", "hashid", "hashids", "ids", "obfuscate", "youtube"], "support": {"issues": "https://github.com/vinkla/hashids/issues", "source": "https://github.com/vinkla/hashids/tree/4.1.0"}, "time": "2020-11-26T19:24:33+00:00"}, {"name": "huaweicloud/huaweicloud-sdk-php", "version": "3.0.56-rc", "source": {"type": "git", "url": "https://github.com/huaweicloud/huaweicloud-sdk-php-v3.git", "reference": "cd5c6e0ae53ada7ca6c6d37171e506f0b880bad3"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/huaweicloud/huaweicloud-sdk-php/3.0.56-rc/huaweicloud-huaweicloud-sdk-php-3.0.56-rc.zip", "reference": "cd5c6e0ae53ada7ca6c6d37171e506f0b880bad3", "shasum": ""}, "require": {"guzzlehttp/guzzle": ">=6.3.0", "guzzlehttp/promises": ">=1.3.1", "guzzlehttp/psr7": ">=1.4.2", "monolog/monolog": ">=1.23.0", "php": ">=5.6.0", "psr/http-message": ">=1.0.1"}, "type": "library", "autoload": {"psr-4": {"HuaweiCloud\\SDK\\": "Services/", "HuaweiCloud\\SDK\\Core\\": "Core/src/"}}, "license": ["Apache-2.0"], "authors": [{"name": "HuaweiCloud_SDK", "email": "<EMAIL>", "homepage": "https://sdkcenter.developer.huaweicloud.com/?language=PHP"}], "description": "Huawei Cloud SDK for PHP", "keywords": ["api", "php", "rest", "sdk"], "time": "2022-08-15T06:40:59+00:00"}, {"name": "monolog/monolog", "version": "1.23.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/monolog/monolog/1.23.0/monolog-monolog-1.23.0.zip", "reference": "fd8c787753b3a2ad11bc60c063cff1358a32a3b4", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2017-06-19T01:22:40+00:00"}, {"name": "overtrue/pinyin", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/overtrue/pinyin.git", "reference": "3b781d267197b74752daa32814d3a2cf5d140779"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/overtrue/pinyin/3.0.6/overtrue-pinyin-3.0.6.zip", "reference": "3b781d267197b74752daa32814d3a2cf5d140779", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-4": {"Overtrue\\Pinyin\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/overtrue"}], "description": "Chinese to pinyin translator.", "homepage": "https://github.com/overtrue/pinyin", "keywords": ["Chinese", "<PERSON><PERSON><PERSON>", "cn2pinyin"], "time": "2017-07-10T07:20:01+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.0.3", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "44d49bab5ab1fef721d3ee07e75dc0865ddf4cc6"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/phpmailer/phpmailer/v6.0.3/phpmailer-phpmailer-v6.0.3.zip", "reference": "44d49bab5ab1fef721d3ee07e75dc0865ddf4cc6", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "php": ">=5.5.0"}, "require-dev": {"doctrine/annotations": "1.2.*", "friendsofphp/php-cs-fixer": "^2.2", "phpdocumentor/phpdocumentor": "2.*", "phpunit/phpunit": "^4.8 || ^5.7", "zendframework/zend-eventmanager": "3.0.*", "zendframework/zend-i18n": "2.7.3", "zendframework/zend-serializer": "2.7.*"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "time": "2018-01-05T13:19:58+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/psr/http-message/1.0.1/psr-http-message-1.0.1.zip", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/psr/log/1.0.2/psr-log-1.0.2.zip", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10T12:19:37+00:00"}, {"name": "topthink/framework", "version": "v5.0.24", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "c255c22b2f5fa30f320ecf6c1d29f7740eb3e8be"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/topthink/framework/v5.0.24/topthink-framework-v5.0.24.zip", "reference": "c255c22b2f5fa30f320ecf6c1d29f7740eb3e8be", "shasum": ""}, "require": {"php": ">=5.4.0", "topthink/think-installer": "~1.0"}, "require-dev": {"johnkary/phpunit-speedtrap": "^1.0", "mikey179/vfsstream": "~1.6", "phpdocumentor/reflection-docblock": "^2.0", "phploc/phploc": "2.*", "phpunit/phpunit": "4.8.*", "sebastian/phpcpd": "2.*"}, "type": "think-framework", "autoload": {"psr-4": {"think\\": "library/think"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the new thinkphp framework", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "time": "2019-01-11T08:04:58+00:00"}, {"name": "topthink/think-captcha", "version": "v1.0.8", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "1d64363c814c92f6086c4fa5e3223fe7e23db09d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/1d64363c814c92f6086c4fa5e3223fe7e23db09d", "reference": "1d64363c814c92f6086c4fa5e3223fe7e23db09d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/framework": "~5.0.0", "topthink/think-installer": ">=1.0.10"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\captcha\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp5", "support": {"issues": "https://github.com/top-think/think-captcha/issues", "source": "https://github.com/top-think/think-captcha/tree/master"}, "time": "2019-01-28T04:48:36+00:00"}, {"name": "topthink/think-helper", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "0c99dc625b0d2d4124e1b6ca15a3ad6f0125963f"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/topthink/think-helper/v1.0.6/topthink-think-helper-v1.0.6.zip", "reference": "0c99dc625b0d2d4124e1b6ca15a3ad6f0125963f", "shasum": ""}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\helper\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Helper Package", "time": "2017-04-05T07:15:37+00:00"}, {"name": "topthink/think-installer", "version": "v1.0.14", "source": {"type": "git", "url": "https://github.com/top-think/think-installer.git", "reference": "eae1740ac264a55c06134b6685dfb9f837d004d1"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/topthink/think-installer/v1.0.14/topthink-think-installer-v1.0.14.zip", "reference": "eae1740ac264a55c06134b6685dfb9f837d004d1", "shasum": ""}, "require": {"composer-plugin-api": "^1.0||^2.0"}, "require-dev": {"composer/composer": "^1.0||^2.0"}, "type": "composer-plugin", "extra": {"class": "think\\composer\\Plugin"}, "autoload": {"psr-4": {"think\\composer\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": "2021-03-25T08:34:02+00:00"}, {"name": "topthink/think-queue", "version": "v1.1.4", "source": {"type": "git", "url": "https://github.com/top-think/think-queue.git", "reference": "ad709611d516e13d6760234bc98e91faa901cae8"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/topthink/think-queue/v1.1.4/topthink-think-queue-v1.1.4.zip", "reference": "ad709611d516e13d6760234bc98e91faa901cae8", "shasum": ""}, "require": {"topthink/think-helper": ">=1.0.4", "topthink/think-installer": ">=1.0.10"}, "type": "think-extend", "extra": {"think-config": {"queue": "src/config.php"}}, "autoload": {"files": ["src/common.php"], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Queue Package", "time": "2017-06-25T00:49:56+00:00"}, {"name": "workerman/gatewayclient", "version": "v3.0.10", "source": {"type": "git", "url": "https://github.com/walkor/GatewayClient.git", "reference": "85ffc1ee84ca303d9c4c5efc2f5235d5d7031601"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/workerman/gatewayclient/v3.0.10/workerman-gatewayclient-v3.0.10.zip", "reference": "85ffc1ee84ca303d9c4c5efc2f5235d5d7031601", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"GatewayClient\\": "./"}}, "license": ["MIT"], "homepage": "http://www.workerman.net", "time": "2018-06-08T03:57:05+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"huaweicloud/huaweicloud-sdk-php": 5}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.0"}, "platform-dev": [], "plugin-api-version": "2.2.0"}