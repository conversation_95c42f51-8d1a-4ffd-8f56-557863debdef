<?php

/**
 * 换绑控制器
 * 
 */

namespace app\mcpsapi\controller;

use app\mcpsapi\controller\Guild;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;

class ChannelRebind extends Guild  {
    
    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;

        $this->redis    = \think\Cache::store('default')->handler();
    }
  
     /**
     * 换绑列表
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= $this->input('list_rows',10);
        $page					= $this->input('page',1);
        $account				= $this->input('account','','trim');   
        $cusername				= $this->input('cusername','','trim');   
        $begin_time				= $this->input('begin_time','','trim');
        $end_time				= $this->input('end_time','','trim');

        $condition				 = [];
		$condition['admin_type'] =  2;
		$condition['admin_id']	 = $this->_channelId;
		if(input('account')){
			$condition['username']	= $account;
		}
	    //申请开始时间和结束时间不为空时
        if ($begin_time != '' && $end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($begin_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($begin_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $condition['create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }

		$whereRaw = '';
		if($this->input('cusername')<>''){
			$whereRaw .= ' origin_cusername="'.$cusername.'" or cusername="'.$cusername.'" ';
		}
		
		if($whereRaw){
			$rebindList = model('PlayerRebind')
							->where($condition)
							->whereRaw($whereRaw)
							->field("id,userid,username,game_id,game_name,origin_cuserid,origin_cusername,cuserid,cusername,create_time,cmmt")
							->order("id desc")
							->paginate(['list_rows'=>$list_rows,'page'=>$page])
							->toArray();
		}
		else{
			$rebindList = model('PlayerRebind')
							->where($condition)
							->field("id,userid,username,game_id,game_name,origin_cuserid,origin_cusername,cuserid,cusername,create_time,cmmt")
							->order("id desc")
							->paginate(['list_rows'=>$list_rows,'page'=>$page])
							->toArray();
		}
	//	echo model('PlayerRebind')->getLastSql();

		$this->jsonResult($rebindList, 20000, '获取列表成功');
    }
    /**
     * 换绑页面
     */
    public function rebind()
    {
		if ($this->request->isPost()) {
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您无权限进行玩家换绑操作');
			}
			else{
				$channelInfo   = model("Channel")->field("id,name,level,parent_id,js_amount")->where(['id'=>$this->_channelId])->find();
				if(!$channelInfo){
					$this->jsonResult('', 0, '账号异常,请重新登录后再试');
				}
				$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
				if(!$adminInfo){
					$this->jsonResult('', 0, '账号异常,请重新登录后再试');
				}
				else if(!$adminInfo['secondary_password']){
					$this->jsonResult('', 0, '请设置二级密码后才能进行换绑');
				}

				$this->jsonResult($channelInfo, 20000, '获取提现信息成功');
			}
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
    /**
     * 换绑处理
     */
    public function doRebind()
    {
		if ($this->request->isPost()) {
			$game_id		= $this->input('game_id',0,'intval');			//游戏ID
			$accounts		= $this->input('accounts','','trim');           //玩家账号,多个玩家账号用换行分隔
			$cusername		= $this->input('cusername','','trim');          //新C账号
			$cmmt			= $this->input('cmmt','','trim');               //备注
			
			//判断当前账号是否有玩家换绑权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您无权限进行玩家换绑操作');
			}
			if(!$game_id){
				$this->jsonResult('', 0, '请选择要换绑的游戏！');
			}
			$gameInfo   = model("Game")->where(['id'=>$game_id])->find();
			if(!$gameInfo){
				$this->jsonResult('', 0, '您选择换绑的游戏不存在');
			}
			if(!$cusername){
				$this->jsonResult('', 0, '请输入新的推广员账号！');
			}
			$promoteChannelInfo   = model("Channel")->field("id,name,level,parent_id")->where(['name'=>$cusername])->find();

			if(!$promoteChannelInfo){
				$this->jsonResult('', 0, '推广员账号不存在或非您所属推广员账号');
			}
			else if($promoteChannelInfo['level']<>3){
				$this->jsonResult('', 0, '请输入推广员账号');
			}
			else if(get_top_channel($promoteChannelInfo['id'])['id'] <> $this->_channelId){
				$this->jsonResult('', 0, '推广员账号不存在或非您所属推广员账号');
			}
			$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
			if(!$adminInfo){
				$this->jsonResult('', 0, '账号异常,请重新登录后再试');
			}
			else if(!$adminInfo['secondary_password']){
				$this->jsonResult('', 0, '请设置二级密码后才能进行玩家换绑');
			}
			if(!$accounts){
				$this->jsonResult('', 0, '请填写要换绑的玩家账号！');
			}
			else{
				$accountArrs = array();
				$accountArrs=  preg_split('/\n/', $accounts);
				//	var_dump($accountArrs);
				$errorMsg = "";
				if ($accountArrs){
					$userRebindArrs = array();
					foreach ($accountArrs as $account){
						$memberInfo = model('Members')->where(['username' => $account])->field('id,username,flag')->find();
						if(!$memberInfo){
							$errorMsg .= "'".$account."' 该玩家账号不存在;";
						}
						else{
							$memberGameChannelInfo = model('MemberChannelGame')->where(['member_id' => $memberInfo['id'], 'game_id' => $game_id])->find();
							if(!$memberGameChannelInfo){
								$errorMsg .= "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中尚未绑定推广员,您不能对其进行绑定;";
							}
							else if(get_top_channel($memberGameChannelInfo['channel_id'])['id'] <> $this->_channelId){
								$errorMsg .= "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中所属推广员不在您的公会下,您不能对其进行换绑;";
							}
							
							else if($memberGameChannelInfo['channel_id'] == $promoteChannelInfo['id']){
								$errorMsg .= "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中已是属于推广员'".$promoteChannelInfo['name']."',您无需再对其进行换绑;";
							}
							else if(model('PlayerRebindApply')->where(['userid' => $memberInfo['id'],'game_id' => $game_id,'status' => 0])->count()){
								$errorMsg .= "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中已存在换绑申请待处理,您暂时不能对其进行换绑;";
							}
							else{
								$userRebind = array();
								$userRebind['userid']			= $memberInfo['id'];
								$userRebind['username']			= $memberInfo['username'];
								$userRebind['game_id']			= $gameInfo['id'];
								$userRebind['game_name']		= $gameInfo['name'];
								$userRebind['origin_cuserid']	= $memberGameChannelInfo['channel_id'];
								$userRebind['origin_cusername'] = get_channel_name($memberGameChannelInfo['channel_id']);
								$userRebind['cuserid']			= $promoteChannelInfo['id'];
								$userRebind['cusername']		= $promoteChannelInfo['name'];
								$userRebindArrs[] = $userRebind;
							}
						}
					}
				}
				else{
					$this->jsonResult('', 0, '请填写要换绑的玩家账号！');
				}
			}
			
			$result = '';   //返回值
			if($userRebindArrs){
				// 启动事务
				Db::startTrans();
				try{
					
					foreach ($userRebindArrs as $userRebind){
						$updData = array();
						$updData['channel_id'] = $userRebind['cuserid'];
						$updResult = model('MemberChannelGame')->where(['member_id' => $userRebind['userid'], 'game_id' => $userRebind['game_id']])->update($updData);
						if (!$updResult) {
							throw new Exception("更新推广员失败");
						}

						$updGameServerData = array();
						$updGameServerData['channel_id'] = $userRebind['cuserid'];
						$updGameServerResult = model('MemberGameServer')->where(['member_id' => $userRebind['userid'], 'game_id' => $userRebind['game_id']])->update($updGameServerData);

						/*
						$updGameServerImeilData = array();
						$updGameServerImeilData['channel_id'] = $userRebind['cuserid'];
						$updGameServerResult = model('GameServerImeil')->where(['member_id' => $userRebind['userid'], 'game_id' => $userRebind['game_id']])->update($updGameServerImeilData);

						$updGameServerIpData = array();
						$updGameServerIpData['channel_id'] = $userRebind['cuserid'];
						$updGameServerResult = model('GameServerIp')->where(['member_id' => $userRebind['userid'], 'game_id' => $userRebind['game_id']])->update($updGameServerIpData);
						*/

						// 插入玩家历史记录
						$insertHistoryId = Db::table('cy_member_history')->insert([
							'userid'      => $userRebind['userid'],
							'channel'     => $userRebind['game_name'] . "，" .$userRebind['cusername'],
							'ip'          => request()->ip(),
							'create_time' => time(),
							'admin_type'  => 2,
							'admin_id'    => $this->_channelId
						]);
						if(!$insertHistoryId){
							throw new Exception("创建玩家历史记录失败! ");
						}

						$rebindData = array();
						$rebindData['userid']			=	$userRebind['userid'];
						$rebindData['username']			=	$userRebind['username'];
						$rebindData['game_id']			=	$userRebind['game_id'];
						$rebindData['game_name']		=	$userRebind['game_name'];
						$rebindData['origin_cuserid']	=	$userRebind['origin_cuserid'];
						$rebindData['origin_cusername']	=	$userRebind['origin_cusername'];
						$rebindData['cuserid']			=	$userRebind['cuserid'];
						$rebindData['cusername']		=	$userRebind['cusername'];
						$rebindData['create_time']		=	time();
						$rebindData['cmmt']				=	$cmmt;
						$rebindData['admin_type']		=	2;
						$rebindData['admin_id']			=	$this->_channelId;
						$rebindData['admin_name']		=	$this->_channelName;
						$insertRebindId = model('PlayerRebind')->insert($rebindData);
						if(!$insertRebindId){
							throw new Exception("创建玩家换绑记录失败! ");
						}

						$rebindPayFromTime = strtotime(date('Y-m-d',(time()-((date('w',time())==0?7:date('w',time()))-1)*24*3600)));
						$updPayData = array();
						$updPayData['channel_id']	=	$userRebind['cuserid'];
						$updPayResult = model('Pay')->where(['userid' => $userRebind['userid'],'gameid' => $userRebind['game_id'],'channel_id' => $userRebind['origin_cuserid'],'create_time'=> ['egt', $rebindPayFromTime]])->update($updPayData);
					//	echo $updPayResult."----updPayResult-----------<br>";
						if ($updPayResult) {
							$errorMsg .= "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中已成功换绑，新推广员为'".$promoteChannelInfo['name']."';该玩家本周后消费订单也已做换绑。";
						}
						else{
							$errorMsg .= "'".$account."' 该玩家在游戏'".$gameInfo['name']."'中已成功换绑，新推广员为'".$promoteChannelInfo['name']."';";
						}
					}
					// 提交事务
					Db::commit();
					
				} catch (\Exception $e) {
					// 回滚事务
					Db::rollback();
					$this->jsonResult('', 0, '玩家换绑操作失败'.$e->getMessage());
				}
				$result['message'] = $errorMsg;
				$this->jsonResult($result, 20000, $errorMsg);
				exit;
			}
			else{
				$result['message'] = $errorMsg;
				$this->jsonResult($result, 20000, $errorMsg);
				exit;
			}
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
}
