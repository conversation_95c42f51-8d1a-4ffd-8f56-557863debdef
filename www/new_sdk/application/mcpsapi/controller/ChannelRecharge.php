<?php

/**
 * 渠道充值接口控制器
 * 
 */

namespace app\mcpsapi\controller;

use app\mcpsapi\controller\Guild;
use app\common\model\ChannelRecharge as ChannelRechargeModel;
use app\common\library\WeixinPay;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;
use app\common\library\MakeReport;
use think\Loader;

class ChannelRecharge extends Guild  {
    
    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;

        $this->redis    = \think\Cache::store('default')->handler();
    }
  
     /**
     * 我的充值列表
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1,2])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= $this->input('list_rows',10);
        $page					= $this->input('page',1);
		$type					= $this->input('type','0','intval');    //1(充值收入),2(充值支出),3(直充),4(结算充值收入)
        $account				= $this->input('account','','trim');    //收入账号
        $apply_begin_time		= $this->input('apply_begin_time','','trim');
        $apply_end_time			= $this->input('apply_end_time','','trim');
        $finish_begin_time		= $this->input('finish_begin_time','','trim');
        $finish_end_time		= $this->input('finish_end_time','','trim');
        $download				= $this->input('download',0,'intval');

		//判断是否选择交易类型
		if(!in_array($type,[1,2,3,4])){
			$this->jsonResult('', 0, '请选择交易类型');
		}

        $condition					= [];
		if($account){
			$condition['channel_name']	= $account;
		}

		if($type==1){
			$condition['channel_id'] = $this->_channelId;
			$condition['recharge_type'] = 1;
		}
		else if($type==2){
			$condition['send_channel_id'] = $this->_channelId;
			$condition['recharge_type'] = 1;
		}
		else if($type==3){
			$condition['channel_id'] = $this->_channelId;
			$condition['recharge_type'] = 2;
		}
		else if($type==4){
			$condition['channel_id'] = $this->_channelId;
			$condition['recharge_type'] = 3;
		}
	    //申请开始时间和结束时间不为空时
        if ($apply_begin_time != '' && $apply_end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($apply_begin_time)],
                ['<=', strtotime($apply_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($apply_begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($apply_begin_time)];
        } //结束时间不为空时
        elseif ($apply_end_time != '') {
            $condition['create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
        }

        //到账开始时间和结束时间不为空时
        if ($finish_begin_time != '' && $finish_end_time != '') {
            $condition['finish_time'] = [
                ['>=', strtotime($finish_begin_time)],
                ['<=', strtotime($finish_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($finish_begin_time != '') {
            $condition['finish_time'] = ['>=', strtotime($finish_begin_time)];
        } //结束时间不为空时
        elseif ($finish_end_time != '') {
            $condition['finish_time'] = ['<=', strtotime($finish_end_time . ' 23:59:59')];
        }
	//	var_dump($condition);

		if($download){
			$sql = model('ChannelRecharge')
						->where($condition)
						->whereRaw('channel_id='.$this->_channelId.' or send_channel_id='.$this->_channelId)
						->order("id desc")
						->fetchSql(true)
						->select();
		//	echo $sql;
			if ((new MakeReport())->addTask('guild.ChannelRechargeIndex', $sql, 'cps'.session('guild_info')['id'])){
				$this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
			}
			else{
				$this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
			}
		}

        $rechargeList = model('ChannelRecharge')
						->where($condition)
						->whereRaw('channel_id='.$this->_channelId.' or send_channel_id='.$this->_channelId)
						->order("id desc")
						->paginate(['list_rows'=>$list_rows,'page'=>$page])
						->toArray();
	//	echo model('ChannelRecharge')->getLastSql()."-----lastsql------<br>";

		$this->jsonResult($rechargeList, 20000, '获取列表成功');
    }

     /**
     * 充值审核列表
     * @return [type] [description]
     */
    public function checklist()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}
	
        $list_rows				= input('list_rows',10);
        $page					= input('page',1);
        $account				= $this->input('account','','trim');
        $orderid				= $this->input('orderid','','trim');
        $apply_begin_time		= $this->input('apply_begin_time','','trim');
        $apply_end_time			= $this->input('apply_end_time','','trim');
        $download				= $this->input('download',0,'intval');

        $condition					= [];
		$condition['send_channel_id']	= $this->_channelId;
		if($orderid){
			$condition['orderid']	= $orderid;
		}
		if($account){
			$condition['channel_name']	= $account;
		}

	    //申请开始时间和结束时间不为空时
        if ($apply_begin_time != '' && $apply_end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($apply_begin_time)],
                ['<=', strtotime($apply_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($apply_begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($apply_begin_time)];
        } //结束时间不为空时
        elseif ($apply_end_time != '') {
            $condition['create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
        }

		if($download){
			$sql = model('ChannelRecharge')
						->where($condition)
						->order("id desc")
						->fetchSql(true)
						->select();
		//	echo $sql;
			if ((new MakeReport())->addTask('guild.ChannelRechargeChecklist', $sql, 'cps'.session('guild_info')['id'])){
				$this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
			}
			else{
				$this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
			}
		}

        $rechargeList = model('ChannelRecharge')
						->where($condition)
						->order("id desc")
						->paginate(['list_rows'=>$list_rows,'page'=>$page])
						->toArray();

		$this->jsonResult($rechargeList, 20000, '获取列表成功');
    }
    /**
     * 充值审核(查看)
     */
    public function audit(){
		if ($this->request->isPost()) {
			$id		= intval($this->input('id'));	//渠道ID
			
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您不能进行该操作');
			}
			if(!$id){
				$this->jsonResult('', 0, '请选择要审核的记录');
			}

			$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
			if(!$adminInfo){
				$this->jsonResult('', 0, '账号异常,请重新登录后再试');
			}
			else if(!$adminInfo['pay_password'] || !$adminInfo['secondary_password']){
				$this->jsonResult('', 0, '请设置交易密码和二级密码后才能进行审核');
			}

			$rechargeInfo = model('ChannelRecharge')->where(['id'=>$id,'send_channel_id'=>$this->_channelId])->find();
			if(empty($rechargeInfo)){
				$this->jsonResult('', 0, '记录不存在或您无权限操作');
			}
	
			$this->jsonResult($rechargeInfo, 20000, '获取记录成功');
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
		}
    }
	
	//充值审核(操作)
	function doAudit(){
		if ($this->request->isPost()) {
			$id		 = intval($this->input('id'));	//渠道ID
			$status	 = intval($this->input('status'));	//审核状态:1(审核通过),2(审核不通过)
			$remark	 = $this->input('remark','','trim');	//审核备注
			$pay_password	= $this->input('pay_password','','trim');            //交易密码

			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您不能进行该操作');
			}
			if(!$id){
				$this->jsonResult('', 0, '请选择要审核的记录');
			}
		    if($status<>1 && $status<>2){
				$this->jsonResult('', 0, '非法审核状态');
            }

			if(!$pay_password){
				$this->jsonResult('', 0, '请输入交易密码');
			}

			$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
			if(!$adminInfo){
				$this->jsonResult('', 0, '账号异常,请重新登录后再试');
			}
			else if(!$adminInfo['pay_password'] || !$adminInfo['secondary_password']){
				$this->jsonResult('', 0, '请设置交易密码和二级密码后才能进行审核');
			}
			else if($adminInfo['pay_password'] != mg_password($pay_password)){
				 $this->jsonResult('', 0, '交易密码错误!');
			}

			$rechargeInfo = model('ChannelRecharge')->where(['id'=>$id,'send_channel_id'=>$this->_channelId])->find();
			if(empty($rechargeInfo)){
				$this->jsonResult('', 0, '记录不存在或您无权限操作');
			}
			else if($rechargeInfo['status']<>0){
				$this->jsonResult('', 0, '该记录已审核过');
			}

			$channelInfo = model("Channel")->where(['id'=>$this->_channelId])->find();
			if($channelInfo){
				if($status==1){
					if($channelInfo['amount'] < $rechargeInfo['amount']){
						$this->jsonResult('', 0, '您当前余额不足,不能进行审核发币');
					}
				}
			}
			else{
				$this->jsonResult('', 0, '您的账号有异常,请联系管理员');
			}

			$auditData = array();
			$auditData['id'] = $id;
			$auditData['status'] = $status;
			$auditData['finish_time'] = NOW_TIMESTAMP;
			$auditData['check_remark'] = $remark;
			$auditData['check_admin_type'] = 2;
			$auditData['check_admin_name'] = $this->_channelName;

            Db::startTrans();
            try {
				//审核通过
				if($status==1){
					$detData = array();
                    $detData['channel_id']      = $this->_channelId;
                    $detData['channel_name']    = $this->_channelName;
                    $detData['change_amount']	= -$rechargeInfo['amount'];
                    $detData['account_type']	= 1;   //通用账户
                    $detData['type']			= 5;   //充值支出
                    $detData['out_orderid']		= $rechargeInfo['orderid'];
                    $detData['create_time']     = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
					if ( !$insertDetId) {
						throw new Exception("添加账户变动明细失败");
					}
					$updData = array();
					$updData['amount'] = Db::raw("amount-".$rechargeInfo['amount']);
					$updData['update_time'] = time();
					$updFromResult = model('Channel')->where(['id'=>$this->_channelId,'amount'=>array('egt',$rechargeInfo['amount'])])->update($updData);
					if (!$updFromResult) {
						throw new Exception("账户金额变动失败");
					}

					$detData = array();
                    $detData['channel_id']      = $rechargeInfo['channel_id'];
                    $detData['channel_name']    = $rechargeInfo['channel_name'];
                    $detData['change_amount']	= $rechargeInfo['amount'];
                    $detData['account_type']	= 1;   //通用账户
                    $detData['type']			= 4;   //充值收入
                    $detData['out_orderid']		= $rechargeInfo['orderid'];
                    $detData['create_time']     = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
					if ( !$insertDetId) {
						throw new Exception("添加账户变动明细失败");
					}
					$updData = array();
					$updData['amount'] = Db::raw("amount+".$rechargeInfo['amount']);
					$updData['update_time'] = time();
					$updToResult = model('Channel')->where(['id'=>$rechargeInfo['channel_id']])->update($updData);
					if (!$updToResult) {
						throw new Exception("账户金额变动失败");
					}
	
					$auditData['finish_status'] = 1;
					$result = model('ChannelRecharge')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
					if (!$result) {
						throw new Exception("充值审核失败");
					}
				}
				else if($status==2){  //审核不通过
					$result = model('ChannelRecharge')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
					if (!$result) {
						throw new Exception("充值审核失败");
					}
				}
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->jsonResult('', 0, '添加失败： ' . $e->getMessage());
            }
			$this->jsonResult('', 20000, '审核成功');
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
		}
	}
    /**
     * 充值页面
     */
    public function recharge()
    {
		if ($this->request->isPost()) {
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1,2])){
				$this->jsonResult('', 0, '您无权限进行充值相关操作');
			}
			else{
				$channelInfo   = model("Channel")->field("id,name,level,parent_id,amount,js_amount")->where(['id'=>$this->_channelId])->find();
				if(!$channelInfo){
					$this->jsonResult('', 0, '账号异常,请重新登录后再试');
				}

				$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
				if(!$adminInfo){
					$this->jsonResult('', 0, '账号异常,请重新登录后再试');
				}
				else if(!$adminInfo['secondary_password']){
					$this->jsonResult('', 0, '请先设置二级密码后才能进行操作');
				}

				//判断支付金额
				$condis = array();
				$condis['channel_id'] = $this->_channelId;
				$condis['begin_time'] = array('elt',$this->nowTime);
				$condis['end_time'] = array('egt',$this->nowTime);
				$rechargeRatio = model("ChannelRechargeRatio")->where($condis)->find();
				if(!empty($rechargeRatio) && $rechargeRatio['ratio']){
					$rechargeRatio = $rechargeRatio['ratio'];
				}
				else{
					$rechargeRatio = 100;
				}
				$channelInfo['recharge_ratio'] = $rechargeRatio;

				$this->jsonResult($channelInfo, 20000, '获取充值相关信息成功');
			}
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
    /**
     * 充值操作处理
     */
    public function doRecharge()
    {
		if ($this->request->isPost()) {
			$amount			= $this->input('amount');                   //充值金额
			$real_amount	= $this->input('real_amount');              //支付金额
			$recharge_type  = $this->input('recharge_type');            //充值方式:1(申请),2(直充),3(结算币充值)
			$paytype		= $this->input('paytype');                 //支付类型:zfb(支付宝),wxpay(微信)
			$out_order_no   = $this->input('out_order_no');             //交易凭证
			$remark			= $this->input('remark');                   //备注
		
			$orderid    = 'R'.makeOrderid();        
			
			$checkResult = $this->validate($this->input,'ChannelRecharge.add');
			if (true !== $checkResult) {
				$this->jsonResult('', 0, $checkResult);
			}
			
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1,2])){
				$this->jsonResult('', 0, '您不能进行充值');
			}
			if(!in_array($recharge_type,[1,2,3])){
				$this->jsonResult('', 0, '非正确的充值方式');
			}
			else{
				if($recharge_type==2 && !in_array($paytype,['zfb','wxpay'])){
					$this->jsonResult('', 0, '非正确的支付类型');
				}
			}
			if($this->_channelLevel==2 && $recharge_type<>1){
				$this->jsonResult('', 0, '您只能使用申请方式充值');
			}
			if(abs(intval($real_amount*100)/100-$real_amount) > 0.01){
				$this->jsonResult('', 0, '请正确填写支付金额！');
			}
			else{
				//判断支付金额
				$condis = array();
				$condis['channel_id'] = $this->_channelId;
				$condis['begin_time'] = array('elt',$this->nowTime);
				$condis['end_time'] = array('egt',$this->nowTime);
				$rechargeRatio = model("ChannelRechargeRatio")->where($condis)->find();
				if(!empty($rechargeRatio) && $rechargeRatio['ratio']){
					$rechargeRatio = $rechargeRatio['ratio'];
				}
				else{
					$rechargeRatio = 100;
				}
				$discountAmt = floatval($amount*$rechargeRatio/100);
				if($discountAmt <> $real_amount){
					$this->jsonResult('', 0, '充值金额异常,请刷新页面后重新充值');
				}
			}

			$channelInfo = model("Channel")->where(['id'=>$this->_channelId])->find();
			if($channelInfo && $channelInfo['parent_id']){
				if($recharge_type==3 && $channelInfo['js_amount']<$amount){
					$this->jsonResult('', 0, '您的结算币余额不足');
				}
			}
			else{
				$this->jsonResult('', 0, '您的账号有异常,请联系管理员');
			}

			//指定时间内，禁止重复下单
			if(!requestDuplicateCheck('recharge_pay_duplicate_'.$this->_channelId,$this->payRequestLimit)){
				$this->jsonResult('', 0, '充值请求过多，请于'.$this->payRequestLimit.'s以后，再次进行充值操作');
			}

			$rechargeData = array();
			$rechargeData['orderid']		= $orderid;
			$rechargeData['channel_id']     = $this->_channelId;
			$rechargeData['channel_name']   = $this->_channelName;
			if($this->_channelLevel==1){
				$rechargeData['send_channel_id']     = '0';
				$rechargeData['send_channel_name']   = '平台';
			}
			else if($this->_channelLevel==2){
				$parentChannelInfo = model("Channel")->where(['id'=>$channelInfo['parent_id']])->find();
				$rechargeData['send_channel_id']     = $parentChannelInfo['id'];
				$rechargeData['send_channel_name']   = $parentChannelInfo['name'];
			}
			$rechargeData['amount']			= $amount;
			$rechargeData['real_amount']	= $real_amount;
			$rechargeData['recharge_type']  = $recharge_type;
			$rechargeData['paytype']		= $paytype;
			$rechargeData['out_order_no']	= $out_order_no;
			$rechargeData['remark']			= $remark;
			$rechargeData['recharge_ratio']	= $rechargeRatio;
			$rechargeData['status']			= 0;
			$rechargeData['create_time']	= time();

			// 启动事务
			Db::startTrans();
			
			try{
				$result = '';   //返回值
				
				//结算币充值
				if($recharge_type==3){  
					$rechargeData['status'] = 1;
					$rechargeData['finish_status'] = 1;
					$rechargeData['finish_time'] = NOW_TIMESTAMP;

					$insertRechargeId = model("ChannelRecharge")->insert($rechargeData);
					if(!$insertRechargeId){
						throw new Exception("创建订单失败! ");
					}

					$detData = array();
                    $detData['channel_id']      = $this->_channelId;
                    $detData['channel_name']    = $this->_channelName;
                    $detData['change_amount']	= -$amount;
                    $detData['account_type']	= 2;   //结算账户
                    $detData['type']			= 7;   //结算充值
                    $detData['out_orderid']		= $orderid;
                    $detData['create_time']     = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
					if ( !$insertDetId) {
						throw new Exception("添加账户变动明细失败");
					}
					$updData = array();
					$updData['js_amount'] = Db::raw("js_amount-".$amount);
					$updData['update_time'] = time();
					$updResult = model('Channel')->where(['id'=>$this->_channelId,'js_amount'=>array('egt',$amount)])->update($updData);
					if (!$updResult) {
						throw new Exception("账户金额变动失败");
					}

					$detData = array();
                    $detData['channel_id']      = $this->_channelId;
                    $detData['channel_name']    = $this->_channelName;
                    $detData['change_amount']	= $amount;
                    $detData['account_type']	= 1;   //通用账户
                    $detData['type']			= 4;   //充值收入
                    $detData['out_orderid']		= $orderid;
                    $detData['create_time']     = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
					if ( !$insertDetId) {
						throw new Exception("添加账户变动明细失败");
					}
					$updData = array();
					$updData['amount'] = Db::raw("amount+".$amount);
					$updData['update_time'] = time();
					$updResult = model('Channel')->where(['id'=>$this->_channelId])->update($updData);
					if (!$updResult) {
						throw new Exception("账户金额变动失败");
					}
				}
				else{
					$insertRechargeId = model("ChannelRecharge")->insert($rechargeData);
					if(!$insertRechargeId){
						throw new Exception("创建订单失败! ");
					}
				}
				// 提交事务
				Db::commit();
				$this->redis->del('recharge_pay_duplicate_'.$this->_channelId);

				if($recharge_type==2){  
					//第三方支付用的商品简单描述   (去除&+等特殊字符)
					$payBody = "用户充值";
					
					//支付方式是支付宝或者支付宝混合支付时
					if($paytype=='zfb'){
						//支付宝支付参数
						
						$alipayParam = $this->getAlipayAopParam($orderid,$rechargeData['real_amount'],$payBody);
						$result['alipay_param']      = $alipayParam;
					}
					//支付方式是微信官方h5支付或者微信官方h5混合支付时
					elseif($paytype=='wxpay')
					{
						$wxpayh5_param = $this->getKjWxpayScanParam($orderid,$rechargeData['real_amount'],$payBody);
						
						$result = ['wxpay_param'=>$wxpayh5_param];
					}
				}
			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();
				$this->jsonResult('', 0, '订单生成失败'.$e->getMessage());
			}
			$result['orderid']      = $orderid;
			
			$this->jsonResult($result, 20000, '订单生成成功');
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
    
    public function queryRechargeOrder(){
		if ($this->request->isPost()) {
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您无权限进行充值结果查询相关操作');
			}
			else{
				$orderid		= $this->input('orderid');            //充值订单号
				$out_trade_no	= $this->input('out_trade_no');    //第三方订单号
				
				if(!$orderid){
					$this->jsonResult('', 0, '请输入您要查询的充值订单号');
				}
				if(!$out_trade_no){
					$this->jsonResult('', 0, '请输入您要查询的第三方订单号');
				}
				$channelInfo   = model("Channel")->field("id,name,level,parent_id,amount,js_amount")->where(['id'=>$this->_channelId])->find();
				if(!$channelInfo){
					$this->jsonResult('', 0, '账号异常,请重新登录后再试');
				}

				$map = array();
				$map['orderid'] = $orderid;
				$map['channel_id'] = $this->_channelId;
				$rechargeInfo = model('ChannelRecharge')->field('id,status,amount,orderid,recharge_type,paytype')->where($map)->find();
				if(!$rechargeInfo){
					$this->jsonResult('', 0, '充值订单不存在或非您所属充值订单');
				}
				else if($rechargeInfo['recharge_type']<>2 || $rechargeInfo['paytype']<>'wxpay'){
					$this->jsonResult('', 0, '非微信直充订单,该接口不支持查询');
				}
				else{
					$rechargeStatus = 0;   //充值结果
					if($rechargeInfo['status']==1){
						$rechargeStatus = 1;
					}
					else{
						vendor('kjpaySdk.Kjpay','.class.php');
						$kjpay = new \Kjpay();
						$is_pay = json_decode($kjpay->queryOrder($out_trade_no), true);
						if($is_pay['data']['status']==1){
							$rechargeStatus = 1;
						//	$this->updateRehcargeOrder($orderid,$is_pay['data']['amount'],$is_pay['data']['trade_no']);
						}
					}
					$retData = array();
					$retData['recharge_status'] = $rechargeStatus;
					$this->jsonResult($retData, 20000, '查询充值结果成功');
				}
			}
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }

    /**
     * 获取支付宝支付参数
     * 
     * @param string $orderid 订单号
     * @param float $real_amount 支付金额
     * @param string $productname 商品名称
     * @param string $productdesc 描述
     * 
     * @return string 支付参数字符串
     */
    private function getAlipayAopParam($orderid,$real_amount,$productname,$productdesc='')
    {
    	//Alipay Aop Sdk支付发
		Loader::import('alipay.pagepay.service.AlipayTradeService');
        Loader::import('alipay.pagepay.buildermodel.AlipayTradePagePayContentBuilder');

		$payRequestBuilder = new \AlipayTradePagePayContentBuilder();

        $payRequestBuilder->setBody(preg_replace("/[\&\+]+/", '', $productdesc));
        $payRequestBuilder->setSubject(preg_replace("/[\&\+]+/", '', $productname));
        $payRequestBuilder->setTotalAmount($real_amount);
        $payRequestBuilder->setOutTradeNo($orderid);

        $aop = new \AlipayTradeService(config('alipayAop'));
        $result = $aop->pagePay($payRequestBuilder,config('alipayAop')['return_url'],config('alipayAop')['notify_url']);
        return $result;
  //       exit;
		//Alipay Aop Sdk支付发起
		// vendor('alipayAop.AopSdk');
  //       $aop = new \AopClient();
		// $aop->gatewayUrl = 'https://openapi.alipaydev.com/gateway.do';
		// $aop->appId = Config::get('alipayAop')["app_id"];
		// $aop->rsaPrivateKey = Config::get('alipayAop')['merchant_private_key'];
		// $aop->alipayrsaPublicKey = Config::get('alipayAop')['alipay_public_key'];
		// $aop->apiVersion = '1.0';
		// $aop->signType = Config::get('alipayAop')['sign_type'];
		// $aop->postCharset = 'utf-8';
		// $aop->format = 'json';

		// $request = new \AlipayTradeAppPayRequest();
  //       $bizcontent = array();
		// $bizcontent['subject']			= preg_replace("/[\&\+]+/", '', $productname);      // 主题(过滤特殊字符)
		// $bizcontent['body']				= preg_replace("/[\&\+]+/", '', $productdesc);      // 商品描述信息(过滤特殊字符)
		// $bizcontent['out_trade_no']		= $orderid;                                         // 订单号
		// $bizcontent['timeout_express']	= '30m';										// 超时时间
		// $bizcontent['total_amount']		= $real_amount;									// 支付金额
		// $bizcontent['product_code']		= 'QUICK_MSECURITY_PAY';						// 支付方式

		// $request->setBizContent(json_encode($bizcontent));
		// $request->setNotifyUrl(Config::get('alipayAop')['notify_url']);
		// $result = $aop->pageExecute($request);

	//	echo htmlspecialchars($result);
		// return $result;
    }

    /**
     * 快接微信h5支付
     *
     * @param string $orderid 订单号
     * @param float $real_amount 支付金额
     * @param string $body 商品名称
     *
     * @return array 支付参数数组
     */
    private function getKjWxpayScanParam($orderid,$real_amount,$body)
    {
        $pay_return = false;
        
		vendor('kjpaySdk.Kjpay','.class.php');
        $kjpay = new \Kjpay();
		$return_url = Config::get('wxpay-h5-kj')['return_url']."?orderid=".$orderid;
		$notify_url = Config::get('wxpay-h5-kj')['notify_url'];

		$response = $kjpay->scanpay($body, $orderid, $real_amount,$return_url,$notify_url);
	//	var_dump($response);

        if (!$response) {
            return false;
        }
        
        $result = $response;
        
        if( array_key_exists("status", $result) && $result["status"] == "1")
        {
            $pay_return['image']		= $result['data']['image'];
			$pay_return['trade_no']		= $result['data']['trade_no'];
            $pay_return['referer']      = Config::get('wxpay-h5-kj')['referer'];
        }
        else{
            throw new \Exception(' 错误代码:'.$result["code"].' 错误代码描述:'.$result["msg"]);
        }
      
        return $pay_return;
    }
    /**
     * 更新订单信息
     * 
     * @param string $orderid:订单编号
     * @param string $real_amount:第三方支付的金额（现金部分）
     * 
     * @return boolean
     */
    private function updateRehcargeOrder($orderid,$real_amount='',$trans_code='')
    {
        $result         = false;
        
        //消费订单信息
        $rechargeInfo = model('ChannelRecharge')->field('id,orderid,channel_id,channel_name,send_channel_id,status,amount,real_amount,recharge_type,paytype')->where(['orderid'=>$orderid])->find();

        if($real_amount!='' && $rechargeInfo['real_amount']!=$real_amount)
        {
            $result = false;
            
            //写入日志
            log_message('充值订单编号:'.$orderid.'支付金额不一致,订单状态更新失败','error',LOG_PATH . '../paylog/');
        }
        //待支付状态时
        elseif($rechargeInfo['status']==0){
            $id = $rechargeInfo['id'];
            // 启动事务
            Db::startTrans();
            try{
				$rechargeData = array();
				$rechargeData['id'] = $id;
				$rechargeData['status'] = 1;
				$rechargeData['finish_time'] = NOW_TIMESTAMP;
				$rechargeData['check_remark'] = '充值查询处理';
				$rechargeData['check_admin_type'] = 1;
				$rechargeData['check_admin_name'] = '系统自动';
				$rechargeData['finish_status'] = 1;
				$rechargeData['out_order_no'] = $trans_code;

				$result = model('ChannelRecharge')->where(['id'=>$id,'status'=>array('in',[0])])->update($rechargeData);
				if (!$result) {
					throw new Exception("充值订单查询处理失败");
				}

				$detData = array();
                $detData['channel_id']      = $rechargeInfo['channel_id'];
                $detData['channel_name']    = $rechargeInfo['channel_name'];
                $detData['change_amount']	= $rechargeInfo['amount'];
                $detData['account_type']	= 1;   //通用账户
                $detData['type']			= 4;   //充值收入
                $detData['out_orderid']		= $rechargeInfo['orderid'];
                $detData['create_time']     = NOW_TIMESTAMP;
                $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
				if ( !$insertDetId) {
					throw new Exception("添加账户变动明细失败");
				}
				$updData = array();
				$updData['amount'] = Db::raw("amount+".$rechargeInfo['amount']);
				$updData['update_time'] = time();
				$updToResult = model('Channel')->where(['id'=>$rechargeInfo['channel_id']])->update($updData);
				if (!$updToResult) {
					throw new Exception("账户金额变动失败");
				}

                $result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                
                $result = false;
                
                //写入日志
                log_message('订单编号:'.$orderid.'充值订单查询处理状态更新失败'.$e->getMessage(),'error',LOG_PATH . '../paylog/');
            }
			log_message('订单编号:'.$orderid.'充值订单查询处理状态更新成功','error',LOG_PATH . '../paylog/');
        }
		else{
			$result = false;
			log_message('订单编号:'.$orderid.'充值订单查询处理状态更新失败:该充值订单已处理过','error',LOG_PATH . '../paylog/');
		}
    }
}
