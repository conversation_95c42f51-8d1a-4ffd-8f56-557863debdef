<?php

/**
 * 直充的服务器异步通知控制器
 * 
 */

namespace app\mcpsapi\controller;

use app\common\controller\Base;
use app\common\library\WeixinPay;
use think\Config;
use think\Db;
use think\Controller;
class RechargeNotify extends Base  {
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {

    }
    
    /**
     * 支付宝Aop支付服务器异步通知页面方法
     */
    public function notify_url() {   

		import('alipay.pagepay.service.AlipayTradeService',EXTEND_PATH);
        $arr =  request()->post();
        $alipaySevice = new \AlipayTradeService(config('alipayAop'));
        /**
         * 说明:校验
         * 1.通过正常写入数据库 返回给支付宝success
         * 2.错误的情况下返回失败通知支付宝fail
         */
        $result = $alipaySevice->check($arr);

        if ($result) {
            // 处理支付成功后的逻辑业务
            if ($arr['trade_status'] == 'TRADE_SUCCESS') {
				//写入日志
                log_message($result,'log',LOG_PATH . '../paylog/');
                
                $out_trade_no = input('out_trade_no');      //订单号
                $trade_no = input('trade_no'); 
                if(!empty($out_trade_no)){
                    
                    //更新订单状态处理
                    $this->updateRehcargeOrder($out_trade_no,input('total_fee'),$trade_no);
                }
                
                echo 'success';exit;   //请不要修改或删除
            }
            echo 'fail';exit;  //验证失败
        }
        echo 'fail';exit;
    }
    /**
     * 支付宝同步通知服务器页面
     * @return [type]        [description]
     */
    public function return_url()
    {
        /* *
         * 功能：支付宝页面跳转同步通知页面
         * 
         *************************页面功能说明*************************
         */
        import('alipay.pagepay.service.AlipayTradeService',EXTEND_PATH);
        $arr =  request()->post();
        $alipaySevice = new \AlipayTradeService(config('alipayAop'));
        /**
         * 说明:校验
         * 1.通过正常写入数据库
         * 2.错误的情况下返回失败通知前端页面
         */
        $result = $alipaySevice->check($arr);
        if ($result) {
            //写入日志
            log_message($result,'log',LOG_PATH . '../paylog/');
            
            $out_trade_no = input('out_trade_no');      //订单号
            $trade_no = input('trade_no');      //订单号
            if(!empty($out_trade_no)){
                
                //更新订单状态处理
                $this->updateRehcargeOrder($out_trade_no,input('total_amount'),$trade_no);
            }
            return json(['data'=>'','code'=>20000,'msg'=>'支付完成']);
        }
        return json(['data'=>'','code'=>10001,'msg'=>'支付失败,请重新支付']);
    }
    /**
     * 快接微信h5支付
     */
    public function wxpayh5kj()
    {
        $request_data = file_get_contents('php://input');
        
        //写入日志
        log_message('快接微信h5支付回调参数:'.$request_data,'log',LOG_PATH . '../paylog/');
                
		vendor('kjpaySdk.Kjpay','.class.php');
        $kjpay = new \Kjpay();

		$data = $_POST;
		$retjson = $kjpay->notifyVerify($data);
		if($retjson && $retjson['status']=='Success'){
            $orderid = $retjson['merchant_order_no'];
			$trans_code = trim($retjson['trade_no']);
            $amount = floatval($retjson['amount']);
            //订单编号不为空时
            if(!empty($orderid)){
                //更新订单状态处理
                $this->updateRehcargeOrder($orderid,$amount,$trans_code);
            }
            echo "success";
			die();
        }
        else{
            log_message('快接微信h5支付签名验证失败:'.print_r($data,true),'error',LOG_PATH . '../paylog/');
			echo 'fail';
            exit();
        }
    }
    
    /**
     * 更新订单信息
     * 
     * @param string $orderid:订单编号
     * @param string $real_amount:第三方支付的金额（现金部分）
     * 
     * @return boolean
     */
    private function updateRehcargeOrder($orderid,$real_amount='',$trans_code='')
    {
        $result         = false;
        
        //消费订单信息
        $rechargeInfo = model('ChannelRecharge')->field('id,orderid,channel_id,channel_name,send_channel_id,status,amount,real_amount,recharge_type,paytype')->where(['orderid'=>$orderid])->find();

        if($real_amount!='' && $rechargeInfo['real_amount']!=$real_amount)
        {
            $result = false;
            
            //写入日志
            log_message('充值订单编号:'.$orderid.'支付金额不一致,订单状态更新失败','error',LOG_PATH . '../paylog/');
        }
        //待支付状态时
        elseif($rechargeInfo['status']==0){
            $id = $rechargeInfo['id'];
            // 启动事务
            Db::startTrans();
            try{
				$rechargeData = array();
				$rechargeData['id'] = $id;
				$rechargeData['status'] = 1;
				$rechargeData['finish_time'] = NOW_TIMESTAMP;
				$rechargeData['check_remark'] = '充值回调处理';
				$rechargeData['check_admin_type'] = 1;
				$rechargeData['check_admin_name'] = '系统自动';
				$rechargeData['finish_status'] = 1;
				$rechargeData['out_order_no'] = $trans_code;

				$result = model('ChannelRecharge')->where(['id'=>$id,'status'=>array('in',[0])])->update($rechargeData);
				if (!$result) {
					throw new Exception("充值订单回调处理失败");
				}

				$detData = array();
                $detData['channel_id']      = $rechargeInfo['channel_id'];
                $detData['channel_name']    = $rechargeInfo['channel_name'];
                $detData['change_amount']	= $rechargeInfo['amount'];
                $detData['account_type']	= 1;   //通用账户
                $detData['type']			= 4;   //充值收入
                $detData['out_orderid']		= $rechargeInfo['orderid'];
                $detData['create_time']     = NOW_TIMESTAMP;
                $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
				if ( !$insertDetId) {
					throw new Exception("添加账户变动明细失败");
				}
				$updData = array();
				$updData['amount'] = Db::raw("amount+".$rechargeInfo['amount']);
				$updData['update_time'] = time();
				$updToResult = model('Channel')->where(['id'=>$rechargeInfo['channel_id']])->update($updData);
				if (!$updToResult) {
					throw new Exception("账户金额变动失败");
				}

                $result = true;
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                
                $result = false;
                
                //写入日志
                log_message('订单编号:'.$orderid.'充值订单回调处理状态更新失败'.$e->getMessage(),'error',LOG_PATH . '../paylog/');
            }
			log_message('订单编号:'.$orderid.'充值订单回调处理状态更新成功','error',LOG_PATH . '../paylog/');
        }
		else{
			$result = false;
			log_message('订单编号:'.$orderid.'充值订单回调处理状态更新失败:该充值订单已处理过','error',LOG_PATH . '../paylog/');
		}
    }
}
