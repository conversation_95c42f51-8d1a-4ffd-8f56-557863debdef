<?php
/**
 * 登录控制器
 */
namespace app\mcpsapi\controller;
use app\common\model\Admin as AdminModel;
use app\common\library\FileUpload;
use app\common\model\Setting;
use app\common\model\GameInfo;
use think\Db;
use think\Env;
use think\Session;
use think\Config;
use think\Exception;

class Account extends Guild
{
    protected $_model;//模块

    protected $_channelId;//渠道ID

    protected function _initialize()
    {
        parent::_initialize();
        $this->_model = model('Game');
        $guildInfo = session('guild_info');
        $this->_channelId = $guildInfo['channel_id'];
        $this->_level = $guildInfo['level'];

    }
    /**
     * 获取所有该账号从属的C账号
     *
     * @param $adminInfo array 登录用户信息
     *
     */
    public function getChildAccount()
    {
        $gameid = input('gameIds',0,'intval');
        $level  = Db::name('nw_channel')->where(['id'=>$this->_channelId])->value('level');
        $exist_channel = Db::name('cy_sdkgamelist')->where(['gameid'=>$gameid])->column('channel_id');
        $apply_channel = Db::name('nw_game_package_apply')->where(['game_id'=>$gameid,'apply_status'=>0])->column('channel_id');//白名单申请列表
        $ChildAccount = '';
        switch ($level) {
          case '1'://B
            $parent_id = Db::name('nw_channel')->where(['id'=>$this->_channelId])->value('parent_id');
            $in_path = ','.$parent_id.','.$this->_channelId.',';
            $ChildAccount = Db::name('nw_channel')->alias('channel')
						->join('nw_channel_frozen frozen', 'channel.id = frozen.channel_id and frozen.game_id='.$gameid,'LEFT')
						->field('channel.id,channel.name,IFNULL(frozen.subpackage, 0) as subpackage')
						->where(['channel.id_path'=>['LIKE', '%' . $in_path . '%'],'channel.level'=>3,'channel.id'=>['not in',$exist_channel]])
						->select();
            break;
          case '2'://B-
            $parent_id = Db::name('nw_channel')->where(['id'=>$this->_channelId])->value('parent_id');
            $in_path = ','.$parent_id.','.$this->_channelId.',';
            $ChildAccount = Db::name('nw_channel')->alias('channel')->field('channel.id,channel.name,IFNULL(frozen.subpackage, 0) as subpackage')->where(['channel.id_path'=>['LIKE', '%' . $in_path . '%'],'channel.level'=>3,'channel.id'=>['not in',$exist_channel]])->join('nw_channel_frozen frozen', 'channel.id = frozen.channel_id and frozen.game_id='.$gameid,'LEFT')->select();
            break;
          case '3'://C
            $ChildAccount = Db::name('nw_channel')->field('id,name')->where(['id'=>$this->_channelId,'channel.id'=>['not in',$exist_channel]])->find();
            break;
          default://B+
            return json(['data'=>$ChildAccount,'code'=>10012,'msg'=>'B+端无权限分包']);
            break;
        }
        foreach ($ChildAccount as $key => $value) {
          $ChildAccount[$key]['apply'] = in_array($value['id'],$apply_channel)?1:0;
        }
        return json(['data'=>$ChildAccount,'code'=>20000,'msg'=>'获取C端账号成功']);
    }
    /**
     * i协议阅读
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function doReadStatus()
    {
      $read_status = input('read_status', 0, 'intval');
      $data['update_time'] = NOW_TIMESTAMP;
      $data['read_status'] = $read_status;
      $res = Db::name('nw_channel_admin')->where(['channel_id' => $this->_channelId])->update($data);
      if ($res) {
        $guild_info = session('guild_info');
        if ($data['read_status']) {
          $guild_info['read_status'] = $data['read_status'];
        }
        session('guild_info', $guild_info);
        return json(['data'=>[],'code'=>20000,'msg'=>'阅读协议成功']);
      }else{
        return json(['data'=>[],'code'=>10020,'msg'=>'网络错误刷新后重试']);
      }
    }
    /**
     * 获取账号个人信息
     * @param string $value [description]
     */
    public function edit()
    {
      $editInfo = Db::name('nw_channel_admin')->field(['id','username','mobile','qq','sex','head_img'])->where(['channel_id' => $this->_channelId])->find();
      $editInfo['head_img'] == null && $editInfo['head_img'] = 'image/cps/headimg/user.png';
      return json(['data'=>$editInfo,'code'=>20000,'msg'=>'获取账号信息成功']);
    }

    /**
     * 修改账户个人信息
     * @param string $value [description]
     */
    public function doEdit()
    {
      $fileUpload = new FileUpload();
      $data = $this->request->post();
      $head_img = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 2048000)->set('dir','image/personpic/'.$this->_channelId.'/headimg/')->upload(request()->file('head_img'));//头像
      if ($head_img) {
        $data['head_img'] = $head_img;
      }
      $data['update_time'] = NOW_TIMESTAMP;
      $res = Db::name('nw_channel_admin')->where(['channel_id' => $this->_channelId])->update($data);
      if ($res) {
        $guild_info = session('guild_info');
        if ($head_img) {
          $guild_info['head_img'] = $head_img;
        }
        if ($data['qq']) {
          $guild_info['qq'] = $data['qq'];
        }
        if ($data['sex']) {
          $guild_info['sex'] = $data['sex'];
        }
        session('guild_info', $guild_info);
        return json(['data'=>[],'code'=>20000,'msg'=>'修改账号信息成功']);
      }else{
        return json(['data'=>[],'code'=>10020,'msg'=>'网络错误刷新后重试']);
      }

    }
    /**
     * 修改账户详细信息
     * @param string $value [description]
     */
    public function doEditInfo()
    {
      $type = input('type');//0 个人 1企业
      $guild_info = session('guild_info');
      switch ($type) {
        case '0':
          $data = [
              'type'           => 0,
              'channel_id'     => $this->_channelId,
              'real_name'      => input('name'),
              'person_id'      => input('person_id'),
              'linkman_mobile' => input('linkman_mobile'),
              'linkman_email'  => input('linkman_email'),
              'address'        => input('address'),
              'create_time'    => NOW_TIMESTAMP
          ];
          $result = $this->validate($data, [
              ['real_name', 'require|max:10|/^[\x{4e00}-\x{9fa5}]+$/u', '姓名不能为空|名称不能超过10个字符|请输入中文'],
              ['person_id', ['require','length:18','regex:/^[1-9]\d{5}(19|20)\d{2}[01]\d[0123]\d\d{3}[xX\d]$/'], '身份证号码不能为空|身份证号码需要18个字符|身份证格式错误'],
              ['linkman_mobile', 'require|length:11|/^1[3456789]\d{9}$/ims', '手机号码不能为空|手机号码需要11个数字|手机号码格式错误'],
              ['linkman_email', 'require|email', '邮箱必须填写|邮箱格式错误'],
              ['address', 'require|max:50', '地址必须填写|地址长度限制50字符'],
          ]);
          if (true !== $result) {
            return json(['data'=>[],'code'=>10013,'msg'=>$result]);
          }
          $res = Db::name('nw_channel_info')->insert($data);
          if ($res) {
            $guild_info['person_id'] = 1;
            session('guild_info', $guild_info);
            return json(['data'=>[],'code'=>20000,'msg'=>'实名认证成功']);
          }else{
            return json(['data'=>[],'code'=>10013,'msg'=>$res]);
          }
          break;
        case '1'://企业
          $data = [
                'type'            => 1,
                'channel_id'      => $this->_channelId,
                'company_name'    => input('company_name'),//企业名称
                'us_code'         => input('us_code'),//信用码
                'leson_name'      => input('leson_name'),//法人姓名
                'leson_person_id' => input('person_id'),
                'linkman_mobile'  => input('linkman_mobile'),
                'linkman_email'   => input('linkman_email'),
                'address'         => input('address'),
                'create_time'     => NOW_TIMESTAMP
            ];
          $result = $this->validate($data, [
              ['leson_name', 'require|max:10|/^[\x{4e00}-\x{9fa5}]+$/u', '法人姓名不能为空|法人名称不能超过10个字符|请输入中文'],
              ['company_name', 'require|max:20', '公司名称不能为空|公司名称不能超过20个字符'],
              ['us_code',['require','regex:[1-9A-GY]{1}[1239]{1}[1-9]{1}[0-9]{5}[0-9A-Z]{10}'], '统一社会信用码不能为空|统一社会信用码格式错误'],
              ['leson_person_id', ['require','length:18','regex:/^[1-9]\d{5}(19|20)\d{2}[01]\d[0123]\d\d{3}[xX\d]$/'], '身份证号码不能为空|身份证号码需要18个字符|身份证格式错误'],
              ['linkman_mobile', 'require|length:11|/^1[3456789]\d{9}$/ims', '手机号码不能为空|手机号码需要11个数字|手机号码格式错误'],
              ['linkman_email', 'require|email', '邮箱必须填写|邮箱格式错误'],
              ['address', 'require|max:50', '地址必须填写|地址长度限制50字符'],
          ]);
          if (true !== $result) {
            return json(['data'=>[],'code'=>10013,'msg'=>$result]);
          }
          $res = Db::name('nw_channel_info')->insert($data);
          if ($res) {
            $guild_info['person_id'] = 1;
            session('guild_info', $guild_info);
            return json(['data'=>[],'code'=>20000,'msg'=>'实名认证成功']);
          }else{
            return json(['data'=>[],'code'=>10013,'msg'=>$res]);
          }
          break;
        default:
          return json(['data'=>[],'code'=>10013,'msg'=>'选择认证类型错误']);
          break;
      }
    }
    /**
     * 发送短信
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function sendMsg()
    {
      $data = ['mobile'  => input('mobile')];
      $result = $this->validate($data, [
          ['mobile', 'require|isEmpty|mobile', '手机号码必须填写|手机号码不能为空|请输入正确的手机号码'],
      ]);
      if (true !== $result) {
          return json(['data'=>'','code'=>10014,'msg'=>$result]);
      }
      //判断该手机号码是否被绑定过
      $exist_res =  Db::name('nw_channel_admin')->where(['channel_id' => ['neq',$this->_channelId],'mobile'=>$data['mobile']])->find();
      if ($exist_res) {
        return json(['data'=>'','code'=>10014,'msg'=>'该手机号码已经被其他账号绑定!']);
      }
      $res = (new \app\common\library\Sms)->sendCode($data['mobile']);
      if ($res['status'] == true) {
        return json(['data'=>'','code'=>20000,'msg'=>'发送短信验证码成功']);
      }else{
        return json(['data'=>'','code'=>10014,'msg'=>$res['msg']]);
      }
    }
    /**
     * 安全认证
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function safeAuthentication()
    {
      $action = input('action');//action 执行模块安全认证操作
      $guild_info = session('guild_info');
      switch ($action) {
        case '2'://绑定手机号码
          $mobile = $guild_info['mobile'];
          if ($mobile) {
            $data = $this->request->post();
            $result = $this->validate($data, [
                ['mobile', 'require|isEmpty|mobile', '手机号码必须填写|手机号码不能为空|请输入正确的手机号码'],
                ['sms_code', 'require|isEmpty', '验证码必须填写|验证码不能为空'],
                ['new_mobile', 'require|isEmpty|mobile', '新手机号码必须填写|新手机号码不能为空|请输入新手机号码'],
                ['new_sms_code', 'require|isEmpty', '新手机验证码必须填写|新手机验证码不能为空'],
            ]);//验证器
            if (true !== $result) {
                return json(['data'=>'','code'=>10015,'msg'=>$result]);
            }
            if ($data['new_mobile'] == $mobile) {
                return json(['data'=>'','code'=>10015,'msg'=>'手机号跟原绑定手机号一样，请重新填写']);
            }
            $res = (new \app\common\library\Sms)->checkCode($data['mobile'], $data['sms_code']);
            if ($res['status'] == false) {
                return json(['data'=>'','code'=>10015,'msg'=>'原手机'.$res['msg']]);
            }
            $new_res = (new \app\common\library\Sms)->checkCode($data['new_mobile'], $data['new_sms_code']);
            if ($new_res['status'] == false) {
                return json(['data'=>'','code'=>10015,'msg'=>'新手机'.$new_res['msg']]);
            }
            if (Db::name('nw_channel_admin')->where(['channel_id' => $guild_info['channel_id']])->update(['mobile' => $data['new_mobile'],'update_time'=>time()])) { // 插入玩家历史记录
                Db::table('cy_member_history')->insert([
                    'userid'      => $guild_info['id'], //todo
                    'password'    => '',
                    'mobile'      => $data['new_mobile'],
                    'ip'          => request()->ip(1),
                    'create_time' => time(),
                ]);
                $guild_info['mobile'] = $data['new_mobile'];
                session('guild_info',$guild_info);
                return json(['data'=>'','code'=>20000,'msg'=>'绑定手机号码成功']);
            }
            return json(['data'=>'','code'=>10015,'msg'=>'手机验证码验证失败']);
          }else{
            $data = $this->request->post();
            $result = $this->validate($data, [
                ['new_mobile', 'require|isEmpty|mobile', '手机号码必须填写|手机号码不能为空|请输入正确的手机号码'],
                ['new_sms_code', 'require|isEmpty', '验证码必须填写|验证码不能为空'],
            ]);
            if (true !== $result) {
                return json(['data'=>'','code'=>10015,'msg'=>$result]);
            }
            $res = (new \app\common\library\Sms)->checkCode($data['new_mobile'], $data['new_sms_code']);
            if ($res['status'] == false) {
                return json(['data'=>'','code'=>10015,'msg'=>$res['msg']]);
            }
            if (Db::name('nw_channel_admin')->where(['channel_id' => $guild_info['channel_id']])->update(['mobile' => $data['new_mobile'],'update_time'=>time()])) { // 插入玩家历史记录
                Db::table('cy_member_history')->insert([
                    'userid'      => $guild_info['id'], //todo
                    'password'    => '',
                    'mobile'      => $data['new_mobile'],
                    'ip'          => request()->ip(1),
                    'create_time' => time(),
                ]);
                $guild_info['mobile'] = $data['new_mobile'];
                session('guild_info',$guild_info);
                return json(['data'=>'','code'=>20000,'msg'=>'绑定手机号码成功']);
            }
            return json(['data'=>'','code'=>10015,'msg'=>'手机验证码验证失败']);
          }
          break;
        case '3'://登录密码
          $mobile = $guild_info['mobile'];
          if ($mobile) {
            $data = $this->request->post();
            $result = $this->validate($data, [
                ['new_mobile', 'require|isEmpty|mobile', '手机号码必须填写|手机号码不能为空|请输入正确的手机号码'],
                ['new_sms_code', 'require|isEmpty', '验证码必须填写|验证码不能为空'],
                ['new_password', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
            ]);
            if (true !== $result) {
                return json(['data'=>'','code'=>10015,'msg'=>$result]);
            }
            $res = (new \app\common\library\Sms)->checkCode($data['new_mobile'], $data['new_sms_code']);
            if ($res['status'] == false) {
                return json(['data'=>'','code'=>10015,'msg'=>$res['msg']]);
            }
            $res = Db::name('nw_channel_admin')->where(['channel_id' => $guild_info['channel_id']])->update(['password' => mg_password($data['new_password']),'update_time'=>time()]);
            if ($res) { // 插入玩家历史记录
                return json(['data'=>'','code'=>20000,'msg'=>'登录密码修改成功']);
            }
          }else{
            return json(['data'=>'','code'=>10015,'msg'=>'您还未绑定手机号，暂时不能修改登录密码，请先绑定手机号。']);
          }
          break;
        case '4'://交易密码
          $mobile = $guild_info['mobile'];
          if ($mobile) {
            $data = $this->request->post();
            $pay_password = Db::name('nw_channel_admin')->where(['channel_id' => $guild_info['channel_id']])->value('pay_password');//判断是否有交易密码
            if (!$pay_password) {
              $result = $this->validate($data, [
                  ['new_paypassword', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
                  ['new_repaypassword', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
              ]);
              if ($data['new_paypassword'] != $data['new_repaypassword']) {
                  return json(['data'=>'','code'=>10015,'msg'=>'两次登录密码输入不一致!']);
              }
            }else{
              $result = $this->validate($data, [
                  ['new_mobile', 'require|isEmpty|mobile', '手机号码必须填写|手机号码不能为空|请输入正确的手机号码'],
                  ['new_sms_code', 'require|isEmpty', '验证码必须填写|验证码不能为空'],
                  ['new_paypassword', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
              ]);
              $res = (new \app\common\library\Sms)->checkCode($data['new_mobile'], $data['new_sms_code']);
              if ($res['status'] == false) {
                  return json(['data'=>'','code'=>10015,'msg'=>$res['msg']]);
              }
            }
            if (true !== $result) {
                return json(['data'=>'','code'=>10015,'msg'=>$result]);
            }


            $res = Db::name('nw_channel_admin')->where(['channel_id' => $guild_info['channel_id']])->update(['pay_password' => mg_password($data['new_paypassword']),'update_time'=>time()]);
            if ($res) {
                return json(['data'=>'','code'=>20000,'msg'=>'交易密码更新成功']);
            }
          }else{
            return json(['data'=>'','code'=>10015,'msg'=>'您还未绑定手机号，暂时不能修改交易密码，请先绑定手机号。']);
          }
          break;
        case '5'://二级密码
          $mobile = $guild_info['mobile'];
          if ($mobile) {
            $data = $this->request->post();
            $secondary_password = Db::name('nw_channel_admin')->where(['channel_id' => $guild_info['channel_id']])->value('secondary_password');//判断是否有二级密码
            if (!$secondary_password) {
              $result = $this->validate($data, [
                  ['new_sepassword', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
                  ['new_serepassword', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
              ]);
              if ($data['new_sepassword'] != $data['new_serepassword']) {
                  return json(['data'=>'','code'=>10015,'msg'=>'两次登录密码输入不一致!']);
              }
            }else{
              $result = $this->validate($data, [
                  ['new_mobile', 'require|isEmpty|mobile', '手机号码必须填写|手机号码不能为空|请输入正确的手机号码'],
                  ['new_sms_code', 'require|isEmpty', '验证码必须填写|验证码不能为空'],
                  ['new_sepassword', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],

              ]);
              $res = (new \app\common\library\Sms)->checkCode($data['new_mobile'], $data['new_sms_code']);
              if ($res['status'] == false) {
                  return json(['data'=>'','code'=>10015,'msg'=>$res['msg']]);
              }
            }

            if (true !== $result) {
                return json(['data'=>'','code'=>10015,'msg'=>$result]);
            }

            if ($data['new_sepassword'] != $data['new_serepassword']) {
                return json(['data'=>'','code'=>10015,'msg'=>'两次密码输入不一致!']);
            }

            $res = Db::name('nw_channel_admin')->where(['channel_id' => $guild_info['channel_id']])->update(['secondary_password' => mg_password($data['new_sepassword']),'update_time'=>time()]);
            if ($res) { // 插入玩家历史记录
              $guild_info['secondary_password'] = 1;
              session('guild_info', $guild_info);
              return json(['data'=>'','code'=>20000,'msg'=>'二级密码更新成功']);
            }
          }else{
            return json(['data'=>'','code'=>10015,'msg'=>'您还未绑定手机号，暂时不能修改二级密码，请先绑定手机号。']);
          }
          break;
        default://默认
          return json(['data'=>'','code'=>10015,'msg'=>'模块错误']);
          break;
      }
    }
    /**
     * 安全认证
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function detailInfoChange()
    {
      $fileUpload = new FileUpload();
      $guild_info = session('guild_info');
      $data = $this->request->post();//数据
      if (empty($data['bankNum']) && empty($data['zhifubao_number'])) {
        return json(['data'=>[],'code'=>10013,'msg'=>"支付宝账号或者银行卡账号必填一个"]);
      }
      $result = $this->validate($data, [
          ['name', 'require|max:50', '姓名/法人不能为空|姓名/法人不能超过50个字符'],
          ['person_id', ['require','length:18','regex:/^[1-9]\d{5}(19|20)\d{2}[01]\d[0123]\d\d{3}[xX\d]$/'], '身份证号码不能为空|身份证号码需要18个字符|身份证格式错误'],
          ['mobile', 'require|length:11|/^1[3456789]\d{9}$/ims', '手机号码不能为空|手机号码需要11个数字|手机号码格式错误'],
      ]);
      if (!empty($data['bankNum'])) {
        $result = $this->validate($data, [
          ['bankNum', 'max:25|min:15|number', '银行卡号码不能为空|银行卡号码格式错误|银行卡号码格式错误'],
          ['provinceCity', 'require', '开户省市不能为空'],
          ['bankName', 'require', '开户银行名称不能为空'],
          ['openBank', 'require', '开户银行不能为空'],
          // ['bankId', 'require', '开户银行编号不能为空'],
        ]);
      }
      if (true !== $result) {
        return json(['data'=>[],'code'=>10013,'msg'=>$result]);
      }

      $back_image_flag = request()->file('back_image_url');
      $apply_info = Db::name('nw_channel_info_apply')->where(['channel_id'=>$this->_channelId])->order('id desc')->value('apply_info');
      $json_info = json_decode($apply_info,1);
      if (!isset($back_image_flag)) {//查看数据库是否有之前保存身份证信息
        if ($apply_info) {
          $back_image_url = isset($json_info['back_image_url'])?$json_info['back_image_url']:'';
        }else{
          return json(['data'=>[],'code'=>10013,'msg'=>'身份证/营业执照背面未上传或者已失效请刷新后重试']);
        }
      }else{
        $back_image_url = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', ********)->set('dir','image/personpic/'.$this->_channelId.'/')->upload(request()->file('back_image_url'));//身份证照片后面
      }
      $front_image_flag = request()->file('front_image_url');
      if (!isset($front_image_flag)) {
        if ($apply_info) {
          $front_image_url = isset($json_info['front_image_url'])?$json_info['front_image_url']:'';
        }else{
          return json(['data'=>[],'code'=>10013,'msg'=>'身份证/营业执照正面未上传或者已失效请刷新后重试']);
        }
      }else{
        $front_image_url      = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', ********)->set('dir','image/personpic/'.$this->_channelId.'/')->upload(request()->file('front_image_url'));//身份证照片前面
      }
      if ($back_image_url && $front_image_url) {
        $data['back_image_url'] = $back_image_url;
        $data['front_image_url'] = $front_image_url;
      }else{
        return json(['data'=>[],'code'=>10013,'msg'=>'图片错误或者超过10M,请刷新后重试']);
      }
      $cont = [
        'channel_id' =>$this->_channelId,
        'channel_name' =>$guild_info['channel_name'],
        'apply_info' => json_encode($data),
        'status' => 0,
        'apply_time' => NOW_TIMESTAMP,
        'apply_admin_name' => $guild_info['channel_name'],
      ];
      $id = Db::name('nw_channel_info_apply')->where(['channel_id'=>$this->_channelId,'status'=>0])->value('id');
      if ($id) {//存在待审核中的数据的话 无法在提交审核
        return json(['data'=>[],'code'=>10013,'msg'=>'身份认证信息还在审核中,无法重复提交']);
      }
      //info 状态修改成0
      Db::startTrans();
      try {
        $res = Db::name('nw_channel_info_apply')->insertGetId($cont);
        if (!$res) {
          throw new Exception("身份认证审核提交失败!");
        }
        if (Db::name('nw_channel_info')->where(['channel_id'=>$this->_channelId])->value('id') && Db::name('nw_channel_info')->where(['channel_id'=>$this->_channelId])->value('apply_status')) {
          $statusUpadte = Db::name('nw_channel_info')->where(['channel_id'=>$this->_channelId])->update(['apply_status' => 0]);
          if (!$statusUpadte) {
            throw new Exception("修改身份认证状态失败!");
          }
        }
        Db::commit();
      } catch (Exception $e) {
        Db::rollback();
        return json(['data'=>'','code'=>10015,'msg'=>"提交审核失败： " . $e->getMessage()]);
      }

      //叮叮通知
      $template = '有新的身份认证申请：推广账号"'.$this->_channelName.'" 发起身份认证审核申请,请及时安排处理，时间：'.date('Y-m-d H:i:s');
      $ddurl = Env::get('operat_url');
      curlDD($template, $ddurl,true);
      return json(['data'=>[],'code'=>20000,'msg'=>'提交申请成功,等待后台审核']);
    }
    /**
     * 获取提交审核内容
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function getDetailInfo()
    {
      $res = Db::name('nw_channel_info_apply')->field('apply_info,status')->where(['channel_id'=>$this->_channelId])->order('id desc')->find();
      if ($res) {//check 提交信息表 查看是否有记录
        return json(['data'=>$res,'code'=>20000,'msg'=>'获取审核内容成功','status'=>$res["status"]]);
      }else{//不存在提交审核记录的情况下,去签署协议找到之前录入的姓名和身份证
        $res = Db::name('nw_channel_info')->field('real_name as name,person_id')->where(['channel_id'=>$this->_channelId])->find();
        return json(['data'=>['apply_info'=>json_encode($res),'status'=>3],'code'=>20000,'msg'=>'获取审核内容成功','status'=>3]);
      }

    }
    /**
     * 获取待审核列表
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function getExamineList()
    {
      if ($this->_level != 1) {
        return json(['data'=>'','code'=>20013,'msg'=>'只有会长有审核权限!']);
      }
      $page             = input('page',1);
      $pageSize         = input('pageSize',10);
      $status           = input('status');
      $apply_time_start = input('apply_time_start');
      $apply_time_end   = input('apply_time_end');
      $channel_id       = input('channel_id');
      $where = [];
      if (!empty($status) || $status === '0') {
          $where['apply.status'] = (int)$status;
      }
      if (!empty($channel_id)) {
          $where['apply.channel_id'] = $channel_id;
      }
      if (!empty($apply_time_start) && !empty($apply_time_end)) {
         $where['apply.apply_time'] = ['BETWEEN', [strtotime($apply_time_start),strtotime($apply_time_end)]];
      }
      // halt($where);
      $list = Db::name('nw_channel_info_apply')->alias('apply')
      ->field('apply.id,apply.channel_id,apply.apply_info,apply.remark,apply.status,apply.apply_time,channel.name')
      ->where($where)
      ->where(['channel.parent_id' => $this->_channelId])
      ->join('nw_channel channel', 'channel.id = apply.channel_id')
      ->paginate(['list_rows'=>$pageSize,'page'=>$page])->toArray();
       $channelList = Db::table('nw_channel')->field('id,name')->where(['level'=>2,'id_path'=>['LIKE', '%,' . $this->_channelId . ',%']])->select();
      return json(['data'=>$list,'code'=>20000,'msg'=>'获取列表信息成功','channelList'=>$channelList]);
    }

    /**
     * 更改审核状态
     */
    public function doExamine()
    {
        if ($this->_level != 1) {
          return json(['data'=>'','code'=>20013,'msg'=>'只有会长有审核权限!']);
        }
        $status     = input('status');
        $e_id       = input('e_id');
        $remark     = input('remark');
        $channel_id = input('channel_id');
        if (empty($status)) {
            return json(['data'=>'','code'=>10025,'msg'=>'请选择审核状态!']);
        }
        if (empty($status)) {
            return json(['data'=>'','code'=>10025,'msg'=>'请选择审核状态!']);
        }
        $data['status']           = $status;
        $data['remark']           = $remark;
        $data['check_time']       = NOW_TIMESTAMP;
        $data['check_admin_type'] = 2;
        $data['check_admin_name'] = $this->_channelId;
        $resultChannel = Db::name('nw_channel_info_apply')->where(['channel_id'=>$channel_id,'id'=>$e_id])->update($data);
        if ($resultChannel) {
            return json(['data'=>'','code'=>20000,'msg'=>'修改成功']);
        }


    }
    /**
     * 获取省数据
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function getProvince()
    {
      $province = Db::name('cy_city')->field('name,id,parent_id,type')->where(['type' => 1])->select();
      $city = Db::name('cy_city')->field('name,id,parent_id,type')->where(['type' => 2])->select();
      $out = [];
      foreach ($province as $key => $value) {
        $out[$value['id']]['value'] = $value['name'];
        $out[$value['id']]['label'] = $value['name'];
        $out[$value['id']]['children'] = [];
      }
      foreach ($city as $key => $value) {
        $out[$value['parent_id']]['children'][] = ['value'=>$value['name'],'label'=>$value['name']];
      }
      $out = array_values($out);
      return json(['data'=>$out,'code'=>20000,'msg'=>'获取省列表成功']);
    }
    /**
     * 子账号管理(列表)
     * @param string $value [description]
     */
    public function childManagerList()
    {
      $level        = Db::name('nw_channel')->where(['id'=>$this->_channelId])->value('level');//层级
      $ChildAccount = [];
      $page         = input('page',1);
      $pageSize     = input('pageSize',10);
      $where        = $this->_getCondition();

      switch ($level) {
        case 0://B+
          $ChildAccount = Db::name('nw_channel')->alias('channel')->field('channel.id,channel.name,channel.level,channel.status,from_unixtime(channel.create_time) as create_time ,channel.share_show,channel.replacement_status,admin.qq,admin.mobile,admin.sex,channel.mcps_settle_period as settle_period')->where($where)->where(['channel.parent_id' => $this->_channelId])->join('nw_channel_admin admin', 'admin.channel_id = nw_channel.id','LEFT')->paginate(['list_rows'=>$pageSize,'page'=>$page])->toArray();
          return json(['data'=>$ChildAccount,'code'=>20000,'msg'=>'获取列表信息成功']);
          break;
        case 1://B
          $parent_id = Db::name('nw_channel')->where(['id'=>$this->_channelId])->value('parent_id');
          $in_path = ','.$parent_id.','.$this->_channelId.',';
          $ChildAccount = Db::name('nw_channel')->alias('channel')->field('channel.id,channel.name,channel.level,channel.status,channel.create_time,parent.name as parent_name,channel.replacement_status,channel.share_show,channel.assets_show,admin.qq,admin.mobile,admin.sex')->where($where)->where(['channel.id_path'=>['LIKE', '%' . $in_path . '%']])->join('nw_channel parent', 'channel.parent_id = parent.id AND parent.level = 2','LEFT')->join('nw_channel_admin admin', 'admin.channel_id = nw_channel.id','LEFT')->paginate(['list_rows'=>$pageSize,'page'=>$page])->toArray();
          foreach ($ChildAccount['data'] as $key => $value) {
            $ChildAccount['data'][$key]["create_time"] = date('Y-m-d H:i:s',$value['create_time']);
            if ($value["level"] == 2) {
              $ChildAccount['data'][$key]["parent_name"] = '';
            }
          }
          return json(['data'=>$ChildAccount,'code'=>20000,'msg'=>'获取列表信息成功']);
          break;
        case 2://B-
          $ChildAccount = Db::name('nw_channel')->alias('channel')->field('channel.id,channel.name,channel.level,channel.status,from_unixtime(channel.create_time) as create_time,admin.qq,admin.mobile,admin.sex')->where($where)->where(['channel.parent_id' => $this->_channelId])->join('nw_channel_admin admin', 'admin.channel_id = nw_channel.id','LEFT')->paginate(['list_rows'=>$pageSize,'page'=>$page])->toArray();
          return json(['data'=>$ChildAccount,'code'=>20000,'msg'=>'获取列表信息成功']);
          break;
        default:
          return json(['data'=>'','code'=>10015,'msg'=>'模块错误']);
          break;
      }
    }
    /**
     * 子账号管理(编辑)
     * @param string $value [description]
     */
    public function childManagerDo()
    {
      $action = input('action');//模块 判断是编辑还是删除
      switch ($action) {
        case '1'://编辑
          $data = $this->request->post();
          if ($this->_channelLevel == 3) {//C账号没有子账号管理权限
            return json(['data'=>'','code'=>20013,'msg'=>'无核权限!']);
          }
          Db::startTrans();
          try {
            $result = $this->validate($data, [
                ['id', 'require', '必须参数错误'],
                ['status', 'require','必须参数错误'],
            ]);
            if (true !== $result) {
              throw new Exception($result);
            }
            $level  = Db::name('nw_channel')->where(['id'=>$data['id']])->value('level');
            $flag  = Db::name('nw_channel')->where(['id'=>$data['id'],'id_path'=>['LIKE', '%,' . $this->_channelId . ',%']])->find();
            //修改的账号必须是该登录账号的下级账号
            if ($this->_channelId >= $level  && !$flag) {
              throw new Exception('该账户不属于您所管理账户!');
            }
            if (($data['new_password'] != $data['new_repassword'])) {
              throw new Exception("两次登录密码输入不一致!");
            }


            $cont['update_time'] = time();
            if ($level == 1) {//B
              $cont['replacement_status'] = $data['replacement_status'];
              $cont['status']             = $data['status'];
              $cont['share_show']         = $data['share_show'];
            }elseif ($level == 2) {//B-
              $cont['assets_show'] = $data['assets_show'];
              $cont['status']      = $data['status'];
              $cont['share_show']  = $data['share_show'];
            }elseif ($level == 3) {//C
              $cont['status']      = $data['status'];
            }
            $pwdres = true;
            if (!empty($data['new_password'])) {
              $flag = preg_match('/(^[a-zA-Z]{1})[0-9a-z_$]{6,19}$/i',$data['new_password']);
              if (!$flag) {
                throw new Exception("密码只能以字母、数字组成并且长度6-20个字符!");
                // return json(['data'=>'','code'=>10015,'msg'=>'密码只能以字母、数字组成并且长度6-20个字符!']);
              }
              $pwdres = Db::name('nw_channel_admin')->where(['channel_id' => $data['id']])->update(['password' => mg_password($data['new_password']),'status'=>$data['status'],'update_time'=>time()]);
            }else{
              $pwdres = Db::name('nw_channel_admin')->where(['channel_id' => $data['id']])->update(['status'=>$data['status'],'update_time'=>time()]);
            }
            $res = Db::name('nw_channel')->where(['id' => $data['id']])->update($cont);
            if (!$res || !$pwdres) {
              throw new Exception("更新账号信息失败");
              // return json(['data'=>'','code'=>10016,'msg'=>'更新账号信息失败']);
            }
            Db::commit();
          } catch (Exception $e) {
            Db::rollback();
            return json(['data'=>'','code'=>10015,'msg'=>"失败： " . $e->getMessage()]);
          }
          return json(['data'=>'','code'=>20000,'msg'=>'更新账号信息成功']);
          break;
        case '2'://冻结
          $data = $this->request->post();
          $result = $this->validate($data, [
              ['id', 'require', '必须参数错误'],
          ]);
          if (true !== $result) {
            return json(['data'=>[],'code'=>10013,'msg'=>$result]);
          }
          $res = Db::name('nw_channel_admin')->where(['id' => $data['id']])->update(['status' => 0 ,'update_time'=>time()]);
          if ($res) {
              return json(['data'=>'','code'=>20000,'msg'=>'冻结账号成功']);
          }else{
            return json(['data'=>'','code'=>10017,'msg'=>'冻结账号失败']);
          }
          break;
        default:
          return json(['data'=>'','code'=>10015,'msg'=>'模块错误']);
          break;
      }
    }
    /**
     * 子账号管理(增加)
     * @param string $value [description]
     */
    public function childManagerAdd()
    {
      $data = $this->request->post();
      $data['name'] = trim($data['name']);
      $guildInfo = session('guild_info');
      $result = $this->validate($data, [
          ['level', 'require', '账号类型必须填写'],
          ['name', 'require','账号必须填写'],
          ['new_password', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
          ['new_repassword', ['require','regex:/(^[a-zA-Z]{1})[0-9a-z_$]{5,19}$/i'], '密码必须填写|密码只能以字母、数字组成并且长度6-20个字符'],
      ]);
      if (true !== $result) {
        return json(['data'=>[],'code'=>10013,'msg'=>$result]);
      }
      if (($data['new_password'] != $data['new_repassword'])) {
        return json(['data'=>'','code'=>10015,'msg'=>'两次登录密码输入不一致!']);
      }
      $level = $data['level'];
      if ($level == 1) {//B
        if ($this->_level !== 0) {
          return json(['data'=>'','code'=>10015,'msg'=>'只有联盟才能创建会长渠道!']);
        }
        $id_path = ','.$this->_channelId.',';
        Db::startTrans();
        try {
            //1. 添加渠道信息
            $channelModel = model('channel');
            $channelInfo = $channelModel->where(['name' => $data['name']])->find();
            if ( $channelInfo ) {
				throw new Exception("该渠道信息已存在");
            //    return json(['data'=>'','code'=>10015,'msg'=>"该渠道信息已存在"]);
            }

            $userInfo = Db::name('nw_channel_admin')->field('id')->where(['username' => $data['name']])->find();
            if ($userInfo) {
				throw new Exception("用户名已存在");
              //  return json(['data'=>'','code'=>10015,'msg'=>"用户名已存在"]);
            }

            //添加渠道信息
            $channelModel->save([
                'name'               => $data['name'],
                // 'replacement_status' => $data['replacement_status'],//代充
                // 'share_show'         => $data['share_show'],//分成比例
                'parent_id'          => $this->_channelId,
                'id_path'            => $id_path,
                'create_time'        => NOW_TIMESTAMP,
                'level'              => $level,
                'flag'               => 3,
                'status'             => 1
              ]);

            if ( !$channelModel->id ) {
           //   return json(['data'=>'','code'=>10015,'msg'=>"添加渠道账号失败"]);
			  throw new Exception("添加渠道账号失败");
            }

            $cont['username']             = $data['name'];
            $cont['password']             = mg_password($data['new_password']);
            $cont['status']               = 1;
            $cont['channel_id']           = $channelModel->id;
            $cont['create_time']          = NOW_TIMESTAMP;
            $cont['password_update_time'] = NOW_TIMESTAMP;

            $res = Db::name('nw_channel_admin')->insertGetId($cont);
            if ($res === false) {
                throw new Exception("添加渠道账号失败");
            }

			$cModel = model('channel');
			$c_name = $data['name'].'-c1';
			$cInfo = $channelModel->where(['name' => $c_name])->find();
			$cUserInfo = Db::name('nw_channel_admin')->field('id')->where(['username' => $c_name])->find();
			if(!empty($cInfo) || !empty($cUserInfo)){
				// 同步增加一个默认推广员账号 规则 原账号账号+ C + 两位随机数mt_rand(10,100)
				$c_name = $data['name'].'-c'.mt_rand(10,100);
				$cInfo = $channelModel->where(['name' => $c_name])->find();
				if ( $cInfo ) {
				   // return json(['data'=>'','code'=>10015,'msg'=>"该默认推广员信息已存在,请尝试重新创建"]);
					throw new Exception("该默认推广员信息已存在,请尝试重新创建");
				}

				$cUserInfo = Db::name('nw_channel_admin')->field('id')->where(['username' => $c_name])->find();
				if ($cUserInfo) {
					//return json(['data'=>'','code'=>10015,'msg'=>"该默认推广员名已存在,请尝试重新创建"]);
					throw new Exception("该默认推广员名已存在,请尝试重新创建");
				}
			}
            //添加渠道信息
            $cid = $cModel->insertGetId([
                'name'               => $c_name,
                'parent_id'          => $channelModel->id,
                'id_path'            => $id_path.$channelModel->id.',',
                'create_time'        => NOW_TIMESTAMP,
                'level'              => 3,
                'flag'               => 3,
                'status'             => 1
            ]);

            if ( !$cid) {
            //  return json(['data'=>'','code'=>10015,'msg'=>"添加默认推广员渠道账号失败"]);
			  throw new Exception("添加默认推广员渠道账号失败");
            }
            $c_cont['username']             = $c_name;
            $c_cont['password']             = mg_password('b123456');
            $c_cont['status']               = 1;
            $c_cont['channel_id']           = $cid;
            $c_cont['create_time']          = NOW_TIMESTAMP;
            $c_cont['password_update_time'] = NOW_TIMESTAMP;

            $cres = Db::name('nw_channel_admin')->insertGetId($c_cont);
            if ($cres === false) {
                throw new Exception("添加渠道账号失败");
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['data'=>'','code'=>10015,'msg'=>"添加失败： " . $e->getMessage()]);
        }
      }elseif ($level == 2) {//B-
        if ($this->_level !== 1) {
          return json(['data'=>'','code'=>10015,'msg'=>'只有会长才能创建子会长渠道!']);
        }
        $parent_id = Db::name('nw_channel')->where(['id'=>$this->_channelId])->value('parent_id');
        $id_path = ','.$parent_id.','.$this->_channelId.',';
        Db::startTrans();
        try {
            //1. 添加渠道信息
            $channelModel = model('channel');
            $channelInfo = $channelModel->where(['name' => $data['name']])->find();
            if ( $channelInfo ) {
				throw new Exception("该渠道信息已存在");
            //    return json(['data'=>'','code'=>10015,'msg'=>"该渠道信息已存在"]);
            }

            $userInfo = Db::name('nw_channel_admin')->field('id')->where(['username' => $data['name']])->find();
            if ($userInfo) {
				throw new Exception("用户名已存在");
            //    return json(['data'=>'','code'=>10015,'msg'=>"用户名已存在"]);
            }

            //添加渠道信息
            $channelModel->save([
                'name'        => $data['name'],
                // 'assets_show' => $data['assets_show'],//资产显示
                // 'share_show'  => $data['share_show'],//分成比例
                'parent_id'   => $guildInfo['channel_id'],
                'id_path'     => $id_path,
                'create_time' => NOW_TIMESTAMP,
                'level'       => $level,
                'flag'        => 3,
                'status'      => 1
              ]);
            if ( !$channelModel->id ) {
				throw new Exception("添加渠道账号失败");
             // return json(['data'=>'','code'=>10015,'msg'=>"添加渠道账号失败"]);
            }

            $cont['username']             = $data['name'];
            $cont['password']             = mg_password($data['new_password']);
            $cont['status']               = 1;
            $cont['channel_id']           = $channelModel->id;
            $cont['create_time']          = NOW_TIMESTAMP;
            $cont['password_update_time'] = NOW_TIMESTAMP;
            $res = Db::name('nw_channel_admin')->insertGetId($cont);
            if ($result === false) {
                throw new Exception("添加渠道账号失败");
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['data'=>'','code'=>10015,'msg'=>"添加失败： " . $e->getMessage()]);
        }
      }elseif ($level == 3) {//C
        if ($this->_level == 1 || $this->_level == 2) {
          $id_path = Db::name('nw_channel')->where(['id'=>$this->_channelId])->value('id_path').$this->_channelId.',';
          Db::startTrans();
          try {
              //1. 添加渠道信息
              $channelModel = model('channel');
              $channelInfo = $channelModel->where(['name' => $data['name']])->find();
              if ( $channelInfo ) {
                 // return json(['data'=>'','code'=>10015,'msg'=>"该渠道信息已存在"]);
				  throw new Exception("该渠道信息已存在");
              }

              $userInfo = Db::name('nw_channel_admin')->field('id')->where(['username' => $data['name']])->find();
              if ($userInfo) {
				  throw new Exception("用户名已存在");
                 // return json(['data'=>'','code'=>10015,'msg'=>"用户名已存在"]);
              }

              //添加渠道信息
              $channelModel->save([
                  'name'        => $data['name'],
                  'parent_id'   => $guildInfo['channel_id'],
                  'id_path'     => $id_path,
                  'create_time' => NOW_TIMESTAMP,
                  'level'       => $level,
                  'flag'        => 3,
                  'status'      => 1
                ]);
              if ( !$channelModel->id ) {
				  throw new Exception("添加渠道账号失败");
               // return json(['data'=>'','code'=>10015,'msg'=>"添加渠道账号失败"]);
              }
              ;

              $cont['username']             = $data['name'];
              $cont['password']             = mg_password($data['new_password']);
              $cont['status']               = 1;
              $cont['channel_id']           = $channelModel->id;
              $cont['create_time']          = NOW_TIMESTAMP;
              $cont['password_update_time'] = NOW_TIMESTAMP;

              $res = Db::name('nw_channel_admin')->insertGetId($cont);
              if ($result === false) {
                  throw new Exception("添加渠道账号失败");
              }

              Db::commit();
          } catch (\Exception $e) {
              Db::rollback();
              return json(['data'=>'','code'=>10015,'msg'=>"添加失败： " . $e->getMessage()]);
          }
        }else{
          return json(['data'=>'','code'=>10015,'msg'=>'目前该渠道无法创建推广员']);
        }

      }
      return json(['data'=>'','code'=>20000,'msg'=>'增加账号成功']);
    }
    /**
     * 搜索条件集合
     * @param  string $self [description]
     * @return [type]       [description]
     */
    protected function _getCondition()
    {
        $name         = input('name');
        $status       = input('status');
        $level        = input('level');

        $condition = [];

        if (! empty($name)) {
            $condition['channel.name'] = ['LIKE', '%' . $name . '%'];
        }

        if (is_numeric($status)) {
            $condition['channel.status'] = (int)$status;
        }

        if (is_numeric($level)) {
            $condition['channel.level'] = (int)$level;
        }

        return $condition;
    }
}
