<?php
/**
 * 登录控制器
 */
namespace app\mcpsapi\controller;
use app\common\model\Admin as AdminModel;
use app\common\model\Setting;
use app\common\model\GameInfo;
use app\common\library\Sms;
use app\common\logic\Websocket;
use think\Db;
use think\Session;
use think\Config;

class Login extends Guild
{

    public function _initialize(){}//不需要继承父级构造
    /**
     * 登录验证
     * @param  integer $allow_channel_id [description]
     * @return [type]                    [description]
     */
    public function checklogin($allow_channel_id=0)
    {
        if (!empty(session('guild_info'))) {
            return json(['data'=>session('guild_info'),'code'=>20000,'msg'=>'用户已经登录']);
        }
        $username = input('post.username','','trim');
        $password = input('post.password');
        $result = $this->validate(['username' => $username,'password' => $password],
            [
                ['username', 'require', '请输入用户名'],
                ['password', 'require', '请输入密码'],
            ]);
        if (true !== $result) {
            return json(['data'=>'','code'=>'10005','msg'=>$result]);
        }
        $adminModel     = new AdminModel();
        $settingModel   = new Setting();
        //外部渠道用户
        $adminInfo = Db::name('nw_channel_admin')->where(['username' => $username])->find();
        //后台登录密码有效期
        $password_valid_day = $settingModel::getSetting('PASSWORD_VALID_DAY');
        //短信验证码登录开关
        $isSmsLogin         = (int)$settingModel::getSetting('ADMIN_SMS_SWITCH');
      //  halt($isSmsLogin);
        if ( empty($adminInfo) ) {
            return json(['data'=>'','code'=>'10001','msg'=>'用户不存在']);
        }
        elseif($adminInfo['status']==0){
            return json(['data'=>'','code'=>'10002','msg'=>'该账号已被冻结，请联系相关人员']);
        }
        elseif ( $adminInfo['password'] != mg_password($password) ) {
            return json(['data'=>'','code'=>'10003','msg'=>'用户名或密码错误']);
        }
        //密码过期
        // elseif(empty($adminInfo['password_update_time']) || (time() - $adminInfo['password_update_time'] > $password_valid_day*86400)) {
        //     return json(['data'=>['id'=>$adminInfo['id']],'code'=>'10004','msg'=>'密码已过有效期,请重新设置']);
        // }

        //短信验证登录
        else if ( $adminInfo['login_check'] && $isSmsLogin) {
            if(empty($adminInfo['mobile'])){
                return json(['data'=>'','code'=>'10005','msg'=>'用户的手机号未填写，无法进行短信辅助验证']);
            }

            else{

                session('cps_loginsms_info',['id'=>$adminInfo['id'],'mobile'=>$adminInfo['mobile']]);   //需要短信验证时的临时session

                log_message('loginProcess:sms username='.$username.' mobile='.$adminInfo['mobile'].' session_id='.session_id(),'log',LOG_PATH . 'guildlogin/');
                if(empty(session('cps_loginsms_info'))){
                    log_message('loginProcess: no session username='.$username.' mobile='.$adminInfo['mobile'].' session_id='.session_id(),'log',LOG_PATH . 'guildlogin/');
                }
                return json(['data'=>['mobilelogin' => $adminInfo['mobile']],'code'=>20000,'msg'=>'需要短信验证']);
            }
        }  
        if($adminInfo['channel_id'] && $allow_channel_id){
            $allowChannelIds = model('Channel')->getChildIds($allow_channel_id);
            $allowChannelIds = array_merge($allowChannelIds,array($allow_channel_id));
            if(!in_array($adminInfo['channel_id'],$allowChannelIds)){
                return json(['data'=>'','code'=>'10006','msg'=>'该用户不属于规定的渠道']);
            }
        }
        // 登录
        return $this->doLogin($adminInfo);
    }

    /**
     * 发送验证码
     */
    public function sendSmsCode()
    {
        //需要短信验证时的临时session不存在时
        if(empty(session('cps_loginsms_info'))){
            log_message('sendSmsCode: no session post_data:'.print_r(input(),true).'  session_id='.session_id(),'log',LOG_PATH . 'adminlogin/');
            return json(['data'=>'','code'=>10006,'msg'=>'短信辅助验证的用户不存在，请重新操作']);
        }
        
        $mobile = session('cps_loginsms_info')['mobile'];
        if (empty($mobile)) {
            return json(['data'=>'','code'=>'10006','msg'=>'手机号不能为空']);
        }
        $result = (new \app\common\library\Sms)->sendCode($mobile);
        if ($result['status']) {
            return json(['data'=>'','code'=>20000,'msg'=>'发送成功，请查收']);
        } else {
            return json(['data'=>'','code'=>10016,'msg'=>$result['msg']]);
        }

    }
    /**
     * 验证码登录
     */
    public function loginSms()
    {
        $code     = input('post.sms_code');
        if($code==''){
            return json(['data'=>'','code'=>10016,'msg'=>'短信验证码不能为空']);
        }
        
        //需要短信验证时的临时session不存在时
        if(empty(session('cps_loginsms_info'))){
            log_message('loginSms: no session post_data:'.print_r(input(),true).'  session_id='.session_id(),'log',LOG_PATH . 'adminlogin/');
            return json(['data'=>'','code'=>10016,'msg'=>'短信辅助验证的用户不存在，请重新操作']);
        }
        
        $mobile = session('cps_loginsms_info')['mobile'];

        if($mobile==''){
            return json(['data'=>'','code'=>10016,'msg'=>'手机号不能为空']);
        }

        $sms = new Sms();
        $codeResult = $sms->checkCode($mobile, $code);

        if(!$codeResult['status']) {
            return json(['data'=>'','code'=>10016,'msg'=>$codeResult['msg']]);
        }
        $adminInfo = Db::name('nw_channel_admin')->where(['id' => session('cps_loginsms_info')['id']])->find();
        if ( empty($adminInfo) ) {
             return json(['data'=>'','code'=>10016,'msg'=>'用户名不存在或已被禁用']);
        }
        
        session('cps_loginsms_info',null);

        // 登录
        return $this->doLogin($adminInfo);
    }
    /**
     * 登录验证成功后处理
     * 
     * @param $adminInfo array 登录用户信息
     * 
     */
    private function doLogin($adminInfo)
    {

        $ws = new Websocket;

        $guild_info = $this->getDetailInfo($adminInfo);
        session('guild_info', $guild_info);//记录Session
        //写入登入日志
        Db::name('cy_loginlog')->insert([
                'username'      => $adminInfo['username'],
                'create_time'   => NOW_TIMESTAMP,
                'admin_id'      => $adminInfo['id'],
                'type'          => 2,
                'ip'            => $this->request->ip()
        ]);
        
        //Websocket绑定token和后台uid
        $ws->registerToken(session_id(), 'cps'.$adminInfo['id']);
        return json(['data'=>$guild_info,'code'=>20000,'msg'=>'登录成功']);

    }

    /**
     * 用户详情
     * @return [type] [description]
     */
    public function getInfo()
    {
        if (session('guild_info') == null) {
          return json(['data'=>'','code'=>10007,'msg'=>'您还没有登录！']);
        }
        $adminInfo = session('guild_info');
        $guild_info = $this->getDetailInfo($adminInfo);
        $guild_info['name,'] =  $guild_info['username'];
        $guild_info['avatar'] =  'B';
        $guild_info['introduction'] =  'B类用户';
        return json(['data'=>$guild_info,'code'=>20000,'msg'=>'成功']);
    }

    private function getDetailInfo($adminInfo)
    {
        $pwd = $this->encode($adminInfo['channel_id'].' '.date('Y-m-d H:i:s'));//水印加密
        $level = Db::name('nw_channel')->where(['id' => $adminInfo['channel_id']])->value('level');//权限等级
        $roles = $level;
        $deposit_amt = 0;
        if ($level == 1) {
          $parent_id = Db::name('nw_channel')->where(['id' => $adminInfo['channel_id']])->value('parent_id');
          if (Db::name('nw_channel')->where(['id' => $parent_id])->value('channel_tg_type') == 1) {
            $roles = 4;
          }
          $deposit_amt = Db::name('nw_channel')->where(['id' => $adminInfo['channel_id']])->value('deposit_amt');
        }elseif ($level == 0) {
          if (Db::name('nw_channel')->where(['id' => $adminInfo['channel_id']])->value('channel_tg_type')  == 1) {
            $roles = 5;
          }
        }
        $channel_name = Db::name('nw_channel')->where(['id' => $adminInfo['channel_id']])->value('name');//渠道名称
        //个人认证状态 0已提交未审核 1已提交审核成功 2已提交审核失败 3未提交
        $apply_status = Db::name('nw_channel_info_apply')->where(['channel_id' => $adminInfo['channel_id']])->order('id desc')->value('status');
        $apply_status === null && $apply_status = 3;
        //身份认证
        $person_id = Db::name('nw_channel_info')->where(['channel_id' => $adminInfo['channel_id']])->value('person_id');
        $leson_pid = Db::name('nw_channel_info')->where(['channel_id' => $adminInfo['channel_id']])->value('leson_person_id');

        //身份认证
        $amount = Db::name('nw_channel')->where(['id' => $adminInfo['channel_id']])->value('amount');
        $js_amount = Db::name('nw_channel')->where(['id' => $adminInfo['channel_id']])->value('js_amount');
        $checkSePwd = !isset($adminInfo['checkSePwd'])?'N':$adminInfo['checkSePwd'];
        if ($leson_pid || $person_id) {
          $person_id = 1;
        }else{
          $person_id = 0;
        }
        //账户信息相关
        $editInfo = Db::name('nw_channel_admin')->field(['id','username','mobile','qq','sex','head_img','secondary_password','pay_password','read_status'])->where(['channel_id' => $adminInfo['channel_id']])->find();
        if ($editInfo['head_img'] === null || empty($editInfo['head_img'])) {
          $editInfo['head_img'] = 'image/game/icons/2020073016105f228087109a0.png';
        }
        $editInfo['secondary_password'] && $editInfo['secondary_password'] = 1;
        $editInfo['pay_password'] && $editInfo['pay_password'] = 1;
        $guild_info = ['id'=>$adminInfo['id'],'username'=>$adminInfo['username'],'channel_name'=>$channel_name,'channel_id'=>$adminInfo['channel_id'],'waterPwd'=>$pwd,'level'=>$level,'head_img'=>$editInfo['head_img'],'token'=>session_id(),'person_id'=>$person_id,'mobile'=>$editInfo['mobile'],'qq'=>$editInfo['qq'],'sex'=>$editInfo['sex'],'secondary_password'=>$editInfo['secondary_password'],'pay_password'=>$editInfo['pay_password'],'roles'=>[$roles],'checkSePwd'=>$checkSePwd,'read_status'=>$editInfo['read_status'],'apply_status'=>$apply_status,'amount'=>$amount,'js_amount'=>$js_amount,'deposit_amt'=>$deposit_amt];
        return $guild_info;
    }
    /**
     * 确认二级密码
     * @return [type] [description]
     */
    public function checkSePwd()
    {
      $pwd = input('pwd');
      if (!$pwd) {
        return json(['data'=>'','code'=>10030,'msg'=>'请输入二级密码!']);
      }
      $guild_info = session('guild_info');
      $id = $guild_info['id'];
      $pwd = mg_password($pwd);
      $res = Db::name('nw_channel_admin')->where(['id'=>$id,'secondary_password'=>$pwd])->value('id');
      if ($res) {
        $guild_info['checkSePwd'] =  'Y';
        session('guild_info', $guild_info);
        return json(['data'=>'','code'=>20000,'msg'=>'二级密码验证成功']);
      }else{
        return json(['data'=>'','code'=>10032,'msg'=>'二级密码验证失败']);
      }

        
    }
    /**
     * 首页热门游戏推荐
     * @param string $value [description]
     */
    public function hotGameList()
    {
      $limit = input('limit',4);

      $list = model('ChannelLoginPicture')->field('id,filename,url,isjump')->limit(4)->select();
      // $gameInfoModel = new GameInfo;
      // $hotGameList = $gameInfoModel->getGameInfoByLanmu(['g.lanmu'=>2],$limit);
      return json(['data'=>$list,'code'=>20000,'msg'=>'获取热门游戏成功']);
    }
    /**
     * 用户登出
     */
    public function logout()
    {
        $ws = new Websocket;
        
        //Websocket注销token和后台uid的绑定关系
        $ws->destroyToken(session_id(), session('guild_info')['id']);
        
        session('guild_info', null);
        
        return json(['data'=>'','code'=>20000,'msg'=>'注销成功']);
    }


    /**
     * 加密规则 
     * @param  string $string [description]
     * @param  string $skey   [description]
     * @return [type]         [description]
     */
    private function encode($string = '', $skey = 'pwd') {
      $strArr = str_split($string);
      $strCount = count($strArr);
      foreach (str_split($skey) as $key => $value)
      $key < $strCount && $strArr[$key].=$value;
      return str_replace(array('-', ' ', ':'), array('ATR', 'MSS', 'XXL'), join('', $strArr));
    }
    /**
     * 解密规则
     * @param  string $string [description]
     * @param  string $skey   [description]
     * @return [type]         [description]
     */
    private function decode($string = '', $skey = 'pwd') {
      $strArr = str_split(str_replace(array('ATR', 'MSS', 'XXL'), array('-', ' ', ':'), $string), 2);
      $strCount = count($strArr);
      foreach (str_split($skey) as $key => $value)
      $key <= $strCount && $strArr[$key][1] === $value && $strArr[$key] = $strArr[$key][0];
      return join('', $strArr);
    }
    /**
     * 测试方法
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function testFun()
    {
      return json(['data'=>'','code'=>10007,'msg'=>'您还没有登录！']);
    }
}
