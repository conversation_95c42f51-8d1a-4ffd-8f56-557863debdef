<?php

/**
 * 转账接口控制器
 * 
 */

namespace app\mcpsapi\controller;

use app\mcpsapi\controller\Guild;
use app\common\model\CoinTransfer as CoinTransferModel;
use app\common\library\WeixinPay;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;
use app\common\library\MakeReport;

class CoinTransfer extends Guild  {
    
    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;

        $this->redis    = \think\Cache::store('default')->handler();
    }
  
     /**
     * 转账管理
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1,2])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= $this->input('list_rows',10);
        $page					= $this->input('page',1);
		$type					= $this->input('type','0','intval');    //1(转账收入),2(转账支出)
        $account				= $this->input('account','','trim');    //收入账号
        $apply_begin_time		= $this->input('apply_begin_time','','trim');
        $apply_end_time			= $this->input('apply_end_time','','trim');
        $finish_begin_time		= $this->input('finish_begin_time','','trim');
        $finish_end_time		= $this->input('finish_end_time','','trim');
		$download				= $this->input('download',0,'intval');

        $condition					= [];
		if($account){
			$condition['t.det_username']	= $account;
		}

		if(!in_array($type,[1,2])){
			$this->jsonResult('', 0, '请选择交易类型');
		}

		if($type==1){
			$condition['t.det_userid'] = $this->_channelId;
			if($this->_channelLevel==1){  //公会
				$condition['t.transfer_type']	= 0;
			}
			else{   //子会长
				$condition['t.transfer_type']	= 1;
			}
		}
		else if($type==2){
			$condition['t.src_channel_id'] = $this->_channelId;
			if($this->_channelLevel==1){
				$condition['t.transfer_type']	= array('in',[1,2]);
			}
			else{
				$condition['t.transfer_type']	= 3;
			}
		}

	    //申请开始时间和结束时间不为空时
        if ($apply_begin_time != '' && $apply_end_time != '') {
            $condition['t.create_time'] = [
                ['>=', strtotime($apply_begin_time)],
                ['<=', strtotime($apply_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($apply_begin_time != '') {
            $condition['t.create_time'] = ['>=', strtotime($apply_begin_time)];
        } //结束时间不为空时
        elseif ($apply_end_time != '') {
            $condition['t.create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
        }

        //到账开始时间和结束时间不为空时
        if ($finish_begin_time != '' && $finish_end_time != '') {
            $condition['t.create_time'] = [
                ['>=', strtotime($finish_begin_time)],
                ['<=', strtotime($finish_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($finish_begin_time != '') {
            $condition['t.create_time'] = ['>=', strtotime($finish_begin_time)];
        } //结束时间不为空时
        elseif ($finish_end_time != '') {
            $condition['t.create_time'] = ['<=', strtotime($finish_end_time . ' 23:59:59')];
        }
	//	var_dump($condition);


		if($download){
			$sql =  model('CoinTransfer')->alias('t')
						->join('cy_game g', 't.game_id = g.id','left')
						->join('nw_channel channel', 't.cuser_id = channel.id','left')
						->field('t.*,t.amount as real_amount,t.status as finish_status,t.create_time as finish_time,g.name as game_name,channel.name as cuser_name')
						->order("t.id desc")
						->where($condition)
						->whereRaw('src_channel_id='.$this->_channelId.' or det_userid='.$this->_channelId.' and transfer_kind=2')
						->fetchSql(true)
						->select();
		//	echo $sql;
			if ((new MakeReport())->addTask('guild.ChannelTransferIndex', $sql, 'cps'.session('guild_info')['id'])){
				$this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
			}
			else{
				$this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
			}
		}

        $transferList = model('CoinTransfer')->alias('t')
						->join('cy_game g', 't.game_id = g.id','left')
						->join('nw_channel channel', 't.cuser_id = channel.id','left')
						->field('t.*,t.amount as real_amount,t.status as finish_status,t.create_time as finish_time,g.name as game_name,channel.name as cuser_name')
						->order("t.id desc")
						->where($condition)
						->whereRaw('src_channel_id='.$this->_channelId.' or det_userid='.$this->_channelId.' and transfer_kind=2')
						->paginate(['list_rows'=>$list_rows,'page'=>$page])
						->toArray();
	//	echo model('CoinTransfer')->getLastSql()."-----lastsql------<br>";

		$this->jsonResult($transferList, 20000, '获取列表成功');
    }

    /**
     * 转账处理
     */
    public function transfer()
    {
		if ($this->request->isPost()) {
			$type			= $this->input('type','','trim');               //转账方式:single(单个转账),batch(批量转账)
			$transfer_kind  = $this->input('transfer_kind','','trim');      //收款对象:1(转给玩家),2(转给渠道)
			$pay_password	= $this->input('pay_password','','trim');            //交易密码
			$orderid    = 'T'.makeOrderid();
			$batchNo	= 'Batch-'.$this->_channelId.'-'.date('YmdHis');
			
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1,2])){
				$this->jsonResult('', 0, '您无权限进行转账操作');
			}
			if(!in_array($type,['single','batch'])){
				$this->jsonResult('', 0, '请正确选择转账方式');
			}
			else{
				if(!in_array($transfer_kind,[1,2])){
					$this->jsonResult('', 0, '请选择收款对象');
				}
				if($transfer_kind==2 && $this->_channelLevel<>1){
					$this->jsonResult('', 0, '您不能转账给子会长');
				}
			}
			if(!$pay_password){
				$this->jsonResult('', 0, '请输入交易密码');
			}

			$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
			if(!$adminInfo){
				$this->jsonResult('', 0, '账号异常,请重新登录后再试');
			}
			else if(!$adminInfo['pay_password'] || !$adminInfo['secondary_password']){
				$this->jsonResult('', 0, '请设置交易密码和二级密码后才能进行提现');
			}
			else if($adminInfo['pay_password'] != mg_password($pay_password)){
				 $this->jsonResult('', 0, '交易密码错误!');
			}

			if($type=='single'){   //单个转账
				$account	= $this->input('account','','trim');			//收款账号
				$amount		= $this->input('amount','','trim');			//转账金额
				$remark		= $this->input('remark','','trim');            //备注

				if(!$account){
					$this->jsonResult('', 0, '请输入收款账号');
				}
				if(!$amount){
					$this->jsonResult('', 0, '请输入正确的转账金额');
				}
				else if(intval($amount) <> $amount){
					$this->jsonResult('', 0, '请输入正确的转账金额');
				}
				$amount		= $this->input('amount',0,'intval');			//转账金额

				$channelInfo = model("Channel")->where(['id'=>$this->_channelId])->find();
				if($channelInfo){
					if($channelInfo['amount'] < $amount){
						$this->jsonResult('', 0, '您的平台币余额不足');
					}
				}
				else{
					$this->jsonResult('', 0, '您的账号有异常,请联系管理员');
				}

				if($transfer_kind==1){  //转给玩家
					$game_id		= $this->input('game_id',0,'intval');           //游戏ID
					$server_id		= $this->input('server_id','','trim');          //区服ID
					$server_name	= $this->input('server_name','','trim');       //区服名称
					
					/*
					if(!$server_id || !$server_name){
						$this->jsonResult('', 0, '请选择区服信息');
					}
					*/
					if(!$game_id){
						$this->jsonResult('', 0, '请选择要转账的游戏');
					}
					else{
						$gameInfo = model('Game')->where(['id'=>$game_id])->find();
						if(!$gameInfo){
							$this->jsonResult('', 0, '该游戏不存在');
						}
						else if($gameInfo['game_kind']<>2){
							$this->jsonResult('', 0, '该游戏不能使用平台币');
						}
						else{   //判断能否对该玩家进行充值
							$chkResult = chkTransferPlayer($this->_channelId,$account,$game_id);
							if($chkResult['errorCode']<>1){
								$this->jsonResult('', 0, $chkResult['errorMsg']);
							}
							else{
								$chkResultData = $chkResult['retData'];

								//指定时间内，禁止重复下单
								if(!requestDuplicateCheck('transfer_pay_duplicate_'.$this->_channelId,$this->payRequestLimit)){
									$this->jsonResult('', 0, '转账请求过多，请于'.$this->payRequestLimit.'s以后，再次进行转账操作');
								}

								$transferData = array();
								$transferData['transfer_kind']		= $transfer_kind;
								$transferData['batch_no']			= $batchNo;
								if($this->_channelLevel==1){
									$transferData['transfer_type']	= 2;
								}
								else{
									$transferData['transfer_type']	= 3;
								}
								$transferData['orderid']			= $orderid;
								$transferData['src_channel_id']     = $this->_channelId;
								$transferData['src_channel_name']   = $this->_channelName;
								$transferData['amount']				= $amount;
								$transferData['game_id']			= $game_id;
								$transferData['det_userid']			= $chkResultData['userid'];
								$transferData['det_username']		= $chkResultData['username'];
								$transferData['cuser_id']			= $chkResultData['cuser_id'];
								$transferData['server_id']			= $server_id;
								$transferData['server_name']		= $server_name;
								$transferData['status']				= 1;
								$transferData['remark']				= $remark;
								$transferData['create_time']		= time();

								// 启动事务
								Db::startTrans();
								try{
									$result = '';   //返回值

									$insertTransferId = model("CoinTransfer")->insert($transferData);
									if(!$insertTransferId){
										throw new Exception("创建转账订单失败! ");
									}

									$detData = array();
									$detData['channel_id']      = $this->_channelId;
									$detData['channel_name']    = $this->_channelName;
									$detData['change_amount']	= -$amount;
									$detData['account_type']	= 1;   //通用账户
									$detData['type']			= 1;   //转账支出
									$detData['out_orderid']		= $orderid;
									$detData['create_time']     = NOW_TIMESTAMP;
									$insertDetId = model('ChannelAccountDet')->insertGetId($detData);
									if ( !$insertDetId) {
										throw new Exception("添加账户变动明细失败");
									}
									$updData = array();
									$updData['amount'] = Db::raw("amount-".$amount);
									$updData['update_time'] = time();
									$updResult = model('Channel')->where(['id'=>$this->_channelId,'amount'=>array('egt',$amount)])->update($updData);
									if (!$updResult) {
										throw new Exception("账户金额变动失败");
									}

									$userCoinInfo = model('MemberZscoin')->field('id,userid,username,game_id,amount,status')->where(['userid'=>$chkResultData['userid'],'game_id'=>$chkResultData['game_id']])->find();
									if(!empty($userCoinInfo)){
										$coinData	=   array();
										$coinData['amount']			  =	Db::raw("amount+".$amount);
										$coinData['update_time']	  =	time();
										$updResult = model('MemberZscoin')->where(['id'=>$userCoinInfo['id'],'userid'=>$userCoinInfo['userid'],'game_id'=>$userCoinInfo['game_id']])->update($coinData);
										if (!$updResult) {
											throw new Exception("用户游戏平台币账户金额变动失败");
										}

										$coinDetData				  =	array();
										$coinDetData['userid']		  =	$userCoinInfo['userid'];
										$coinDetData['username']      =	$userCoinInfo['username'];
										$coinDetData['game_id']		  =	$userCoinInfo['game_id'];
										$coinDetData['type']		  =	1;
										$coinDetData['prev_amount']	  =	$userCoinInfo['amount'];
										$coinDetData['change_amount'] =	$amount;
										$coinDetData['after_amount']  =	$coinDetData['prev_amount'] + $amount;
										$coinDetData['out_orderid']	  =	$orderid;
										$coinDetData['create_time']	  =	time();
										$coinDetData['update_time']	  =	time();

										$insertDetId = model('MemberZscoinDet')->insertGetId($coinDetData);
										if ( !$insertDetId) {
											throw new Exception("添加用户游戏平台币变动明细失败");
										}

									}
									else{
										$coinData = array();
										$coinData['userid']			  =	$chkResultData['userid'];
										$coinData['username']		  =	$chkResultData['username'];
										$coinData['game_id']		  =	$chkResultData['game_id'];
										$coinData['amount']			  =	$amount;
										$coinData['status']			  =	1;
										$coinData['create_time']	  =	time();
										$coinData['update_time']	  =	time();
										$insertResult = model('MemberZscoin')->insertGetId($coinData);
										if (!$insertResult) {
											throw new Exception("添加用户游戏平台币账户失败");
										}

										$coinDetData				  =	array();
										$coinDetData['userid']		  =	$chkResultData['userid'];
										$coinDetData['username']      =	$chkResultData['username'];
										$coinDetData['game_id']		  =	$chkResultData['game_id'];
										$coinDetData['type']		  =	1;
										$coinDetData['prev_amount']	  =	0;
										$coinDetData['change_amount'] =	$amount;
										$coinDetData['after_amount']  =	$coinDetData['prev_amount'] + $amount;
										$coinDetData['out_orderid']	  =	$orderid;
										$coinDetData['create_time']	  =	time();
										$coinDetData['update_time']	  =	time();

										$insertDetId = model('MemberZscoinDet')->insertGetId($coinDetData);
										if ( !$insertDetId) {
											throw new Exception("添加用户游戏平台币变动明细失败");
										}
									}
									
									$this->redis->del('transfer_pay_duplicate_'.$this->_channelId);
									
									// 提交事务
									Db::commit();
									
								} catch (\Exception $e) {
									// 回滚事务
									Db::rollback();
									
									$this->jsonResult('', 0, '订单生成失败'.$e->getMessage());
								}
								
								$result['orderid']      = $orderid;
								
								$this->jsonResult($result, 20000, '转账成功');
								exit;
							}
						}
					}
				}
				else{  //转给子公会
					$chkResult = chkTransferChannel($this->_channelId,$account);
					if($chkResult['errorCode']<>1){
						$this->jsonResult('', 0, $chkResult['errorMsg']);
					}
					else{
						$chkResultData = $chkResult['retData'];

						//指定时间内，禁止重复下单
						if(!requestDuplicateCheck('transfer_pay_duplicate_'.$this->_channelId,$this->payRequestLimit)){
							$this->jsonResult('', 0, '转账请求过多，请于'.$this->payRequestLimit.'s以后，再次进行转账操作');
						}

						$transferData = array();
						$transferData['transfer_kind']		= $transfer_kind;
						$transferData['batch_no']			= $batchNo;
						$transferData['transfer_type']		= 1;
						$transferData['orderid']			= $orderid;
						$transferData['src_channel_id']     = $this->_channelId;
						$transferData['src_channel_name']   = $this->_channelName;
						$transferData['amount']				= $amount;
						$transferData['det_userid']			= $chkResultData['userid'];
						$transferData['det_username']		= $chkResultData['username'];
						$transferData['status']				= 1;
						$transferData['remark']				= $remark;
						$transferData['create_time']		= time();

						// 启动事务
						Db::startTrans();
						try{
							$result = '';   //返回值

							$insertTransferId = model("CoinTransfer")->insert($transferData);
							if(!$insertTransferId){
								throw new Exception("创建转账订单失败! ");
							}

							$detData = array();
							$detData['channel_id']      = $this->_channelId;
							$detData['channel_name']    = $this->_channelName;
							$detData['change_amount']	= -$amount;
							$detData['account_type']	= 1;   //通用账户
							$detData['type']			= 1;   //转账支出
							$detData['out_orderid']		= $orderid;
							$detData['create_time']     = NOW_TIMESTAMP;
							$insertDetId = model('ChannelAccountDet')->insertGetId($detData);
							if ( !$insertDetId) {
								throw new Exception("添加账户变动明细失败");
							}
							$updData = array();
							$updData['amount'] = Db::raw("amount-".$amount);
							$updData['update_time'] = time();
							$updResult = model('Channel')->where(['id'=>$this->_channelId,'amount'=>array('egt',$amount)])->update($updData);
							if (!$updResult) {
								throw new Exception("账户金额变动失败");
							}

							$detData = array();

							$detData['channel_id']      = $transferData['det_userid'];
							$detData['channel_name']    = $transferData['det_username'];
							$detData['change_amount']	= $transferData['amount'];
							$detData['account_type']	= 1;   //通用账户
							$detData['type']			= 2;   //转账收入
							$detData['out_orderid']		= $transferData['orderid'];
							$detData['create_time']     = NOW_TIMESTAMP;
							$insertDetId = model('ChannelAccountDet')->insertGetId($detData);
							if ( !$insertDetId) {
								throw new Exception("添加收款账户变动明细失败");
							}
							$updData = array();
							$updData['amount'] = Db::raw("amount+".$transferData['amount']);
							$updData['update_time'] = time();
							$updToResult = model('Channel')->where(['id'=>$transferData['det_userid']])->update($updData);
							if (!$updToResult) {
								throw new Exception("收款账户金额变动失败");
							}
									
							$this->redis->del('transfer_pay_duplicate_'.$this->_channelId);
									
							// 提交事务
							Db::commit();
									
						} catch (\Exception $e) {
							// 回滚事务
							Db::rollback();
									
							$this->jsonResult('', 0, '转账失败'.$e->getMessage());
						}
								
						$result['orderid']      = $orderid;
								
						$this->jsonResult($result, 20000, '转账成功');
						exit;
					}
				}
			}
			else{      //批量转账

			   //设置文件上传的最大限制
				ini_set('memory_limit','1024M');
				//加载第三方类文件
				vendor("PHPExcel.PHPExcel");
				//防止乱码
				header("Content-type:text/html;charset=utf-8");
				//实例化主文件
				//接收前台传过来的execl文件
				
				if(!isset($_FILES['file'])){
					$this->jsonResult('', 0, '请上传批量导入文件');
				}
				$file = $_FILES['file'];
				//截取文件的后缀名，转化成小写
				$extension = strtolower(pathinfo($file['name'],PATHINFO_EXTENSION));
				if($extension == "xlsx"){
					//2007(相当于是打开接收的这个excel)
					$objReader =\PHPExcel_IOFactory::createReader('Excel2007');
				}else{
					//2003(相当于是打开接收的这个excel)
					$objReader = \PHPExcel_IOFactory::createReader('Excel5');
				}

				$objContent = $objReader -> load($file['tmp_name']);

				if ($objContent){

					$sheetContent = $objContent -> getSheet(0) -> toArray();
					$sheetHeaderArr = $sheetContent[0];

					$headNameValArr = [];
					for($i=0;$i<count($sheetHeaderArr);$i++){
					//	echo $sheetHeaderArr[$i]."---$i---<br>";
						if($transfer_kind==1){   //转账给玩家
							if(trim($sheetHeaderArr[$i])=='收款账号'){
								$headNameValArr['det_username'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='金额'){
								$headNameValArr['amount'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='游戏ID'){
								$headNameValArr['game_id'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='游戏名'){
								$headNameValArr['game_name'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='区服ID'){
								$headNameValArr['server_id'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='区服名'){
								$headNameValArr['server_name'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='备注'){
								$headNameValArr['remark'] = $i;
							}
						}
						else{  //转账给子公会
							if(trim($sheetHeaderArr[$i])=='收款账号'){
								$headNameValArr['det_username'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='金额'){
								$headNameValArr['amount'] = $i;
							}
							else if(trim($sheetHeaderArr[$i])=='备注'){
								$headNameValArr['remark'] = $i;
							}
						}
					}

					if($transfer_kind==1){   //转账给玩家
						if(!(isset($headNameValArr['det_username']) && isset($headNameValArr['amount']) && isset($headNameValArr['game_id']) && isset($headNameValArr['game_name']) && isset($headNameValArr['server_id'])  && isset($headNameValArr['server_name']) && isset($headNameValArr['remark']))){
							$this->jsonResult('', 0, '请选择正确的玩家批量转账导入模板 !');
						}
					}
					else{  //转账给子公会
						if(!(isset($headNameValArr['det_username']) && isset($headNameValArr['amount']) && isset($headNameValArr['remark']))){
							$this->jsonResult('', 0, '请选择正确的子公会批量转账导入模板!');
						}
					}
				
					unset($sheetContent[0]);    //删除第一行标题

					/*
					$headNameValArr['det_username'] = 0;
					$headNameValArr['amount'] = 1;
					$headNameValArr['remark'] = 2;

					$sheetContent = array();
					$sheetContent[0] = array("B-Promoter3","10","批量转账");
					$sheetContent[1] = array("B-Promoter4","10","批量转账");
					$sheetContent[2] = array("B-Promoter5","10","批量转账");
					*/

					$totalCnt = $succcessCnt = 0;
					$accountArrs = array();
					foreach ($sheetContent as $k => $v){
						if($v[0]) $totalCnt++;
						$arr = array();
						$arr['orderid']				= $orderid."-".($succcessCnt+1);
						$arr['src_channel_id']		= $this->_channelId;
						$arr['src_channel_name']	= $this->_channelName;
						$arr['batch_no']			= $batchNo;
						$arr['status']				= 1;
						$arr['transfer_kind']		= $transfer_kind;
						$arr['create_time']			= time();

						if(!$v{$headNameValArr['amount']}){
							$this->jsonResult('', 0, '收款账号:'.trim($v{$headNameValArr['det_username']}).' 游戏名:'.trim($v{$headNameValArr['game_name']}).' 金额:'.trim($v{$headNameValArr['amount']}).' 请输入正确的金额');
						}
						else if(trim($v{$headNameValArr['amount']}) <> intval($v{$headNameValArr['amount']})){
							$this->jsonResult('', 0, '收款账号:'.trim($v{$headNameValArr['det_username']}).' 游戏名:'.trim($v{$headNameValArr['game_name']}).' 金额:'.trim($v{$headNameValArr['amount']}).' 请输入正确的金额');
						}
						if($transfer_kind==1){   //玩家
							if(trim($v{$headNameValArr['det_username']}) && trim($v{$headNameValArr['amount']}) && intval($v{$headNameValArr['game_id']}) && trim($v{$headNameValArr['game_name']})){
								if($this->_channelLevel==1){
									$arr['transfer_type']	= 2;
								}
								else{
									$arr['transfer_type']	= 3;
								}
								$arr['amount']		= intval($v{$headNameValArr['amount']});
								$arr['game_id']		= intval($v{$headNameValArr['game_id']});
								$arr['server_id']	= trim($v{$headNameValArr['server_id']});
								$arr['server_name'] = trim($v{$headNameValArr['server_name']});
								$arr['remark']		= trim($v{$headNameValArr['remark']});

								if(!$arr['game_id']){
									$this->jsonResult('', 0, '请选择要转账游戏');
								}
								else{
									$gameInfo = model('Game')->where(['id'=>$arr['game_id']])->find();
									if(!$gameInfo){
										$this->jsonResult('', 0, '收款账号:'.trim($v{$headNameValArr['det_username']}).' 游戏名:'.trim($v{$headNameValArr['game_name']}).' 金额:'.trim($v{$headNameValArr['amount']}).' 游戏不存在');
									}
									else if(trim($gameInfo['name'])<>trim($v{$headNameValArr['game_name']})){
										$this->jsonResult('', 0, '收款账号:'.trim($v{$headNameValArr['det_username']}).' 游戏名:'.trim($v{$headNameValArr['game_name']}).' 金额:'.trim($v{$headNameValArr['amount']}).' 游戏无法匹配');
									}
									else if($gameInfo['game_kind']<>2){
										$this->jsonResult('', 0, '收款账号:'.trim($v{$headNameValArr['det_username']}).' 游戏名:'.trim($v{$headNameValArr['game_name']}).' 金额:'.trim($v{$headNameValArr['amount']}).' 游戏不能使用平台币');
									}
									else{   //判断能否对该玩家进行充值
										$chkResult = chkTransferPlayer($this->_channelId,trim($v{$headNameValArr['det_username']}),intval($v{$headNameValArr['game_id']}));
										if($chkResult['errorCode']<>1){
											unset($arr);
											$this->jsonResult('', 0, $chkResult['errorMsg']);
										}
										else{
											$chkResultData	= $chkResult['retData'];
											$arr['det_userid']		=  $chkResultData['userid'];
											$arr['det_username']	=  $chkResultData['username'];
											$arr['cuser_id']		=  $chkResultData['cuser_id'];
											$succcessCnt++;
										}
									}
								}
							}
							else{
								unset($arr);
								$this->jsonResult('', 0, '收款账号:'.trim($v{$headNameValArr['det_username']}).' 游戏名:'.trim($v{$headNameValArr['game_name']}).' 金额:'.trim($v{$headNameValArr['amount']}).' 该记录不完整,不能进行导入!');
							}
						}
						else{  //子公会
							if(trim($v{$headNameValArr['det_username']}) && trim($v{$headNameValArr['amount']})){
								$arr['transfer_type']	= 1;
								$arr['amount'] = floatval($v{$headNameValArr['amount']});
								$arr['remark'] = trim($v{$headNameValArr['remark']});

								$chkResult = chkTransferChannel($this->_channelId,trim($v{$headNameValArr['det_username']}));
								if($chkResult['errorCode']<>1){
									unset($arr);
									$this->jsonResult('', 0, $chkResult['errorMsg']);
								}
								else{
									$chkResultData	=  $chkResult['retData'];
									$arr['det_userid']		=  $chkResultData['userid'];
									$arr['det_username']	=  $chkResultData['username'];
									$succcessCnt++;
								}
							}
							else{
								unset($arr);
								$this->jsonResult('', 0, '收款账号:'.trim($v{$headNameValArr['det_username']}).' 金额:'.trim($v{$headNameValArr['amount']}).' 该记录不完整,不能进行导入!');
							}
						}
						if(isset($arr)){
							$accountArrs[] = $arr;
						}
					}

					if(!$accountArrs){
						$this->jsonResult('', 0, '您未导入任何数据，请检查导入文件 !');
					}
					else{
						$result = '';   //返回值
						$errorMsg = '';
							
						$totalAmount = 0;
						foreach ($accountArrs as $detAccount){
							$totalAmount += floatval($detAccount['amount']);
						}
						reset($accountArrs);

						$channelInfo = model("Channel")->where(['id'=>$this->_channelId])->find();
						if($channelInfo){
							if($channelInfo['amount'] < $totalAmount){
								$this->jsonResult('', 0, '您的平台币余额不足');
							}
						}
						else{
							$this->jsonResult('', 0, '您的账号有异常,请联系管理员');
						}

						//指定时间内，禁止重复下单
						if(!requestDuplicateCheck('transfer_pay_duplicate_'.$this->_channelId,$this->payRequestLimit)){
							$this->jsonResult('', 0, '转账请求过多，请于'.$this->payRequestLimit.'s以后，再次进行转账操作');
						}
							
						// 启动事务
						Db::startTrans();
						try{	
							foreach ($accountArrs as $transferData){
								if($transfer_kind==1){  //转给玩家
									$insertTransferId = model("CoinTransfer")->insert($transferData);
									if(!$insertTransferId){
										throw new Exception("创建转账订单失败! ");
									}

									$detData = array();
									$detData['channel_id']      = $this->_channelId;
									$detData['channel_name']    = $this->_channelName;
									$detData['change_amount']	= -$transferData['amount'];
									$detData['account_type']	= 1;   //通用账户
									$detData['type']			= 1;   //转账支出
									$detData['out_orderid']		= $transferData['orderid'];
									$detData['create_time']     = NOW_TIMESTAMP;
									$insertDetId = model('ChannelAccountDet')->insertGetId($detData);
									if ( !$insertDetId) {
										throw new Exception("添加账户变动明细失败");
									}
									$updData = array();
									$updData['amount'] = Db::raw("amount-".$transferData['amount']);
									$updData['update_time'] = time();
									$updResult = model('Channel')->where(['id'=>$this->_channelId,'amount'=>array('egt',$transferData['amount'])])->update($updData);
									if (!$updResult) {
										throw new Exception("账户金额变动失败");
									}

									$userCoinInfo = model('MemberZscoin')->field('id,userid,username,game_id,amount,status')->where(['userid'=>$transferData['det_userid'],'game_id'=>$transferData['game_id']])->find();
									if(!empty($userCoinInfo)){
										$coinData	=   array();
										$coinData['amount']			  =	Db::raw("amount+".$transferData['amount']);
										$coinData['update_time']	  =	time();
										$updResult = model('MemberZscoin')->where(['id'=>$userCoinInfo['id'],'userid'=>$userCoinInfo['userid'],'game_id'=>$userCoinInfo['game_id']])->update($coinData);
										if (!$updResult) {
											throw new Exception("用户游戏平台币账户金额变动失败");
										}

										$coinDetData				  =	array();
										$coinDetData['userid']		  =	$userCoinInfo['userid'];
										$coinDetData['username']      =	$userCoinInfo['username'];
										$coinDetData['game_id']		  =	$userCoinInfo['game_id'];
										$coinDetData['type']		  =	1;
										$coinDetData['prev_amount']	  =	$userCoinInfo['amount'];
										$coinDetData['change_amount'] =	$transferData['amount'];
										$coinDetData['after_amount']  =	$coinDetData['prev_amount'] + $transferData['amount'];
										$coinDetData['out_orderid']	  =	$transferData['orderid'];
										$coinDetData['create_time']	  =	time();
										$coinDetData['update_time']	  =	time();

										$insertDetId = model('MemberZscoinDet')->insertGetId($coinDetData);
										if ( !$insertDetId) {
											throw new Exception("添加用户游戏平台币变动明细失败");
										}
									}
									else{
										$coinData = array();
										$coinData['userid']			  =	$transferData['det_userid'];
										$coinData['username']		  =	$transferData['det_username'];
										$coinData['game_id']		  =	$transferData['game_id'];
										$coinData['amount']			  =	$transferData['amount'];
										$coinData['status']			  =	1;
										$coinData['create_time']	  =	time();
										$coinData['update_time']	  =	time();
										$insertResult = model('MemberZscoin')->insertGetId($coinData);
										if (!$insertResult) {
											throw new Exception("添加用户游戏平台币账户失败");
										}

										$coinDetData				  =	array();
										$coinDetData['userid']		  =	$transferData['det_userid'];
										$coinDetData['username']      =	$transferData['det_username'];
										$coinDetData['game_id']		  =	$transferData['game_id'];
										$coinDetData['type']		  =	1;
										$coinDetData['prev_amount']	  =	0;
										$coinDetData['change_amount'] =	$transferData['amount'];
										$coinDetData['after_amount']  =	$coinDetData['prev_amount'] + $transferData['amount'];
										$coinDetData['out_orderid']	  =	$transferData['orderid'];
										$coinDetData['create_time']	  =	time();
										$coinDetData['update_time']	  =	time();

										$insertDetId = model('MemberZscoinDet')->insertGetId($coinDetData);
										if ( !$insertDetId) {
											throw new Exception("添加用户游戏平台币变动明细失败");
										}
									}
									$errorMsg .= '收款账号:'.$transferData['det_username'].' 游戏ID:'.$transferData['game_id'].' 金额:'.$transferData['amount'].' 转账成功;';
								}
								else{  //转给子公会
									$insertTransferId = model("CoinTransfer")->insert($transferData);
									if(!$insertTransferId){
										throw new Exception("创建转账订单失败! ");
									}

									$detData = array();
									$detData['channel_id']      = $this->_channelId;
									$detData['channel_name']    = $this->_channelName;
									$detData['change_amount']	= -$transferData['amount'];
									$detData['account_type']	= 1;   //通用账户
									$detData['type']			= 1;   //转账支出
									$detData['out_orderid']		= $transferData['orderid'];
									$detData['create_time']     = NOW_TIMESTAMP;
									$insertDetId = model('ChannelAccountDet')->insertGetId($detData);
									if ( !$insertDetId) {
										throw new Exception("添加账户变动明细失败");
									}
									$updData = array();
									$updData['amount'] = Db::raw("amount-".$transferData['amount']);
									$updData['update_time'] = time();
									$updResult = model('Channel')->where(['id'=>$this->_channelId,'amount'=>array('egt',$transferData['amount'])])->update($updData);
									if (!$updResult) {
										throw new Exception("账户金额变动失败");
									}

									$detData = array();

									$detData['channel_id']      = $transferData['det_userid'];
									$detData['channel_name']    = $transferData['det_username'];
									$detData['change_amount']	= $transferData['amount'];
									$detData['account_type']	= 1;   //通用账户
									$detData['type']			= 2;   //转账收入
									$detData['out_orderid']		= $transferData['orderid'];
									$detData['create_time']     = NOW_TIMESTAMP;
									$insertDetId = model('ChannelAccountDet')->insertGetId($detData);
									if ( !$insertDetId) {
										throw new Exception("添加收款账户变动明细失败");
									}
									$updData = array();
									$updData['amount'] = Db::raw("amount+".$transferData['amount']);
									$updData['update_time'] = time();
									$updToResult = model('Channel')->where(['id'=>$transferData['det_userid']])->update($updData);
									if (!$updToResult) {
										throw new Exception("收款账户金额变动失败");
									}
									$errorMsg .= '收款账号:'.$transferData['det_username'].' 金额:'.$transferData['amount'].' 转账成功;';
								}
							}
							// 提交事务
							Db::commit();
						} catch (\Exception $e) {
							// 回滚事务
							Db::rollback();
							$this->jsonResult('', 0, '转账操作失败'.$e->getMessage());
						}
						$this->redis->del('transfer_pay_duplicate_'.$this->_channelId);

						$result['message'] = $errorMsg;
						$this->jsonResult($result, 20000, $errorMsg);
						exit;
					}
				}else{
					$this->jsonResult('', 0, '请上传批量导入表格 !');
				}
			}
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
}
