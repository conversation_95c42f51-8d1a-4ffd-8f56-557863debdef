<?php

/**
 * 交易记录接口控制器
 * 
 */

namespace app\mcpsapi\controller;

use app\mcpsapi\controller\Guild;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;
use app\common\library\MakeReport;

class ChannelTrade extends Guild  {
    
    protected $nowTime;
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;

    }
  
     /**
     * 我的交易记录列表
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1,2])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= $this->input('list_rows',10);
        $page					= $this->input('page',1);
		$type					= $this->input('type','0','intval');    //1转账(收入),2转账(支出),3充值(收入),4充值(支出),5(直充),6结算充值(收入)
        $account				= $this->input('account','','trim');    //收入账号
        $apply_begin_time		= $this->input('apply_begin_time','','trim');
        $apply_end_time			= $this->input('apply_end_time','','trim');
        $finish_begin_time		= $this->input('finish_begin_time','','trim');
        $finish_end_time		= $this->input('finish_end_time','','trim');
		$download				= $this->input('download',0,'intval');

		if(!in_array($type,[1,2,3,4,5,6])){
			$this->jsonResult('', 0, '请选择交易类型');
		}

		$condition					= [];

		if(in_array($type,[1,2])){
			$condition['t.status'] = 1;

			if($account){
				$condition['t.det_username']	= $account;
			}

			if($type==1){
				$condition['t.det_userid'] = $this->_channelId;
				if($this->_channelLevel==1){  //公会
					$condition['t.transfer_type']	= 0;
				}
				else{   //子会长
					$condition['t.transfer_type']	= 1;
				}
			}
			else if($type==2){
				$condition['t.src_channel_id'] = $this->_channelId;
				if($this->_channelLevel==1){
					$condition['t.transfer_type']	= array('in',[1,2]);
				}
				else{
					$condition['t.transfer_type']	= 3;
				}
			}
			//申请开始时间和结束时间不为空时
			if ($apply_begin_time != '' && $apply_end_time != '') {
				$condition['t.create_time'] = [
					['>=', strtotime($apply_begin_time)],
					['<=', strtotime($apply_end_time . ' 23:59:59')],
				];
			} //开始时间不为空时
			elseif ($apply_begin_time != '') {
				$condition['t.create_time'] = ['>=', strtotime($apply_begin_time)];
			} //结束时间不为空时
			elseif ($apply_end_time != '') {
				$condition['t.create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
			}
			//到账开始时间和结束时间不为空时
			if ($finish_begin_time != '' && $finish_end_time != '') {
				$condition['t.create_time'] = [
					['>=', strtotime($finish_begin_time)],
					['<=', strtotime($finish_end_time . ' 23:59:59')],
				];
			} //开始时间不为空时
			elseif ($finish_begin_time != '') {
				$condition['t.create_time'] = ['>=', strtotime($finish_begin_time)];
			} //结束时间不为空时
			elseif ($finish_end_time != '') {
				$condition['t.create_time'] = ['<=', strtotime($finish_end_time . ' 23:59:59')];
			}

			if($download){
				$sql =  model('CoinTransfer')->alias('t')
							->join('cy_game g', 't.game_id = g.id','left')
							->join('nw_channel channel', 't.cuser_id = channel.id','left')
							->field("t.id,t.orderid,t.det_username,t.src_channel_name,t.amount,t.amount as real_amount,t.status,t.create_time,t.status as finish_status,t.create_time as finish_time,t.remark,g.name as game_name,t.cuser_id,channel.name as cuser_name")
							->order("t.id desc")
							->where($condition)
							->whereRaw('src_channel_id='.$this->_channelId.' or det_userid='.$this->_channelId.' and transfer_kind=2')
							->fetchSql(true)
							->select();
			//	echo $sql;
				if ((new MakeReport())->addTask('guild.ChannelTradeIndex', $sql, 'cps'.session('guild_info')['id'])){
					$this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
				}
				else{
					$this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
				}
			}

			$tradeList = model('CoinTransfer')->alias('t')
							->join('cy_game g', 't.game_id = g.id','left')
							->join('nw_channel channel', 't.cuser_id = channel.id','left')
							->field("t.id,t.orderid,t.det_username,t.src_channel_name,t.amount,t.amount as real_amount,t.status,t.create_time,t.status as finish_status,t.create_time as finish_time,t.remark,g.name as game_name,t.cuser_id,channel.name as cuser_name")
							->order("t.id desc")
							->where($condition)
							->whereRaw('src_channel_id='.$this->_channelId.' or det_userid='.$this->_channelId.' and transfer_kind=2')
							->paginate(['list_rows'=>$list_rows,'page'=>$page])
							->toArray();
		//	echo model('CoinTransfer')->getLastSql()."-----lastsql------<br>";
		}
		else{
			$condition['status'] = 1;
			if($account){
				$condition['channel_name']	= $account;
			}
			if($type==3){
				$condition['channel_id'] = $this->_channelId;
				$condition['recharge_type'] = 1;
			}
			else if($type==4){
				$condition['send_channel_id'] = $this->_channelId;
				$condition['recharge_type'] = 1;
			}
			else if($type==5){
				$condition['channel_id'] = $this->_channelId;
				$condition['recharge_type'] = 2;
			}
			else if($type==6){
				$condition['channel_id'] = $this->_channelId;
				$condition['recharge_type'] = 3;
			}
			//申请开始时间和结束时间不为空时
			if ($apply_begin_time != '' && $apply_end_time != '') {
				$condition['create_time'] = [
					['>=', strtotime($apply_begin_time)],
					['<=', strtotime($apply_end_time . ' 23:59:59')],
				];
			} //开始时间不为空时
			elseif ($apply_begin_time != '') {
				$condition['create_time'] = ['>=', strtotime($apply_begin_time)];
			} //结束时间不为空时
			elseif ($apply_end_time != '') {
				$condition['create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
			}
			//到账开始时间和结束时间不为空时
			if ($finish_begin_time != '' && $finish_end_time != '') {
				$condition['finish_time'] = [
					['>=', strtotime($finish_begin_time)],
					['<=', strtotime($finish_end_time . ' 23:59:59')],
				];
			} //开始时间不为空时
			elseif ($finish_begin_time != '') {
				$condition['finish_time'] = ['>=', strtotime($finish_begin_time)];
			} //结束时间不为空时
			elseif ($finish_end_time != '') {
				$condition['finish_time'] = ['<=', strtotime($finish_end_time . ' 23:59:59')];
			}

			if($download){
				$sql = model('ChannelRecharge')
							->where($condition)
							->whereRaw('channel_id='.$this->_channelId.' or send_channel_id='.$this->_channelId)
							->field("id,orderid,channel_name as det_username,send_channel_name as src_channel_name,amount,real_amount,status,create_time,finish_status,finish_time,paytype,remark,'' as game_name,'' as cuser_id,'' as cuser_name")
							->order("id desc")
							->fetchSql(true)
							->select();
			//	echo $sql;
				if ((new MakeReport())->addTask('guild.ChannelTradeIndex', $sql, 'cps'.session('guild_info')['id'])){
					$this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
				}
				else{
					$this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
				}
			}

			$tradeList = model('ChannelRecharge')
							->where($condition)
							->whereRaw('channel_id='.$this->_channelId.' or send_channel_id='.$this->_channelId)
							->field("id,orderid,channel_name as det_username,send_channel_name as src_channel_name,amount,real_amount,status,create_time,finish_status,finish_time,paytype,remark,'' as game_name,'' as cuser_id,'' as cuser_name")
							->order("id desc")
							->paginate(['list_rows'=>$list_rows,'page'=>$page])
							->toArray();


		}
	//	var_dump($condition);


	//	echo model('ChannelRecharge')->getLastSql()."-----lastsql------<br>";

		$this->jsonResult($tradeList, 20000, '获取列表成功');
    }
}
