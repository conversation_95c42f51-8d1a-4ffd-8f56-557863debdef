<?php

/**
 * 开发联盟的公共控制器类
 *
 */

namespace app\mcpsapi\controller;

use app\common\controller\Base;
use think\Config;

class Guild extends Base
{
    
    protected $channelIds;      //登录渠道自己及子渠道ID集
    protected $sub_channelIds;  //登录渠道子渠道ID集(不包含自身)
    protected $_channelLevel;    //渠道等级
    protected $current_node;    //当前操作的action
    protected $_adminId;		//当前操作的渠道管理员ID
    protected $_channelIdPath;  //当前渠道路径

    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        //已登录
        if (session('guild_info')) {
            
            $channelModel = model('Common/Channel');
            
            $channelIds = $channelModel->getChildIds(session('guild_info')['channel_id']);
            
            $this->sub_channelIds = $channelIds;
            
            if(!empty($channelIds)){
                array_push($channelIds,session('guild_info')['channel_id']);
                $this->channelIds = $channelIds;
            }
            else{
                $this->channelIds = session('guild_info')['channel_id'];
            }
            
            $this->_channelLevel = get_channel_level(session('guild_info')['channel_id']);
            $this->_channelId = session('guild_info')['channel_id'];
			$this->_channelName = session('guild_info')['channel_name'];
			$this->_adminId = session('guild_info')['id'];

			$ParentIdPath = model('Channel')->where(['id'=>$this->_channelId])->value('id_path');
			if($ParentIdPath){
				$this->_channelIdPath =  $ParentIdPath.$this->_channelId.',';
			}
			else{
				$this->_channelIdPath =  ','.$this->_channelId.',';
			}
        }
        else{
            json(['data'=>'','code'=>10007,'msg'=>'您还没有登录！'])->send();
            exit;
        }
		$this->input = input();
    }
    
    /**
     * 插入操作日志
     * 
     * @param string $node
     * @param string $content
     * @param number $type
     * @param string $admin_id
     * @param string $username
     * @return mixed
     */
    protected function insertLog($node = '', $content = '', $type = 0, $admin_id = '', $username = '')
    {
        if (empty($admin_id)) {
            $admin_id = session('guild_info')['id'] ?: 1;
        }
        if (empty($username)) {
            $username = session('guild_info')['username'] ?: '';
        }
        
        return \app\common\logic\Log::insertOperateLog($node, $content, $type, $admin_id, $username);
    }

    /**
     * 获取参数
     * @param string $key 数组index
     * @param string $default 默认值
     * @return mixed|null
     */
    protected function input($key, $default = null)
    {
        return isset($this->input[$key]) ? $this->input[$key] : ($default!==null ? $default : null);
    }

    /**
     * 返回封装后的 API json数据到客户端
     * @access protected
     * @param mixed  $data   要返回的数据
     * @param int    $code   返回的 code
     * @param mixed  $msg    提示信息
     * @param $isExit boolean 是否强制退出,默认true
     * @return void
     *
     */
    public function jsonResult($data, $code = 0, $msg = '',$isExit=true)
    {
        $result = [
            'code' => $code,
            'msg'  => $msg,
            'time' => request()->server('REQUEST_TIME'),
            'data' => $data,
        ];
        
        echo json_encode($result);
        
        //为了使用fastcgi_finish_request等函数时，后续的执行能够继续生效
        if($isExit) exit;
    }
}
