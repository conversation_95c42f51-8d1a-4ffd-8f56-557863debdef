<?php

namespace app\service;

use app\admin\controller\RetaineGame;
use think\Db;
use think\Env;
use think\Exception;

/**
 * QuickSDK游戏同步加解密算法描述
 * @copyright quicksdk 2015
 * <AUTHOR>
 * @version quicksdk v 0.0.1 2014/9/2
 */
class RetaineService
{
    /**
     * 需要统计的游戏
     * @return array|false|string
     */
    public function getGameIds()
    {
        return model('common/Game')->cache('retaine:game', 500)->where('cooperation_status', 'in', [1, 2])->column('id');
    }

    /**
     * 当天注册账号
     * @param $day
     * @return array|false|string
     */
    public function getMembers($day)
    {
        return model('common/Members')->cache('retaine:members:' . strtotime($day), 500)->whereBetween('reg_time', [strtotime($day), strtotime($day . ' 23:59:59')])->column('id');
    }
    
    /**
     * 注册玩家总数
     *
     * @param $where
     * @return array|false|string
     */
    public function getMembersTotal($game_id, $channel_id)
    {
        $where = [];
        if($game_id){
            $where['gameid'] = $game_id;
        }
        if($channel_id){
            $where['channel_id'] = $channel_id;
        }
        return model('common/Members')
            ->cache('retaine:roleInfo_total:'.$game_id.'_'.$channel_id, 500)
            ->where($where)->fetchSql(false)->count();
    }

    /**
     * 新增玩家
     *
     * @param $day
     * @param $mebmers
     * @param int $server
     * @param int $channel
     * @param int $platform
     * @param string $type count=统计数据, sum=汇总数据
     *
     * @return array|bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function regNum($day, $mebmers, $server = 0, $channel = 0, $platform = 0, $type='reg_num')
    {
        // if (!$mebmers || $type == 'reg_total') {
            $sql = model('common/RoleInfo')
                ->where('serverid', '<>', '')
                ->where('channel_id', '<>', 0);
                // ->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')]);
            if($type == 'reg_total'){
                $sql = $sql->where('create_time', '<', strtotime($day . ' 23:59:59'));
            }else{
                // ->where('userid', 'in', $mebmers)
                $sql = $sql->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')]);
            }
            
            if ($platform) {
                $field = ' 0 as game_id,count(DISTINCT userid) as '.$type.', ';
                if ($channel) {
                    $list = $sql->field($field . 'channel_id,0 as server_id')
                        ->group('channel_id')->select();
                } else {
                    $list = $sql->field($field . ' 0 as channel_id,0 as server_id')
                        ->select();
                }
            } else {
                $field = ' gameid as game_id,count(DISTINCT userid) as '.$type.', ';
                //当天登陆游戏
                if ($channel) {
                    if ($server) {
                        $list = $sql->field($field . 'channel_id,serverid as server_id')
                            ->group('gameid,channel_id,serverid')->select();
                    } else {
                        $list = $sql->field($field . 'channel_id,0 as server_id')
                            ->group('gameid,channel_id')->select();
                    }
                } else {
                    if ($server) {
                        $list = $sql->field($field . ' 0 as channel_id,serverid as server_id')
                            ->group('gameid,serverid')->select();
                    } else {
                        $list = $sql->field($field . ' 0 as channel_id,0 as server_id')
                            ->group('gameid')->select();
                    }
                }
            }
            return $list??[];
        // }
        // return [];
    }
    
    public function regNumNew($day, $mebmers, $server = 0, $channel = 0, $platform = 0, $type='reg_num')
    {
        $sql = model('common/MemberGameServer')
            ->where('serverid', '<>', '')
            ->where('channel_id', '<>', 0);
        // ->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')]);
        if($type == 'reg_total'){
            $sql = $sql->where('create_time', '<', strtotime($day . ' 23:59:59'));
        }else{
            $sql = $sql->where('member_id', 'in', $mebmers)->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')]);
        }
        
        if ($platform) {
            $field = ' 0 as game_id,count(DISTINCT member_id,game_id,roleid) as '.$type.', ';
            if ($channel) {
                $list = $sql->field($field . 'channel_id,0 as server_id')
                    ->group('channel_id')->select();
            } else {
                $list = $sql->field($field . ' 0 as channel_id,0 as server_id')
                    ->select();
            }
        } else {
            $field = ' game_id,count(DISTINCT member_id,game_id,roleid) as '.$type.', ';
            //当天登陆游戏
            if ($channel) {
                if ($server) {
                    $list = $sql->field($field . 'channel_id,serverid as server_id')
                        ->group('game_id,channel_id,serverid')->select();
                } else {
                    $list = $sql->field($field . 'channel_id,0 as server_id')
                        ->group('game_id,channel_id')->select();
                }
            } else {
                if ($server) {
                    $list = $sql->field($field . ' 0 as channel_id,serverid as server_id')
                        ->group('game_id,serverid')->select();
                } else {
                    $list = $sql->field($field . ' 0 as channel_id,0 as server_id')
                        ->group('game_id')->select();
                }
            }
        }
        return $list??[];
    }
    
    /**
     * 新增角色
     *
     * @param $day
     * @param int $server 是否根据游戏分组：0=否、1=是
     * @param int $channel 是否根据游戏分组：0=否、1=是
     * @param int $platform 是否根据游戏分组：0=否、1=是
     * @param string $type role_num=统计数据, reg_total=汇总数据
     *
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getFirstRoleMember($day, $server = 0, $channel = 0, $platform = 0, $type='role_num')
    {
        $sql = model('common/MemberGameServer')
            ->where('serverid', '<>', '');
        if($type == 'role_num'){
            $sql = $sql->where('create_time', '<', strtotime($day . ' 23:59:59'));
        }else{
            $sql = $sql->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')]);
        }

        if ($platform) {
            $field = ' 0 as game_id,count(DISTINCT member_id) as '.$type.', ';
            if ($channel) {
                $list = $sql->field($field . 'channel_id,0 as server_id')
                    ->group('channel_id')->select();
            } else { //按游戏
                $list = $sql->field($field . '0 as channel_id,0 as server_id')
                    ->select();
            }
        } else {
            $field = ' game_id,count(DISTINCT member_id) as '.$type.', ';
            if ($channel) {
                if ($server) {
                    $list = $sql->field($field . 'channel_id,serverid as server_id')
                        ->group('game_id,channel_id,serverid')->select();
                } else {
                    $list = $sql->field($field . 'channel_id,0 as server_id')
                        ->group('game_id,channel_id')->select();
                }
            } else { //按游戏
                if ($server) {
                    $list = $sql->field($field . '0 as channel_id,serverid as server_id')
                        ->group('game_id,serverid')->select();
                } else {
                    $list = $sql->field($field . '0 as channel_id,0 as server_id')
                        ->group('game_id')->select();
                }
            }
        }
        return $list;
    }

    /**
     * 活跃玩家
     * @param $day
     * @param $param
     * @param int $server
     * @param int $channel
     * @param array $members
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function activeMember($day, $param, $server = 0, $channel = 0, $members = [], $platform = 0)
    {

        $sql = model('common/RoleInfo')->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')])->where('serverid', '<>', '')->where('channel_id', '<>', 0)
            ->where(function ($query) use ($members) {
                if ($members) {
                    $query->where('userid', 'in', $members);
                }
            });
        if ($platform) {
            $field = '0 as game_id,count(DISTINCT userid) as ' . $param . ', ';
            if ($channel) {
                $list = $sql->field($field . ' channel_id, 0 as server_id')
                    ->group('channel_id')->select();

            } else {
                $list = $sql->field($field . ' 0 as channel_id, 0 as server_id')
                    ->select();
            }
        }else{
            $field = 'gameid as game_id,count(DISTINCT userid) as ' . $param . ', ';
            if ($channel) {
                if ($server) {
                    $list = $sql->field($field . ' channel_id, serverid as server_id')
                        ->group('gameid,channel_id,serverid')->select();
                } else {
                    $list = $sql->field($field . ' channel_id, 0 as server_id')
                        ->group('gameid,channel_id')->select();
                }

            } else {
                if ($server) {
                    $list = $sql->field($field . ' 0 as channel_id, serverid as server_id')
                        ->group('gameid,serverid')->select();
                } else {
                    $list = $sql->field($field . ' 0 as channel_id, 0 as server_id')
                        ->group('gameid')->select();
                }
            }
        }

        return $list;
    }

    /**
     * 付费人数&充值金额
     * @param $day
     * @param int $server
     * @param int $channel
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function rechargeMoney($day, $server = 0, $channel = 0, $platform = 0)
    {

        $sql = model('common/Pay')->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')])->where(['status' => 1]);

        if($platform){
            $field = '0 as game_id,sum(amount) as recharge_num,count(DISTINCT userid) as pay_num, ';
            if ($channel) {
                $list = $sql->field($field . 'channel_id, 0 as server_id')->group('channel_id')->select();
            } else {
                $list = $sql->field($field . ' 0 as channel_id, 0 as server_id')->select();
            }
        }else{
            $field = 'gameid as game_id,sum(amount) as recharge_num,count(DISTINCT userid) as pay_num, ';
            if ($channel) {
                if ($server) {
                    $list = $sql->field($field . 'channel_id,serverid as server_id')->group('gameid,channel_id,server_id')->select();
                } else {
                    $list = $sql->field($field . 'channel_id, 0 as server_id')->group('gameid,channel_id')->select();
                }

            } else {

                if ($server) {
                    $list = $sql->field($field . ' 0 as channel_id,serverid as server_id')->group('gameid,server_id')->select();
                } else {
                    $list = $sql->field($field . ' 0 as channel_id, 0 as server_id')->group('gameid')->select();
                }
            }
        }
        return $list;
    }

    public function getFirstRoleMemberList($day)
    {

        $list = model('common/MemberGameServer')
            ->cache('retaine:getFirstRoleMemberList:' . strtotime($day), 500)
            ->whereBetween('create_time', [strtotime($day), strtotime($day . ' 23:59:59')])->column(' DISTINCT member_id');

        return $list;

    }


    public function insertOrUpdate($list, $type_id, $day, $param)
    {
        Db::startTrans();
        try {
            foreach ($list as $k => $v) {
                $retaineGame = model('common/RetaineGame')
                    ->cache('retaine:insertOrUpdate:' . $type_id . '_' . strtotime($day) . '_' . $v['game_id'] . '_' . $v['channel_id'], 60)
                    ->where(['type_id' => $type_id, 'day' => $day, 'game_id' => $v['game_id'], 'channel_id' => $v['channel_id']])
                    ->find();

                if ($retaineGame) {
                    $data = [];
                    if ($retaineGame['s_channel_id'] == 0) {
                        $data = $this->getChannel($v['channel_id']);
                    }
                    $data[$param] = $v[$param];
                    if ($param == 'recharge_num') {
                        $this->saveInfo($type_id, $day, $v['game_id'], $v['channel_id'], ['recharge_num' => $v['recharge_num'], 'pay_num' => $v['pay_num']]);
                    } else {
                        $this->saveInfo($type_id, $day, $v['game_id'], $v['channel_id'], $data);
                    }
                } else {
                    $data = $this->getChannel($v['channel_id']);
                    $data[$param] = $v[$param];
                    $this->insertInfo($type_id, $day, $v['game_id'], $v['channel_id'], $data);
                }
            }
            DB::commit();
        } catch (Exception $e) {
            curlDD('【风谷】数据统计异常' . $type_id . '_' . $day . '_' . $param . '_' . $e->getMessage(), Env::get('dingtalk.warning_url'));
            DB::rollback();
        }
    }

    public function insertInfo($type_id, $day, $game_id, $channel_id, $param)
    {
        model('common/RetaineGame')->insert(array_merge($param, [
            'type_id' => $type_id,
            'day' => $day,
            'game_id' => $game_id,
            'channel_id' => $channel_id,
            'update_time' => time(),
        ]));
    }

    public function saveInfo($type_id, $day, $game_id, $channel_id, $param)
    {
        $param = array_merge($param, ['update_time' => time()]);
        model('common/RetaineGame')->where(['type_id' => $type_id, 'day' => $day, 'game_id' => $game_id, 'channel_id' => $channel_id])->update($param);
    }


    public function insertOrUpdateServer($list, $type_id, $day, $param)
    {
        Db::startTrans();
        try {
            foreach ($list as $k => $v) {
                
                // if (in_array($v['game_id'], [175])) {
                //     if (!is_numeric($v['server_id'])) {
                //         $gameServer = model('common/GameServer')->field('serverid')->cache('retaineService:insertOrUpdateServer:' . $v['game_id'] . "_" . $v['server_id'])->where(['game_id' => $v['game_id'], 'servername' => $v['server_id']])->where('serverid', '<>', $v['server_id'])->find();
                //         $v['server_id'] = $gameServer['serverid'];
                //     }
                // }
                if ($retaineGame = model('common/RetaineServer')->cache('retaine:insertOrUpdate:' . $type_id . '_' . strtotime($day) . '_' . $v['game_id'] . '_' . $v['channel_id'] . '_' . $v['server_id'], 60)->where(['type_id' => $type_id, 'day' => $day, 'game_id' => $v['game_id'], 'channel_id' => $v['channel_id'], 'server_id' => $v['server_id']])->find()) {
                    $data = [];
                    if ($retaineGame['s_channel_id'] == 0) {
                        $data = $this->getChannel($v['channel_id']);
                    }
                    $data[$param] = $v[$param];
                    if ($param == 'recharge_num') {
                        $this->saveInfoServer($type_id, $day, $v['game_id'], $v['channel_id'], $v['server_id'], ['recharge_num' => $v['recharge_num'], 'pay_num' => $v['pay_num']]);
                    } else {
                        $this->saveInfoServer($type_id, $day, $v['game_id'], $v['channel_id'], $v['server_id'], $data);
                    }

                } else {

                    $data = $this->getChannel($v['channel_id']);
                    $data[$param] = $v[$param];
                    $this->insertInfoServer($type_id, $day, $v['game_id'], $v['channel_id'], $v['server_id'], $data);
                }
            }
            DB::commit();
        } catch (Exception $e) {

            curlDD('【风谷】数据统计异常' . $type_id . '_' . $day . '_' . $param . '_' . $e->getMessage(), Env::get('dingtalk.warning_url'));
            DB::rollback();
        }
    }

    public function insertInfoServer($type_id, $day, $game_id, $channel_id, $server_id, $param)
    {
        model('common/RetaineServer')->insert(array_merge($param, [
            'type_id' => $type_id,
            'day' => $day,
            'game_id' => $game_id,
            'server_id' => $server_id,
            'channel_id' => $channel_id,
            'update_time' => time(),
        ]));
    }

    public function saveInfoServer($type_id, $day, $game_id, $channel_id, $server_id, $param)
    {
        $param = array_merge($param, ['update_time' => time()]);
        model('common/RetaineServer')->where(['type_id' => $type_id, 'day' => $day, 'game_id' => $game_id, 'channel_id' => $channel_id, 'server_id' => $server_id])->update($param);
    }

    public function getChannel($channel_id)
    {
        $data = [];
        if ($channel_id > 0) {
            $channelList = model('common/Channel')->cache('retaine:channelList', 120)->column('id,name,id_path,level', 'id');

            if (isset($channelList[$channel_id])) {
                $channel = $channelList[$channel_id];
                $id_path = explode(',', $channel['id_path']);
                foreach ($id_path as $kk => $vv) {
                    if (isset($vv) && isset($channelList[$vv]) && $channelList[$vv] && $vv != $channel_id) {
                        $channel_ch = $channelList[$vv];

                        if ($channel_ch['level'] == 0) {
                            $data['s_channel_id'] = $channel_ch['id'];
                        } elseif ($channel_ch['level'] == 1) {
                            $data['b_channel_id'] = $channel_ch['id'];
                        } elseif ($channel_ch['level'] == 2) {
                            $data['bz_channel_id'] = $channel_ch['id'];
                        }
                    }

                }
            }
            return $data;

        }
    }

    public function insertOrUpdatePlatform($list, $type_id, $day, $param)
    {
        Db::startTrans();
        try {
            foreach ($list as $k => $v) {
                if ($retaine = model('common/Retaine')->cache('retaine:insertOrUpdatePlatform:' . $type_id . '_' . strtotime($day) . '_' . $v['channel_id'], 60)->where(['type_id' => $type_id, 'day' => $day, 'channel_id' => $v['channel_id']])->find()) {
                    $data = [];
                    if ($retaine['s_channel_id'] == 0) {
                        $data = $this->getChannel($v['channel_id']);
                    }
                    $data[$param] = $v[$param];
                    if ($param == 'recharge_num') {
                        $this->saveInfoPlatform($type_id, $day, $v['channel_id'], ['recharge_num' => $v['recharge_num'], 'pay_num' => $v['pay_num']]);
                    } else {
                        $this->saveInfoPlatform($type_id, $day, $v['channel_id'], $data);
                    }

                } else {
                    $data = $this->getChannel($v['channel_id']);
                    $data[$param] = $v[$param];
                    $this->insertInfoPlatform($type_id, $day, $v['channel_id'], $data);
                }
            }
            DB::commit();
        } catch (Exception $e) {
            curlDD('【风谷】数据统计异常' . $type_id . '_' . $day . '_' . $param . '_' . $e->getMessage(), Env::get('dingtalk.warning_url'));
            DB::rollback();
        }
    }

    public function insertInfoPlatform($type_id, $day, $channel_id, $param)
    {
        model('common/Retaine')->insert(array_merge($param, [
            'type_id' => $type_id,
            'day' => $day,
            'channel_id' => $channel_id,
            'update_time' => time(),
        ]));
    }

    public function saveInfoPlatform($type_id, $day, $channel_id, $param)
    {
        $param = array_merge($param, ['update_time' => time()]);
        model('common/Retaine')->where(['type_id' => $type_id, 'day' => $day, 'channel_id' => $channel_id])->update($param);
    }
}
