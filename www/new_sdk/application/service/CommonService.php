<?php
namespace app\service;

use app\common\model\Setting;
use think\Db;

class CommonService
{
    /**
     * 实名认证--游戏充值值防沉迷判断
     */
    public function authentication($member_id, $gameid, $amount)
    {
        //游戏防沉迷限制
        $restrictInfo = model('GameRestrict')->where(['game_id' => $gameid, 'restrict_type' => 'preventhook'])->find();
        if ($restrictInfo) {
            $isPreventHook = intval($restrictInfo['restrict_status']);
        } else {
            $settingModel = new Setting;
            $isPreventHook = intval($settingModel::getSetting('CHILD_LIMIT'));
        }
        if ($isPreventHook == 2) {
            // 已实名认证用户做防沉迷判断
            $membersTwoInfo = model('MembersTwo')->where(['userid' => $member_id])->find();

            $gameInfo = model('Game')->where(['id' => $gameid])->find();
            if ($gameInfo['game_auth_type'] == 1 || $gameInfo['game_auth_type'] == 2) {  //中宣部实名认证
                $gameAccountInfo = model('Subaccount')->where(['game_id' => $gameid, 'member_id' => $member_id])->find();
                if ($gameAccountInfo) {
                    if ($gameAccountInfo['auth_status'] == 3) {
                        $membersTwoInfo['realname'] = trim($gameAccountInfo['real_name']);
                        $membersTwoInfo['idcard'] = trim($gameAccountInfo['idcard_num']);
                    } else if ($gameAccountInfo['auth_status'] == 1) {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    } else {
                        $membersTwoInfo['realname'] = '';
                        $membersTwoInfo['idcard'] = '';
                    }
                } else {
                    $membersTwoInfo['realname'] = '';
                    $membersTwoInfo['idcard'] = '';
                }
            }

            if ($membersTwoInfo && !empty($membersTwoInfo['realname']) && !empty($membersTwoInfo['idcard']) && $membersTwoInfo['realname'] <> -1 && $membersTwoInfo['idcard'] <> -1) {
                $totalMonthRechargeAmt = model('Pay')->where(['userid' => $member_id, 'status' => 1, 'create_time' => ['egt', strtotime(date('Y-m-01'))]])->sum('amount');

                if (isMeetAgeByIDCard($membersTwoInfo['idcard'], 0, 12)) {
                    return ['code' => 0, 'msg' => '未满12周岁的未成年用户，无法进行充值'];
                } else if (isMeetAgeByIDCard($membersTwoInfo['idcard'], 12, 16)) {
                    if ($amount > 50) {
                        return ['code' => 0, 'msg' => '12周岁以上未满16周岁的未成年用户，单次充值金额不得超过50元，每月累计充值上限为200元'];
                    }
                    if (($totalMonthRechargeAmt + $amount) > 200) {
                        return ['code' => 0, 'msg' => '12周岁以上未满16周岁的未成年用户，单次充值金额不得超过50元，每月累计充值上限为200元'];
                    }
                } else if (isMeetAgeByIDCard($membersTwoInfo['idcard'], 16, 18)) {
                    if ($amount > 100) {
                        return ['code' => 0, 'msg' => '16周岁以上未满18周岁的未成年用户，单次充值金额不得超过100元，每月累计充值上限为400元'];
                    }
                    if (($totalMonthRechargeAmt + $amount) > 400) {
                        return ['code' => 0, 'msg' => '16周岁以上未满18周岁的未成年用户，单次充值金额不得超过100元，每月累计充值上限为400元'];
                    }
                }
            }
        }
        return ['code' => 1];
    }
    
    /**
     * 获取玩家其他端游戏的渠道ID
     *
     * @param $member_id 用户ID
     * @param $game_id 游戏ID
     *
     * @return void
     */
    public function getGameOtherChannelId($member_id, $game_id) {
        
        $gameType = model('Common/Game')->where(['id' => $game_id])->value('type');
        $where = [];
        // 游戏类型：1=ios、2=安卓、3=h5
        if($gameType == 1){
            $where['ios_game_id'] = $game_id;
            $otherGameIds = ['android_game_id', 'h5_game_id'];
        } else if ($gameType == 2){
            $where['android_game_id'] = $game_id;
            $otherGameIds = ['ios_game_id', 'h5_game_id'];
        } else if ($gameType == 3) {
            $where['h5_game_id'] = $game_id;
            $otherGameIds = ['android_game_id', 'ios_game_id'];
        }
        
        $gameBandInfo = model('common/GameBand')->where($where)->find();
        // 没有绑定
        if(!$gameBandInfo){
            return '';
        }
        
        $memberGameIds = [];
        if($gameType == 1){
            $memberGameIds = [$gameBandInfo['android_game_id'], $gameBandInfo['h5_game_id']];
        } else if ($gameType == 2){
            $memberGameIds = [$gameBandInfo['ios_game_id'], $gameBandInfo['h5_game_id']];
        } else if ($gameType == 3) {
            $memberGameIds = [$gameBandInfo['android_game_id'], $gameBandInfo['ios_game_id']];
        }
        $gameIds = array_filter($memberGameIds, function($v) {
            return $v>0;
        });
        
        $channelId = model('common/MemberChannelGame')->where(['member_id' => $member_id, 'game_id' => ['in', $gameIds]])->value('channel_id');
        if($channelId){
            return $channelId;
        }
        
        return '';
    }
    
    /**
     * 处理归属绑定保护
     *
     * 根据设定时间，查询范围内的实名信息，处理游戏的归属是否需要进行归属保护。
     *
     * @param $member_id 处理的玩家ID
     * @param $member_channel_id 当前绑定的渠道ID
     * @param $game_id 游戏ID
     * @param $realname 实名-姓名
     * @param $idcard 实名-身份证号
     *
     * @return false
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function handleBelongToBinding($member_id, $member_channel_id, $game_id, $realname, $idcard) {
        $date = date('Y-m-d', time());
        $time = time();
        // 获取是否需要处理绑定关联
        $gameBelongInfo = model('common/GameBelong')->getCacheInfo(['game_id' => $game_id, 'status' => 1], ['id', 'start_date', 'end_date']);
        if(!$gameBelongInfo){
            return false;
        }
        
        if ($date >= $gameBelongInfo['start_date'] && $date <= $gameBelongInfo['end_date']) {
            // 1. 获取当前游戏的实名类型(中宣/平台)
            $gameInfo = model('common/Game')->where(['id' => $game_id])->field('id,name,game_auth_type')->find();
            
            // 认证类型: 0=平台认证, 1=中宣部实名认证, 2=通过上游认证
            // 获取指定时间的，对应游戏最新玩家的的渠道ID
            if($gameInfo['game_auth_type'] === 0){
                // 这里只查询身份证号，因为带姓名，平台只有正则验证，玩家可以造假
                $userIds = model('common/MembersTwo')
                    ->where(['idcard' => $idcard, 'userid' => ['neq', $member_id]])
                    ->whereTime('create_time', '>=', $gameBelongInfo['start_date'])
                    ->column('userid');
                if($userIds){
                    $subInfo = model('common/Subaccount')
                        ->where(['game_id' => $game_id, 'member_id' => ['in', $userIds]])
                        ->order('id asc')
                        ->field('channel_id,create_time')
                        ->find();
                }
            } else if ($gameInfo['game_auth_type'] == 1){
                $subInfo = model('common/Subaccount')
                    ->where(['game_id' => $game_id, 'realname' => $realname, 'idcard' => $idcard, 'member_id' => ['neq', $member_id]])
                    ->whereTime('create_time', '>=', $gameBelongInfo['start_date'])
                    ->order('id asc')
                    ->find('channel_id,create_time');
            }
            
            if(empty($subInfo)){
                return false;
            }
            
            // 注册时间的一个月绑定保护时间，是否在当前时间范围内
            $timestamp = (new \DateTime($subInfo['create_time']))->modify('+1 month')->getTimestamp();
            if($timestamp < $time){
                return false;
            }
            
            try {
                $channel_id = $subInfo['channel_id'];
                $updateTime = NOW_TIMESTAMP;
                // 用户 - 初始化注册的记录，可以不用改
                // model('common/Members')->update(["channel_id" => $channel_id, "update_time" => $updateTime], ['id' => $member_id]);
                // 子账户
                model('common/Subaccount')->update(["channel_id" => $channel_id, "update_time" => $updateTime], ['game_id' => $game_id, 'member_id' => $member_id]);
                // 玩家-游戏-区服 关联
                model('common/MemberGameServer')->update(["channel_id" => $channel_id, "update_time" => $updateTime], ['member_id' => $member_id, 'game_id' => $game_id]);
                // 玩家-渠道-游戏 关联
                model('common/MemberChannelGame')->update(["channel_id" => $channel_id, "update_time" => $updateTime], ['member_id' => $member_id, 'game_id' => $game_id]);
                
                // 记录日志
                $channelName = model('common/Channel')->where(['id' => $channel_id])->value('name');
                Db::table('cy_member_history')->insert([
                   'userid' => $member_id,
                   'channel' => $gameInfo['name'] . " 渠道归属改为 " . $channelName.'(归属绑定处理)',
                   'ip' => request()->ip(),
                   'create_time' => $time
               ]);
                
                return true;
            }catch (\Exception $e) {
                log_message('## CommonService@handleBelongToBinding: ' . $e->getMessage().' - '.$e->getFile().':'.$e->getLine(), 'log', LOG_PATH . 'exception/');
            }
        }
        
        return false;
    }
}
