<?php

namespace app\service;

use HuaweiCloud\SDK\Cdn\V1\CdnClient;
use HuaweiCloud\SDK\Cdn\V1\Model\CreatePreheatingTasksRequest;
use HuaweiCloud\SDK\Cdn\V1\Model\CreateRefreshTasksRequest;
use HuaweiCloud\SDK\Cdn\V1\Model\PreheatingTaskRequest;
use HuaweiCloud\SDK\Cdn\V1\Model\PreheatingTaskRequestBody;
use HuaweiCloud\SDK\Cdn\V1\Model\RefreshTaskRequest;
use HuaweiCloud\SDK\Cdn\V1\Model\RefreshTaskRequestBody;

use HuaweiCloud\SDK\Cdn\V1\Model\ShowUrlTaskInfoRequest;
use HuaweiCloud\SDK\Core\Auth\GlobalCredentials;
use HuaweiCloud\SDK\Core\Exceptions\RequestTimeoutException;
use HuaweiCloud\SDK\Core\Exceptions\ServiceResponseException;
use HuaweiCloud\SDK\Core\Http\HttpConfig;
use MongoDB\Driver\Exception\ConnectionException;
use think\Env;

class HuaWeiCloudService
{
    /**
     * 创建预热缓存任务
     */
    public function createPreheatingTasks($urls = []){
        if(!$urls){
            return false;
        }
        $ak = HW_OBS_ACCESS_KEYID;
        $sk = HW_OBS_ACCESS_KEYSECRET;
        $endpoint = 'https://cdn.myhuaweicloud.com';
        $domainId = HW_DOMAIN_ID;
        $credentials = new GlobalCredentials($ak, $sk, $domainId);
        $config = HttpConfig::getDefaultConfig();
        $config->setIgnoreSslVerification(true);

        $client = CdnClient::newBuilder(new CdnClient)
            ->withHttpConfig($config)
            ->withEndpoint($endpoint)
            ->withCredentials($credentials)
            ->build();
        $request = new CreatePreheatingTasksRequest();

        $body = new PreheatingTaskRequest();
//        $listPreheatingTaskUrls = array();
//        array_push($listPreheatingTaskUrls,"https://static.46yx.com/test/icon.png");
        $preheatingTaskbody = new PreheatingTaskRequestBody();
        $preheatingTaskbody->setUrls($urls);
        $body->setPreheatingTask($preheatingTaskbody);
        $request->setBody($body);
        try {
            $response = $client->CreatePreheatingTasks($request);
            return true;
        } catch (ConnectionException $e) {
            $template = 'cdn创建缓存异常，' . $e->getMessage() . '时间：' . date('Y-m-d H:i:s');
            $ddurl = Env::get('dingtalk.warning_url');
            curlDD($template, $ddurl, true);
        } catch (RequestTimeoutException $e) {
            $template = 'cdn创建缓存异常，' . $e->getMessage() . '时间：' . date('Y-m-d H:i:s');
            $ddurl = Env::get('dingtalk.warning_url');
            curlDD($template, $ddurl, true);
        } catch (ServiceResponseException $e) {
            $template = 'cdn创建缓存异常，' . $e->getHttpStatusCode() . "\n" . $e->getErrorCode() . "\n" . $e->getErrorMsg() . "\n" . '时间：' . date('Y-m-d H:i:s');
            $ddurl = Env::get('dingtalk.warning_url');
            curlDD($template, $ddurl, true);
        }
    }

    /**
     * 创建刷新缓存任务
     */
    public function createRefreshTasks($urls = [])
    {
        if(!$urls){
            return false;
        }
        $ak = HW_OBS_ACCESS_KEYID;
        $sk = HW_OBS_ACCESS_KEYSECRET;
        $endpoint = 'https://cdn.myhuaweicloud.com';
        $domainId = HW_DOMAIN_ID;
        $credentials = new GlobalCredentials($ak, $sk, $domainId);
        $config = HttpConfig::getDefaultConfig();
        $config->setIgnoreSslVerification(true);

        $client = CdnClient::newBuilder(new CdnClient)
            ->withHttpConfig($config)
            ->withEndpoint($endpoint)
            ->withCredentials($credentials)
            ->build();
        $request = new CreateRefreshTasksRequest();

        $body = new RefreshTaskRequest();
//        $listRefreshTaskUrls = $urls;
//        array_push($listRefreshTaskUrls, "https://static.46yx.com/test/icon.png");
        $refreshTaskbody = new RefreshTaskRequestBody();
        $refreshTaskbody->setType("file")
            ->setUrls($urls);
        $body->setRefreshTask($refreshTaskbody);
        $request->setBody($body);
        try {
            $response = $client->CreateRefreshTasks($request);
            echo "\n";
            echo $response;
        } catch (ConnectionException $e) {
            $msg = $e->getMessage();
            echo "\n" . $msg . "\n";
        } catch (RequestTimeoutException $e) {
            $msg = $e->getMessage();
            echo "\n" . $msg . "\n";
        } catch (ServiceResponseException $e) {
            echo "\n";
            echo $e->getHttpStatusCode() . "\n";
            echo $e->getErrorCode() . "\n";
            echo $e->getErrorMsg() . "\n";
        }
    }

    public function showUrlTaskInfo()
    {
        $ak = "QLQDGJW1PRU6RFVALSJB";
        $sk = "FWccYNCnpnzi6tKsDyROQMYyVrIledqziRZmt5Fw";
        $endpoint = "https://cdn.myhuaweicloud.com";
        $domainId = "0c8c75773e00f2350f9dc018374f3600";
        $credentials = new GlobalCredentials($ak, $sk, $domainId);
        $config = HttpConfig::getDefaultConfig();
        $config->setIgnoreSslVerification(true);

        $client = CdnClient::newBuilder(new CdnClient)
            ->withHttpConfig($config)
            ->withEndpoint($endpoint)
            ->withCredentials($credentials)
            ->build();

        $request = new ShowUrlTaskInfoRequest();
        $request->setOffset(1);
        $request->setLimit(10);
        $request->setTaskType("REFRESH");
        $request->setFileType("file");
        try {
            $response = $client->ShowUrlTaskInfo($request);
            echo "\n";
            echo $response;
        } catch (ConnectionException $e) {
            $msg = $e->getMessage();
            echo "\n" . $msg . "\n";
        } catch (RequestTimeoutException $e) {
            $msg = $e->getMessage();
            echo "\n" . $msg . "\n";
        } catch (ServiceResponseException $e) {
            echo "\n";
            echo $e->getHttpStatusCode() . "\n";
            echo $e->getErrorCode() . "\n";
            echo $e->getErrorMsg() . "\n";
        }
    }
}
