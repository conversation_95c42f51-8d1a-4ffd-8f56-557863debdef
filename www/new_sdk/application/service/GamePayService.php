<?php

namespace app\service;

use app\common\logic\PayHandle;
use app\common\model\ChannelFrozen;
use app\common\model\Cpurl;
use app\common\model\Members;
use app\common\model\Game;
use app\common\model\Pay as PayModel;
use app\common\model\PayCpinfo;
use app\common\model\MemberChannelGame;
use app\common\logic\Pay as PayLogic;
use app\common\model\Subaccount;
use think\Config;
use think\Db;
use think\Exception;
use app\common\model\App;
use think\Request;

class GamePayService
{
    protected $payRequestLimit = 5;        //重复下单的限制时间

    /**
     * 游戏支付
     * @param $data
     */
    public function gamePay($data, $urlType = 1)
    {
        $paytype = $data['paytype'];// 支付方式
        $pay_scene = $data['paytype'];
        
        // ## 随机为新的支付渠道 ##
        // 取消代金卷、平台币的判断处理
        if(!in_array($paytype, ['coupon', 'coinpay'])){
            // if(!in_array($paytype, ['wx_h5', 'ali_h5']) && $data['device'] != 4){
                if (version_compare($data['version'], config('pay_handle_version'), '>')) {
                    $payType = [
                        'wx_h5' => ['qzl_wx_h5', 'ldys_wx_h5', 'ybzf_wxmp_h5', 'yyyb_wx_h5', 'xty_wx_h5', 'qmf_wxmp_h5'],
                        'ali_h5' => ['old-zfb-wap', 'ldys_zfb_h5', 'xty_ali_h5', 'yyyb_ali_h5'],
                        'ali_app' => ['zfb'],
                        'ptb' => ['coinpay'],
                    ];
                } else {
                    // 老版本
                    $payType = [
                        'wx_h5' => ['qzl_wx_h5', 'ldys_wx_h5', 'ybzf_wxmp_h5', 'yyyb_wx_h5', 'xty_wx_h5', 'qmf_wxmp_h5'],
                        'ali_h5' => ['old-zfb-wap', 'xty_ali_h5'],
                        'ali_app' => [],
                    ];
                }
                $newPayType = handlePayType($payType);
                
                // 判断所属类型
                if (in_array($data['paytype'], $newPayType['wx_h5'])) {
                    $pay_scene = 'wx_h5';
                } else if (in_array($data['paytype'], $newPayType['ali_h5'])) {
                    $pay_scene = 'ali_h5';
                } else if (in_array($data['paytype'], $newPayType['ali_app'])) {
                    $pay_scene = 'ali_app';
                } else if (in_array($data['paytype'], $newPayType['ptb'])) {
                    $pay_scene = 'ptb';
                } else {
                    // 不能这样判断，因为会有ptb
                    // return ['code' => -111, 'msg' => '支付方式有误！', 'data' => []];
                }
            // }
    
            // 去除支付中包含有混合支付标识（获取支付方式）
            $new_pay_scene = str_replace(['mix-', 'coin-'], '', $pay_scene);
            // 随机支付管控，没有则走默认配置的
            if ((version_compare($data['version'], config('pay_handle_version'), '>') && $new_pay_scene) || (isInArrType($newPayType, $paytype) && $new_pay_scene)) {
                $newPayType = (new PayHandle())->getPayChannel($new_pay_scene, $data['gameid'], $data['version']);
                if (empty($newPayType['data']['paytype'])) {
                    return ['data' => '', 'code' => -110, 'msg' => '支付配置有误！当前游戏或者版本未开启支付！'];
                }
                $paytype = $newPayType['data']['paytype'];
                if (substr($data['paytype'], 0, 4) == 'mix-') {
                    $data['paytype'] = 'mix-' . $paytype;
                } else if (substr($data['paytype'], 0, 5) == 'coin-') {
                    $data['paytype'] = 'coin-' . $paytype;
                } else {
                    $data['paytype'] = $newPayType['data']['paytype'];
                }
            } else {
                // 老版支付使用指定
                $setPayType = model('common/Setting')->where(['name' => 'OLD_PAY_WX_TYPE'])->value('value');
                if ($setPayType) {
                    // qmf_wxmp_h5 = 标识代替其他支付方式
                    if (strstr($data['paytype'], 'qmf_wxmp_h5') !== false) {
                        $paytype = $setPayType;
                        if ($data['paytype'] == 'qmf_wxmp_h5') {
                            $data['paytype'] = $setPayType;
                        } else if ($data['paytype'] == 'mix-qmf_wxmp_h5') {
                            $data['paytype'] = 'mix-' . $setPayType;
                        } else if ($data['paytype'] == 'coin-qmf_wxmp_h5') {
                            $data['paytype'] = 'coin-' . $setPayType;
                        }
                    }
                }
            }
        }
        // ## 随机为新的支付渠道 - END ##

        $time = NOW_TIMESTAMP;
        $data['orderid'] = $orderid = makeOrderid();
        $pay_status = 0;                                //cy_pay表的支付状态
        $cp_payflag = 0;                                //cy_paycpinfo表的支付状态

        $memberModel = new Members();
        $frozenModel = new ChannelFrozen();
        $cpurlModel = new Cpurl();
        $gameModel = new Game;
        $payModel = new PayModel;
        $payCpinfoModel = new PayCpinfo;
        $memberChannelGameModel = new MemberChannelGame;
        $payLogic = new PayLogic();

        //当前游戏的包是否禁止消费(非归属渠道)
        if (isFrozenOption($data['channel_id'], $data['gameid'], 'consume')) {
            return ['data' => '', 'code' => 0, 'msg' => '您所在渠道已经被禁止在此游戏消费'];
        }
        if (empty($data['ptb_amt']) && (substr($data['paytype'], 0, 4) == 'mix-' || $data['paytype'] == 'ptb')) {
            return ['code' => 0, 'msg' => '请输入专属币使用数量'];
        }

        if (empty($data['coin_amt']) && (substr($data['paytype'], 0, 5) == 'coin-' || $data['paytype'] == 'coinpay')) {
            return ['code' => 0, 'msg' => '请输入平台币使用数量'];
        }

        if (empty($data['coupon_member_id']) && $data['paytype'] == 'coupon') {
            return ['code' => 0, 'msg' => '请选择代金券'];
        }

        $memberInfo = $memberModel->field('id,username,amount')->where(['id' => $data['member_id']])->find();
        if (empty($memberInfo)) {
            return ['code' => 0, 'msg' => '用户不存在'];
        }

        //子账号用户名
        $subaccountData = (new Subaccount())->field(['sub_username', 'channel_id'])->where(['member_id' => $data['member_id'], 'game_id' => $data['gameid']])->find();
        $data['sub_username'] = $subaccountData['sub_username']??'';
        if (!$data['sub_username']) {
            return ['data' => '', 'code' => 0, 'msg' => '账号异常'];
        }

        //玩家在这款游戏的归属渠道
        $data['channel_id'] = $subaccountData['channel_id']??'';
        // $channel_id = $memberChannelGameModel->where(['member_id' => $data['member_id'], 'game_id' => $data['gameid']])->value('channel_id');
        if (empty($data['channel_id'])) {
            return ['data' => '', 'code' => 0, 'msg' => '无关联的归属渠道信息'];
        }
        // $data['channel_id'] = $channel_id;
        //判断渠道包是否合法
        $channelInfo = model('Channel')->where(['status' => 1, 'id' => $data['channel_id']])->find();
        if (!$channelInfo && intval($data['channel_id']) <> config('EMPTY_CHANNEL_ID')) {
            return ['data' => '', 'code' => 0, 'msg' => '当前渠道已禁用'];
        } else if ($channelInfo['level'] <> 3) {
            return ['data' => '', 'code' => 0, 'msg' => '非推广员渠道,不能进行推广,请联系客服'];
        }

        //当前游戏的渠道是否禁止消费(非归属渠道)
        if (isFrozenOption($data['channel_id'], $data['gameid'], 'consume')) {
            return ['data' => '', 'code' => 0, 'msg' => '您所在渠道已经被禁止在此游戏消费'];
        }
        $appModel = new App();
        if (!isset($data['appInfo'])) {
            $data['appInfo'] = $appModel->field('id,appkey,client_appkey,gameid')->where(['gameid' => $data['gameid']])->find()->toArray();
        }

        //充值回调地址
        if ($urlType == 1) {
            $cpurl = $cpurlModel->where(['appid' => $data['appInfo']['id']])->value('url');
        } else {
            $cpurl = $cpurlModel->where(['appid' => $data['appInfo']['id']])->value('web_url');

            if (!$gameServerInfo = model('GameServer')->field('serverid,servername')->where(['game_id' => $data['gameid'], 'serverid' => $data['serverid']])->find()) {
                return ['data' => '', 'code' => 0, 'msg' => '游戏区服id参数错误'];
            }
            $data['servername'] = $gameServerInfo['servername'];

            if (!$memberGameServerInfo = model('MemberGameServer')->where(['member_id' => $data['member_id'], 'game_id' => $data['gameid'], 'serverid' => $data['serverid'], 'roleid' => $data['roleid']])->find()) {
                return ['data' => '', 'code' => 0, 'msg' => '游戏角色信息不存在'];
            }
            $data['rolename'] = $memberGameServerInfo['rolename'];
            $data['rolelevel'] = $memberGameServerInfo['rolelevel'];
        }

        if (empty($cpurl)) {
            return ['data' => '', 'code' => 0, 'msg' => '没有回调地址，请通知我方配置'];
        }

        //游戏信息
        $gameInfo = $gameModel->field('order_recheck,is_pay')->where(['id' => $data['gameid']])->find();
        if (empty($gameInfo)) {
            return ['data' => '', 'code' => 0, 'msg' => '游戏id参数错误'];
        }

        if ($gameInfo['is_pay'] != 1) {
            return ['data' => '', 'code' => 0, 'msg' => '游戏已停止充值'];
        }
        if ($data['amount'] > 100000) {
            return ['data' => '', 'code' => 0, 'msg' => '您的充值金额过大,请确认是否为合法充值'];
        }

        $data['discount'] = $discount = getDiscount($data['member_id'], $data['gameid'], $data['channel_id']); // 折扣比例

        if ($data['ptb_amt'] > 0 && $data['coin_amt'] > 0) {
            return ['data' => '', 'code' => 0, 'msg' => '平台币和专属币不能同时使用'];
        }
        $data['coupon_amount'] = 0;
        $amount = $data['amount'];
        // ## 代金券使用
        if ($data['coupon_member_id'] > 0) {
            $list = model('common/CouponMember')->getPayCoupon($data['member_id'], $data['gameid'], $data['amount'], $data['coupon_member_id']);
            if (count($list) != 1) {
                return ['data' => '', 'code' => 0, 'msg' => '代金券不存在'];
            }
            if ($list[0]['is_pay'] != 1) {
                return ['data' => '', 'code' => 0, 'msg' => '代金券不可使用'];
            }

            if ($list[0]['money'] > $data['amount']) {
                if ($data['paytype'] != 'coupon') {
                    return ['data' => '', 'code' => 0, 'msg' => '支付类型错误'];
                }
                $data['coupon_amount'] = $data['amount'];
                $amount = 0;
            } else {
                $data['coupon_amount'] = $list[0]['money'];
                $amount = priceFormat($data['amount'] - $data['coupon_amount']);
            }
        }


        // ## 有使用专属币时
        if (!empty($data['ptb_amt'])) {
            if ($data['ptb_amt'] > $amount) {
                return ['data' => '', 'code' => 0, 'msg' => '游戏专属币支付金额选择异常,平台币使用金额大于总金额'];
            }
            $userCoinInfo = model('MemberZscoin')->field('id,userid,username,game_id,amount,status')->where(['userid' => $data['member_id'], 'game_id' => $data['gameid']])->find();
            if (empty($userCoinInfo)) {
                return ['data' => '', 'code' => 0, 'msg' => '您的游戏专属币余额不足'];
            } else if (!$userCoinInfo['status']) {
                return ['data' => '', 'code' => 0, 'msg' => '您的游戏专属币账户已被冻结,不能使用'];
            } else if ($userCoinInfo['amount'] < $data['ptb_amt']) {
                return ['data' => '', 'code' => 0, 'msg' => '您的游戏专属币余额不足'];
            }

            $arrPayAmount = $payLogic->calPayAmount($amount, $data['ptb_amt'], $discount);
        } else if (!empty($data['coin_amt'])) {    //有使用平台币时
            if ($data['coin_amt'] > $amount) {
                return ['data' => '', 'code' => 0, 'msg' => '游戏平台币支付金额选择异常,平台币使用金额大于总金额'];
            }
            if ($memberInfo['amount'] < $data['coin_amt']) {
                return ['data' => '', 'code' => 0, 'msg' => '您的游戏平台币余额不足'];
            }
            $arrPayAmount = $payLogic->calCoinPayAmount($amount, $data['coin_amt'], $discount);
        } else{
            $arrPayAmount = $payLogic->calPayAmount($amount,0, $discount);
        }

        // // 全民付 替换其他支付
        // if (strstr($data['paytype'], 'qmf_wxmp_h5')) {
        //     if ($data['paytype'] == 'qmf_wxmp_h5') {
        //         $data['paytype'] = 'yyyb_wx_h5';
        //     } else if ($data['paytype'] == 'mix-qmf_wxmp_h5') {
        //         $data['paytype'] = 'mix-yyyb_wx_h5';
        //     } else if ($data['paytype'] == 'coin-qmf_wxmp_h5') {
        //         $data['paytype'] = 'coin-yyyb_wx_h5';
        //     }
        // }
        // // ## 支付方式适配 -- 未使用
        // if (strstr($data['paytype'], 'pay_h5_001')) { // 备用支付 - 001
        //     if ($data['paytype'] == 'pay_h5_001') {
        //         $data['paytype'] = 'test';
        //     } else if($data['paytype'] == 'mix-pay_h5_001'){
        //         $data['paytype'] = 'mix-test';
        //     } else if($data['paytype'] == 'coin-pay_h5_001'){
        //         $data['paytype'] = 'coin-test';
        //     }
        // }
        // if (strstr($data['paytype'], 'pay_h5_002')) { // 备用支付 - 002
        //     if ($data['paytype'] == 'pay_h5_002') {
        //         $data['paytype'] = 'test';
        //     } else if($data['paytype'] == 'mix-pay_h5_002'){
        //         $data['paytype'] = 'mix-test';
        //     } else if($data['paytype'] == 'coin-pay_h5_002'){
        //         $data['paytype'] = 'coin-test';
        //     }
        // }

        //实际支付金额为0时
        if ($arrPayAmount['real_amount'] == 0) {
            $pay_status = 1;            //支付成功
            $cp_payflag = 1;
            //付款时间
            $payData['pay_time'] = NOW_TIMESTAMP;
        } elseif ($arrPayAmount['real_amount'] > 0 && $data['paytype'] == 'ptb') { // 有实际支付金额并且支付方式是平台币时
            return ['data' => '', 'code' => 0, 'msg' => '平台币余额不足，请使用混合支付'];
        } elseif ($arrPayAmount['real_amount'] > 0 && $arrPayAmount['real_ptb'] > 0 && substr($data['paytype'], 0, 4) <> 'mix-') {
            return ['data' => '', 'code' => 0, 'msg' => '请选择正确的支付方式.'];
        } //有实际支付金额并且支付方式是平台币时
        elseif ($arrPayAmount['real_amount'] > 0 && $data['paytype'] == 'coinPay') {
            return ['data' => '', 'code' => 0, 'msg' => '平台币余额不足，请使用混合支付'];
        } elseif ($arrPayAmount['real_amount'] > 0 && $arrPayAmount['real_coin'] > 0 && substr($data['paytype'], 0, 5) <> 'coin-') {
            return ['data' => '', 'code' => 0, 'msg' => '请选择正确的支付方式..'];
        }


        //指定时间内，禁止重复下单
        if (!requestDuplicateCheck('pay_duplicate_' . $data['member_id'] . '_' . $data['gameid'], $this->payRequestLimit)) {
            return ['data' => '', 'code' => 0, 'msg' => '充值请求过多，请于' . $this->payRequestLimit . 's以后，再次进行充值操作'];
        }
        //订单已重复
        if ($payModel->where(['attach' => $data['attach'], 'gameid' => $data['gameid']])->find()) {
            //解锁
            requestDuplicateCheckUnlock('pay_duplicate_' . $data['member_id'] . '_' . $data['gameid']);
            return ['data' => '', 'code' => 0, 'msg' => '游戏合作方的订单参数不能重复'];
        }

        //游戏防沉迷限制
        $authentication = (new CommonService())->authentication($data['member_id'], $data['gameid'], $data['amount']);
        if ($authentication['code'] == 0) {
            return $authentication;
        }
        //海外支付添加比例
        if (in_array($data['paytype'], ['airwallexh5', 'airwallex', 'coin-airwallexh5', 'coin-airwallex'])) {
            $airPaytype = $data['paytype'];
            if ($data['paytype'] == 'coin-airwallexh5') {
                $airPaytype = 'airwallexh5';
            } elseif ($data['paytype'] == 'coin-airwallex') {
                $airPaytype = 'airwallex';
            }
            $paytypeInfo = model('common/PayType')->where('paytype', $airPaytype)->find();
            $arrPayAmount['real_amount'] = priceFormat($arrPayAmount['real_amount'] * (1 + $paytypeInfo['commission']));
        }

        //订单参数
        $payData = $this->orderData($data, $arrPayAmount, $memberInfo, $gameInfo, $pay_status);

        try {
            // 启动事务
            Db::startTrans();
            $id = $payModel->insertGetId($payData);

            //如果是自己聚合自己时,支付回调地址固定
            if (mb_substr($data['attach'], 0, 8, 'UTF-8') == 'xkhyn###') {
                $cpurl = Config::get('self_complex_callback_url');
                $attach = str_replace("xkhyn###", "", $data['attach']);       //聚合的订单编号，去掉[aoyou###]前缀
            } else {
                $attach = $data['attach'];
            }

            //给Cp的用户名用子账号名
            $paycp_username = (!empty($data['sub_username']) ? $data['sub_username'] : $memberInfo['username']);

            // ## 订单回调签名 ##
            $str = "orderid=" . urlencode($orderid) . "&username=" . urlencode($paycp_username) . "&gameid=" . $data['gameid'] . "&roleid=" . urlencode($data['roleid']) . "&serverid=" . urlencode($data['serverid']) . "&paytype=" . urlencode($data['paytype']) . "&amount=" . $data['amount'] . "&paytime=" . $time . "&attach=" . urlencode($attach);
            $param = $str . "&appkey=" . urlencode($data['appInfo']['appkey']);
            $md5params = md5($param);
            $params = $str . "&sign=" . urlencode($md5params);

            // 订单通知游戏数据
            // log_message('order_id: ' . $orderid .' - URL: '. $cpurl ." - sign_data: ".$param. " - md5: ". $md5params, 'log', LOG_PATH . 'notify_msg/');
            trace('CP_SIGN - order_id: ' . $orderid .' - URL: '. $cpurl ." - sign_data: ".$param. " - md5: ". $md5params, 'GamePayService@gamePay.cp_notify_body');


            $paycpData['orderid'] = $orderid;
            $paycpData['fcallbackurl'] = $cpurl;
            $paycpData['params'] = $params;
            $paycpData['create_time'] = $time;
            $paycpData['payflag'] = $cp_payflag;

            //写入cy_paycpinfo表数据
            $payCpinfoModel->insert($paycpData);

            //代金券状态修改
            if ($payData['coupon_amount'] > 0) {
                if(!$payLogic->saveCouponData($payData)){
                    return ['data' => '', 'code' => 0, 'msg' => '代金券使用失败'];
                }
            }
            // 如果使用了专属币
            if ($arrPayAmount['real_ptb'] > 0) {
                $payLogic->saveCoinData($userCoinInfo, $payData);
            } else if ($arrPayAmount['real_coin'] > 0) {
                if (!$payLogic->saveCoinPayData($memberInfo, $payData)) {
                    return ['data' => '', 'code' => 0, 'msg' => '金额异常.'];
                }
            }else if($payData['paytype'] == 'coupon'){
                    //纯代金券支付
            }else {
               // $arrPayAmount   = $payLogic->calPayAmount($payData['pay_amount']);
               //  if ($arrPayAmount['real_amount'] <= 0) {
               //      return ['data' => '', 'code' => 0, 'msg' => '金额异常..'];
               //  }
            }
            // $real_amount = $arrPayAmount['real_amount'];

            //全部平台币支付，并且支付成功时
            if ($pay_status) {
                //更新member表的total_pay_amount
                $memberModel->where(['id' => $data['member_id']])->update(['total_pay_amount' => Db::raw('total_pay_amount+' . $data['amount']), 'update_time' => time()]);
            }
            // 提交事务
            Db::commit();
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();

            return ['data' => '', 'code' => 0, 'msg' => '订单生成失败: ' . $e->getMessage()];
        }

        
        
        /**
         * 微信支付:
         *  可用：'qzl_wx_h5', 'ybzf_wxmp_h5', 'yyyb_wx_h5', 'xty_wx_h5'
         *  不可用：'ldys_wx_h5'
         *
         * 支付宝：
         *  可用：'xty_ali_h5', 'yyyb_ali_h5'
         *  不可用：'old-zfb-wap', 'ldys_zfb_h5'
         */
        try {
            //第三方支付用的商品简单描述   (去除&+等特殊字符)
            $payBody = preg_replace("/[\&\+]+/", '', $data['productname']);
            $pay = new PayService();
            // $payData['real_amount'] = $real_amount;
            $real_amount = $payData['real_amount'];
            
            $payInfoRes = [];
            if (in_array($data['paytype'], ['zfb', 'coin-zfb', 'mix-zfb', 'coin-mix-zfb'])) { // 支付宝-app
                $notify_url = Config::get('ali_pay_config')['notify_url'];
                $payInfoRes = $pay->getAlipayAopParam($orderid, $real_amount, $payBody,  '', $notify_url);

            } elseif (in_array($data['paytype'], ['old-zfb-wap', 'coin-old-zfb-wap', 'mix-old-zfb-wap', 'coin-mix-old-zfb-wap'])) { // 支付宝-h5
                $notify_url = Config::get('ali_pay_config')['notify_url'];
                $payInfoRes = $pay->getAlipayWapPostAopParam($orderid, $real_amount, $payBody, '', $notify_url);
                if(empty($payInfoRes['data']['mweb_url'])){
                    return ['data' => '', 'code' => 0, 'msg' => "当前支付方式开发中..."];
                }

            } elseif (in_array($data['paytype'], ['wxpay-h5', 'coin-wxpay-h5', 'wx-wap', 'coin-wx-wap'])) { // 微信-h5
                if ($real_amount > 3000) {
                    requestDuplicateCheckUnlock('pay_duplicate_' . $data['member_id'] . '_' . $data['gameid']);
                    return ['data' => '', 'code' => 0, 'msg' => '微信单笔支付限额3000元,您可选择其它支付'];
                }
                $notify_url = Config::get('wxpay-h5')['notify_url'];
                $payInfoRes = $pay->getWxpayH5Param($orderid, $real_amount, $payBody, $data['ip'], $notify_url, $data['gameid']);
                
            } elseif (in_array($data['paytype'], ['ybzf_wxmp_h5', 'coin-ybzf_wxmp_h5', 'mix-ybzf_wxmp_h5'])) { // 易宝wxmp-h5
                $notify_url = Config::get('ybzf_pay')['notify_url'];
                $payInfoRes = $pay->getYbzfParam($data['paytype'], $orderid, $real_amount, $payBody, $notify_url);
            } elseif (in_array($data['paytype'], ['ldys_zfb_h5', 'coin-ldys_zfb_h5', 'mix-ldys_zfb_h5', 'ldys_wx_h5', 'coin-ldys_wx_h5', 'mix-ldys_wx_h5'])) { // 联动优势
                $payType = 'payZfbH5';
                if (in_array($data['paytype'], ['ldys_wx_h5', 'coin-ldys_wx_h5', 'mix-ldys_wx_h5'])) {
                    $payType = 'payWxmpH5V2';
                }
                $payData['is_coin'] = 0;
                $payInfoRes = $pay->getldysParam($payData, $payType);
            } elseif (in_array($data['paytype'], ['qzl_wx_h5', 'coin-qzl_wx_h5', 'mix-qzl_wx_h5'])) { // 趣智连支付-H5
                $payData['notify_url'] = Config::get('qzl_pay')['notify_url'];
                $payInfoRes = $pay->getqzlParam($payData, 'payWxH5');
            } elseif (in_array($data['paytype'], ['yyyb_wx_h5', 'coin-yyyb_wx_h5', 'mix-yyyb_wx_h5', 'yyyb_ali_h5', 'coin-yyyb_ali_h5', 'mix-yyyb_ali_h5'])) { // 优亿支付-H5
                $payData['notify_url'] = Config::get('yyyb_pay')['notify_url'];
                $payType = 'WX_H5';
                if (in_array($data['paytype'], ['yyyb_ali_h5', 'coin-yyyb_ali_h5', 'mix-yyyb_ali_h5'])) {
                    $payType = 'ALI_H5';
                }
                $payInfoRes = $pay->getYyYbPayParam($payData, $payType);
            } elseif (in_array($data['paytype'], ['xty_wx_h5', 'coin-xty_wx_h5', 'mix-xty_wx_h5', 'xty_ali_h5', 'coin-xty_ali_h5', 'mix-xty_ali_h5'])) { // 喜钛游支付-H5
                $payData['notify_url'] = Config::get('xty_pay')['notify']['notify_url'];
                $payType = 'payWxH5';
                if (in_array($data['paytype'], ['xty_ali_h5', 'coin-xty_ali_h5', 'mix-xty_ali_h5'])) {
                    $payType = 'payAliH5';
                }
                $payInfoRes = $pay->getXtyPayParam($payData, $payType);
            } elseif (in_array($data['paytype'], ['test', 'coin-test', 'mix-test'])) {
                return ['data' => '', 'code' => 0, 'msg' => "当前支付方式开发中..."];
            }
            
            // TODO： 新加支付方式时，这块判断要加上支付方式的返回判断。
            if ($payInfoRes && $data['paytype'] != 'coinpay') {
                if ($payInfoRes['error'] == true) {
                    trace("支付方式下单失败 - {$data['paytype']}: " . json_encode($payInfoRes), 'GamePayService@gamePay.$payInfoRes');
                    ddMsg("warning", '', ['支付方式-' . $data['paytype'], $payInfoRes['msg'], json_encode(['orderid' => $orderid, 'username' => $paycp_username, 'gameid' => $data['gameid']], JSON_UNESCAPED_UNICODE)]);
                    return ['data' => '', 'code' => 0, 'msg' => "发起下单失败！(" . $data['paytype'] . ")"];
                }

                $device = Request::instance()->header('device', 0, 'intval');
                if (in_array($data['paytype'], ['zfb', 'coin-zfb', 'mix-zfb', 'coin-mix-zfb'])) {
                    $result = ['alipay_app_param' => $payInfoRes['data'], 'wxpayh5_param' => ['mweb_url' => '']];
                } elseif (in_array($data['paytype'], [
                    'old-zfb-wap', 'coin-old-zfb-wap', 'mix-old-zfb-wap', 'coin-mix-old-zfb-wap',
                    'ldys_zfb_h5', 'coin-ldys_zfb_h5', 'mix-ldys_zfb_h5',
                    'yyyb_ali_h5', 'coin-yyyb_ali_h5', 'mix-yyyb_ali_h5',
                    'xty_ali_h5', 'coin-xty_ali_h5', 'mix-xty_ali_h5',
                ])) {
                    $result = ['alipay_param' => $payInfoRes['data']];
                    if($device == 4){
                        $result = ['pay_url' => $payInfoRes['data']['mweb_url']];
                    }
                } elseif (in_array($data['paytype'], [
                    'wxpay-h5', 'coin-wxpay-h5',
                    'wx-wap', 'coin-wx-wap',
                    'qzl_wx_h5', 'coin-qzl_wx_h5', 'mix-qzl_wx_h5',
                    'qmf_wxmp_h5', 'coin-qmf_wxmp_h5', 'mix-qmf_wxmp_h5',
                    'ybzf_wxmp_h5', 'coin-ybzf_wxmp_h5', 'mix-ybzf_wxmp_h5',
                    'ldys_wx_h5', 'coin-ldys_wx_h5', 'mix-ldys_wx_h5',
                    'yyyb_wx_h5', 'coin-yyyb_wx_h5', 'mix-yyyb_wx_h5',
                    'xty_wx_h5', 'coin-xty_wx_h5', 'mix-xty_wx_h5'
                ])) {
                    $result = ['wxpayh5_param' => $payInfoRes['data']];
                    if($device == 4){
                        $result = ['pay_url' => $payInfoRes['data']['mweb_url']];
                    }
                } else {
                    return ['data' => '', 'code' => 0, 'msg' => '支付渠道未配置返回类型！'];
                }
            }
            
            requestDuplicateCheckUnlock('pay_duplicate_' . $data['member_id'] . '_' . $data['gameid']);

        } catch (Exception $e) {
            // 回滚事务
            // Db::rollback();

            return ['data' => '', 'code' => 0, 'msg' => '订单生成失败.:' . $e->getMessage()];
        }

        $result['pay_status'] = $pay_status;    //支付状态
        $result['orderid'] = $orderid;
        $result['channel_id'] = $data['channel_id'];
        $result['paytype'] = $data['paytype'];
        return ['data' => $result, 'code' => 1, 'msg' => '订单生成成功'];
    }

    /**
     * 订单参数
     * @param $data
     * @param $arrPayAmount
     * @param $memberInfo
     * @param $gameInfo
     */
    public function orderData($data, $arrPayAmount, $memberInfo, $gameInfo, $pay_status)
    {

        if ($arrPayAmount['real_amount'] > 0) {
            $real_amount = $arrPayAmount['real_amount'];
        } else {
            $real_amount = 0;
        }
        $payData['orderid'] = $data['orderid'];
        $payData['discount'] = $data['discount'] ?: 1;
        $payData['amount'] = $data['amount'];
        $payData['real_amount'] = $real_amount;
        $payData['real_ptb'] = $arrPayAmount['real_ptb'] ?: 0;
        $payData['real_coin'] = $arrPayAmount['real_coin'] ?: 0;
        $payData['coupon_amount'] = $data['coupon_amount'];
//        $payData['real_ptb_amount'] = $arrPayAmount['real_ptb_amount'];
        $payData['userid'] = $data['member_id'];
        $payData['username'] = $memberInfo['username'];
        $payData['roleid'] = $data['roleid'];
        $payData['rolename'] = $data['rolename'];
        $payData['rolelevel'] = $data['rolelevel'];
        $payData['paytype'] = $data['paytype'];
        $payData['productname'] = $data['productname'];
        $payData['serverid'] = $data['serverid'];
        $payData['servername'] = $data['servername'];
        $payData['gameid'] = $data['gameid'];
        $payData['status'] = $pay_status;
        $payData['ip'] = $data['ip'];
        $payData['imeil'] = $data['imeil'];
        $payData['create_time'] = NOW_TIMESTAMP;
        $payData['channel_id'] = $data['channel_id'];
        $payData['recheck_status'] = $gameInfo['order_recheck'];
        $payData['attach'] = $data['attach'];
        $payData['coupon_member_id'] = $data['coupon_member_id'];
        $payData['pay_amount'] = $data['amount'];
        if($payData['discount'] > 0 && $payData['discount'] < 1){
            $payData['pay_amount'] = number_format($data['amount'] * $payData['discount'], 2);
        }

        return $payData;
    }


}
