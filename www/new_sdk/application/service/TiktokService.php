<?php

namespace app\service;

use app\common\library\Tiktok;
use think\Config;

class TiktokService
{

    public function push($action, $userid, $gameid, $channelId, $extend)
    {

        if (!($extend['Oaid'] || $extend['Idfa'] || $extend['Androidid'] || $extend['Imei']) && $action == 'tiktok.active_register') {
//            log_message('TiktokService:push'.json_encode($extend), 'info', LOG_PATH . '../tiktok/');
            return false;
        }

        if (!$game = model('TiktokPush')->alias('a')->cache('TiktokService:push:channel:' . $channelId, 300)
            ->join('cy_gameinfo b', 'a.game_id=b.game_id')
            ->where(['a.game_id' => $gameid, 'a.channel_id' => $channelId])->field('a.id,b.platform')->find()) {
//            log_message('TiktokService:push:game_id'.$gameid.'channel_id:'.$channelId.'不需要推送', 'info', LOG_PATH . '../tiktok/');
            return false;
        }

        $tiktok = new Tiktok();
        $extend = [
            'Oaid' => $extend['Oaid'],
            'Gameid' => $gameid,
            'Idfa' => $extend['Idfa'],
            'Androidid' => $extend['Androidid'],
            'Imei' => $extend['Imei'],
            'ChannelId' => $channelId,
            'Platform' => $game['platform'] == 1 ? 'ios' : 'android',
            'PayAmount' => $extend['PayAmount'] * 100,
            'Ip' => $extend['Ip'],
        ];
        return $tiktok->addTask($action, $userid, $extend);
    }
}
