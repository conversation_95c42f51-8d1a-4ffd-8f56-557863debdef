<?php

namespace app\service;


use app\common\logic\PayHandle;
use app\common\model\MemberCoinPay;
use think\Config;
use think\Db;
use think\Request;

class MemberCoinService
{
    protected $payRequestLimit = 5;        //重复下单的限制时间

    /**
     * 充值平台币
     */
    public function payCoin($data)
    {
        // 转换支付标识为指定的标识
        // if ($data['paytype'] == 'qmf_wxmp_h5') {
        //     $data['paytype'] = 'yyyb_wx_h5';
        // }
        // if ($data['paytype'] == 'pay_h5_001') { // 备用支付 - 001
        //     $data['paytype'] = 'test';
        // }
        // if ($data['paytype'] == 'pay_h5_002') { // 备用支付 - 001
        //     $data['paytype'] = 'test';
        // }
        
        $pay_scene = $data['paytype'];
        if(!in_array($data['paytype'], ['wx_h5', 'ali_h5']) && $data['device'] != 4) {
            // ## 随机为新的支付渠道 ##
            if (version_compare($data['version'], config('pay_handle_version'), '>')) {
                $payType = [
                    'wx_h5' => ['qzl_wx_h5', 'ldys_wx_h5', 'ybzf_wxmp_h5', 'yyyb_wx_h5', 'xty_wx_h5', 'qmf_wxmp_h5'],
                    'ali_h5' => ['old-zfb-wap', 'ldys_zfb_h5', 'xty_ali_h5', 'yyyb_ali_h5'],
                    'ali_app' => ['zfb'],
                ];
            } else {
                $payType = [
                    'wx_h5' => ['qzl_wx_h5', 'ldys_wx_h5', 'ybzf_wxmp_h5', 'yyyb_wx_h5', 'xty_wx_h5', 'qmf_wxmp_h5'],
                    'ali_h5' => [],
                    'ali_app' => [],
                ];
            }
    
            $pay_scene = '';
            if (in_array($data['paytype'], $payType['wx_h5'])) {
                $pay_scene = 'wx_h5';
            } else if (in_array($data['paytype'], $payType['ali_h5'])) {
                $pay_scene = 'ali_h5';
            } else if (in_array($data['paytype'], $payType['ali_app'])) {
                $pay_scene = 'ali_app';
            } else {
                // 不能这样判断，因为会有ptb
                // return ['code' => -111, 'msg' => '支付方式有误！', 'data' => []];
            }
        }

        // 新版本+支持的支付方式才会走获取随机支付，没有则走默认配置的
        if ((version_compare($data['version'], config('pay_handle_version'), '>') && $pay_scene) || (isInArrType($payType, $data['paytype']) && $pay_scene)) {
            $newPayType = (new PayHandle())->getPayChannel($pay_scene, $data['gameid'], $data['version']);
            if (empty($newPayType['data']['paytype'])) {
                return ['data' => '', 'code' => -110, 'msg' => '支付配置有误！当前游戏或者版本未开启支付！'];
            }
            $data['paytype'] = $newPayType['data']['paytype'];
        } else {
            $setPayType = model('common/Setting')->where(['name' => 'OLD_PAY_WX_TYPE'])->value('value');
            if ($setPayType) {
                if (strstr($data['paytype'], 'qmf_wxmp_h5') !== false)
                {
                    if ($data['paytype'] == 'qmf_wxmp_h5') {
                        $data['paytype'] = $setPayType;
                    }
                }
            }
        }
        // ## 随机为新的支付渠道 - END ##

        $payData['userid'] = $data['member_id'];
        $payData['orderid'] = makeOrderid('COIN');
        $payData['amount'] = $data['amount'];
        if (in_array($data['paytype'], ['airwallexh5', 'airwallex'])) {
            $paytypeInfo = model('common/PayType')->where('paytype', $data['paytype'])->find();
            $payData['real_amount'] = priceFormat($data['amount'] * (1 + $paytypeInfo['commission']));
        } else {
            $payData['real_amount'] = $data['amount'];
        }

        $payData['paytype'] = $data['paytype'];
        $payData['ip'] = $data['ip'];
        $payData['create_time'] = NOW_TIMESTAMP;
        $payData['place_user_id'] = $data['place_user_id'];
        // 启动事务
        Db::startTrans();

        try {
            //指定时间内，禁止重复下单
            $cacheKey = 'pay_duplicate_' . $payData['userid'] . '_' . 'coin';
            if (!requestDuplicateCheck($cacheKey, $this->payRequestLimit)) {
                return ['code' => 0, 'msg' => '充值请求过多，请于' . $this->payRequestLimit . 's以后，再次进行充值操作'];
            }
            if (!$id = (new MemberCoinPay())->insertGetId($payData)) {
                // 回滚事务
                Db::rollback();

                return ['code' => 0, 'msg' => '订单生成失败1'];
            }
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            return ['code' => 0, 'msg' => '订单生成失败' . $e->getMessage()];
        }
        
        /**
         * 微信支付:
         *  可用：'qzl_wx_h5', 'ybzf_wxmp_h5', 'yyyb_wx_h5', 'xty_wx_h5'
         *  不可用：'ldys_wx_h5'
         *
         * 支付宝：
         *  可用：'xty_ali_h5', 'yyyb_ali_h5'
         *  不可用：'old-zfb-wap', 'ldys_zfb_h5'
         */
        try {
            //支付方式是支付宝扫码
            $payBody = '平台币';
            $pay = new PayService();
            $payData['productname'] = $payBody;
            $payData['pay_amount'] = $payData['amount'];
            $payData['gameid'] = $data['gameid'];

            $payInfoRes = [];
            if ($data['paytype'] == 'zfb') { // 支付宝-app 扫码（官方支付宝快捷支付）
                $notify_url = Config::get('ali_pay_config')['notify_coin_url'];
                $payInfoRes = $pay->getAlipayAopParam($payData['orderid'], $payData['amount'], $payBody, '', $notify_url);

            } elseif (in_array($data['paytype'], ['zfb-wap', 'old-zfb-wap'])) {  //支付宝-h5（官方支付宝h5支付）带referer
                $notify_url = Config::get('ali_pay_config')['notify_coin_url'];
                $payInfoRes = $pay->getAlipayWapPostAopParam($payData['orderid'], $payData['amount'], $payBody, '', $notify_url);

            } elseif (in_array($data['paytype'], ['wxpay-h5', 'wx-wap'])) {  // 微信-h5
                if ($payData['amount'] > 3000) {
                    return ['code' => 0, 'msg' => '微信单笔支付限额3000元,您可选择其它支付'];
                }
                $notify_url = Config::get('wxpay-h5')['notify_coin_url'];
                $payInfoRes = $pay->getWxpayH5Param($payData['orderid'], $payData['amount'], $payBody, $payData['ip'], $notify_url, $data['gameid']);

            } elseif ($data['paytype'] == 'ybzf_wxmp_h5') { // 易宝-wxmp_h5
                $notify_url = Config::get('ybzf_pay')['notify_coin_url'];
                $payInfoRes = $pay->getYbzfParam($data['paytype'], $payData['orderid'], $payData['amount'], $payBody, $notify_url);

            } elseif (in_array($data['paytype'], ['ldys_zfb', 'ldys_wx_h5'])) { // 联动优势-zfb_H5
                $payType = 'payZfbH5';
                if (in_array($data['paytype'], ['ldys_wx_h5'])) {
                    $payType = 'payWxmpH5V2';
                }
                $payData['is_coin'] = 1;
                $payInfoRes = $pay->getldysParam($payData, $payType);
            } elseif ($data['paytype'] == 'qmf_wxmp_h5') { // 趣智连-wxmp_h5
                $payData['notify_url'] = Config::get('qzl_pay')['notify_coin_url'];
                $payInfoRes = $pay->getqzlParam($payData, 'payWxH5');
            } elseif (in_array($data['paytype'], ['yyyb_wx_h5', 'yyyb_ali_h5'])) { // 优亿支付-H5
                $payData['notify_url'] = Config::get('yyyb_pay')['notify_coin_url'];
                $payType = 'WX_H5';
                if (in_array($data['paytype'], ['yyyb_ali_h5'])) {
                    $payType = 'ALI_H5';
                }
                $payInfoRes = $pay->getYyYbPayParam($payData, $payType);
            } elseif (in_array($data['paytype'], ['xty_wx_h5', 'xty_ali_h5'])) { // 喜钛游支付-H5
                $payData['notify_url'] = Config::get('xty_pay')['notify']['notify_coin_url'];
                $payType = 'payWxH5';
                if (in_array($data['paytype'], ['xty_ali_h5'])) {
                    $payType = 'payAliH5';
                }
                $payInfoRes = $pay->getXtyPayParam($payData, $payType);
            } elseif ($data['paytype'] == 'test') {
                return ['data' => '', 'code' => 0, 'msg' => '当前支付方式开发中...'];
            }

            if($payInfoRes && $data['paytype'] != 'coinpay'){
                if($payInfoRes['error'] == true){
                    trace("充值方式下单失败 - {$data['paytype']}: " . json_encode($payInfoRes), 'MemberCoinService@payCoin.$payInfoRes');
                    ddMsg("warning", '', ['充值方式-'.$data['paytype'], $payInfoRes['msg'], json_encode(['orderid'=>$payData['orderid'], 'username' => $data['username'], 'gameid'=>$data['gameid']], JSON_UNESCAPED_UNICODE)]);
                    return ['data' => '', 'code' => 0, 'msg' => $payInfoRes['msg']];
                }

                // TODO： 新加支付方式时，这块判断要加上
                if (in_array($data['paytype'], ['zfb'])) {
                    $result = ['alipay_app_param' => $payInfoRes['data'], 'wxpayh5_param' => ['mweb_url' => '']];
                } elseif (in_array($data['paytype'], ['old-zfb-wap', 'xty_ali_h5', 'ldys_zfb', 'yyyb_ali_h5'])) {
                    $result = ['alipay_param' => $payInfoRes['data']];
                    if($data['device'] == 4){
                        $result = ['pay_url' => $payInfoRes['data']['mweb_url']];
                    }
                } elseif (in_array($data['paytype'], ['wxpay-h5', 'ybzf_wxmp_h5', 'wx-wap', 'qzl_wx_h5', 'qzl_wx_h5', 'ldys_wx_h5', 'yyyb_wx_h5', 'ybzf_wxmp_h5', 'qmf_wxmp_h5', 'xty_wx_h5'])) {
                    $result = ['wxpayh5_param' => $payInfoRes['data']];
                    if($data['device'] == 4){
                        $result = ['pay_url' => $payInfoRes['data']['mweb_url']];
                    }
                } else {
                    return ['data' => '', 'code' => 0, 'msg' => $data['paytype'].' - 订单支付类型配置有误！！'];
                }
            }

            $result['orderid'] = $payData['orderid'];
            $result['paytype'] = $data['paytype'];
            return ['code' => 1, 'msg' => '订单生成成功', 'data' => $result];
        } catch (\Exception $e) {
            // 回滚事务
//            Db::rollback();

            return ['code' => 0, 'msg' => '订单生成失败：' . $e->getMessage()];
        }
    }
}
