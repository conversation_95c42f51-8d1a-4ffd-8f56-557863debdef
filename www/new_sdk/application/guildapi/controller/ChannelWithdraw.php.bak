<?php

/**
 * 提现控制器
 * 
 */

namespace app\guildapi\controller;

use app\guildapi\controller\Guild;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;
use app\common\library\MakeReport;

class ChannelWithdraw extends Guild  {
    
    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;

        $this->redis    = \think\Cache::store('default')->handler();
    }
  
     /**
     * 提现列表
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= $this->input('list_rows',10);
        $page					= $this->input('page',1);
        $status					= $this->input('status','0','intval');   
        $begin_time				= $this->input('begin_time','','trim');
        $end_time				= $this->input('end_time','','trim');
        $download				= $this->input('download',0,'intval');

        $condition					= [];
		$condition['channel_id'] = $this->_channelId;
		if(input('status') <> ''){
			$condition['status']	= intval($status);
		}

	    //申请开始时间和结束时间不为空时
        if ($begin_time != '' && $end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($begin_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($begin_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $condition['create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }
	//	var_dump($condition);

		if($download){
			$sql = model('ChannelWithdraw')
							->where($condition)
							->field("id,orderid,channel_id,channel_name,withdraw_amt,remark,create_time,status")
							->order("id desc")
							->fetchSql(true)
							->select();
		//	echo $sql;
			if ((new MakeReport())->addTask('guild.ChannelWithdrawIndex', $sql, 'cps'.session('guild_info')['id'])){
				$this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
			}
			else{
				$this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
			}
		}

        $withdrawList = model('ChannelWithdraw')
						->where($condition)
						->field("id,orderid,channel_id,channel_name,withdraw_amt,remark,create_time,status")
						->order("id desc")
						->paginate(['list_rows'=>$list_rows,'page'=>$page])
						->toArray();
	//	echo model('ChannelWithdraw')->getLastSql()."-----lastsql------<br>";

		$this->jsonResult($withdrawList, 20000, '获取列表成功');
    }
    /**
     * 提现信息展示
     */
    public function withdraw()
    {
		if ($this->request->isPost()) {
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您无权限进行提现相关操作');
			}
			else{
				$channelInfo   = model("Channel")->field("id,name,level,parent_id,js_amount")->where(['id'=>$this->_channelId])->find();
				if(!$channelInfo){
					$this->jsonResult('', 0, '账号异常,请重新登录后再试');
				}

				$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
				if(!$adminInfo){
					$this->jsonResult('', 0, '账号异常,请重新登录后再试');
				}
				else if(!$adminInfo['pay_password'] || !$adminInfo['secondary_password']){
					$this->jsonResult('', 0, '请设置交易密码和二级密码后才能进行提现');
				}
				$relChannelInfo   = model("ChannelInfo")->where(['channel_id'=>$this->_channelId])->find();
				if(!$relChannelInfo){
					$this->jsonResult('', 0, '请先补充完提现相关信息后再进行提现');
				}

				$channelInfoApply   = model("ChannelInfo")->field("id,channel_id,real_name,apply_status,type")->where(['channel_id'=>$this->_channelId,'apply_status'=>1])->find();
				if(empty($channelInfoApply)){
					$this->jsonResult('', 0, '您的身份尚未认证通过,不能进行提现');
				}

				$this->jsonResult($channelInfo, 20000, '获取提现信息成功');
			}
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
    /**
     * 提现处理
     */
    public function doWithdraw()
    {
		if ($this->request->isPost()) {
			$amount			= $this->input('amount',0,'floatval');			//提现金额
			$pay_password	= $this->input('pay_password','','trim');            //交易密码
			$remark			= $this->input('remark','','trim');                   //备注
		
			$orderid    = 'W'.makeOrderid();
			
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您无权限进行提现操作');
			}
			//外放的无法结算
			if (isset(session('guild_info')['roles'][0]) && session('guild_info')['roles'][0] == 4) {
				$this->jsonResult('', 0, '您无权限使用该功能');
			}
			if(!$amount){
				$this->jsonResult('', 0, '请填写提现金额！');
			}
			else if(abs(intval($amount*100)/100-$amount) > 0.01){
				$this->jsonResult('', 0, '请正确填写提现金额！');
			}
			else if($amount < 100){
				$this->jsonResult('', 0, '提现金额不能小于100元！');
			}
			$channelInfo   = model("Channel")->field("id,name,level,parent_id,js_amount")->where(['id'=>$this->_channelId])->find();
			if(!$channelInfo){
				$this->jsonResult('', 0, '账号异常,请重新登录后再试');
			}
			else if($channelInfo['js_amount']<$amount){
				$this->jsonResult('', 0, '您的结算余额不足');
			}
			$relChannelInfo   = model("ChannelInfo")->where(['channel_id'=>$this->_channelId])->find();
			if(!$relChannelInfo){
				$this->jsonResult('', 0, '请先补充完提现相关信息后再进行提现');
			}

			$channelInfoApply   = model("ChannelInfo")->field("id,channel_id,real_name,apply_status,type,person_id,zfb_account,bank_ban_mobile,bank_number,bank_province_city,bank_name,bank_open_name,bank_open_number")->where(['channel_id'=>$this->_channelId,'apply_status'=>1])->find();
			if(empty($channelInfoApply)){
				$this->jsonResult('', 0, '您的身份尚未认证通过,不能进行提现');
			}

			if(!(date("w")==1 && (date('Hi')>='0200' && date('Hi')<'2359') || date("w")==2 && (date('Hi')>='0200' && date('Hi')<'1200'))){
				$this->jsonResult('', 0, '不在提现时间内，请在每周一02:00~23:59 或 每周二02:00~12:00 内提交！');
			}

			//指定时间内，禁止重复下单
			if(!requestDuplicateCheck('channel_withdraw_duplicate_'.$this->_channelId,$this->payRequestLimit)){
				$this->jsonResult('', 0, '提现请求过多，请于'.$this->payRequestLimit.'s以后，再次进行提现操作');
			}

			$adminInfo   = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id'=>session('guild_info')['id'],'channel_id'=>$this->_channelId])->find();
			if(!$adminInfo){
				$this->jsonResult('', 0, '账号异常,请重新登录后再试');
			}
			else if(!$adminInfo['pay_password'] || !$adminInfo['secondary_password']){
				$this->jsonResult('', 0, '请设置交易密码和二级密码后才能进行提现');
			}
			else if($adminInfo['pay_password'] != mg_password($pay_password)){
				 $this->jsonResult('', 0, '交易密码错误!');
			}

			$withdrawData = array();
			$withdrawData['orderid']		= $orderid;
			$withdrawData['channel_id']     = $this->_channelId;
			$withdrawData['channel_name']   = $this->_channelName;
			$withdrawData['withdraw_amt']	= $amount;
			$withdrawData['remark']			= $remark;
			$withdrawData['status']			= 0;
			$withdrawData['create_time']	= time();

			$withdrawData['real_name']		= $channelInfoApply['real_name'];
			$withdrawData['person_id']		= $channelInfoApply['person_id'];
			$withdrawData['zfb_account']	= $channelInfoApply['zfb_account'];
			$withdrawData['bank_ban_mobile']= $channelInfoApply['bank_ban_mobile'];
			$withdrawData['bank_number']	= $channelInfoApply['bank_number'];
			$withdrawData['bank_province_city']	= $channelInfoApply['bank_province_city'];
			$withdrawData['bank_name']		= $channelInfoApply['bank_name'];
			$withdrawData['bank_open_name']	= $channelInfoApply['bank_open_name'];
			$withdrawData['bank_open_number']	= $channelInfoApply['bank_open_number'];

			// 启动事务
			Db::startTrans();
			
			try{
				$result = '';   //返回值
				
				$insertWithdrawId = model("ChannelWithdraw")->insert($withdrawData);
				if(!$insertWithdrawId){
					throw new Exception("创建提现订单失败! ");
				}

				$detData = array();
                $detData['channel_id']      = $this->_channelId;
                $detData['channel_name']    = $this->_channelName;
                $detData['change_amount']	= -$amount;
                $detData['account_type']	= 2;   //结算账户
                $detData['type']			= 9;   //提现
                $detData['out_orderid']		= $orderid;
                $detData['create_time']     = NOW_TIMESTAMP;
                $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
				if ( !$insertDetId) {
					throw new Exception("添加账户变动明细失败");
				}
				$updData = array();
				$updData['js_amount'] = Db::raw("js_amount-".$amount);
				$updData['update_time'] = time();
				$updResult = model('Channel')->where(['id'=>$this->_channelId,'js_amount'=>array('egt',$amount)])->update($updData);
				if (!$updResult) {
					throw new Exception("账户金额变动失败");
				}
				
				$this->redis->del('channel_withdraw_duplicate_'.$this->_channelId);
				// 提交事务
				Db::commit();
				
			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();
				
				$this->jsonResult('', 0, '提现订单生成失败'.$e->getMessage());
			}
			
			$result['orderid']      = $orderid;
			
			$template = '有新的提现申请：会长"'.$this->_channelName.'" 发起了提现申请，订单号：'.$orderid.' 。请及时安排处理，时间：'.date('Y-m-d H:i:s');
			$ddurl = OPERATE_DINGDING_URL;
			curlDD($template, $ddurl,true);

			$this->jsonResult($result, 20000, '提现订单生成成功');
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
}
