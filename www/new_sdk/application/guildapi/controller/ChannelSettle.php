<?php

/**
 * 渠道结算接口控制器
 *
 */

namespace app\guildapi\controller;

use app\common\library\MakeReportGo;
use app\guildapi\controller\Guild;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Env;
use think\Exception;
use app\common\library\MakeReport;

class ChannelSettle extends Guild
{

    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();

        $this->nowTime = NOW_TIMESTAMP;

        $this->redis = \think\Cache::store('default')->handler();
    }

    /**
     * 我的结算列表
     * @return [type] [description]
     */
    public function index()
    {
        //判断当前账号是否有此功能
        if (!in_array($this->_channelLevel, [1])) {
            $this->jsonResult('', 0, '您无权限使用该功能');
        }

        $list_rows = $this->input('list_rows', 10);
        $page = $this->input('page', 1);
        $orderid = $this->input('orderid', '', 'trim');    //收入账号
        $begin_time = $this->input('begin_time', '', 'trim');
        $end_time = $this->input('end_time', '', 'trim');
        $download = $this->input('download', 0, 'intval');

        $condition = [];
        $condition['channel_id'] = $this->_channelId;
        $condition['settle_type'] = 1;    //专服
        if ($orderid) {
            $condition['orderid'] = $orderid;
        }
        //申请开始时间和结束时间不为空时
        if ($begin_time != '' && $end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($begin_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($begin_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $condition['create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }

        //	var_dump($condition);

        if ($download) {
            $sql = model('ChannelDivideSettle')
                ->where($condition)
                ->field("id,orderid,channel_id,total_amount,total_divide_amt,remark,create_time,first_audit_status,second_audit_status")
                ->order("id desc")
                ->fetchSql(true)
                ->select();
            //	echo $sql;
//            if ((new MakeReport())->addTask('guild.ChannelSettleIndex', $sql, 'cps' . session('guild_info')['id'])) {
            if ((new MakeReportGo())->addTask('guild.ChannelSettleIndex', $sql, 'cps' . session('guild_info')['id'])) {
                $this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            } else {
                $this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }

        $settleList = model('ChannelDivideSettle')
            ->where($condition)
            ->field("id,orderid,channel_id,total_amount,total_divide_amt,remark,create_time,first_audit_status,second_audit_status")
            ->order("id desc")
            ->paginate(['list_rows' => $list_rows, 'page' => $page])
            ->toArray();

        foreach ($settleList["data"] as &$val) {
            if ($val['first_audit_status'] == 1 && $val['second_audit_status'] == 1) {  //审核通过
                $val['status'] = 1;
            } else if ($val['first_audit_status'] == 2 || $val['second_audit_status'] == 2) {   //审核不通过
                $val['status'] = 2;
            } else {     //审核中
                $val['status'] = 0;
            }
            unset($val['first_audit_status']);
            unset($val['second_audit_status']);
        }

        $this->jsonResult($settleList, 20000, '获取列表成功');
    }

    /**
     * 结算明细数据
     */
    public function getSettleDetail()
    {
        if ($this->request->isPost()) {
            //判断当前账号是否有结算权限
            if (!in_array($this->_channelLevel, [1])) {
                $this->jsonResult('', 0, '您无权限进行结算相关操作');
            } else {
                $settleId = input('settleId', 0, 'intval');
                $download = input('download', 0, 'intval');

                if (!$settleId) {
                    $this->jsonResult('', 0, '请选择结算单ID');
                }
                $where = array();
                $where['channel_id'] = $this->_channelId;
                $where['id'] = $settleId;
                $settleInfo = model("ChannelDivideSettle")->where($where)->field("id,orderid,total_amount,total_divide_amt,coupon_amount_amt,actual_amount")->find();
                if (!$settleInfo) {
                    $this->jsonResult('', 0, '该结算单不存在或您不能查看');
                }

                $list_rows = input('list_rows', 10, 'intval');
                $page = input('page', 1, 'intval');
                $where = array();
                $where['p.settle_id'] = $settleId;
                //	var_dump($where);
                $settleDetInfo = model("ChannelDivideSettleDet")->alias('p')
                    ->join('cy_game g', 'p.game_id = g.id', 'left')
                    ->field("p.game_id AS game_id,g.name as game_name,p.pay_amt,p.ratio,p.divide_amt,p.coupon_amount_amt,p.actual_amount")
                    ->where($where)
                    ->select();

                $where = array();
                $where['p.settle_id'] = $settleId;

                if ($download) {
                    $sql = model("ChannelDivideSettleDetPay")->alias('p')
                        ->join('cy_game g', 'p.game_id = g.id', 'left')
                        ->field("p.orderid,p.username,p.create_time,p.game_id AS game_id,g.name as game_name,p.amount AS amount,p.divide_amt,p.ratio,p.serverid,p.servername,p.rolename,p.coupon_amount,p.actual_amount,p.real_coin")
                        ->where($where)
                        ->order("p.id desc")
                        ->fetchSql(true)
                        ->select();
                    //	echo $sql;
//                    if ((new MakeReport())->addTask('guild.ChannelSettleDetail', $sql, 'cps' . session('guild_info')['id'])) {
                    if ((new MakeReportGo())->addTask('guild.ChannelSettleDetail', $sql, 'cps' . session('guild_info')['id'])) {
                        $this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
                    } else {
                        $this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
                    }
                }

                $detPaylist = model("ChannelDivideSettleDetPay")->alias('p')
                    ->join('cy_game g', 'p.game_id = g.id', 'left')
                    ->field("p.orderid,p.username,p.create_time,p.game_id AS game_id,g.name as game_name,p.amount AS amount,p.divide_amt,p.ratio,p.serverid,p.servername,p.rolename,p.coupon_amount,p.actual_amount,p.real_coin")
                    ->where($where)
                    ->order("p.id desc")
                    ->paginate(['list_rows' => $list_rows, 'page' => $page])
                    ->toArray();
                //	var_dump($detPaylist);
                $retData['settle'] = $settleInfo;
                $retData['settleDet'] = $settleDetInfo;
                $retData['detPaylist'] = $detPaylist;

                $this->jsonResult($retData, 20000, '获取结算详情成功');
            }
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
            exit;
        }
    }

    /**
     * 可结算数据
     */
    public function settle()
    {
        if ($this->request->isPost()) {
            //判断当前账号是否有结算权限
            if (!in_array($this->_channelLevel, [1])) {
                $this->jsonResult('', 0, '您无权限进行结算相关操作');
            } else {
                $channelInfoApply = model("ChannelInfo")->field("id,channel_id,real_name,apply_status,type")->where(['channel_id' => $this->_channelId, 'apply_status' => 1])->find();
                if (empty($channelInfoApply)) {
                    $this->jsonResult('', 0, '您的身份尚未认证通过,不能进行结算1');
                }

                $channelInfo = model("Channel")->field("id,name,level,cps_settle_period,mcps_settle_period,status")->where(['id' => $this->_channelId])->find();
                if (empty($channelInfo)) {
                    $this->jsonResult('', 0, '账号异常，请重新登录后再试');
                }
                if ($channelInfo['cps_settle_period'] == 2) {
                    $this->lastTime = strtotime(date('Y-m-d')) - 1;
                } else {
                    $this->lastTime = strtotime(date('Y-m-d', (time() - ((date('w', time()) == 0 ? 7 : date('w', time())) - 1) * 24 * 3600))) - 1;
                }

                $list_rows = input('list_rows', 10);
                $page = input('page', 1);
                $where = array();
                $where['p.status'] = 1;
                $where['p.create_time'] = array('elt', $this->lastTime);
                //所有下级渠道(包括自己)
                $channelIds = get_child_channel_arr($this->_channelId);
                array_push($channelIds, $this->_channelId);
                $where['p.channel_id'] = ['in', $channelIds];
                $where['p.settle_id'] = array('eq', 0);
                $where['g.id'] = ['in', model('game')->getTakingGame($this->_channelId)];
                //	var_dump($where);
                $GameChannelData = Db::table('cy_pay')->alias('p')
                    ->join('cy_game g', 'p.gameid = g.id and g.game_kind=1', 'inner')
                    ->join('nw_game_channel_divide d', 'p.gameid = d.game_id and d.channel_id=' . $this->_channelId, 'left')
                    ->field("p.gameid AS game_id,g.name as game_name,g.channel_split_ratio,count(*) as pay_cnt,SUM(p.amount) AS pay_amt,d.id as ratio_id,d.ratio,SUM(p.real_amount) AS pay_real_amt,SUM(p.real_ptb) AS pay_real_ptb,SUM(p.coupon_amount) as coupon_amount_amt,SUM(p.real_coin) as pay_real_coin,SUM(p.amount-p.coupon_amount) as actual_amount")
                    ->where($where)
                    ->group('p.gameid')
                    ->select();

                $retData = array();
                $total_amt = $divide_amt = $total_cnt = $total_coupon_amount_amt = $actual_amount = 0;
                foreach ($GameChannelData as &$det) {
                    if (intval($det['ratio_id'])) {
                        $ratio_id = $det['ratio_id'];
                        $ratio = floatval($det['ratio']);
                    } else {
                        $ratio_id = 0;
                        $ratio = floatval($det['channel_split_ratio']);
                    }
                    //if(!$ratio){
                    //	$this->jsonResult('', 0, '您尚有游戏未配置分成比例,请先联系平台设置');
                    //}
                    //else{
                    $det['ratio_id'] = $ratio_id;
                    $det['ratio'] = $ratio;
                    $det['amount_amt'] = floatval($det['pay_amt']-$det['coupon_amount_amt']);
                    $total_cnt += floatval($det['pay_cnt']);
                    $total_amt += floatval($det['pay_amt']);
                    $total_coupon_amount_amt += floatval($det['coupon_amount_amt']);
                    $divide_amt += floatval(intval(($det['pay_amt']-$det['coupon_amount_amt']) * $det['ratio']) / 100);
                    $det['divide_amt'] = floatval(intval(($det['pay_amt']-$det['coupon_amount_amt']) * $det['ratio']) / 100);

                    $actual_amount += floatval($det['pay_amt']-$det['coupon_amount_amt']);
                    $det['actual_amount'] = floatval($det['pay_amt']-$det['coupon_amount_amt']);
                    //}
                }
                $settle = array();
                $settle['total_amount'] = $total_amt;
                $settle['divide_amount'] = $divide_amt;
                $settle['total_cnt'] = $total_cnt;
                $settle['total_coupon_amount_amt'] = $total_coupon_amount_amt;
                $detPaylist = Db::table('cy_pay')->alias('p')
                    ->join('cy_game g', 'p.gameid = g.id and g.game_kind=1', 'inner')
                    ->join('nw_game_channel_divide d', 'p.gameid = d.game_id and d.channel_id=' . $this->_channelId, 'left')
                    ->field("p.orderid AS orderid,p.gameid AS game_id,g.name as game_name,p.amount AS amount,round((p.amount-p.coupon_amount)*(case when d.id then d.ratio else g.channel_split_ratio end)/100,2) AS divide_amt,case when d.id then d.id else '0' end as ratio_id,case when d.id then d.ratio else g.channel_split_ratio end as ratio,p.real_amount AS real_amount,p.real_ptb AS real_ptb,p.serverid,p.servername,p.rolename,p.userid,p.username,p.create_time,p.coupon_amount,round(p.amount-p.coupon_amount,2) as actual_amount,p.real_coin")
                    ->where($where)
                    ->order("p.id desc")
                    ->paginate(['list_rows' => $list_rows, 'page' => $page])
                    ->toArray();
                //	var_dump($detPaylist);
                $retData['settle'] = $settle;
                $retData['settleDet'] = $GameChannelData;
                $retData['detPaylist'] = $detPaylist;

                $this->jsonResult($retData, 20000, '获取结算列表成功');
            }
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
            exit;
        }
    }

    /**
     * 结算处理
     */
    public function doSettle()
    {
        if ($this->request->isPost()) {
            $total_amount = $this->input('total_amount', 0, 'floatval');            //流水
            $divide_amount = $this->input('divide_amount', 0, 'floatval');           //分成金额
            $total_cnt = $this->input('total_cnt', 0, 'intval');             //结算订单笔数
            $remark = $this->input('remark', '', 'trim');                   //备注

            if (!$total_amount || !$divide_amount || !$total_cnt) {
                $this->jsonResult('', 0, '没有可结算单的订单');
            } else if ($divide_amount < 100) {
                $this->jsonResult('', 0, '结算金额不能小于100元！');
            }

            $channelInfoApply = model("ChannelInfo")->field("id,channel_id,real_name,apply_status,type")->where(['channel_id' => $this->_channelId, 'apply_status' => 1])->find();
            if (empty($channelInfoApply)) {
                $this->jsonResult('', 0, '您的身份尚未认证通过,不能进行结算');
            }
            //外放的无法结算
            if (isset(session('guild_info')['roles'][0]) && session('guild_info')['roles'][0] == 4) {
                $this->jsonResult('', 0, '您无权限使用该功能');
            }
            $orderid = 'ZFS' . makeOrderid();

            $channelInfo = model("Channel")->field("id,name,level,cps_settle_period,mcps_settle_period,status")->where(['id' => $this->_channelId])->find();
            if (empty($channelInfo)) {
                $this->jsonResult('', 0, '账号异常，请重新登录后再试');
            }
            if ($channelInfo['cps_settle_period'] == 2) {   //日结
                $this->lastTime = strtotime(date('Y-m-d')) - 1;
                if (!(date('Hi') >= '0200' && date('Hi') < '2359')) {
                    $this->jsonResult('', 0, '不在结算时间内，请在每日02:00 ~ 23:59分内提交结算申请！');
                }
            } else {  //周结
                $this->lastTime = strtotime(date('Y-m-d', (time() - ((date('w', time()) == 0 ? 7 : date('w', time())) - 1) * 24 * 3600))) - 1;
                //if(date("w")<>1 || !(date('Hi') >= '0200' && date('Hi') < '2359')){
                //$this->jsonResult('', 0, '不在结算时间内，请在每周一02:00~23:59 内提交结算申请！');
                //}
                if (!(date('Hi') >= '0000' && date('Hi') < '2359')) {
                    $this->jsonResult('', 0, '不在结算时间内，请在下周每日00:00~23:59 内提交结算申请！');
                }
            }

            $where = array();
            $where['p.status'] = 1;
            $where['p.create_time'] = array('elt', $this->lastTime);
            //所有下级渠道(包括自己)
            $channelIds = get_child_channel_arr($this->_channelId);
            array_push($channelIds, $this->_channelId);
            $where['p.channel_id'] = ['in', $channelIds];
            $where['p.settle_id'] = array('eq', 0);
            $where['g.id'] = ['in', model('game')->getTakingGame($this->_channelId)];
            //	var_dump($where);
            $GameChannelData = Db::table('cy_pay')->alias('p')
                ->join('cy_game g', 'p.gameid = g.id and g.game_kind=1', 'inner')
                ->join('nw_game_channel_divide d', 'p.gameid = d.game_id and d.channel_id=' . $this->_channelId, 'left')
                ->field("p.gameid AS game_id,g.name as game_name,g.channel_split_ratio,min(p.create_time) as begin_time,max(p.create_time) as end_time,count(*) as pay_cnt,SUM(p.amount) AS pay_amt,d.id as ratio_id,d.ratio,SUM(p.real_amount) AS pay_real_amt,SUM(p.real_ptb) AS pay_real_ptb,SUM(p.coupon_amount) as coupon_amount_amt,SUM(p.real_coin) as pay_real_coin,SUM(p.amount-p.coupon_amount) as actual_amount")
                ->where($where)
                ->group('p.gameid')
                ->select();
            $retData = array();
            //	echo Db::table('cy_pay')->getLastSql()."----sql-----------<br>";
            $totalAmount = $divideAmount = $totalCnt =$total_coupon_amount_amt = $actual_amount = 0;
            $beginTime = time();
            $endTime = 0;
            foreach ($GameChannelData as &$det) {
                if (intval($det['ratio_id'])) {
                    $ratio_id = $det['ratio_id'];
                    $ratio = floatval($det['ratio']);
                } else {
                    $ratio_id = 0;
                    $ratio = floatval($det['channel_split_ratio']);
                }
                //if(!$ratio){
                //	$this->jsonResult('', 0, '您尚有游戏未配置分成比例,请先联系平台设置');
                //}
                //else{
                $det['ratio_id'] = $ratio_id;
                $det['ratio'] = $ratio;
                $totalCnt += floatval($det['pay_cnt']);
                $totalAmount += floatval($det['pay_amt']);
                $total_coupon_amount_amt += floatval($det['coupon_amount_amt']);
                $divideAmount += floatval(intval(($det['pay_amt']-$det['coupon_amount_amt']) * $det['ratio']) / 100);
                $det['divide_amt'] = floatval(intval(($det['pay_amt']-$det['coupon_amount_amt']) * $det['ratio']) / 100);
                $actual_amount += floatval($det['pay_amt']-$det['coupon_amount_amt']);
                $det['actual_amount'] = floatval($det['pay_amt']-$det['coupon_amount_amt']);
                if ($beginTime > $det['begin_time']) {
                    $beginTime = $det['begin_time'];
                }
                if ($endTime < $det['end_time']) {
                    $endTime = $det['end_time'];
                }
                //}
            }
            reset($GameChannelData);

            if (!$totalAmount || !$divideAmount || !$totalCnt) {
                $this->jsonResult('', 0, '您已没有可结算的订单');
            }
            /*
            if($totalAmount<>$total_amount || $totalCnt<>$total_cnt || $divideAmount<>$divide_amount){
                $this->jsonResult('', 0, '结算数据与后台计算数据不一致,请重新核对后再提交');
            }
            */
            if (abs($totalAmount - $total_amount) > 0.01 || $totalCnt <> $total_cnt || abs($divideAmount - $divide_amount) > 0.01) {
                $this->jsonResult('', 0, '结算数据与后台计算数据不一致,请重新核对后再提交');
            }

            //指定时间内，禁止重复下单
            if (!requestDuplicateCheck('channel_settle_duplicate_' . $this->_channelId, $this->payRequestLimit)) {
                $this->jsonResult('', 0, '结算请求过多，请于' . $this->payRequestLimit . 's以后，再次进行结算操作');
            }

            $detPaylist = Db::table('cy_pay')->alias('p')
                ->join('cy_game g', 'p.gameid = g.id and g.game_kind=1', 'inner')
                ->join('nw_game_channel_divide d', 'p.gameid = d.game_id and d.channel_id=' . $this->_channelId, 'left')
                ->field("p.id as payid,p.orderid AS orderid,p.gameid AS game_id,p.userid,p.username,g.name as game_name,g.channel_split_ratio,p.channel_id,p.amount AS amount,((p.amount-p.coupon_amount)*(case when d.id then d.ratio else g.channel_split_ratio end)/100) AS divide_amt,case when d.id then d.id else '0' end as ratio_id,case when d.id then d.ratio else g.channel_split_ratio end as ratio,p.real_amount AS real_amount,p.real_ptb AS real_ptb,p.create_time,p.serverid,p.servername,p.rolename,p.coupon_amount,round(p.amount-p.coupon_amount,2) as actual_amount,p.real_coin")
                ->where($where)
                ->select();

            // 启动事务
            Db::startTrans();
            try {
                $settleData = array();
                $settleData['orderid'] = $orderid;
                $settleData['settle_type'] = 1;
                $settleData['channel_id'] = $this->_channelId;
                $settleData['channel_name'] = $this->_channelName;
                $settleData['total_amount'] = $totalAmount;
                $settleData['total_divide_amt'] = $divideAmount;
                $settleData['coupon_amount_amt'] = $total_coupon_amount_amt;
                $settleData['actual_amount'] = $actual_amount;
                $settleData['begin_time'] = $beginTime;
                $settleData['end_time'] = $endTime;
                $settleData['remark'] = $remark;
                $settleData['create_time'] = time();
                $settleData['first_audit_status'] = 1;
                $settleData['first_audit_time'] = time();
                if ($channelInfo['cps_settle_period'] == 2) {
                    $settleData['settle_period'] = 2;
                } else {
                    $settleData['settle_period'] = 1;
                }
                $insertSettleId = model('ChannelDivideSettle')->insertGetId($settleData);
                if (!$insertSettleId) {
                    throw new Exception("添加结算单失败");
                }

                while (list($key, $val) = @each($GameChannelData)) {
                    $detData = array();
                    $detData['settle_id'] = $insertSettleId;
                    $detData['divide_id'] = $val['ratio_id'];
                    $detData['begin_time'] = $val['begin_time'];
                    $detData['end_time'] = $val['end_time'];
                    $detData['game_id'] = $val['game_id'];
                    $detData['channel_id'] = $this->_channelId;
                    $detData['ratio'] = $val['ratio'];
                    $detData['pay_cnt'] = $val['pay_cnt'];
                    $detData['pay_amt'] = floatval($val['pay_amt']);
                    $detData['pay_real_amt'] = floatval($val['pay_real_amt']);
                    $detData['pay_real_ptb'] = floatval($val['pay_real_ptb']);
                    $detData['divide_amt'] = floatval($val['divide_amt']);
                    $detData['coupon_amount_amt'] = floatval($val['coupon_amount_amt']);
                    $detData['actual_amount'] = $val['actual_amount'];
                    $detData['pay_real_coin'] = $val['pay_real_coin'];
                    $detData['create_time'] = time();
                    ${'insertDet' . $val['game_id'] . 'ID'} = model('ChannelDivideSettleDet')->insertGetId($detData);
                    if (!${'insertDet' . $val['game_id'] . 'ID'}) {
                        throw new Exception("添加结算单明细失败");
                    }
                }

                $orderIds = array();
                while (list($key, $val) = @each($detPaylist)) {
                    //	echo ${'insertDet'.$val['game_id'].'ID'}."---insertDet".$val['game_id']."ID'--------<br>";
                    $orderIds[] = $val['payid'];
                    $detPayData['settle_id'] = $insertSettleId;
                    $detPayData['settle_det_id'] = ${'insertDet' . $val['game_id'] . 'ID'};
                    $detPayData['orderid'] = $val['orderid'];
                    $detPayData['game_id'] = $val['game_id'];
                    $detPayData['channel_id'] = $val['channel_id'];
                    $detPayData['ratio'] = $val['ratio'];
                    $detPayData['userid'] = $val['userid'];
                    $detPayData['username'] = $val['username'];
                    $detPayData['amount'] = floatval($val['amount']);
                    $detPayData['real_amount'] = floatval($val['real_amount']);
                    $detPayData['real_ptb'] = floatval($val['real_ptb']);
                    $detPayData['divide_amt'] = floatval($val['divide_amt']);
                    $detPayData['coupon_amount'] = floatval($val['coupon_amount']);
                    $detPayData['serverid'] = trim($val['serverid']);
                    $detPayData['servername'] = trim($val['servername']);
                    $detPayData['rolename'] = trim($val['rolename']);
                    $detPayData['create_time'] = $val['create_time'];
                    $detPayData['actual_amount'] = $val['actual_amount'];
                    $detPayData['real_coin'] = $val['real_coin'];
                    $detPayData['add_time'] = time();
                    //	var_dump($detPayData);
                    $insertDetPayId = model('ChannelDivideSettleDetPay')->insertGetId($detPayData);
                    if (!$insertDetPayId) {
                        throw new Exception("添加结算订单明细失败");
                    }
                }

                $updPayData = array();
                $updPayData['settle_channel_id'] = $this->_channelId;
                $updPayData['settle_id'] = $insertSettleId;
                $updPayData['settle_update_time'] = time();
                /*
                $gameids = model('Game')->where(['game_kind'=>1])->column('id');
                $where['p.gameid']= array('IN',$gameids);
                */
                unset($where['g.id']);
                $where['p.id'] = array('IN', $orderIds);
                $updResult = model('Pay')->alias('p')->where($where)->update($updPayData);
                if (!$updResult) {
                    throw new Exception("更新订单为已结算失败");
                }

                $this->redis->del('channel_settle_duplicate_' . $this->_channelId);

                // 提交事务
                Db::commit();

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();

                $this->jsonResult('', 0, '结算申请生成失败' . $e->getMessage());
            }

            $result['orderid'] = $orderid;

            $template = '有新的专服结算申请：会长"' . $this->_channelName . '" 发起结算申请，订单号：' . $orderid . ' 。请及时安排处理，时间：' . date('Y-m-d H:i:s');
            $ddurl = Env::get('operat_url');
            curlDD($template, $ddurl, true);

            $this->jsonResult($result, 20000, '结算申请生成成功');
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
            exit;
        }
    }

    /**
     * 未结算订单
     */
    public function unsettle()
    {
        if ($this->request->isPost()) {
            //判断当前账号是否有结算权限
            if (!in_array($this->_channelLevel, [1])) {
                $this->jsonResult('', 0, '您无权限进行结算相关操作');
            } else {
                $list_rows = input('list_rows', 10);
                $page = input('page', 1);

                $orderid = $this->input('orderid', '', 'trim');    //订单编号
                $game_id = $this->input('game_id', 0, 'intval');    //游戏ID
                $begin_time = $this->input('begin_time', '', 'trim');
                $end_time = $this->input('end_time', '', 'trim');
                $download = $this->input('download', 0, 'intval');

                $where = array();
                $where['p.status'] = 1;
                if ($orderid) {
                    $where['p.orderid'] = $orderid;
                }
                if ($game_id) {
                    if (in_array($game_id, model('game')->getTakingGame($this->_channelId))) {
                        $where['p.gameid'] = $game_id;
                    } else {
                        $this->jsonResult('', 20000, '获取未结算订单列表成功');
                    }
                } else {
                    $where['p.gameid'] = ['in', model('game')->getTakingGame($this->_channelId)];
                }
                //申请开始时间和结束时间不为空时
                if ($begin_time != '' && $end_time != '') {
                    $where['p.create_time'] = [
                        ['>=', strtotime($begin_time)],
                        ['<=', strtotime($end_time . ' 23:59:59')],
                    ];
                } //开始时间不为空时
                elseif ($begin_time != '') {
                    $where['p.create_time'] = ['>=', strtotime($begin_time)];
                } //结束时间不为空时
                elseif ($end_time != '') {
                    $where['p.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
                }
                //所有下级渠道(包括自己)
                $channelIds = get_child_channel_arr($this->_channelId);
                array_push($channelIds, $this->_channelId);
                $where['p.channel_id'] = ['in', $channelIds];
                $where['p.settle_id'] = array('eq', 0);
                //	var_dump($where);
                $GameChannelData = Db::table('cy_pay')->alias('p')
                    ->join('cy_game g', 'p.gameid = g.id and g.game_kind=1', 'inner')
                    ->join('nw_game_channel_divide d', 'p.gameid = d.game_id and d.channel_id=' . $this->_channelId, 'left')
                    ->field("p.gameid AS game_id,g.name as game_name,g.channel_split_ratio,count(*) as pay_cnt,SUM(p.pay_amount) AS pay_amt,d.id as ratio_id,d.ratio,SUM(p.pay_amount) AS pay_real_amt,SUM(p.real_ptb) AS pay_real_ptb,SUM(p.coupon_amount) as coupon_amount_amt")
                    ->where($where)
                    ->group('p.gameid')
                    ->select();
                $retData = array();
                $total_amt = $divide_amt = $total_cnt = $total_coupon_amount_amt = $actual_amount = 0;
                foreach ($GameChannelData as &$det) {
                    if (intval($det['ratio_id'])) {
                        $ratio_id = $det['ratio_id'];
                        $ratio = floatval($det['ratio']);
                    } else {
                        $ratio_id = 0;
                        $ratio = floatval($det['channel_split_ratio']);
                    }
                    //if(!$ratio){
                    //	$this->jsonResult('', 0, '您尚有游戏未配置分成比例,请先联系平台设置');
                    //}
                    //else{
                    $det['ratio_id'] = $ratio_id;
                    $det['ratio'] = $ratio;
                    $total_cnt += floatval($det['pay_cnt']);
                    $total_amt += floatval($det['pay_amt']);
                    $total_coupon_amount_amt += floatval($det['coupon_amount_amt']);
                    $divide_amt += floatval(intval(($det['pay_amt']-$det['coupon_amount_amt']) * $det['ratio']) / 100);
                    $det['divide_amt'] = floatval(intval(($det['pay_amt']-$det['coupon_amount_amt']) * $det['ratio']) / 100);
                    $actual_amount += floatval($det['pay_amt']-$det['coupon_amount_amt']);
                    $det['actual_amount'] = floatval($det['pay_amt']-$det['coupon_amount_amt']);
                    //}
                }
                $settle = array();
                $settle['total_amount'] = priceFormat($total_amt);
                $settle['divide_amount'] = priceFormat($divide_amt);
                $settle['total_cnt'] = priceFormat($total_cnt);
                $settle['total_coupon_amount_amt']  = priceFormat($total_coupon_amount_amt);
                $settle['actual_amount']  = priceFormat($actual_amount);
                if ($download) {
                    $sql = Db::table('cy_pay')->alias('p')
                        ->join('cy_game g', 'p.gameid = g.id and g.game_kind=1', 'inner')
                        ->join('nw_game_channel_divide d', 'p.gameid = d.game_id and d.channel_id=' . $this->_channelId, 'left')
                        ->field("p.orderid AS orderid,p.gameid AS game_id,g.name as game_name,p.amount AS amount,round((p.pay_amount-p.coupon_amount)*(case when d.id then d.ratio else g.channel_split_ratio end)/100,2) AS divide_amt,case when d.id then d.id else '0' end as ratio_id,case when d.id then d.ratio else g.channel_split_ratio end as ratio,p.real_amount AS real_amount,p.real_ptb AS real_ptb,p.serverid,p.servername,p.rolename,p.userid,p.username,p.create_time,p.coupon_amount,round((p.pay_amount-p.coupon_amount),2) as actual_amount")
                        ->where($where)
                        ->order("p.id desc")
                        ->fetchSql(true)
                        ->select();
                    //	echo $sql;
//                    if ((new MakeReport())->addTask('guild.ChannelSettleUnsettle', $sql, 'cps' . session('guild_info')['id'])) {
                    if ((new MakeReportGo())->addTask('guild.ChannelSettleUnsettle', $sql, 'cps' . session('guild_info')['id'])) {
                        $this->jsonResult('', 20000, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
                    } else {
                        $this->jsonResult('', 20013, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
                    }
                }

                $detPaylist = Db::table('cy_pay')->alias('p')
                    ->join('cy_game g', 'p.gameid = g.id and g.game_kind=1', 'inner')
                    ->join('nw_game_channel_divide d', 'p.gameid = d.game_id and d.channel_id=' . $this->_channelId, 'left')
                    ->field("p.orderid AS orderid,p.gameid AS game_id,g.name as game_name,p.amount AS amount,round((p.pay_amount-p.coupon_amount)*(case when d.id then d.ratio else g.channel_split_ratio end)/100,2) AS divide_amt,case when d.id then d.id else '0' end as ratio_id,case when d.id then d.ratio else g.channel_split_ratio end as ratio,p.real_amount AS real_amount,p.real_ptb AS real_ptb,p.serverid,p.servername,p.rolename,p.userid,p.username,p.create_time,p.coupon_amount,round((p.pay_amount-p.coupon_amount),2) as actual_amount")
                    ->where($where)
                    ->order("p.id desc")
                    ->paginate(['list_rows' => $list_rows, 'page' => $page])
                    ->toArray();
                //	var_dump($detPaylist);
                $retData['settle'] = $settle;
                //	$retData['settleDet'] = $GameChannelData;
                $retData['detPaylist'] = $detPaylist;

                $this->jsonResult($retData, 20000, '获取未结算订单列表成功');
            }
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
            exit;
        }
    }

}
