<?php
/**
 * 游戏管理控制器
 */

namespace app\guildapi\controller;

use app\common\model\PromotionShortLink;
use think\Db;
use think\Env;
use think\Exception;
use app\common\logic\Member as MemberService;
use app\common\model\GamePoint;
use GuzzleHttp\Client;
use app\common\model\ServerInfo;

class Game extends Guild
{
    protected $_model;

    protected $_channelId;

    // 特殊处理的 IOS游戏id 数组
    protected $gameIOS;

    // 游戏平台
    protected $_platform = [
        0 => '安卓',
        1 => 'IOS'
    ];

    // 游戏状态
    protected $_status = [
        0 => ['index'=>1,'value'=>'正常'],
        1 => ['index'=>2,'value'=>'白名单'],
        // 3 => '下架',
    ];

    //新闻类型
    protected $_type = [
        1 => '资讯',
        2 => '麻花网络',
        3 => '评测',
        4 => '攻略',
        5 => '视频',
        6 => '活动',
        7 => '更新',
        8 => '新闻',
        9 => '充值活动'
    ];

    // todo 显示的新闻类型
    private $_newsType
        = array(
            6 => '活动' ,
            7 => '更新' ,
            8 => '新闻' ,
            9 => '充值活动'
        );

    private $_newsTypeList = ['6','7','8','9'];

    // 游戏等级
    protected $gameLevel = ['S' , 'A1' , 'A2' , 'B1' , 'B2' , 'B3' , 'B4' , 'C' , 'D'];

    protected function _initialize()
    {
        parent::_initialize();

        $this->_model = model('Game');

        $guildInfo = session('guild_info');

        $this->_channelId = $guildInfo['channel_id'];
        $this->_level = $guildInfo['level'];
        $this->gameIOS = config('gameIOS');
    }

    /**
     * 获取游戏列表
     * @return [type] [description]
     */
    public function getGameList()
    {
        $page     = input('page',1);
        $pageSize = input('pageSize',10);
        $where    = $this->_getCondition();
        $games = $this->gameList();
        $exist_game = Db::name('cy_sdkgamelist')->where(['channel_id'=>$this->_channelId])->column('gameid');
        $apply_game = Db::name('nw_game_package_apply')->where(['channel_id'=>$this->_channelId,'apply_status'=>0])->column('game_id');//白名单申请列表
        $list = $this->_model->alias('game')
            ->join('cy_gameinfo info', 'game.id = info.game_id')
            ->join('nw_game_channel_divide devide','game.id = devide.game_id and devide.channel_id = '.$this->_channelId,'left')
            ->field(['game.id', 'game.name', 'info.mobileicon', 'game.type', 'game.subject', 'game.cooperation_status','info.platform','game.pinyin','ratio'])
            ->where($where)
            ->whereIn('game.id',array_column(json_decode(json_encode($games),true),'id'))
            ->where("game.is_default = 0 and game.cooperation_status != 3")
            ->where(['game.id'=>['not in',$exist_game]])
            ->order('game.id desc')
            ->paginate($pageSize,false,['page'=>$page])->toArray();
        if ($list['data']) {
            foreach ($list['data'] as $key => $value) {//在申请表的话并且状态是申请中 0 吧对应的游戏状态改成4
                $list['data'][$key]['cooperation_status'] = in_array($value['id'],$apply_game)?'apply':$list['data'][$key]['cooperation_status'];
                if(in_array($this->_channelId,[5851,3549])){
                    if (empty($list['data'][$key]['ratio'])) {
                        $list['data'][$key]['ratio'] = db::name('cy_game')->where(['id'=>$value['id']])->value('channel_split_ratio');
                    }
                }else{
                    $list['data'][$key]['ratio'] = "--";
                }
            }
        }
        return json(['data'=>$list,'code'=>20000,'msg'=>'获取游戏列表成功']);
//        switch ($this->_level) {
//            case '3':
//                $exist_game = Db::name('cy_sdkgamelist')->where(['channel_id'=>$this->_channelId])->column('gameid');
//                $apply_game = Db::name('nw_game_package_apply')->where(['channel_id'=>$this->_channelId,'apply_status'=>0])->column('game_id');//白名单申请列表
//                $list = $this->_model->alias('game')
//                ->join('cy_gameinfo info', 'game.id = info.game_id')
//                ->field(['game.id', 'game.name', 'info.mobileicon', 'game.type', 'game.subject', 'game.cooperation_status','info.platform','game.pinyin'])
//                ->where($where)
//                ->where("game.is_default = 0 and game.cooperation_status != 3")
//                ->where(['game.id'=>['not in',$exist_game]])
//                ->order('game.id desc')
//                ->paginate($pageSize,false,['page'=>$page])->toArray();
//                if ($list['data']) {
//                    foreach ($list['data'] as $key => $value) {//在申请表的话并且状态是申请中 0 吧对应的游戏状态改成4
//                        $list['data'][$key]['cooperation_status'] = in_array($value['id'],$apply_game)?'apply':$list['data'][$key]['cooperation_status'];
//                    }
//                }
//                return json(['data'=>$list,'code'=>20000,'msg'=>'获取游戏列表成功']);
//                break;
//            case '1':
//                $list = $this->_model->alias('game')
//                ->join('cy_gameinfo info', 'game.id = info.game_id')
//                ->join('nw_game_channel_divide devide','game.id = devide.game_id and devide.channel_id = '.$this->_channelId,'left')
//                ->field(['game.id', 'game.name', 'info.mobileicon', 'game.type', 'game.subject', 'game.cooperation_status','info.platform','game.pinyin','ratio'])
//                ->where($where)
//                ->where("game.is_default = 0 and game.cooperation_status != 3")
//                ->order('game.id desc')
//                ->paginate($pageSize,false,['page'=>$page])->toArray();
//                if (!empty($list['data']) ) {
//                    foreach ($list['data'] as $key => $value) {
//                        if (empty($list['data'][$key]['ratio'])) {
//                            $list['data'][$key]['ratio'] = db::name('cy_game')->where(['id'=>$value['id']])->value('channel_split_ratio');
//                        }
//                    }
//                }
//                return json(['data'=>$list,'code'=>20000,'msg'=>'获取游戏列表成功']);
//                break;
//            default:
//               $list = $this->_model->alias('game')
//                ->join('cy_gameinfo info', 'game.id = info.game_id')
//                ->field(['game.id', 'game.name', 'info.mobileicon', 'game.type', 'game.subject', 'game.cooperation_status','info.platform','game.pinyin'])
//                ->where($where)
//                ->where("game.is_default = 0 and game.cooperation_status != 3")
//                ->order('game.id desc')
//                ->paginate($pageSize,false,['page'=>$page])->toArray();
//                return json(['data'=>$list,'code'=>20000,'msg'=>'获取游戏列表成功']);
//                break;
//        }

    }
    /**
     * 获取搜索菜单列表
     * @param string $value [description]
     */
    public function getGameMenu()
    {
        $meue = [];
        $meue['game_platform'] = $this->_platform;
        $meue['game_status']   = $this->_status;
        $meue['game_type']     = $this->_getType();
        $meue['game_subject']  = $this->_getSubject();
        $meue['game_list']     = $this->gameList();
        return json(['data'=>$meue,'code'=>20000,'msg'=>'获取搜索菜单列表成功']);
    }
    /**
     * 我的游戏链接read
     * @param string $value [description]
     */
    public function myGameLink()
    {
        $page        = input('page',1);
        $pageSize    = input('pageSize',10);
        $platform    = input('platform'); // 适用平台 0-安卓 1-IOS
        $level       = input('level');//账号类型
        $gameName    = input('gameName', '', 'trim');
        $accountName = input('accountName', '', 'trim');
        $upload_status = input('upload_status', ''); // 是否处理成功：-1=已禁用、0=待处理、1=已处理、2=待审核、3=分包中、4=分包失败
        $platform_type = input('platform_type', ''); // 平台类型（2=安卓、1=ios）

        $condition = [];
        $condition['game.game_kind'] = 1;
        if (is_numeric($platform)) {
            $condition['info.platform'] = (int)$platform;
        }
        if (is_numeric($upload_status)) {
            $condition['sdk.upload_status'] = (int)$upload_status;
        }
        if ($platform_type) {
            $condition['game.type'] = (int)$platform_type;
        }
        if (! empty($gameName)) {
            $condition['game.name'] = ['LIKE', '%' . $gameName . '%'];
        }
        if (! empty($accountName)) {
            $level == 2 && $condition['p_channel.name'] = ['LIKE', $accountName . '%'];
            $level == 3 && $condition['channel.name'] = ['LIKE', $accountName . '%'];
        }
        $account_level = Db::name('nw_channel')->where(['id' => $this->_channelId])->value('level');
        switch ($account_level) {
            case '1'://B账号
                $list = model('SdkGameList')->alias('sdk')
                    ->join('cy_game game', 'game.id = sdk.gameid AND game.cooperation_status != 3 AND (game.is_default = 0)')
                    ->join('cy_gameinfo info', 'game.id = info.game_id', 'left')
                    ->join('nw_channel channel', 'sdk.channel_id = channel.id', 'left')
                    ->join('nw_channel p_channel', 'channel.parent_id = p_channel.id AND p_channel.level = 2', 'left')
                    ->join('nw_game_package_apply gpa', 'sdk.gameid = gpa.game_id AND sdk.channel_id = gpa.channel_id', 'left')
                    ->field(
                        [
                            'game.id', 'game.name', 'info.mobileicon','game.pinyin','info.platform',
                            'sdk.id' => 'sdk_id','sdk.filename', 'sdk.update_time', 'sdk.upload_status','sdk.package_type','sdk.channel_id','channel.name' =>'c_name','p_channel.name'=>'b_name','sdk.remark','game.pinyin'
                            ,'gpa.apply_status', 'gpa.remark'
                        ]
                    )
                    ->where(['channel.id_path' => ['LIKE', '%,' . $this->_channelId . ',%']])
                    ->where($condition)
                    ->order('sdk.update_time desc')
                    ->paginate(['list_rows'=>$pageSize,'page'=>$page])->toArray();
                break;
            case '2'://B-
                $list = model('SdkGameList')->alias('sdk')
                    ->join('cy_game game', 'game.id = sdk.gameid AND game.cooperation_status != 3 AND (game.is_default = 0)')
                    ->join('cy_gameinfo info', 'game.id = info.game_id', 'left')
                    ->join('nw_channel channel', 'sdk.channel_id = channel.id', 'left')
                    ->join('nw_game_package_apply gpa', 'sdk.gameid = gpa.game_id AND sdk.channel_id = gpa.channel_id', 'left')
                    ->field(
                        [
                            'game.id', 'game.name', 'info.mobileicon','game.pinyin','info.platform',
                            'sdk.id' => 'sdk_id','sdk.filename', 'sdk.update_time', 'sdk.upload_status','sdk.package_type','sdk.channel_id','channel.name' =>'c_name','sdk.remark','game.pinyin'
                            ,'gpa.apply_status', 'gpa.remark'
                        ]
                    )
                    ->where(['channel.id_path' => ['LIKE', '%,' . $this->_channelId . ',%']])
                    ->where($condition)
                    ->order('sdk.update_time desc')
                    ->paginate(['list_rows'=>$pageSize,'page'=>$page])->toArray();
                break;
            case '3'://C
                $list = model('SdkGameList')->alias('sdk')
                    ->join('cy_game game', 'game.id = sdk.gameid AND game.cooperation_status != 3 AND (game.is_default = 0)')
                    ->join('cy_gameinfo info', 'game.id = info.game_id', 'left')
                    ->join('nw_channel channel', 'sdk.channel_id = channel.id', 'left')
                    ->join('nw_game_package_apply gpa', 'sdk.gameid = gpa.game_id AND sdk.channel_id = gpa.channel_id', 'left')
                    ->field(
                        [
                            'game.id', 'game.name', 'info.mobileicon','game.pinyin','info.platform',
                            'sdk.id' => 'sdk_id','sdk.filename', 'sdk.update_time', 'sdk.upload_status','sdk.package_type','sdk.channel_id','channel.name' =>'c_name','sdk.remark','game.pinyin'
                            ,'gpa.apply_status', 'gpa.remark'
                        ]
                    )
                    ->where('channel.id = '.$this->_channelId)
                    ->where($condition)
                    ->order('sdk.update_time desc')
                    ->paginate(['list_rows'=>$pageSize,'page'=>$page])->toArray();
                break;
            default:
                $list = [];
                break;
        }
        if (!isset($list['data'])) {
            return json(['data'=>$list,'code'=>10038,'msg'=>'暂无数据']);
        }
        foreach ($list['data'] as $key => $value) {
            $short_link = db('nw_promotion_short_link')->where(['game_id'=>$value['id'],'channel_id'=>$value['channel_id'],'package_type'=>$value['package_type'], 'type' => 1])->value('short_link');
            if (!empty($short_link)) {
                $share_url = T_DOMAIN.'/'.$short_link;
            } else {
                $share_url = T_DOMAIN . '/game_promotion/index/game_id/' . $value['id'] . '/channel_id/' . $value['channel_id'] . '/package_type/' . $value['package_type'];
            }

            if($value['update_time'] > 1711596600){
                $list['data'][$key]['down_url'] = APK_DOWN_DOMAIN_NEW . '/sygame/' . $value['pinyin'] . '/' . $value['filename'];
            }else{
                $list['data'][$key]['down_url'] = APK_DOWN_DOMAIN . '/sygame/' . $value['pinyin'] . '/' . $value['filename'];
            }

            $list['data'][$key]['share_url'] = $share_url;
            // if ($value['upload_status'] == 0) {
            //     $list['data'][$key]['upload_status'] = time() - $value['update_time'] >= PACKAGE_TIME_OUT?2:0;
            // }
            $list['data'][$key]['share_all_url'] = T_DOMAIN . '/game/index.html?cid=' . base64_encode(auth_code($value['channel_id'], "ENCODE", Env::get('auth_key')));

            $short_page_link = db('nw_promotion_short_link')->where(['game_id' => $value['id'], 'channel_id' => $value['channel_id'], 'package_type' => $value['package_type'], 'type' => 2])->value('short_link');
            if (!$short_page_link) {
                (new PromotionShortLink())->insertData($value['id'], $value['channel_id'], $value['package_type'], 2);
                $short_page_link = db('nw_promotion_short_link')->where(['game_id' => $value['id'], 'channel_id' => $value['channel_id'], 'package_type' => $value['package_type'], 'type' => 2])->value('short_link');
            }
            if ($short_page_link) {
                $list['data'][$key]['share_all_url'] = T_DOMAIN . '/tgy/' . $short_page_link;
            }

            //审核中
            // if($value['apply_status'] === 0){
            //     $list['data'][$key]['upload_status'] = 3;
            // }
            if ($value['update_time']) {
                $list['data'][$key]['update_time'] = date("Y-m-d H:i:s", $value['update_time']);
            }
        }

        return json(['data'=>$list,'code'=>20000,'msg'=>'获取游戏列表成功']);
    }
    /**
     * 批量注册列表
     * @param string $value [description]
     */
    public function myBatchLog()
    {
        $page      = input('page',1);
        $list_rows = input('pageSize',10);
        $gameid = input('gameid');
        $status = input('status');
        $create_time = input('create_time');
        $where = [];
        if (! empty($gameid)) {
            $where['log.game_id'] = $gameid;
        }
        if (! empty($status) || $status==='0') {
            $where['log.status'] = $status;
        }
        if (! empty($create_time)) {
            $where['log.create_time'] = [['>=', strtotime($create_time .'0:0:0')],['<=', strtotime(date($create_time .'23:59:59'))]];
        }
        $list  = Db::name('nw_batch_reg')
            ->alias('log')
            ->join('cy_game game', 'log.game_id = game.id')
            ->field('log.id as log_id,log.tal_cnt,log.status,FROM_UNIXTIME(log.create_time) as create_time,log.channel_id,log.remark,game.name')
            ->where($where)
            ->where(['channel_id'=>$this->_channelId,'game.game_kind'=>1])
            ->where("game.is_default = 0 and game.cooperation_status != 3")
            ->order('log.create_time desc')
            ->paginate(['list_rows'=>$list_rows,'page'=>$page])->toArray();
        $gameList = model('Common/Game')->getAllByCondition('id,name',['game_kind'=>1,'cooperation_status'=>['NOT IN',[0,3]]],'','self');
        return json(['data'=>$list,'code'=>20000,'gameList'=>$gameList,'msg'=>'获取批量注册记录成功']);
    }
    /**
     * 批量注册具体数据列表
     * @param string $value [description]
     */
    public function myBatchLogList()
    {
        $log_id    = input('log_id',0);
        $page      = input('page',1);
        $list_rows = input('pageSize',10);
        if (empty($log_id) || !isset($log_id)) {
            return json(['data'=>'','code'=>10023,'msg'=>'log_id参数错误']);
        }
        $list  = Db::name('nw_batch_reg_log')->alias('log')
            ->field('log.id,log.account,log.status,log.channel_id,game.name')
            ->where(['log.log_id'=>$log_id,'log.channel_id'=>$this->_channelId])
            ->join('cy_game game', 'log.game_id = game.id')
            ->where("game.is_default = 0 and game.cooperation_status != 3")
            ->paginate(['list_rows'=>$list_rows,'page'=>$page])->toArray();
        return json(['data'=>$list,'code'=>20000,'msg'=>'获取批量注册详情成功']);
    }
    /**
     * 批量注册
     */
    public function doBatchReg()
    {
        if ($this->_level != 3) {
            return json(['data'=>'','code'=>10023,'msg'=>'只有推广员能增加账号!']);
        }
        $game_id = input('game_id');
        $str = input('account_str');
        if (empty($game_id) || !isset($game_id)) {
            return json(['data'=>'','code'=>10023,'msg'=>'game_id参数错误']);
        }
        $str = array_filter(explode(',', $str));
        if (count($str)>=100) {
            return json(['data'=>'','code'=>10025,'msg'=>'批量注册一次性最多支持100条账号注册']);//校验长度
        }
        foreach ($str as $key => $value) {
            //校验账号规则
            if (!preg_match('/^[a-zA-Z]{1}[a-zA-Z0-9]{5,19}$/i',$value)) {
                unset($str[$key]);
            }
        }
        $str = array_values($str);
        $where = [];
        // $where['gameid'] = $game_id;
        $where['username'] = ['in',$str];
        $accountList = Db::name('cy_memberstwo')->where($where)-> column('username');//取出重复的值
        if ($accountList) {//重复的情况下去除重复反馈给前端
            $str = array_diff($str, $accountList);
            $msg = implode(',', $accountList);
            return json(['data'=>implode(',', $str),'code'=>20000,'msg'=>'账号:'.$msg.'重复以去除,请确认其他账号是否继续创建','type'=>1]);//校验长度
        }
        // 判断该游戏是否处于白名单状态，若是，则判断是否有分包记录，没有的话，禁止注册关联
        if(!empty($game_id)) {
            $game_info  = model('game')->where(['id' => $game_id])->find();
            // 处于白名单状态
            if(2 == $game_info['cooperation_status']) {
                $conditions = array();
                $conditions['gameid'] = $game_id;
                $conditions['channel_id'] = $this->_channelId;
                $subPackageCnt = model('SdkGameList')->where($conditions)->count();
                if(!$subPackageCnt){
                    return json(['data'=>'','code'=>10023,'msg'=>'该游戏处于白名单状态，无法新增']);
                }
            }
        }
        //都符合的情况下写入数据
        Db::startTrans();
        try {
            //1. 添加到批次表
            $channelModel = Db::name('nw_batch_reg');
            $log_id = $channelModel->insertGetId([
                'game_id'     => $game_id,
                'status'      => 1,
                'create_time' => NOW_TIMESTAMP,
                'tal_cnt'     => count($str),
                'channel_id'  => $this->_channelId
            ]);
            if (!$log_id) {
                return json(['data'=>'','code'=>10015,'msg'=>"添加批次信息失败"]);
            }
            $cont = [];
            foreach ($str as $key => $value) {
                $cont[$key]['game_id']    = $game_id;
                $cont[$key]['account']    = $value;
                $cont[$key]['log_id']     = $log_id;
                $cont[$key]['status']     = 1;
                $cont[$key]['channel_id'] = $this->_channelId;
            }
            $res = Db::name('nw_batch_reg_log')->insertAll($cont);
            if (!$res) {
                return json(['data'=>'','code'=>10015,'msg'=>"添加批次信息日志失败"]);
            }

            $data = [];
            foreach ($str as $key => $value) {
                $data['reg_time']   = NOW_TIMESTAMP;
                $data['login_time'] = NOW_TIMESTAMP;
                $data['ip']         = request()->ip();
                $data['gameid']     = $game_id;
                $data['username']   = $value;
                $data['password']   = auth_code('b123456', "ENCODE", Env::get('auth_key'));
                $data['channel_id'] = $this->_channelId;
                Db::startTrans();

                try {
                    $member_id = Db::table('cy_members')->insertGetId($data);

                    if ($member_id) {
                        // 插入玩家历史记录
                        Db::table('cy_member_history')->insert([
                            'userid'      => $member_id,
                            'password'    => auth_code('b123456', "ENCODE", Env::get('auth_key')),
                            'ip'          => request()->ip(1),
                            'create_time' => NOW_TIMESTAMP
                        ]);

                        //插入用户扩展记录
                        Db::table('cy_memberstwo')->insert([
                            'userid'      => $member_id,
                            'username'    => $data['username'],
                            'realname'    => -1,
                            'idcard'      => -1,
                            'create_time' => NOW_TIMESTAMP
                        ]);

                        // 登录时 绑定该游戏的归属渠道
                        Db::table('cy_member_channel_game_rel')->insert([
                            'member_id'         => $member_id,
                            'channel_id'        => $data['channel_id'],
                            'game_id'           => $data['gameid'],
                            'mcgr_createtime'   => time(),
                            //  'update_time'       => time(),
                            'mcgr_ip'           => request()->ip()
                        ]);
                    } else {
                        throw new Exception("注册失败!");
                    }
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    return json(['data'=>'','code'=>10015,'msg'=>"添加失败： " . $e->getMessage()]);
                }
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['data'=>'','code'=>10015,'msg'=>"添加失败： " . $e->getMessage()]);
        }
        return json(['data'=>'','code'=>20000,'msg'=>"注册成功"]);

    }
    /**
     * 已接游戏备注
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function toEditGameLink()
    {
        $sdk_id = input('sdk_id');
        $data = [];
        $data['remark'] = $remark = input('remark');
        if (empty($sdk_id)) {
            return json(['data'=>[],'code'=>10021,'msg'=>'sdk_id参数错误']);
        }
        $res = model('SdkGameList')->update($data, ['id'=>$sdk_id]);
        if ($res) {
            return json(['data'=>'','code'=>20000,'msg'=>'修改成功']);
        }else{
            return json(['data'=>'','code'=>10022,'msg'=>'修改失败']);
        }
    }

    /**
     * 获取游戏区服信息
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function getSeverList()
    {
        $gameId = input('game_id', 0, 'intval');
        $res = Db::name('nw_game_server')->field('servername,serverid')->where(['game_id'=>$gameId])->where(['status'=>1])->select();
        return json(['data'=>$res,'code'=>20000,'msg'=>'获取区服列表成功']);
    }

    // 获取游戏名称
    protected function _getGameName($gameId)
    {
        return model('Game')->where(['id' => $gameId])->value('name');
    }

    // 所有游戏类型
    protected function _getType()
    {
        return Db::table('cy_gametype')->field("id as 'index',name as 'value'")->select();
    }

    // 所有游戏题材
    protected function _getSubject()
    {
        return Db::table('cy_gamesubject')->field("id as 'index',name as 'value'")->select();
    }

    // 公共搜索条件
    protected function _getCondition($self = 'false')
    {
        $status   = input('status');
        $platform = input('platform');
        $type     = input('type', 0, 'intval');
        $subject  = input('subject', 0, 'intval');
        $gameName = input('gameName', '', 'trim');
        $gameid   = input('gameid');
        if ($self) {
            $package_type   = input('package_type','');
        }

        $condition = [];
        $condition['game.game_kind'] = 1;
        if (! empty($gameName)) {
            $condition['game.name'] = ['LIKE', '%' . $gameName . '%'];
        }

        if (! empty($subject)) {
            $condition['game.subject'] = $subject;
        }

        if (! empty($type)) {
            $condition['game.type'] = $type;
        }

        if (is_numeric($status)) {
            $condition['game.cooperation_status'] = (int)$status;
        } else {
            $condition['game.cooperation_status'] = ['IN', [1, 2]];
        }

        if (is_numeric($platform)) {
            $condition['info.platform'] = (int)$platform;
        }

        //包体类型
        if ($package_type!='') {
            $condition['sdk.package_type'] = (int)$package_type;
        }
        return $condition;
    }

    /**
     * 获取游戏列表
     * @return mixed
     */
    public function gameList(){
        if($this->_channelLevel == 0){
            return model('Common/Game')->getAllByCondition('id,name',['game_kind'=>1,'cooperation_status'=>['neq',0]],'id desc','self');
        }else if($this->_channelLevel == 1){

            $channelGame = model('common/ChannelGame')->where(['channel_id'=>$this->_channelId])->column('game_id');

        }else {

            $channel = model('common/Channel')->field('id')->whereIn('id',$this->_channelIdPath)->where(['level'=>1])->find();
            $channelGame = model('common/ChannelGame')->where(['channel_id'=>$channel['id']])->column('game_id');

        }

        if(!$channelGame){
            return model('Common/Game')->where(['game_kind'=>1])
                ->where(['cooperation_status'=>1])->field('id,name')->select();
        }

        $where = ['cooperation_status'=>2,'id'=>['in',$channelGame]];
        return model('Common/Game')->where(['game_kind'=>1])
            ->where(['cooperation_status'=>1])->whereOr(function($query) use($where){
                $query->where($where);
            } )->field('id,name')->order('id desc')->select();
//        return model('Common/Game')->getAllByCondition('id,name',['game_kind'=>1,'cooperation_status'=>['neq',0],'id'=>['in',$channelGame]],'id desc','self');

    }

    /**
     * 获取所有游戏区服列表
     * @return mixed
     */
    public function gameServerList(){
        $page      = input('page',1);
        $pageSize  = input('pageSize',10);
        $game_id   = input('game_id');
        $server_id = input('server_id');
        $where = [];
        if (!empty($game_id)) {
            $where['game.id'] = $game_id;
        }
        if (!empty($server_id)) {
            $where['server.id'] = $server_id;
        }
        $res = Db::name('cy_game')->alias('game')
            ->join('nw_game_server server','game.id = server.game_id')
            ->field(['game.id as game_id','game.name as game_name','servername','server.id as server_id'])
            ->where(['game_kind'=>1])
            ->where($where)
            ->where("game.is_default = 0 and game.cooperation_status != 3")
            ->paginate($pageSize,false,['page'=>$page])->toArray();
        if ($res['data']) {
            $rank = ($page-1)*10;
            foreach ($res['data'] as $key => $value) {
                $rank ++ ;
                $res['data'][$key]['rank'] = $rank;
            }
        }
        return json(['data'=>$res,'code'=>20000,'msg'=>'获取游戏列表成功']);
    }
    /**
     * 下载excel模板
     * @return [type] [description]
     */
    public function download()
    {
        $type  = input('type',1);
        switch ($type) {
            case '1'://子会长模板
                return json(['data' => "https://cdn." . QM_DOMAIN_URL . "/common/excel/批量转账_子会长(模板).xlsx", 'code' => 20000, 'msg' => '下载链接']);
                break;
            case '2'://玩家模板
                return json(['data' => "https://cdn." . QM_DOMAIN_URL . "/common/excel/批量转账_玩家(模板).xlsx", 'code' => 20000, 'msg' => '下载链接']);
                break;
            default:
                return json(['data'=>'','code'=>15000,'msg'=>'参数错误']);
                break;
        }

    }

}
