<?php

/**
 * 交易记录接口控制器
 * 
 */

namespace app\guildapi\controller;

use app\guildapi\controller\Guild;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;

class ChannelTrade extends Guild  {
    
    protected $nowTime;
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;

    }
  
     /**
     * 我的交易记录列表
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1,2])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= $this->input('list_rows',10);
        $page					= $this->input('page',1);
		$type					= $this->input('type','0','intval');    //类型:1(转账支出),2(转账收入),3(直充),4(充值收入),5(充值支出),6(币追回),7(结算充值),8(结算收入),9(提现),10(提现打回)
        $account				= $this->input('account','','trim');    //收入账号
        $apply_begin_time		= $this->input('apply_begin_time','','trim');
        $apply_end_time			= $this->input('apply_end_time','','trim');
        $finish_begin_time		= $this->input('finish_begin_time','','trim');
        $finish_end_time		= $this->input('finish_end_time','','trim');

        $condition			=	[];
		if(!in_array($type,array(8,9,10))){
			$this->jsonResult('', 0, '请选择正确的交易类型');
		}
		else{
			$condition['a.type']			=  $type;
			$condition['a.account_type']	=  '2';    //结算币账户
		}	
	
		if($account){
			$condition['a.channel_name']	= $account;
		}

        //到账开始时间和结束时间不为空时
        if ($finish_begin_time != '' && $finish_end_time != '') {
            $condition['a.create_time'] = [
                ['>=', strtotime($finish_begin_time)],
                ['<=', strtotime($finish_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($finish_begin_time != '') {
            $condition['a.create_time'] = ['>=', strtotime($finish_begin_time)];
        } //结束时间不为空时
        elseif ($finish_end_time != '') {
            $condition['a.create_time'] = ['<=', strtotime($finish_end_time . ' 23:59:59')];
        }

	    //申请开始时间和结束时间不为空时
        if ($apply_begin_time != '' && $apply_end_time != '') {
            $condition['b.create_time'] = [
                ['>=', strtotime($apply_begin_time)],
                ['<=', strtotime($apply_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($apply_begin_time != '') {
            $condition['b.create_time'] = ['>=', strtotime($apply_begin_time)];
        } //结束时间不为空时
        elseif ($apply_end_time != '') {
            $condition['b.create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
        }

	//	var_dump($condition);

        $rechargeList = model('ChannelAccountDet')->alias('a')
						->join('nw_game_channel_divide_settle b','a.out_orderid = b.orderid and a.channel_id=b.channel_id','inner')
						->where($condition)
						->paginate(['list_rows'=>$list_rows,'page'=>$page])
						->toArray();


	//	echo model('ChannelRecharge')->getLastSql()."-----lastsql------<br>";

		$this->jsonResult($rechargeList, 20000, '获取列表成功');
    }
}
