<?php

/**
 * 分包控制器
 * 
 * Created by PhpStorm.
 * User: Admin
 * Date: 2018/5/10
 * Time: 10:27
 */

namespace app\guildapi\controller;

use app\common\model\Game as GameModel;
use app\common\logic\SubPackage as SubChannel;
use think\Db;
use think\Env;

class SubPackage extends Guild
{
    protected $gameIOS;  // 特殊处理的 IOS游戏id 数组

    protected function _initialize()
    {
        parent::_initialize();
     
        // 特殊处理的 IOS游戏id 数组
        $this->gameIOS = config('gameIOS');
        $guildInfo = session('guild_info');
        $this->_level = $guildInfo['level'];
    }
    
    /**
     * 批量打包请求限制
     * 
     * @param int $expire_time 过期时间
     * 
     * @return boolean
     */
    private function checkBatchPackageLimit($expire_time){
        
        $redis = \think\Cache::store('default');
        
        $cache_key = 'guild_batch_package_limit_'.session('guild_info')['channel_id'];
        
        if($redis->has($cache_key)) {
            return true;
        }else{
            $redis->set($cache_key, 1, $expire_time);
            return false;
        }
    }

    /**
     * 获取子账号
     * @param [type] $channelId [description]
     */
    private function getChildAccount($channelId)
    {
        $parent_id = Db::name('nw_channel')->where(['id'=>$channelId])->value('parent_id');
        $in_path = ','.$parent_id.','.$channelId.',';
        $where = [];
        if ($this->_level == 3) {
            $where = ['id'=>$channelId];
        }else{
            $where = ['id_path'=>['LIKE', '%' . $in_path . '%'],'level'=>3];
        }
        $ChildAccount = Db::name('nw_channel')->where($where)->column('name','id');
        return $ChildAccount;
    }
    /**
     * 游戏打包
     * @return [type] [description]
     */
    public function package()
    {
        $gameId = input('gameIds'); //打包游戏组
        $package_type = input('package_type', 0, 'intval');     //包体类型
        $from = input('from');                        //请求来源地
        $channelIds = input('channelIds', '', 'trim');
        $parent = input('parent', '', 'trim');
        if($parent){
            $this->_channelId = $parent;
        }
       return $this->dopackage($gameId,$package_type,$channelIds);
    }
    public function dopackage($gameId,$package_type,$channelIds){
        $channels = empty($channelIds) ? [] : explode(',', $channelIds);
        $childAccount = $this->getChildAccount($this->_channelId);//获取子账号列表 用于消息提示
        $sdk_channel_list = model('SdkGameList')->where(['gameid'=>$gameId])->column('channel_id');//白名单申请列表
        $gameName = '';
        $AccountStr = ''; 
        if (!$gameId) {
            return json(['data'=>[],'code'=>'10008','msg'=>'请选择要分包的游戏']);
        }else{
            $cooperation_status = Db::name('cy_game')->where(['id'=>$gameId])->value('cooperation_status');
            $game_kind = Db::name('cy_game')->where(['id'=>$gameId])->value('game_kind');
            if ($game_kind == 2) {
                return json(['data'=>[],'code'=>10008,'msg'=>'该游戏为混服游戏,无法在该端编辑']);
            }
			if($cooperation_status==0){
				 return json(['data'=>[],'code'=>10008,'msg'=>'该游戏尚在对接中,不能进行分包']);
			}
			else if($cooperation_status==3){
				 return json(['data'=>[],'code'=>10008,'msg'=>'该游戏已下架,不能再进行分包']);
			}
            $gameName = db::name('cy_game')->where(['id'=>$gameId])->value('name');
        }
        $msgList = [];
        if (count($channels)>0) {
            foreach ($channels as $key => $value) {
                if (!isset($childAccount[$value])) {
                    $msgList[] = ['id'=>$value,'msg'=>'该渠道id不属于本账号的推广子渠道，请联系管理员','type'=>0];
                    unset($channels[$key]);
                }elseif (isFrozenOption($value, $gameId)) {
                    $msgList[] = ['id'=>$childAccount[intval($value)],'msg'=>'该渠道禁止推广游戏，请联系管理员','type'=>0];
                    unset($channels[$key]);
                    
                }
            }
        }else{
            return json(['data'=>[],'code'=>10032,'msg'=>'未选择推广员']); 
        }
        $subpackage = new \app\common\logic\SubPackage();
        $adminId    = session('guild_info')['id'];
        $game_status = true;
        if (count($channels)>0) {
            foreach ($channels as $key => $value) {
                $gameFrozenInfo = Db::name('nw_game_frozen')->where(['game_id'=>$gameId])->find();
                if($gameFrozenInfo){
                    //无该渠道、该游戏的冻结信息时，添加
                    if(!Db::name('nw_channel_frozen')->where(['game_id'=>$gameId,'channel_id'=>$value])->find()){
                        $frozenData['game_id']      = $gameId;
                        $frozenData['channel_id']   = $value;
                        $frozenData['subpackage']   = $gameFrozenInfo['subpackage'];
                        $frozenData['grant_money']  = $gameFrozenInfo['grant_money'];
                        $frozenData['consume']      = $gameFrozenInfo['consume'];
                        $frozenData['member_login'] = $gameFrozenInfo['member_login'];
                        $frozenData['register']     = $gameFrozenInfo['register'];
                        $frozenData['create_time']  = NOW_TIMESTAMP;
                        $frozenData['update_time']  = NOW_TIMESTAMP;
                        Db::name('nw_channel_frozen')->insert($frozenData);
                        //被禁止打包
                        if($frozenData['subpackage']==1){
                            $msgList[] = ['id'=>$childAccount[intval($value)],'msg'=>'您的渠道已被禁止打包','type'=>0];
                            continue;
                        }
                    }
                }

                if ($this->_allowSubpackage($gameId,$value)) {
                    $game_status = false;
                    $cont = [];
                    $packagelModel = model('PackageApply');
                    $cont['game_id'] = $gameId;
                    $cont['channel_id'] = $value;
                    $cont['applicant_id'] = $this->_adminId;
                    $cont['create_time'] = NOW_TIMESTAMP;
                    $cont['apply_time'] = NOW_TIMESTAMP;
                    $cont['apply_status'] = 1;
                    if ($packagelModel->where(['game_id'=>$gameId,'channel_id'=>$value])->find()) {
                        $packagelModel->where(['game_id'=>$gameId,'channel_id'=>$value])->update($cont);
                    }else{
                        $packagelModel->insert($cont);
                    }
                    $AccountStr .= ','.$childAccount[intval($value)];
                    $msgList[] = ['id'=>$childAccount[intval($value)],'msg'=>'已经提交至后台审核,请耐心等待！','type'=>0];

                    // ## 游戏分包申请时，直接通过申请，并开始分包
                    // 游戏平台
                   $platform = model('GameInfo')->where(['game_id'=>$gameId])->value('platform');
                   $platform = ($platform==false) ? 0 : $platform;
                   $result = $subpackage->index($value, $gameId, 'cps'.$adminId,$platform,$package_type);
                   // 打包结果
                   if (is_array($result) && $result['code'] == 0) {
                       $msgList[] = ['id'=>$childAccount[intval($value)],'msg'=>'打包请求提交成功','type'=>1];
                   } else {
                       $msgList[] = ['id'=>$childAccount[intval($value)],'msg'=>$result['msg'],'type'=>0];
                   }

                } else {//游戏白名单 并且渠道id没有在sdk里面的话  申请提交
                    $game_status = false;
                    $cont = [];
                    $packagelModel = model('PackageApply');
                    $cont['game_id'] = $gameId;
                    $cont['channel_id'] = $value;
                    $cont['applicant_id'] = $this->_adminId;
                    $cont['create_time'] = NOW_TIMESTAMP;
                    $cont['apply_time'] = NOW_TIMESTAMP;
                    $cont['apply_status'] = 0;
                    if ($packagelModel->where(['game_id'=>$gameId,'channel_id'=>$value])->find()) {
                        $packagelModel->where(['game_id'=>$gameId,'channel_id'=>$value])->update($cont);
                    }else{
                        $packagelModel->insert($cont);
                    }
                    $AccountStr .= ','.$childAccount[intval($value)];
                    $msgList[] = ['id'=>$childAccount[intval($value)],'msg'=>'游戏白名单限制,已经提交至后台审核','type'=>0];
                }
            }
        }
        if (!$game_status) {
            $template = '有新的游戏接单申请：推广账号"'.$this->_channelName.'" 发起游戏的接单申请,推广员:"'.trim($AccountStr,',').'"申请接入白名单游戏:"'.$gameName.'",请及时安排处理，时间：'.date('Y-m-d H:i:s');
            $ddurl = Env::get('operat_url');
            curlDD($template, $ddurl,true);
        }
        return json(['data'=>$msgList,'code'=>20000,'msg'=>'获取打包结果成功']); 
    }
    /**
     * 是否允许分包
     * @param $gameId
     * @return bool
     */
    public function _allowSubpackage($gameId,$channelId)
    {
        $res = false;
        $cooperation_status = model('Game')->where(['id' => $gameId])->value('cooperation_status');
        // $res = ($cooperation_status == 1 || $cooperation_status == 2);
        //	推广平台，白名单的游戏，渠道第一次不能自行分包，需要管理员给他们分，分完一次之后渠道就能在推广后台-我的游戏中，自行对这个白名单游戏进行分包。
        if($cooperation_status == 1){  //正常游戏
            $res = true;
        }
        else if($cooperation_status == 2){
            $result = model('SdkGameList')
            ->where(['gameid' => $gameId, 'channel_id' => $channelId])
            ->find();
            if (!empty($result)) {
                $res = true;
            }
            else{
                $res = false;
            }
        }
        return $res;
    }
    
}