<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\guildapi\controller;

use app\common\model\Channel;
use app\service\WelfareService;
use think\Db;
use think\Exception;
use app\common\logic\Member as MemberService;
use app\common\library\MakeReport;

class Welfare extends Guild
{
    protected $channelList = [];

    protected function _initialize()
    {
        parent::_initialize();

        if (!is_array($this->channelIds)) {
            $this->channelList = (new Channel())->channelListById($this->channelIds);
        } else {
            $this->channelList = (new Channel())->channelList($this->_channelIdPath);
        }

    }

    public function welfareGrantList()
    {

//        $checkResult = $this->validate($this->input, 'Welfare.enterGameWelfare');
//
//        if (true !== $checkResult) {
//            $this->jsonResult('', 20013, $checkResult);
//        }

        $data = $this->input;
        $welfareService = new WelfareService();
        if (!is_array($this->channelIds)) {
            $channelIds = [$this->channelIds];
        } else {
            $channelIds = $this->channelIds;
        }
        $result = $welfareService->welfareGrantList($data, $channelIds, $this->channelList);
        if ($result['code'] == 20000) {
            $this->jsonResult(['data' => $result['data']], 20000, '获取列表成功');
        } else {
            $this->jsonResult([], 20013, $result['msg']);
        }

    }

    public function getGameWelfareList()
    {
        if (!isset($this->input['grant_type_id'])) {
            $this->jsonResult([], 20013, '类型错误');
        }
        switch ($this->input['grant_type_id']) {
            case 1:
                $checkResult = $this->validate($this->input, 'Welfare.enterGameWelfare');
                break;
            case 2:
                $checkResult = $this->validate($this->input, 'Welfare.everydayWelfare');
                break;
            case 3:
                $checkResult = $this->validate($this->input, 'Welfare.singleWelfare');
                break;
            case 4:
                $checkResult = $this->validate($this->input, 'Welfare.grandWelfare');
                break;
            case 5:
                $checkResult = $this->validate($this->input, 'Welfare.singleGrandWelfare');
                break;
            case 6:
                $checkResult = $this->validate($this->input, 'Welfare.sevenDayWelfare');
                break;
            case 7:
                $checkResult = $this->validate($this->input, 'Welfare.initialWelfare');
                break;
            case 8:
                $checkResult = $this->validate($this->input, 'Welfare.monthWelfare');
                break;
            case 9:
                $checkResult = $this->validate($this->input, 'Welfare.weekWelfare');
                break;
            default:
                $this->jsonResult([], 20013, '类型错误');
        }


        if (true !== $checkResult) {
            $this->jsonResult('', 20013, $checkResult);
        }

        $data = $this->input;
        $welfareService = new WelfareService();
        if (!is_array($this->channelIds)) {
            $channelIds = [$this->channelIds];
        } else {
            $channelIds = $this->channelIds;
        }
        $result = $welfareService->wefare($data['grant_type_id'], $data['game_id'], $channelIds, $this->channelList, $data['page'], $data['pageSize'], $data, false);
        if ($result['code'] == 20000) {
            $this->jsonResult(['data' => $result['data'],'a'=>$this->channelIds,'b'=>$this->_channelIdPath], 20000, '获取列表成功');
        } else {
            $this->jsonResult([], 20013, $result['msg']);
        }

    }

    public function applyWelfare()
    {

        if (!isset($this->input['grant_type_id'])) {
            $this->jsonResult([], 20013, '类型错误');
        }
        switch ($this->input['grant_type_id']) {
            case 1:
                $checkResult = $this->validate($this->input, 'Welfare.applyEnterGameWelfare');
                break;
            case 2:
                $checkResult = $this->validate($this->input, 'Welfare.applyEverydayWelfare');
                break;
            case 3:
                $checkResult = $this->validate($this->input, 'Welfare.applySingleWelfare');
                break;
            case 4:
                $checkResult = $this->validate($this->input, 'Welfare.applyGrandWelfare');
                break;
            case 5:
                $checkResult = $this->validate($this->input, 'Welfare.applySingleGrandWelfare');
                break;
            case 6:
                $checkResult = $this->validate($this->input, 'Welfare.applySevenDayWelfare');
                break;
            case 7:
                $checkResult = $this->validate($this->input, 'Welfare.applyInitialWelfare');
                break;
            case 8:
                $checkResult = $this->validate($this->input, 'Welfare.applyMonthWelfare');
                break;
            case 9:
                $checkResult = $this->validate($this->input, 'Welfare.applyWeekWelfare');
                break;
            default:
                $this->jsonResult([], 20013, '类型错误');
        }


        if (true !== $checkResult) {
            $this->jsonResult('', 20013, $checkResult);
        }

        $data = $this->input;
        $page = isset($data['page']) ? $data['page'] : 1;
        $pageSize = isset($data['pageSize']) ? $data['pageSize'] : 10;
        $welfareService = new WelfareService();
        if (!is_array($this->channelIds)) {
            $channelIds = [$this->channelIds];
        } else {
            $channelIds = $this->channelIds;
        }
        $result = $welfareService->wefare($data['grant_type_id'], $data['game_id'],$channelIds, $this->channelList, $page, $pageSize, $data, true);
        if ($result['code'] == 20000) {
            $this->jsonResult([], 20000, '申请成功');
        } else {
            $this->jsonResult([], 20013, $result['msg']);
        }

    }

    public function getGameServer()
    {
        $game_id = $this->input('game_id', 0);
        if (!$game_id) {
            $this->jsonResult('', 20013, '请先选择游戏');
        }
        $list = model('GameServer')->where(['game_id' => $game_id])->field('id,servername as value,create_time')->order('id desc')->select();
        if ($this->input('grant_type_id') == 6) {
            foreach ($list as $k => $v) {
                $list[$k]['value'] = $v['value'] . ' (开服时间：' . date('Y-m-d', $v['create_time']) . ')';
            }
        }

        $this->jsonResult(['data' => $list], 20000, '获取列表成功');
    }

    public function getChannelList()
    {
        $this->jsonResult(['data' => $this->channelList], 20000, '获取列表成功');
    }

    public function getWelfareGameList()
    {



        if($this->_channelLevel == 0){
            $meue['game_list'] = model('Common/Game')->getAllByCondition('id,name', ['game_kind' => 1, 'cooperation_status' => ['neq', 0], 'is_welfare' => 2], 'id desc', 'self');
        }else if($this->_channelLevel == 1){

            $channelGame = model('common/ChannelGame')->where(['channel_id'=>$this->_channelId])->column('game_id');

        }else {

            $channel = model('common/Channel')->field('id')->whereIn('id',$this->_channelIdPath)->where(['level'=>1])->find();
            $channelGame = model('common/ChannelGame')->where(['channel_id'=>$channel['id']])->column('game_id');

        }

        if(!$channelGame){
            $meue['game_list'] =  model('Common/Game')->where(['game_kind'=>1, 'is_welfare' => 2])
                ->whereOr(['cooperation_status'=>1, 'is_welfare' => 2])->field('id,name')->select();
        }

        $where = ['cooperation_status'=>2,'id'=>['in',$channelGame], 'is_welfare' => 2];
        $meue['game_list'] =  model('Common/Game')->where(['game_kind'=>1, 'is_welfare' => 2])
            ->where(['cooperation_status'=>1])->whereOr(function($query) use($where){
                $query->where($where);
            } )->field('id,name')->order('id desc')->select();


        $this->jsonResult($meue, 20000, '获取搜索菜单列表成功');
    }

    public function getWelfareList()
    {
        $game_id = $this->input('game_id', 0);
        $grant_type_id = $this->input('grant_type_id', 0);
        $is_show = $this->input('is_show', 0);
        if ($list = model('Welfare')
            ->where(function ($query) use ($grant_type_id,$is_show) {
                if ($grant_type_id > 0) {
                    $query->where('grant_type_id', $grant_type_id);
                }
                if($is_show == 0){
                    $query->where('is_show',1);
                }
            })
            ->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $game_id))->field('id,name as value')->select()) {
            $this->jsonResult($list, 20000, '获取列表成功');
        } else {
            $this->jsonResult([], 20013, '暂未配置福利');
        }
    }

    public function getWelfareGrantData()
    {
        $welfare_grant_id = $this->input('welfare_grant_id', 0);
        $pageSize = $this->input('pageSize', 10);
        $page = $this->input('page', 1);
        $status = $this->input('status', 0);
        $where = [];
        if ($status > 0) {
            $where['a.status'] = $status;
        }
        if ($list = model('welfareGrantData')
            ->alias('a')
            ->join('cy_welfare_resources b', 'a.welfare_resources_id=b.id')
            ->where('welfare_grant_id', $welfare_grant_id)
            ->where($where)
            ->order('grant_date asc,grant_time desc')
            ->field('a.*,b.type_id,b.name')->paginate(['list_rows' => $pageSize, 'page' => $page])
            ->toArray()) {
            foreach ($list['data'] as $k => $v) {
                $list['data'][$k]['type_name'] = $v['type_id'] == 1 ? '接口发放' : '兑换码发放';
                $list['data'][$k]['status_str'] = $v['status'] == 1 ? '未发放' : '已发放';
            }
            $this->jsonResult($list, 20000, '获取列表成功');
        } else {
            $this->jsonResult([], 20013, '暂未配置福利');
        }
    }
}
