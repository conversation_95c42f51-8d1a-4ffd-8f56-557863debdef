<?php

/**
 * 转账接口控制器
 * 
 */

namespace app\guildapi\controller;

use app\guildapi\controller\Guild;
use app\common\model\CoinTransfer as CoinTransferModel;
use app\common\library\WeixinPay;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;

class CoinTransfer extends Guild  {
    
    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;

        $this->redis    = \think\Cache::store('default')->handler();
    }
  
     /**
     * 转账管理
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有此功能
		if(!in_array($this->_channelLevel,[1,2])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= $this->input('list_rows',10);
        $page					= $this->input('page',1);
		$type					= $this->input('type','0','intval');    //1(转账收入),2(转账支出)
        $account				= $this->input('account','','trim');    //收入账号
        $apply_begin_time		= $this->input('apply_begin_time','','trim');
        $apply_end_time			= $this->input('apply_end_time','','trim');
        $finish_begin_time		= $this->input('finish_begin_time','','trim');
        $finish_end_time		= $this->input('finish_end_time','','trim');

        $condition					= [];
		if($account){
			$condition['det_username']	= $account;
		}

		if($type==1){
			$condition['det_userid'] = $this->_channelId;
		}
		else if($type==2){
			$condition['src_channel_id'] = $this->_channelId;
		}

	    //申请开始时间和结束时间不为空时
        if ($apply_begin_time != '' && $apply_end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($apply_begin_time)],
                ['<=', strtotime($apply_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($apply_begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($apply_begin_time)];
        } //结束时间不为空时
        elseif ($apply_end_time != '') {
            $condition['create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
        }

        //到账开始时间和结束时间不为空时
        if ($finish_begin_time != '' && $finish_end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($finish_begin_time)],
                ['<=', strtotime($finish_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($finish_begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($finish_begin_time)];
        } //结束时间不为空时
        elseif ($finish_end_time != '') {
            $condition['create_time'] = ['<=', strtotime($finish_end_time . ' 23:59:59')];
        }
	//	var_dump($condition);

        $rechargeList = model('CoinTransfer')
						->where($condition)
						->whereRaw('src_channel_id='.$this->_channelId.' or det_userid='.$this->_channelId.' and transfer_kind=1')
						->paginate(['list_rows'=>$list_rows,'page'=>$page])
						->toArray();
	//	echo model('ChannelRecharge')->getLastSql()."-----lastsql------<br>";

		$this->jsonResult($rechargeList, 20000, '获取列表成功');
    }

    /**
     * 转账处理
     */
    public function transfer()
    {
		if ($this->request->isPost()) {
			$account		= $this->input('account','','trim');			//转账账号
			$amount			= $this->input('amount',0,'intval');			//转账金额
			$type			= $this->input('type','','trim');               //转账方式:single(单个转账),batch(批量转账)
			$transfer_kind  = $this->input('transfer_kind','','trim');      //收款对象:1(转给玩家),2(转给渠道)
			$game_id		= $this->input('game_id',0,'intval');           //游戏ID
			$server_id		= $this->input('server_id','','trim');            //区服ID
			$server_name	= $this->input('server_name','','trim');       //区服名称
			$remark			= $this->input('remark','','trim');                   //备注
		
			$orderid    = 'T'.makeOrderid();
			$batchNo	= 'Batch-'.$this->_channelId.'-'.date('YmdHis');
			
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1,2])){
				$this->jsonResult('', 0, '您无权限进行转账操作');
			}
			if(!in_array($type,['single','batch'])){
				$this->jsonResult('', 0, '请正确选择转账方式');
			}
			else{
				if(!in_array($transfer_kind,[1,2])){
					$this->jsonResult('', 0, '请选择收款对象');
				}
			}
			if($type=='single'){   //单个转账
				if(!$account){
					$this->jsonResult('', 0, '请输入收款账号');
				}
				if($transfer_kind==1){  //转给玩家
					if(!$game_id){
						$this->jsonResult('', 0, '请选择要充值的游戏');
					}
					else{
						$gameInfo = model('Game')->where(['id'=>$game_id])->find();
						if(!$gameInfo){
							$this->jsonResult('', 0, '该游戏不存在');
						}
						else{   //判断能否对该玩家进行充值
							$chkHasPriv = chkTransferPlayer($this->_channelId,$account);
						}
					}
					
				} else{  //转给渠道
					$chkHasPriv = chkTransferChannel($this->_channelId,$account);
				}
			} else {      //批量转账
			}


			if(abs(intval($amount*100)/100-$amount) > 0.01){
				$this->jsonResult('', 0, '请正确填写支付金额！');
			} else {
				//判断支付金额
				$condis = array();
				$condis['channel_id'] = $this->_channelId;
				$condis['begin_time'] = array('elt',$this->nowTime);
				$condis['end_time'] = array('egt',$this->nowTime);
				$rechargeRatio = model("ChannelRechargeRatio")->where($condis)->find();
				if(!empty($rechargeRatio) && $rechargeRatio['ratio']){
					$rechargeRatio = $rechargeRatio['ratio'];
				}
				else{
					$rechargeRatio = 100;
				}
				$discountAmt = floatval($amount*$rechargeRatio/100);
				if($discountAmt <> $amount){
					$this->jsonResult('', 0, '充值金额异常,请刷新页面后重新充值');
				}
			}

			$channelInfo = model("Channel")->where(['id'=>$this->_channelId])->find();
            $recharge_type = 3; // 结算币充值
			if($channelInfo && $channelInfo['parent_id']){
				if($recharge_type==3 && $channelInfo['js_amount']<$amount){
					$this->jsonResult('', 0, '您的结算币余额不足');
				}
			}
			else{
				$this->jsonResult('', 0, '您的账号有异常,请联系管理员');
			}

			//指定时间内，禁止重复下单
			if(!requestDuplicateCheck('recharge_pay_duplicate_'.$this->_channelId,$this->payRequestLimit)){
				$this->jsonResult('', 0, '充值请求过多，请于'.$this->payRequestLimit.'s以后，再次进行充值操作');
			}

			$rechargeData = array();
			$rechargeData['orderid']		= $orderid;
			$rechargeData['channel_id']     = $this->_channelId;
			$rechargeData['channel_name']   = $this->_channelName;
			if($this->_channelLevel==1){
				$rechargeData['send_channel_id']     = '0';
				$rechargeData['send_channel_name']   = '平台';
			} else if($this->_channelLevel==2) {
				$parentChannelInfo = model("Channel")->where(['id'=>$channelInfo['parent_id']])->find();
				$rechargeData['send_channel_id']     = $parentChannelInfo['id'];
				$rechargeData['send_channel_name']   = $parentChannelInfo['name'];
			}

            $paytype = 1;
			$rechargeData['amount']			= $amount;
			$rechargeData['real_amount']	= $amount;
			$rechargeData['recharge_type']  = $recharge_type;
			$rechargeData['paytype']		= $paytype;
			$rechargeData['out_order_no']	= $batchNo;
			$rechargeData['remark']			= $remark;
			$rechargeData['status']			= 0;
			$rechargeData['create_time']	= time();

			// 启动事务
			Db::startTrans();
			
			try{
				$result = [];   //返回值
				
				//结算币充值
				if($recharge_type==3){  
					$rechargeData['status'] = 1;
					$insertRechargeId = model("ChannelRecharge")->insert($rechargeData);
					if(!$insertRechargeId){
						throw new Exception("创建订单失败! ");
					}

					$detData = array();
                    $detData['channel_id']      = $this->_channelId;
                    $detData['channel_name']    = $this->_channelName;
                    $detData['change_amount']	= -$amount;
                    $detData['account_type']	= 2;   //结算账户
                    $detData['type']			= 7;   //结算充值
                    $detData['out_orderid']		= $orderid;
                    $detData['create_time']     = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
					if ( !$insertDetId) {
						throw new Exception("添加账户变动明细失败");
					}
					$updData = array();
					$updData['js_amount'] = Db::raw("js_amount-".$amount);
					$updData['update_time'] = time();
					$updResult = model('Channel')->where(['id'=>$this->_channelId,'js_amount'=>array('egt',$amount)])->update($updData);
					if (!$updResult) {
						throw new Exception("账户金额变动失败");
					}

					$detData = array();
                    $detData['channel_id']      = $this->_channelId;
                    $detData['channel_name']    = $this->_channelName;
                    $detData['change_amount']	= $amount;
                    $detData['account_type']	= 1;   //通用账户
                    $detData['type']			= 4;   //充值收入
                    $detData['out_orderid']		= $orderid;
                    $detData['create_time']     = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
					if ( !$insertDetId) {
						throw new Exception("添加账户变动明细失败");
					}
					$updData = array();
					$updData['amount'] = Db::raw("amount+".$amount);
					$updData['update_time'] = time();
					$updResult = model('Channel')->where(['id'=>$this->_channelId])->update($updData);
					if (!$updResult) {
						throw new Exception("账户金额变动失败");
					}
				}
				else{
					$insertRechargeId = model("ChannelRecharge")->insert($rechargeData);
					if(!$insertRechargeId){
						throw new Exception("创建订单失败! ");
					}
				}
				
				$this->redis->del('recharge_pay_duplicate_'.$this->_channelId);
				
				// 提交事务
				Db::commit();
				
			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();
				
				$this->jsonResult('', 0, '订单生成失败'.$e->getMessage());
			}
			
			$result['orderid']      = $orderid;
			
			$this->jsonResult($result, 20000, '订单生成成功');
			exit;
		} else {
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
}
