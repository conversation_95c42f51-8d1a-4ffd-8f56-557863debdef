<?php

/**
 * 渠道充值折扣接口
 * 
 */

namespace app\guildapi\controller;

use app\Guildapi\controller\Guild;
use app\common\model\ChannelRecharge as ChannelRechargeModel;
use think\Db;
use think\Config;
use app\common\model\Setting;
use think\Exception;

class ChannelRechargeRatio extends Guild  {
    
    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        
        $this->nowTime  = NOW_TIMESTAMP;
        $this->redis    = \think\Cache::store('default')->handler();
    }
    
    /**
     * 获取列表
     * @return [type] [description]
     */
    public function index()
    {
		//判断当前账号是否有充值功能
		if(!in_array($this->_channelLevel,[1])){
			$this->jsonResult('', 0, '您无权限使用该功能');
		}

        $list_rows				= input('list_rows',10);
        $page					= input('page',1);
        $account			= $this->input('account','','trim');

        $where					= [];
		$where['c.parent_id']	= $this->_channelId;
		$where['c.level']		= 2;
		if($account){
			$condition['r.channel_name']	= $account;
		}
        $ratioList = Db::name('nw_channel_recharge_ratio')->alias('r')
						->join('nw_channel c', 'r.channel_id = c.id','inner')
						->field('r.*')
						->where($where)
						->paginate(['list_rows'=>$list_rows,'page'=>$page])
						->toArray();

		$this->jsonResult($ratioList, 20000, '获取列表成功');
    }

    /**
     * 批量添加处理
     */
    public function doAdd()
    {
		if ($this->request->isPost()) {
			$accounts		= $this->input('accounts');  //多个账号用逗号分隔
			$ratio			= $this->input('ratio');                //充值比例
			$begin_time		= $this->input('begin_time');              //开始时间
			$end_time		= $this->input('end_time');				  //结束时间
			$remark			= $this->input('remark');                 //备注
			
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您不能进行充值比例设置');
			}
			if(strtotime($begin_time) > strtotime($end_time)){
				$this->jsonResult('', 0, '请选择正确的时间范围');
			}
			if(!preg_match("/^[1-9][0-9]*$/" ,$ratio) || $ratio<=0 || $ratio>=100){
				$this->jsonResult('', 0, '充值比例必须是0-100之间的整数');
			}
			//判断账号是否可以充值
			$ActiveAccounts = array();
			$flag = true;
			$error_msg = '';
			$accountArrs = explode(',',$accounts);
			while(list($key,$val)=@each($accountArrs)){
				if($val){
					$channelInfo =  model('Channel')->where(['name' => trim($val)])->find();
					if(empty($channelInfo)){
						$flag = false;
						$error_msg .= trim($val).' 账号不存在或非您子公会;';
					}
					else if($channelInfo['level']<>2 || $channelInfo['parent_id']<>$this->_channelName){
						$flag = false;
						$error_msg .= trim($val).' 账号不存在或非您子公会;';
					}
					else{
						$existRatioYn = model('ChannelRechargeRatio')->where(['channel_id' => $channelInfo['id']])->count();
						if($existRatioYn){
							$flag = false;
							$error_msg .= trim($val).' 该账号已配置充值比例;';
						}
					}
					$ActiveAccounts[] = $channelInfo['id'];
				}
			}
			if(!$flag){
				$this->jsonResult('', 0, $error_msg);
			}
		
			if($ActiveAccounts){
				// 启动事务
				Db::startTrans();
				try{
					while(list($key,$val)=@each($ActiveAccounts)){
						$channelInfo = model('Channel')->where(['id'=>$val])->find();
						if(empty($channelInfo) || $channelInfo['parent_id']<>$this->_channelId || $channelInfo['level']<>2){
							throw new Exception("存在账号异常,请核对用户后再进行操作");
						}
						$ratioData = array();
						$ratioData['channel_id']		= $channelInfo['id'];
						$ratioData['channel_name']		= $channelInfo['name'];
						$ratioData['ratio']				= $ratio;
						$ratioData['begin_time']		= strtotime($begin_time);
						$ratioData['end_time']			= strtotime($end_time);
						$ratioData['remark']			= $remark;
						$ratioData['create_time']		= time();
						$ratioData['update_time']		= time();
						$ratioData['admin_type']		= '2';
						$ratioData['admin_name']		= $this->_channelName;
						//添加充值比例记录
						$insertRatioId = model('ChannelRechargeRatio')->insert($ratioData);
						if ( !$insertRatioId) {
							throw new Exception("添加充值比例失败");
						}

						$logData['ratio_id']		= $insertRatioId;
						$logData['channel_id']      = $channelInfo['id'];
						$logData['ratio']			= $ratio;
						$logData['begin_time']		= $begin_time;
						$logData['end_time']		= $end_time;
						$logData['remark']			= $remark;
						$logData['type']			= 1;
						$logData['create_time']		= time();
						$logData['admin_type']		= 2;
						$logData['admin_name']		= $this->_channelName;
						$insertLogId = model("ChannelRechargeRatioLog")->insert($logData);
						if(!$insertLogId){
							throw new Exception("创建充值比例日志失败! ");
						}
					}
					// 提交事务
					Db::commit();
					
				} catch (\Exception $e) {
					// 回滚事务
					Db::rollback();
					
					$this->jsonResult('', 0, '充值比例添加失败'.$e->getMessage());
				}
			}
			else{
				$this->jsonResult('', 0, '您正确输入账号');
			}
						
			$this->jsonResult('', 20000, '充值比例添加成功');
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
		}
    }

    /**
     * 修改资料
     */
    public function edit()
    {
		if ($this->request->isPost()) {
			$id		= intval($this->input('id'));	//渠道ID
			
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您不能进行充值比例设置');
			}
			if(!$id){
				$this->jsonResult('', 0, '请选择要修改的记录');
			}
	
			$ratioInfo = model('ChannelRechargeRatio')->where(['id'=>$id])->find();
			if($ratioInfo){
				$channelInfo = model('Channel')->where(['id'=>$ratioInfo['channel_id']])->find();
				if(empty($channelInfo) || $channelInfo['parent_id']<>$this->_channelId || $channelInfo['level']<>2){
					$this->jsonResult('', 0, '您无权限操作');
				}
				$this->jsonResult($ratioInfo, 20000, '获取充值比例记录成功');
			}
			else{
				$this->jsonResult('', 0, '该充值比例记录不存在');
			}
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
		}
    }

    /**
     * 修改保存
     */
    public function doEdit()
    {
		if ($this->request->isPost()) {
			$id				= intval($this->input('id'));			//记录ID
			$ratio			= $this->input('ratio');                //充值比例
			$begin_time		= $this->input('begin_time');              //开始时间
			$end_time		= $this->input('end_time');				  //结束时间
			$remark			= $this->input('remark');                 //备注
			
			//判断当前账号是否有充值权限
			if(!in_array($this->_channelLevel,[1])){
				$this->jsonResult('', 0, '您不能进行充值比例设置');
			}
			if(strtotime($begin_time) > strtotime($end_time)){
				$this->jsonResult('', 0, '请选择正确的时间范围');
			}
			if(!preg_match("/^[1-9][0-9]*$/" ,$ratio) || $ratio<=0 || $ratio>=100){
				$this->jsonResult('', 0, '充值比例必须是0-100之间的整数');
			}
			
			if(!$id){
				$this->jsonResult('', 0, '请选择要修改的记录');
			}

			$ratioInfo = model('ChannelRechargeRatio')->where(['id'=>$id])->find();
			if($ratioInfo){
				$channelInfo = model('Channel')->where(['id'=>$ratioInfo['channel_id']])->find();
				if(empty($channelInfo) || $channelInfo['parent_id']<>$this->_channelId || $channelInfo['level']<>2){
					$this->jsonResult('', 0, '无该记录或您无权限操作');
				}
			}
		
			// 启动事务
			Db::startTrans();
			try{
				$ratioData = array();
				$ratioData['ratio']				= $ratio;
				$ratioData['begin_time']		= strtotime($begin_time);
				$ratioData['end_time']			= strtotime($end_time);
				$ratioData['remark']			= $remark;
				$ratioData['update_time']		= time();
				$ratioData['admin_type']		= '2';
				$ratioData['admin_name']		= $this->_channelName;
				//添加充值比例记录
				$updRatioResult = model('ChannelRechargeRatio')->where(['id'=>$ratioInfo['id']])->update($ratioData);
				if ( !$updRatioResult) {
					throw new Exception("修改充值比例失败");
				}
				$logData['ratio_id']		= $ratioInfo['id'];
				$logData['channel_id']      = $channelInfo['id'];
				$logData['ratio']			= $ratio;
				$logData['begin_time']		= strtotime($begin_time);
				$logData['end_time']		= strtotime($end_time);
				$logData['remark']			= $remark;
				$logData['type']			= 1;
				$logData['create_time']		= time();
				$logData['admin_type']		= 2;
				$logData['admin_name']		= $this->_channelName;
				$insertLogId = model("ChannelRechargeRatioLog")->insert($logData);
				if(!$insertLogId){
					throw new Exception("新增充值比例日志失败! ");
				}
				// 提交事务
				Db::commit();
					
			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();
					
				$this->jsonResult('', 0, '充值比例修改失败'.$e->getMessage());
			}
			$this->jsonResult('', 20000, '充值比例修改成功');
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
		}
    }
}
