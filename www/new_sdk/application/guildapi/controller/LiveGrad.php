<?php
/**
 * 直播部门抓取数据整理
 */

namespace app\guildapi\controller;

use app\api\validate\Pay;
use app\common\logic\SubPackage as SubChannel;
use think\Db;
use think\Env;
use think\Exception;
use think\Controller;

class LiveGrad extends Guild
{
    protected $_channelId; // 渠道ID
    protected $_level;     // 层级：0(B+账号),1(B账号)，2(B-账号),3(推广员)

    protected function _initialize()
    {
        parent::_initialize();
        $guildInfo = session('guild_info');
        $this->_channelId = $guildInfo['channel_id'];
        //        $this->_level = $guildInfo['level'];

        log_message("input: ".json_encode(input()), 'log', LOG_PATH . 'live_grad_log/');
    }

    /**
     * 添加推广员
     *
     * @param $channel_id C级的渠道ID（用于判断是否有有上级，没有则新建）
     *
     * @return \think\response\Json|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function userCreate()
    {
        $channel_id = $this->request->post('channel');
        $level = $this->request->post('level');
        $this->_level = $level;
        if ($channel_id != ""){
            $this->_channelId = $channel_id;
        }

        Db::startTrans();
        try {
            // 判断B级账户，没有则创建
            //            if ($channel_id) {
            //                $c_info = Db::name('nw_channel')->where(['id' => $channel_id, 'level' => 3])->find();
            //                if (!$c_info){
            //                    throw new Exception("渠道号错误");
            //                }
            //                $c_info = Db::name('nw_channel')->where(['id' => $c_info['parent_id'], 'level' => 2])->find();
            //                $parent_id = $c_info['id'];
            //                $parent_name = $c_info['name'];
            //            }else{
            //                $b_res = $this->_judgeUserCreateB();
            //                $parent_id = $b_res['data']['id'];
            //                $parent_name = $b_res['data']['name'];
            //            }
            if ($level != 3){
                $b_res = $this->_judgeUserCreateB();
            }else{
                $c_info = Db::name('nw_channel')->where(['id' => $channel_id])->find();
                $b_res = $this->_judgeUserCreateC($channel_id,$c_info['name']);
            }


            // 查询B级账户信息
            // $b_info = Db::name('nw_channel')->where(['id' => $parent_id, 'level' => 2])->find();
            // dump("## b_info: ", $b_info);
            // if($c_info['code'] != 20000){
            //     return $this->jsonResult(['data' => '', 'code' => $c_info['code'], 'msg' => $c_info['msg']]);
            // }
            // 创建C级账户
            //            $c_info = $this->_judgeUserCreateC($parent_id,$parent_name);
            // dump("## c_info: ", $c_info);
            if($b_res['code'] != 20000){
                // return $this->jsonResult(['data' => '', 'code' => $c_info['code'], 'msg' => $c_info['msg']]);
                throw new Exception($b_res['msg']);
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            // return ['data' => '', 'code' => 10015, 'msg' => "添加失败： " . $e->getMessage()];
            $this->jsonResult('', 10015, "添加失败： " . $e->getMessage());
        }

        return $this->jsonResult($b_res['data'], 20000, 'success');
    }


    private function _judgeUserCreateB(){
        $channel = Db::name('nw_channel')->where(['id' => $this->_channelId])->find();
        if ($this->_level == 1){
            $id_path = ','.$this->_channelId.',';
        }else{
            $id_path = ',' . $channel['parent_id'] . ','.$this->_channelId.',';
        }


        //        $parent_id = Db::name('nw_channel')->where(['id' => $this->_channelId])->value('parent_id');


        // 获取最新用户名及其标识

        $user_pwd = random(8);
        if ($this->_level == 1){
            $newChannelName = Db::name('nw_channel')->where(['parent_id' => $this->_channelId, 'id_path' => $id_path, 'level' => $this->_level, 'name' => ['like', 'qmsy%']])->order('create_time', 'desc')->value('name');
            $user_name = 'qmsy00001';
            if (strpos($newChannelName, 'qmsy') !== false) {
                $startChannel = explode('qmsy', $newChannelName);
                if (is_numeric($startChannel[1]) === true) {
                    $channelNum = $startChannel[1] + 1;
                    $user_name = 'qmsy' . str_pad($channelNum, 5, "0", STR_PAD_LEFT);
                }
            }
        }else{
            $user_name = $channel['name'].'t1';
            $newChannelName = Db::name('nw_channel')->where(['parent_id' => $this->_channelId, 'id_path' => $id_path, 'level' => $this->_level, 'name' => ['like', $channel['name'].'%']])->order('create_time', 'desc')->value('name');
            if (strpos($newChannelName, $channel['name'].'t') !== false) {
                $startChannel = explode($channel['name'].'t', $newChannelName);
                if (is_numeric($startChannel[1]) === true) {
                    $channelNum = $startChannel[1] + 1;
                    $user_name = $channel['name'].'t'.$channelNum;
                }
            }
        }

        $channelModel = model('channel');
        //        return $user_name;
        // Db::startTrans();
        try {
            //1. 添加渠道信息

            $channelInfo = $channelModel->where(['name' => $user_name])->find();
            if ($channelInfo) {
                throw new Exception("该渠道信息已存在");
            }

            $userInfo = Db::name('nw_channel_admin')->field('id')->where(['username' => $user_name])->find();
            if ($userInfo) {
                throw new Exception("用户名已存在");
            }


            $channelModel->save([
                'name'               => $user_name,
                // 'replacement_status' => $data['replacement_status'],//代充
                // 'share_show'         => $data['share_show'],//分成比例
                'parent_id'          => $this->_channelId,
                'id_path'            => $id_path,
                'create_time'        => NOW_TIMESTAMP,
                'level'              => $this->_level,
                'flag'               => 3,
                'status'             => 1,
                'gh_status'          => 1,
            ]);

            if ( !$channelModel->id ) {
                //   return json(['data'=>'','code'=>10015,'msg'=>"添加渠道账号失败"]);
                throw new Exception("添加渠道账号失败");
            }

            //添加渠道信息
            //            $id = $channelModel->insertGetId([
            //                'name' => $user_name,
            //                'parent_id' => $this->_channelId,
            //                'id_path' => $id_path,
            //                'create_time' => NOW_TIMESTAMP,
            //                'level' => 1,
            //                'flag' => 3,
            //                'status' => 1
            //            ]);
            //            if (!$id) {
            //                throw new Exception("添加渠道账号失败");
            //            }

            $cont['username'] = $user_name;
            $cont['password'] = mg_password($user_pwd);
            $cont['status'] = 1;
            $cont['channel_id'] = $channelModel->id;
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['password_update_time'] = NOW_TIMESTAMP;
            $b_id = Db::name('nw_channel_admin')->insertGetId($cont);
            if ($b_id === false) {
                throw new Exception("添加渠道账号失败");
            }

            // Db::commit();
        } catch (\Exception $e) {
            // Db::rollback();
            return ['data' => '', 'code' => 10015, 'msg' => "添加失败： " . $e->getMessage().' line'.$e->getLine()];
        }

        return [
            'data' => [
                'channel' => $channelModel->id,
                'name' => $user_name,
                'password'=>$user_pwd
            ],
            'code' => 20000,
            'msg'  => "",
        ];
    }

    /**
     * 返回封装后的 API json数据到客户端
     * @access protected
     * @param mixed $data 要返回的数据
     * @param int $code 返回的 code
     * @param mixed $msg 提示信息
     * @param $isExit boolean 是否强制退出,默认true
     * @return void
     *
     */
    public function jsonResult($data, $code = 0, $msg = '', $isExit = true)
    {
        $result = [
            'code' => $code,
            'msg' => $msg,
            'time' => request()->server('REQUEST_TIME'),
            'data' => $data,
        ];

        log_message("result: ".json_encode($result), 'log', LOG_PATH . 'live_grad_log/');

        echo json_encode($result);

        //为了使用fastcgi_finish_request等函数时，后续的执行能够继续生效
        if ($isExit) exit;
    }

    /**
     * 创建C级账户
     *
     * @param $parent_id B级关联ID
     * @param $parent_name B级用户名
     *
     * @return array
     */
    private function _judgeUserCreateC($parent_id, $parent_name){
        $id_path = Db::name('nw_channel')->where(['id'=>$parent_id])->value('id_path').$parent_id.',';
        // Db::startTrans();
        try {

            // 获取最新用户名及其标识
            $user_name = $parent_name.'-1';
            $user_pwd = random(8);
            $newChannelName = Db::name('nw_channel')->where(['parent_id' => $parent_id, 'level' => 3, 'name' => ['like', 'qmsy%']])->order('create_time', 'desc')->value('name');
            if (strpos($newChannelName, 'qmsy') !== false) {
                $startChannel = explode('-', $newChannelName);
                if (is_numeric($startChannel[1]) === true) {
                    $channelNum = $startChannel[1] + 1;
                    $user_name = $parent_name .'-'. $channelNum;
                }
            }

            //1. 添加渠道信息
            $channelModel = model('channel');
            $channelInfo = $channelModel->where(['name' => $user_name])->find();
            if ( $channelInfo ) {
                throw new Exception("该渠道信息已存在");
            }

            $userInfo = Db::name('nw_channel_admin')->field('id')->where(['username' => $user_name])->find();
            if ($userInfo) {
                throw new Exception("用户名已存在");
            }

            //添加渠道信息
            $id = $channelModel->insertGetId([
                'name'        => $user_name,
                'parent_id'   => $parent_id,
                'id_path'     => $id_path,
                'create_time' => NOW_TIMESTAMP,
                'level'       => 3,
                'flag'        => 3,
                'status'      => 1,
                'gh_status'   => 1,
            ]);
            if ( !$id) {
                throw new Exception("添加渠道账号失败");
            }


            $cont['username']             = $user_name;
            $cont['password']             = mg_password($user_pwd);
            $cont['status']               = 1;
            $cont['channel_id']           = $id;
            $cont['create_time']          = NOW_TIMESTAMP;
            $cont['password_update_time'] = NOW_TIMESTAMP;

            $c_id = Db::name('nw_channel_admin')->insertGetId($cont);
            if ($c_id === false) {
                throw new Exception("添加渠道账号失败");
            }

            // Db::commit();
        } catch (\Exception $e) {
            // Db::rollback();
            return ['data'=>'','code'=>10016,'msg'=>"添加失败： " . $e->getMessage()];
        }

        return [
            'data' => [
                'channel' => $id,
                'name' => $user_name,
                'password' => $user_pwd,
            ],
            'code' => 20000,
            'msg' =>"",
        ];
    }

    // 分包
    public function gamePack()
    {
        $game_id = $this->request->post('yxid');
        $channel_id = $this->request->post('channel');
        // $status = $this->request->post('status');
        if (!$game_id) {
            return $this->jsonResult('', 10015, '非法操作：缺少游戏ID！');
        }
        if (!$channel_id) {
            return $this->jsonResult('', 10015, '非法操作：缺少渠道ID！');
        }

        $gameInfo = model('Game')->field(['id', 'channel_version', 'type', 'cooperation_status', 'channel_version', 'pinyin'])->where(['id' => $game_id])->find();
        $sdkInfo = model('SdkGameList')->field(['id', 'upload_status', 'channel_version', 'filename'])->where(['gameid' => $game_id, 'channel_id' => $channel_id])->find();
        // upload_status=文件状态：0=待处理、1=已处理、2=待审核、3=申请拒绝、4=分包中、5=打包失败、9=已禁用

        // 游戏不存在
        if (!$gameInfo) {
            return $this->jsonResult('', 10015, '当前游戏不存在！');
        }
        // if (!$sdkInfo) {
        //     $res = $this->package($game_id, $channel_id, $gameInfo['type']);
        //     if ($res['code'] == 0) {
        //         return $this->jsonResult('', 20001, '分包中....');
        //     }
        //     return $this->jsonResult('', 10015, "打包失败：" . $res['msg']);
        //     // return $this->jsonResult('', 10015, '当前游戏渠道不存在！');
        // }
        // 游戏关闭
        if ($gameInfo['cooperation_status'] != 1) {
            return $this->jsonResult('', 10015, '当前游戏未上线！');
        }
        // 渠道禁用
        if (!empty($sdkInfo) && $sdkInfo['upload_status'] == 9) {
            return $this->jsonResult('', 10017, '当前渠道已禁用！');
        }
        $pack_status = false;
        $msg = '打包失败';

        if ($sdkInfo['upload_status'] == 4 || $sdkInfo['upload_status'] == 1) {
            $pack_status = true;
            $msg = '打包成功';
        }

        $filename = $sdkInfo['filename'];
        if (empty($sdkInfo) || $gameInfo['channel_version'] != $sdkInfo['channel_version'] || $sdkInfo['upload_status'] == 5) {
            $result = $this->package($game_id, $channel_id, $gameInfo['type'], 1);
            if ($result['code'] == 1) {
                $filename = $result['data']['filename'];
                $pack_status = true;
            } else {
                $msg .= ': ' . $result['msg'];
            }
        }

        if ($pack_status) {
            $short_link = model('promotion_short_link')->where(['game_id' => $game_id, 'channel_id' => $channel_id, 'type' => 1])->value('short_link');
            if (!empty($short_link)) {
                $share_url = T_DOMAIN . '/' . $short_link;
            } else {
                $share_url = T_DOMAIN . '/game_promotion/index/game_id/' . $game_id . '/channel_id/' . $channel_id . '/package_type/' . $sdkInfo['package_type'];
            }
            $app_link = '';
            if ($gameInfo['type'] == 2) {
                $app_link = APK_DOWN_DOMAIN_NEW . '/sygame/' . $gameInfo['pinyin'] . '/' . $filename;
            }

            return $this->jsonResult([
                'tgy_link' => $share_url,
                'app_link' => $app_link,
            ], 20002, 'success');
        }
        return $this->jsonResult('', 10015, $msg);
    }

    /**
     * 分包
     *
     * @param $game_id    游戏ID
     * @param $channel_id 渠道ID
     * @param $platform   游戏平台: 0=安卓, 1=IOS
     * @param $platform   分包方式: 0=异步(队列), 1=同步
     *
     * @return bool
     */
    private function package($game_id, $channel_id, $platform, $pack_type = 1)
    {
        $subChannel = new SubChannel;
        // $res = $subChannel->index($channel_id, $game_id, 'ydd_fb', $platform, 0);
        return $subChannel->index($channel_id, $game_id, 'ydd_fb', $platform, 0, $pack_type);
    }
}
