<?php

/**
 * 渠道充值接口控制器
 *
 */

namespace app\guildapi\controller;


use app\common\library\WeixinPay;
use app\service\PayService;
use think\Db;
use think\Config;
use think\Exception;

class ChannelRechargeCoin extends Guild
{

    protected $nowTime;
    protected $payRequestLimit = 5;        //重复下单的限制时间
    protected $redis;                       //redis的句柄对象

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();

        $this->nowTime = NOW_TIMESTAMP;

        $this->redis = \think\Cache::store('default')->handler();
    }

    /**
     * 我的充值列表
     * @return [type] [description]
     */
    public function index()
    {
        //判断当前账号是否有此功能
        if (!in_array($this->_channelLevel, [1, 2])) {
            $this->jsonResult('', 0, '您无权限使用该功能');
        }

        $list_rows = $this->input('list_rows', 10);
        $page = $this->input('page', 1);
        $type = $this->input('type', '0', 'intval');    //1(充值收入),2(充值支出),3(直充),4(结算充值收入),5(结算充值支出)
        $account = $this->input('account', '', 'trim');    //收入账号
        $apply_begin_time = $this->input('apply_begin_time', '', 'trim');
        $apply_end_time = $this->input('apply_end_time', '', 'trim');
        $finish_begin_time = $this->input('finish_begin_time', '', 'trim');
        $finish_end_time = $this->input('finish_end_time', '', 'trim');

        $condition = [];
        if ($account) {
            $condition['channel_name'] = $account;
        }

        if ($type == 1) {
            $condition['channel_id'] = $this->_channelId;
            $condition['recharge_type'] = 1;
        } else if ($type == 2) {
            $condition['send_channel_id'] = $this->_channelId;
            $condition['recharge_type'] = 1;
        } else if ($type == 3) {
            $condition['channel_id'] = $this->_channelId;
            $condition['recharge_type'] = 2;
        } else if ($type == 4) {
            $condition['channel_id'] = $this->_channelId;
            $condition['recharge_type'] = 3;
        } else if ($type == 5) {
            $condition['send_channel_id'] = $this->_channelId;
            $condition['recharge_type'] = 3;
        }
        //申请开始时间和结束时间不为空时
        if ($apply_begin_time != '' && $apply_end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($apply_begin_time)],
                ['<=', strtotime($apply_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($apply_begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($apply_begin_time)];
        } //结束时间不为空时
        elseif ($apply_end_time != '') {
            $condition['create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
        }

        //到账开始时间和结束时间不为空时
        if ($finish_begin_time != '' && $finish_end_time != '') {
            $condition['finish_time'] = [
                ['>=', strtotime($finish_begin_time)],
                ['<=', strtotime($finish_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($finish_begin_time != '') {
            $condition['finish_time'] = ['>=', strtotime($finish_begin_time)];
        } //结束时间不为空时
        elseif ($finish_end_time != '') {
            $condition['finish_time'] = ['<=', strtotime($finish_end_time . ' 23:59:59')];
        }
        //	var_dump($condition);

        $rechargeList = model('ChannelRechargeCoin')
            ->where($condition)
            ->whereRaw('channel_id=' . $this->_channelId . ' or send_channel_id=' . $this->_channelId)
            ->order('id desc')
            ->paginate(['list_rows' => $list_rows, 'page' => $page])

            ->toArray();
        //	echo model('ChannelRecharge')->getLastSql()."-----lastsql------<br>";
        foreach ($rechargeList['data'] as $k=>$v){
            if($v['recharge_type'] == 1){
                $rechargeList['data'][$k]['recharge_type_str'] = '申请';
            }else if($v['recharge_type'] == 2){
                $rechargeList['data'][$k]['recharge_type_str'] = '直充';
            }else{
                $rechargeList['data'][$k]['recharge_type_str'] = '结算币充值';
            }
        }
        $this->jsonResult($rechargeList, 20000, '获取列表成功');
    }

    /**
     * 充值审核列表
     * @return [type] [description]
     */
    public function checklist()
    {
        //判断当前账号是否有此功能
        if (!in_array($this->_channelLevel, [1])) {
            $this->jsonResult('', 0, '您无权限使用该功能');
        }

        $list_rows = input('list_rows', 10);
        $page = input('page', 1);
        $account = $this->input('account', '', 'trim');
        $orderid = $this->input('orderid', '', 'trim');
        $apply_begin_time = $this->input('apply_begin_time', '', 'trim');
        $apply_end_time = $this->input('apply_end_time', '', 'trim');

        $condition = [];
        $condition['send_channel_id'] = $this->_channelId;
        if ($orderid) {
            $condition['orderid'] = $orderid;
        }
        if ($account) {
            $condition['channel_name'] = $account;
        }

        //申请开始时间和结束时间不为空时
        if ($apply_begin_time != '' && $apply_end_time != '') {
            $condition['create_time'] = [
                ['>=', strtotime($apply_begin_time)],
                ['<=', strtotime($apply_end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($apply_begin_time != '') {
            $condition['create_time'] = ['>=', strtotime($apply_begin_time)];
        } //结束时间不为空时
        elseif ($apply_end_time != '') {
            $condition['create_time'] = ['<=', strtotime($apply_end_time . ' 23:59:59')];
        }

        $rechargeList = model('ChannelRechargeCoin')
            ->where($condition)
            ->paginate(['list_rows' => $list_rows, 'page' => $page])
            ->toArray();
        $this->jsonResult($rechargeList, 20000, '获取列表成功');
    }

    /**
     * 充值审核(查看)
     */
    public function audit()
    {
        if ($this->request->isPost()) {
            $id = intval($this->input('id'));    //渠道ID

            if (!in_array($this->_channelLevel, [1])) {
                $this->jsonResult('', 0, '您不能进行该操作');
            }
            if (!$id) {
                $this->jsonResult('', 0, '请选择要审核的记录');
            }

            $rechargeInfo = model('ChannelRechargeCoin')->where(['id' => $id, 'send_channel_id' => $this->_channelId])->find();
            if (empty($rechargeInfo)) {
                $this->jsonResult('', 0, '记录不存在或您无权限操作');
            }

            $this->jsonResult($rechargeInfo, 20000, '获取记录成功');
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
        }
    }

    //充值审核(操作)
    function doAudit()
    {
        if ($this->request->isPost()) {
            $id = intval($this->input('id'));    //渠道ID
            $status = intval($this->input('status'));    //审核状态:1(审核通过),2(审核不通过)
            $remark = $this->input('remark', '', 'trim');    //审核备注

            if (!in_array($this->_channelLevel, [1])) {
                $this->jsonResult('', 0, '您不能进行该操作');
            }
            if (!$id) {
                $this->jsonResult('', 0, '请选择要审核的记录');
            }
            if ($status <> 1 && $status <> 2) {
                $this->jsonResult('', 0, '非法审核状态');
            }

            $rechargeInfo = model('ChannelRechargeCoin')->where(['id' => $id, 'send_channel_id' => $this->_channelId])->find();
            if (empty($rechargeInfo)) {
                $this->jsonResult('', 0, '记录不存在或您无权限操作');
            } else if ($rechargeInfo['status'] <> 0) {
                $this->jsonResult('', 0, '该记录已审核过');
            }

            $channelInfo = model("Channel")->where(['id' => $this->_channelId])->find();
            if ($channelInfo) {
                if ($channelInfo['amount'] < $rechargeInfo['amount']) {
                    $this->jsonResult('', 0, '您当前余额不足,不能进行审核发币');
                }
            } else {
                $this->jsonResult('', 0, '您的账号有异常,请联系管理员');
            }

            $auditData = array();
            $auditData['id'] = $id;
            $auditData['status'] = $status;
            $auditData['finish_time'] = NOW_TIMESTAMP;
            $auditData['check_remark'] = $remark;
            $auditData['check_admin_type'] = 2;
            $auditData['check_admin_name'] = $this->_channelName;

            Db::startTrans();
            try {
                //审核通过
                if ($status == 1) {

                    $detData = array();
                    $detData['channel_id'] = $this->_channelId;
                    $detData['channel_name'] = $this->_channelName;
                    $detData['change_amount'] = -$rechargeInfo['amount'];
                    $detData['account_type'] = 3;   //平台币账户
                    $detData['type'] = 5;   //充值支出
                    $detData['out_orderid'] = $rechargeInfo['orderid'];
                    $detData['create_time'] = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
                    if (!$insertDetId) {
                        throw new Exception("添加账户变动明细失败");
                    }
                    $updData = array();
                    $updData['amount_coin'] = Db::raw("amount_coin-" . $rechargeInfo['amount']);
                    $updData['update_time'] = time();
                    $updFromResult = model('Channel')->where(['id' => $this->_channelId, 'amount_coin' => array('egt', $rechargeInfo['amount'])])->update($updData);
                    if (!$updFromResult) {
                        throw new Exception("账户金额变动失败");
                    }

                    $detData = array();
                    $detData['channel_id'] = $rechargeInfo['channel_id'];
                    $detData['channel_name'] = $rechargeInfo['channel_name'];
                    $detData['change_amount'] = $rechargeInfo['amount'];
                    $detData['account_type'] = 3;   //平台币账户
                    $detData['type'] = 4;   //充值收入
                    $detData['out_orderid'] = $rechargeInfo['orderid'];
                    $detData['create_time'] = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
                    if (!$insertDetId) {
                        throw new Exception("添加账户变动明细失败");
                    }
                    $updData = array();
                    $updData['amount_coin'] = Db::raw("amount_coin+" . $rechargeInfo['amount']);
                    $updData['update_time'] = time();
                    $updToResult = model('Channel')->where(['id' => $rechargeInfo['channel_id']])->update($updData);
                    if (!$updToResult) {
                        throw new Exception("账户金额变动失败");
                    }

                    $auditData['finish_status'] = 1;
                    $result = model('ChannelRechargeCoin')->where(['id' => $id, 'status' => array('in', [0])])->update($auditData);
                    if (!$result) {
                        throw new Exception("充值审核失败");
                    }
                } else if ($status == 2) {  //审核不通过
                    $result = model('ChannelRechargeCoin')->where(['id' => $id, 'status' => array('in', [0])])->update($auditData);
                    if (!$result) {
                        throw new Exception("充值审核失败");
                    }
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->jsonResult('', 0, '添加失败： ' . $e->getMessage());
            }
            $this->jsonResult('', 20000, '审核成功');
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
        }
    }

    /**
     * 充值页面
     */
    public function recharge()
    {
        if ($this->request->isPost()) {
            //判断当前账号是否有充值权限
            if (!in_array($this->_channelLevel, [1, 2])) {
                $this->jsonResult('', 0, '您无权限进行充值相关操作');
            } else {
                $channelInfo = model("Channel")->field("id,name,level,parent_id,amount_coin,js_amount")->where(['id' => $this->_channelId])->find();
                if (!$channelInfo) {
                    $this->jsonResult('', 0, '账号异常,请重新登录后再试');
                }

                $adminInfo = model("ChannelAdmin")->field("id,username,channel_id,pay_password,secondary_password")->where(['id' => session('guild_info')['id'], 'channel_id' => $this->_channelId])->find();
                if (!$adminInfo) {
                    $this->jsonResult('', 0, '账号异常,请重新登录后再试');
                } else if (!$adminInfo['secondary_password']) {
                    $this->jsonResult('', 0, '请先设置二级密码后才能进行操作');
                }

                //判断支付金额
//                $condis = array();
//                $condis['channel_id'] = $this->_channelId;
//                $condis['begin_time'] = array('elt',$this->nowTime);
//                $condis['end_time'] = array('egt',$this->nowTime);
//                $rechargeRatio = model("ChannelRechargeCoinRatio")->where($condis)->find();
//                if(!empty($rechargeRatio) && $rechargeRatio['ratio']){
//                    $rechargeRatio = $rechargeRatio['ratio'];
//                }
//                else{
//                    $rechargeRatio = 100;
//                }
                $channelInfo['recharge_ratio'] = 100;

                $this->jsonResult($channelInfo, 20000, '获取充值相关信息成功');
            }
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
            exit;
        }
    }

    /**
     * 充值操作处理
     */
    public function doRecharge()
    {
        if ($this->request->isPost()) {
            $amount = $this->input('amount');                   //充值金额
            $real_amount = $this->input('real_amount');              //支付金额
            $recharge_type = $this->input('recharge_type');            //充值方式:1(申请),2(直充),3(结算币充值)
            $paytype = $this->input('paytype');                 //支付类型:zfb(支付宝),wxpay(微信)
            $out_order_no = $this->input('out_order_no');             //交易凭证
            $remark = $this->input('remark');                   //备注

            $orderid = makeOrderid('CPSCOIN');

            $checkResult = $this->validate($this->input, 'ChannelRechargeCoin.add');
            if (true !== $checkResult) {
                $this->jsonResult('', 0, $checkResult);
            }

            //判断当前账号是否有充值权限
            if (!in_array($this->_channelLevel, [1, 2])) {
                $this->jsonResult('', 0, '您不能进行充值');
            }
            if (!in_array($recharge_type, [1, 2, 3])) {
                $this->jsonResult('', 0, '非正确的充值方式');
            } else {
                if ($recharge_type == 2 && !in_array($paytype, ['zfbsmzf', 'kdsm'])) {
                    $this->jsonResult('', 0, '非正确的支付类型');
                }
            }
            if ($this->_channelLevel == 2 && $recharge_type <> 1) {
                $this->jsonResult('', 0, '您只能使用申请方式充值');
            }
            if (abs(intval($real_amount * 100) / 100 - $real_amount) > 0.01) {
                $this->jsonResult('', 0, '请正确填写支付金额！');
            } else {
                //判断支付金额
//                $condis = array();
//                $condis['channel_id'] = $this->_channelId;
//                $condis['begin_time'] = array('elt', $this->nowTime);
//                $condis['end_time'] = array('egt', $this->nowTime);
//                $rechargeRatio = model("ChannelRechargeCoinRatio")->where($condis)->find();
//                if(!empty($rechargeRatio) && $rechargeRatio['ratio']){
//                    $rechargeRatio = $rechargeRatio['ratio'];
//                }
//                else{
//                    $rechargeRatio = 100;
//                }
                $rechargeRatio = 100;
                $discountAmt = floatval($amount * $rechargeRatio / 100);
                if ($discountAmt <> $real_amount) {
                    $this->jsonResult('', 0, '充值金额异常,请刷新页面后重新充值');
                }
            }

            $channelInfo = model("Channel")->where(['id' => $this->_channelId])->find();
            if ($channelInfo && $channelInfo['parent_id']) {
                if ($recharge_type == 3 && $channelInfo['js_amount'] < $amount) {
                    $this->jsonResult('', 0, '您的结算币余额不足');
                }
            } else {
                $this->jsonResult('', 0, '您的账号有异常,请联系管理员');
            }

            //指定时间内，禁止重复下单
            if (!requestDuplicateCheck('recharge_pay_duplicate_' . $this->_channelId, $this->payRequestLimit)) {
                $this->jsonResult('', 0, '充值请求过多，请于' . $this->payRequestLimit . 's以后，再次进行充值操作');
            }

            $rechargeData = array();
            $rechargeData['orderid'] = $orderid;
            $rechargeData['channel_id'] = $this->_channelId;
            $rechargeData['channel_name'] = $this->_channelName;
            if ($this->_channelLevel == 1) {
                $rechargeData['send_channel_id'] = '0';
                $rechargeData['send_channel_name'] = '平台';
            } else if ($this->_channelLevel == 2) {
                $parentChannelInfo = model("Channel")->where(['id' => $channelInfo['parent_id']])->find();
                $rechargeData['send_channel_id'] = $parentChannelInfo['id'];
                $rechargeData['send_channel_name'] = $parentChannelInfo['name'];
            }
            $rechargeData['amount'] = $amount;
            $rechargeData['real_amount'] = $real_amount;
            $rechargeData['recharge_type'] = $recharge_type;
            $rechargeData['paytype'] = $paytype;
            $rechargeData['out_order_no'] = $out_order_no;
            $rechargeData['remark'] = $remark;
            $rechargeData['recharge_ratio'] = $rechargeRatio;
            $rechargeData['status'] = 0;
            $rechargeData['create_time'] = time();

            // 启动事务
            Db::startTrans();

            try {
                $result = [];   //返回值

                //结算币充值
                if ($recharge_type == 3) {
                    $rechargeData['status'] = 1;
                    $rechargeData['finish_status'] = 1;
                    $rechargeData['finish_time'] = NOW_TIMESTAMP;

                    $insertRechargeId = model("ChannelRechargeCoin")->insert($rechargeData);
                    if (!$insertRechargeId) {
                        throw new Exception("创建订单失败! ");
                    }

                    $detData = array();
                    $detData['channel_id'] = $this->_channelId;
                    $detData['channel_name'] = $this->_channelName;
                    $detData['change_amount'] = -$amount;
                    $detData['account_type'] = 2;   //结算账户
                    $detData['type'] = 7;   //结算充值
                    $detData['out_orderid'] = $orderid;
                    $detData['create_time'] = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
                    if (!$insertDetId) {
                        throw new Exception("添加账户变动明细失败");
                    }
                    $updData = array();
                    $updData['js_amount'] = Db::raw("js_amount-" . $amount);
                    $updData['update_time'] = time();
                    $updResult = model('Channel')->where(['id' => $this->_channelId, 'js_amount' => array('egt', $amount)])->update($updData);
                    if (!$updResult) {
                        throw new Exception("账户金额变动失败");
                    }

                    $detData = array();
                    $detData['channel_id'] = $this->_channelId;
                    $detData['channel_name'] = $this->_channelName;
                    $detData['change_amount'] = $amount;
                    $detData['account_type'] = 3;   //平台币账户
                    $detData['type'] = 4;   //充值收入
                    $detData['out_orderid'] = $orderid;
                    $detData['create_time'] = NOW_TIMESTAMP;
                    $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
                    if (!$insertDetId) {
                        throw new Exception("添加账户变动明细失败");
                    }
                    $updData = array();
                    $updData['amount_coin'] = Db::raw("amount_coin+" . $amount);
                    $updData['update_time'] = time();
                    $updResult = model('Channel')->where(['id' => $this->_channelId])->update($updData);
                    if (!$updResult) {
                        throw new Exception("账户金额变动失败");
                    }
                } else {
                    $insertRechargeId = model("ChannelRechargeCoin")->insert($rechargeData);
                    if (!$insertRechargeId) {
                        throw new Exception("创建订单失败! ");
                    }
                }
                // 提交事务
                Db::commit();
                $this->redis->del('recharge_pay_duplicate_' . $this->_channelId);

                if ($recharge_type == 2) {
                    //第三方支付用的商品简单描述   (去除&+等特殊字符)
                    $payBody = "cps充值";
                    $pay = new PayService();
                    //支付方式是支付宝或者支付宝混合支付时
                    if ($paytype == 'zfbsmzf') {
                        //支付宝支付参数
                        $notify_url = "http://" . HTTP_HOST_URL . "/v1.cps_notify/alipayAop.html";
                        // $notify_url = Config::get('ali_pay_config')['notify_url'];
                        $qrcodePay = $pay->chargeByZfbWap($orderid, $rechargeData['real_amount'], $payBody, '', $notify_url);
                        $qrcodePay = str_replace("method='POST'","method='POST' target='_blank'",$qrcodePay);
                        $result['alipay_param'] = $qrcodePay;
                    } //支付方式是微信官方h5支付或者微信官方h5混合支付时
                    elseif ($paytype == 'kdsm') {
                        throw new Exception("非法操作! ");

                       //  $notify_url = "http://" . HTTP_HOST_URL . "/v1.cps_notify/kdh5zhifu.html";
                       // // $notify_url =  Config::get('kdh5')['notify_coin_url'];
                       //  $kdsm_param = $pay->getkdsmweixinParam($orderid, $rechargeData['real_amount'], $payBody, $notify_url);
                       //  $dataPay = [
                       //      'id' => $insertRechargeId,
                       //      'orderid' => $orderid,
                       //      'kdsm_param' => $kdsm_param,
                       //      'real_amount' => priceFormat($real_amount),
                       //      'create_time' => date('Y-m-d'),
                       //      'goods' => 'cps充值',
                       //  ];
                       //  $pay_url = Config::get('kdh5')['pay_url'] . '?data=' . opensslEncrypt(json_encode($dataPay));
                       //  $result = ['kdsm_param' => $pay_url];
                    }
                }
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                $this->jsonResult('', 0, '订单生成失败' . $e->getMessage());
            }
            $result['orderid'] = $orderid;

            $this->jsonResult($result, 20000, '订单生成成功');
            exit;
        } else {
            $this->jsonResult('', 0, '非法请求');
            exit;
        }
    }
}
