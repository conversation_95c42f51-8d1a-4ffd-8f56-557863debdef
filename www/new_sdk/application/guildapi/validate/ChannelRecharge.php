<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\guildapi\validate;

use app\common\library\ValidateExtend;

class ChannelRecharge extends ValidateExtend
{
    protected $rule = [
        'recharge_type'      => 'require|positiveInteger',
        'amount'             => 'require|integer|gt:0',
		'real_amount'        => 'require|number|gt:0',
    ];
    protected $message = [
        'recharge_type.require'         => '充值方式不能为空',
        'recharge_type.positiveInteger' => '充值方式必须为正整型',
        'amount.require'				=> '充值金额不能为空',
        'amount.integer'				=> '充值金额必须为正整型',
        'amount.gt'						=> '充值金额必须大于0',
        'real_amount.require'			=> '支付金额不能为空',
        'real_amount.number'			=> '支付金额必须为数值类型',
        'real_amount.gt'				=> '支付金额必须大于0',
    ];

    protected $scene = [
        'add'=> ['recharge_type', 'amount', 'real_amount'],
    ];
}