<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>斜向背景文字 - 自然打乱版</title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }

        .bg-text-container {
            position: fixed;
            top: -100%;
            left: -100%;
            width: 300vw;
            height: 300vh;
            transform: rotate(-30deg);
            pointer-events: none;
            z-index: 9999;
            display: flex;
            flex-wrap: wrap;
            font-family: sans-serif;
            opacity: 0.04;
        }

        .bg-text {
            width: 200px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            white-space: nowrap;
            color: #000;
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body>

<div id="bgTextContainer" class="bg-text-container"></div>

<script>
    // ========== 配置参数 ==========
    const config = {
        downloadUrl: '', // 下载地址，可以通过JS设置
        backgroundText: "王者荣耀" // 背景文字
    };

    // ========== 背景文字生成 ==========
    const text = config.backgroundText;
    const container = document.getElementById('bgTextContainer');

    const vw = window.innerWidth * 3;
    const vh = window.innerHeight * 3;

    const blockWidth = 200;
    const blockHeight = 80;

    const cols = Math.ceil(vw / blockWidth);
    const rows = Math.ceil(vh / blockHeight);

    for (let row = 0; row < rows; row++) {
        const offset = Math.floor(Math.random() * 60) - 30; // 每行左右错位
        for (let col = 0; col < cols; col++) {
            const div = document.createElement('div');
            div.className = 'bg-text';
            div.innerText = text;

            const angle = (Math.random() * 10 - 5).toFixed(2); // -5~+5度微旋转
            div.style.transform = `translateX(${offset}px) rotate(${angle}deg)`;

            container.appendChild(div);
        }
    }

    // ========== 下载功能 ==========

    // 设置下载地址的函数（外部可调用）
    function setDownloadUrl(url) {
        config.downloadUrl = url;
    }

    // 设置背景文字的函数（外部可调用）
    function setBackgroundText(text) {
        config.backgroundText = text;
        // 重新生成背景文字
        container.innerHTML = '';
        for (let row = 0; row < rows; row++) {
            const offset = Math.floor(Math.random() * 60) - 30;
            for (let col = 0; col < cols; col++) {
                const div = document.createElement('div');
                div.className = 'bg-text';
                div.innerText = text;
                const angle = (Math.random() * 10 - 5).toFixed(2);
                div.style.transform = `translateX(${offset}px) rotate(${angle}deg)`;
                container.appendChild(div);
            }
        }
    }

    // 下载按钮点击事件
    document.getElementById('downloadBtn').addEventListener('click', function() {
        if (config.downloadUrl && config.downloadUrl.trim() !== '') {
            // 有下载地址，打开新窗口下载
            window.open(config.downloadUrl, '_blank');
        } else {
            // 没有下载地址，显示提示弹框
            showModal();
        }
    });

    // 显示弹框
    function showModal() {
        document.getElementById('errorModal').style.display = 'block';
    }

    // 关闭弹框
    function closeModal() {
        document.getElementById('errorModal').style.display = 'none';
    }

    // 点击弹框外部关闭弹框
    window.onclick = function(event) {
        const modal = document.getElementById('errorModal');
        if (event.target === modal) {
            closeModal();
        }
    }

    // ========== 设备适配检测 ==========
    function isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
    }

    // 根据设备类型调整样式
    function adjustForDevice() {
        const downloadBtn = document.getElementById('downloadBtn');
        if (isMobile()) {
            downloadBtn.style.fontSize = '16px';
            downloadBtn.style.padding = '12px 25px';
            downloadBtn.style.minWidth = '180px';
        } else {
            downloadBtn.style.fontSize = '20px';
            downloadBtn.style.padding = '20px 40px';
            downloadBtn.style.minWidth = '250px';
        }
    }

    // 页面加载完成后执行设备适配
    window.addEventListener('load', adjustForDevice);
    window.addEventListener('resize', adjustForDevice);

    // ========== 示例用法 ==========
    // 设置下载地址示例（可以在外部调用）
    // setDownloadUrl('https://example.com/download/app.apk');

    // 设置背景文字示例（可以在外部调用）
    // setBackgroundText('新的游戏名称');
</script>

</body>
</html>
