<?php
namespace app\api\controller;

use app\common\service\RiskControlService;
use app\common\service\FkStrategiesService;
use think\Controller;
use think\Request;

class RiskControl extends Controller
{
    /**
     * @var RiskControlService
     */
    private $riskControlService;
    private $fkStrategiesService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $this->riskControlService = new RiskControlService();
        $this->fkStrategiesService = new FkStrategiesService();
    }
    
    /**
     * 处理登录事件
     * @return \think\response\Json
     */
    public function login()
    {
        $request = Request::instance();
        $data = $request->post();

        $data = [
            // 'event_type' => 'register', // 事件类型：login（登录）、register（注册）
            'user_name' => 'wen_01', // username
            'ip' => '127.0.0.1', // ip
            'imei' => '00000000-1645-d5b5-ffff-ffffef05ac4a-wen', // imei
            'duration' => '10', // 注册耗时
            'phone' => '13121776520', // 注册手机号
            'id_card' => '110101199001011231', // 身份证号
        ];

        // 添加事件类型和时间戳
        // $data['event_type'] = 'login';
        // $data['timestamp'] = time();

        // 调用风控服务
        // $result = $this->riskControlService->processEvent($data);
        // $result = $this->fkStrategiesService->processEvent($data, 'realtime', 'REGISTER_USERNAME_REAL_TIME'); // 注册
        $result = $this->fkStrategiesService->processEvent($data, 'realtime', 'USERNAME_LOGINI_STRATEGY'); // 登录
        dump($result);
        
        if ($result['is_blocked']) {
            return json(['code' => 403, 'msg' => $result['msg']]);
        }
        
        if ($result['is_risky']) {
            return json(['code' => 200, 'msg' => $result['msg'], 'data' => ['need_verify' => true]]);
        }
        
        return json(['code' => 200, 'msg' => 'success', 'data' => ['need_verify' => false]]);
    }
    
    /**
     * 处理注册事件
     * @return \think\response\Json
     */
    public function register()
    {
        $request = Request::instance();
        $data = $request->post();
        
        // 添加事件类型和时间戳
        $data['event_type'] = 'register';
        $data['timestamp'] = time();
        
        // 调用风控服务
        $result = $this->riskControlService->processEvent($data);
        
        if ($result['is_blocked']) {
            return json(['code' => 403, 'msg' => $result['msg']]);
        }
        
        if ($result['is_risky']) {
            return json(['code' => 200, 'msg' => $result['msg'], 'data' => ['need_verify' => true]]);
        }
        
        return json(['code' => 200, 'msg' => 'success', 'data' => ['need_verify' => false]]);
    }
} 