<?php

namespace app\api\controller;

use app\common\library\RsaSign;
use app\common\logic\LdysPay;
use app\common\logic\PayHandle;
use app\common\logic\Websocket;
use app\common\model\FkRosterLists;
use app\common\model\GameBindDouyinOpen;
use app\common\model\Setting;
use app\service\PayService;
use LdzfPay\Api\Common\RSACryptUtil;
use think\Cache;
use think\Config;
use think\Controller;
use think\Db;
use think\Env;
use DouYinGameOpen\Init;
use DouYinGameOpen\User;
use think\Request;

class Index extends Controller
{
    public function index()
    {
        // echo phpinfo();
        // $return_arr['mweb_url'] = 'http://sdkapi.46yx.com';
        // $return_url = 'https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx05112758904301e15b6b8e875970b00000&package=2416986125';
        // header("location:" . $return_arr['mweb_url'] . "&redirect_url={$return_url}");
        // return json_encode(["code" => 200, "message" => "HELLO API!", "data" => ""]);

        $gameid = input('gameid');
        if(!$gameid){
            return json_encode(["code" => 200, "message" => "HELLO API!111", "data" => ""]);
        }
        $userAgent = $_SERVER["HTTP_USER_AGENT"];
        if((stripos($userAgent, "iPhone") !== false || stripos($userAgent, "iPad") !== false)){
            $gameIds = Config::get('ios_jump_game');
            if(array_key_exists($gameid, $gameIds)){
                header("location: ". $gameIds[$gameid]);
            }
            echo "请联系开发配置当前游戏跳转！";
        }
    }

    public function transpondUrlAction()
    {
        // $url = $request->get("url");  // 获取接口传入需要转发的url注意需要用url编码。由于基于Symfony框架实现$request可以直接获取url参数，原生可用$_GET['url']代替
        $url = 'https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx05112758904301e15b6b8e875970b00000&package=2416986125';
        $userAgent = $this->getAgent('user-agent');
        if (empty($url) or $url == "") return false;
        if (empty($userAgent)) $userAgent = $this->getAgent();
        $ch = curl_init();
        if (preg_match("/^https:\\/\\/.+/", $url)) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        }
        $cip = '************';
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
        @curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        //        curl_setopt ($ch, CURLOPT_COOKIEJAR, './cookie.txt');  // cookie日志
        // 微信Wap支付需要伪造请求头Referer该参数，该参数需设置与微信支付配置都安全域名一致
        curl_setopt($ch, CURLOPT_REFERER, "http://sdkapi.46yx.com");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ["CLIENT-IP:$cip"]);// 这里需要设置客户端的Ip地址，用于伪造请求IP地址
        curl_setopt($ch, CURLOPT_HEADER, 1);                     //显示返回的HEAD区域的内容
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $result = curl_exec($ch);
        curl_close($ch);
        var_dump($result);
    }

    public function getAgent()
    {
        $agentarry = [
            "iPhone11" => "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Mobile/14G60 MicroMessenger/6.5.18 NetType/WIFI Language/en",
            "华为P9全网通" => "Mozilla/5.0 (Linux; Android 7.0; EVA-AL00 Build/HUAWEIEVA-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.49 Mobile MQQBrowser/6.2 TBS/043508 Safari/537.36 MicroMessenger/6.5.13.1100 NetType/WIFI Language/zh_CN",
            "小米5X" => "Mozilla/5.0 (Linux; U; Android 7.1.2; zh-cn; MI 5X Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.146 Mobile Safari/537.36 XiaoMi/MiuiBrowser/9.2.2",
            "一加手机3" => "Mozilla/5.0 (Linux; Android 7.1.1; ONEPLUS A3000 Build/NMF26F; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.49 Mobile MQQBrowser/6.2 TBS/043508 Safari/537.36 MicroMessenger/6.5.13.1100 NetType/WIFI Language/zh_CN",
            "努比亚Z11" => "Mozilla/5.0 (Linux; U; Android 6.0.1; zh-cn; NX531J Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko)Version/4.0 Chrome/37.0.0.0 MQQBrowser/6.8 Mobile Safari/537.36",
            "小米5s" => "Mozilla/5.0 (Linux; Android 6.0.1; MI 5s Build/MXB48T; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.49 Mobile MQQBrowser/6.2 TBS/043508 Safari/537.36 V1_AND_SQ_7.2.0_730_YYB_D QQ/7.2.0.3270 NetType/WIFI WebP/0.3.0 Pixel/1080",
            "华为nova" => "Mozilla/5.0 (Linux; Android 7.0; HUAWEI CAZ-AL10 Build/HUAWEICAZ-AL10; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.49 Mobile MQQBrowser/6.2 TBS/043508 Safari/537.36 V1_AND_SQ_7.1.0_692_YYB_D QQ/7.1.0.3175 NetType/WIFI WebP/0.3.0 Pixel/1080",
            "联想ZUK Z2 Pro" => "Mozilla/5.0 (Linux; Android 7.0; ZUK Z2121 Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.49 Mobile MQQBrowser/6.2 TBS/043508 Safari/537.36 V1_AND_SQ_7.2.0_730_YYB_D QQ/7.2.0.3270 NetType/4G WebP/0.3.0 Pixel/1080",
            "魅蓝note 3" => "Mozilla/5.0 (Linux; Android 5.1; m3 note Build/LMY47I; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Mobile Safari/537.36 T7/9.3 baiduboxapp/9.3.0.10 (Baidu; P1 5.1)",
            "三星GALAXY S8+" => "Mozilla/5.0 (Linux; U; Android 7.0; zh-CN; SM-G9550 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.7.0.953 Mobile Safari/537.36",
            "魅族MX6    " => "Mozilla/5.0 (Linux; Android 6.0; MX6 Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.49 Mobile MQQBrowser/6.2 TBS/043508 Safari/537.36 MicroMessenger/6.5.13.1100 NetType/4G Language/zh_CN",
            "vivo Xplay5A" => "Mozilla/5.0 (Linux; Android 5.1.1; vivo Xplay5A Build/LMY47V; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Mobile Safari/537.36 T7/9.3 baiduboxapp/9.3.0.10 (Baidu; P1 5.1.1)",
            "三星GALAXY C7" => "Mozilla/5.0 (Linux; U; Android 6.0.1; zh-CN; SM-C7000 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.6.2.948 Mobile Safari/537.36",
            "三星GALAXY S8" => "Mozilla/5.0 (Linux; U; Android 7.0; zh-CN; SM-G9500 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.7.0.953 Mobile Safari/537.36",
            "荣耀8青春版" => "Mozilla/5.0 (Linux; U; Android 7.0; zh-CN; PRA-AL00 Build/HONORPRA-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.7.0.953 Mobile Safari/537.36",
            "UCOpenwave" => "Openwave/ UCWEB7.0.2.37/28/999",
            "UC Opera" => "Mozilla/4.0 (compatible; MSIE 6.0; ) Opera/UCWEB7.0.2.37/28/999",
            "小米4S" => "Mozilla/5.0 (Linux; U; Android 5.1.1; zh-cn; MI 4S Build/LMY47V) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/53.0.2785.146 Mobile Safari/537.36 XiaoMi/MiuiBrowser/9.1.3",
            "OPPO R12" => "Mozilla/5.0 (Linux; U; Android 7.1.1; zh-CN; OPPO R11 Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/11.7.0.953 Mobile Safari/537.36",
            "iPhone2" => "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_3 like Mac OS X) AppleWebKit/603.3.8 (KHTML, like Gecko) Mobile/14G60 MicroMessenger/6.5.7 NetType/WIFI Language/zh_CN",
        ];
        return $agentarry[array_rand($agentarry, 1)];
    }

    // 接口测试专用
    public function wen_test()
    {
        // http://sdkapi.newsdk.com/index/wen_test
        echo "<pre>";
        // $info = Request::instance()->header();
        // dump($info);

        //
        // \think\Cache::store('redis')->set('fk_register_time:13121776520', time(), 60);
        // $res = fkRegisterTimeHandle("13121776520", "127.0.0.1", "00000000-1645-d5b5-ffff-ffffef05ac4a", "13121776520");
        // dump($res);
        //
        //
        // $model = new FkRosterLists();
        // $result = $model->getCacheInfo(['list_type' => 'black', 'object_type' => 'username', 'object_value' => '1231321']);
        // dump($result);


        $str = 'ZguvdXQS6xQ06XFo80uC9fyyaexeW9y0ff946CefHQ2IMJTF9uZt920NR8xo8Jy5L/QIJ520OZ15r73WVGoDQsX1Z+2h0ua5bbf8ahMcvTWHsiqfKKJ+NyoG6tgo+h/Jijqcg32gX/+JUFdslPo9TnwVOYwYQm7GZYctpV9AAtfBR1j2ENZ8fJ8ZOvkDIOx8MKg+nVdJRTKhhPqNNd8whnWj0NC7VTWfy8yKwxE2mluchkLv1QQxmyB0g3GmLBg/q6Vpe+3nRPrwEv19QcZ+ztIT0o3eKrDmIZ7dOyYNuV2Lvmc9qN29aAvRmpyfOnQRNDRS66BQ6Yc4tAprcuVe43VvrMU3ifSwn+zq1n66mDEoZ2SnWS04z6K7N2o5AYqHJgOjagR7PiFN9yo4I1ID54VM8jfCjUqj6Fv+AlY/qlg=';
        // aes_key | aes_key_h5
        $decryptStr = openssl_decrypt($str, 'AES-128-ECB', Env::get('aes_key'));
        dump(json_decode($decryptStr));


        // 加密
        $res = auth_code('111111', "ENCODE", Env::get('auth_key'));
        dump($res);
        // dump(urlEncode('J3YrYmrn7cOLBjIubTOz5HavJg39G3C40T/voMiuvR2nSIt5LknEs4r+o+MwVWho57cSBzGjOcF/sTB4HjhF8/W9ol0'));
        // // 解密
        $token = "rpLZ2+63fXImSZMMkfvGPEYMHwdovcmURSB7mnZdIOHuSSmVP174mNeUSL8JHfiUMXRhDg1BiMndjUNJl7t6ccAztk/5Iz0";
        $res1 = auth_code($token, "DECODE", Env::get('auth_key'));
        $res = explode('|', auth_code($token, "DECODE", Env::get('aes_key')));
        dump($res1, $res);
        // list($userid, $username, $gameid, $sub_username, $token_random, $type) = explode('|', auth_code($token, "DECODE", Env::get('aes_key')));
        // dump($userid, $username, $gameid, $sub_username, $token_random, $type);

        var_dump(Env::get('auth_key'));

        // dump(auth_code($token,"DECODE", Env::get('auth_key')));
        // list($userid2, $gameid2,$sub_username,$token_random,$type) = explode('|', auth_code($token,"DECODE", Env::get('auth_key')));
        // dump($userid2, $username2, $gameid2,$sub_username,$token_random,$type);
        // dump(Env::get('aes_key'), $password1, $res);
        // // 获取支付的配置参数
        // $public_key = Config::get('ali_pay_config')['configs'][Config::get('ali_pay_config')['default']['zfb']]['public_key'];
        // dump($public_key);
        // dump(Config::get('ali_pay_config')['sign_type']);
        // // 游戏订单回调签名生成 - 订单通知表: cy_paycpinfo
        // $str = "orderid=WL16982863361387186922&username=103570&gameid=261&roleid=526286&serverid=1998&paytype=coinpay&amount=6&paytime=1698286336&attach=243220231026101155959465553"; // 订单数据拿过来的时候，这里记得去除sign参数
        // $param = $str . "&appkey=c531455fe1aef8c97c8b6d85d7a3dc19";
        // $md5params = md5($param);
        // $params = $str . "&sign=" . urlencode($md5params);
        // dump($params);




        // return json_encode(['code' => 1, 'msg' => 'success', 'data' => '']);
        // return json(['err_no' => 0, 'err_msg' => '']);

        // $dy_config = [
        //     'app_id' => Env::get('dy_game_open.app_id', ''),
        //     'app_secret' => Env::get('dy_game_open.app_secret', ''),
        //     'client_key' => Env::get('dy_game_open.client_key', ''),
        //     'client_secret' => Env::get('dy_game_open.client_secret', ''),
        // ];
        // $dyUser = new User($dy_config);
        // // $dyInit = new Init($dy_config);
        // // $client_token = $dyInit->client_token();
        // // print_r($client_token);
        // // die;
        // $client_token = [
        //     'data' => [
        //         "access_token" => "clt.ba0e29c786adbdad9f67a1b69e1a4196xKAXDNSLKtaUWMTIRHBV1w9eprMW_lq",
        //         "description" => "",
        //         "error_code" => 0,
        //         "expires_in" => 7200,
        //         "log_id" => "202505231744385DF1918AA263A0057F19",
        //     ],
        //     "message" => "success",
        // ];


        // ## 根据code获取token
        // $info = (new Init($config))->access_token('54d8d2e1a345281ci4W0QoRq6Y3LuN0s3AYF_lq');
        // dump($info);
        // $info = [
        //     'data' => [
        //         "access_token" => "act.3.Db88ppxc_2nckhTcjbE9Z2-lZ9PMcp4CrzRQckv7rrzaPOT8a7TkU4Ls9W9dCCmOok13FWfiE1hySTT0PtyxnxaG4Fs_wIbTPaQa729YfZ54kZRow5Ic2W8gZtpL7n-E1ArbGPp-bG0lUZ5AfX_Cf01NVwSfqBVv3eezpQ==_lf",
        //         "description" => "",
        //         "error_code" => "0",
        //         "expires_in" => "1296000",
        //         "log_id" => "20250521100953C76761CDD18B97348C5A",
        //         "open_id" => "_0003YHzVQz7jHbDlXzei6geJ0FR3hhRoVbQ",
        //         "refresh_expires_in" => "2592000",
        //         "refresh_token" => "rft.62a7620229764c902b1c0f7da87c5815HEPO6yeLoPKtFxwdA0BvxOp1In5F_lf",
        //         "scope" => "user_info",
        //     ],
        //     "message" => "success",
        // ];

        // // ## --
        // $result = $dyUser->get_user_hash_mobile($client_token['data']['access_token'], $info['data']['open_id']);
        // echo "<pre>";
        // print_r($result);
        // die;

        // ## 用户详情
        // $userInfo = $dyUser->userinfo($info['data']['access_token'], $info['data']['open_id']);
        // print_r($userInfo);
        // die;
        // $userInfo = [
        //     'data' => [
        //         "avatar" => "https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_e3fdcb8675ad9ed338aabdc02609d664.jpeg?from=**********", // 头像
        //         "avatar_larger" => "https://p11.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_e3fdcb8675ad9ed338aabdc02609d664.jpeg?from=**********", // 头像大图
        //         "captcha" => "",
        //         "city" => "",
        //         "client_key" => "aw27g06ljcx8gvek",
        //         "country" => "",
        //         "desc_url" => "",
        //         "description" => "",
        //         "district" => "",
        //         "e_account_role" => "", // 类型： EAccountM：普通企业号, EAccountS：认证企业号, EAccountK：品牌企业号
        //         "error_code" => 0,
        //         "gender" => 0,
        //         "log_id" => "20250521130444490D744278C2C301D6BB",
        //         "nickname" => "开心小玩家", // 昵称
        //         "open_id" => "_0003YHzVQz7jHbDlXzei6geJ0FR3hhRoVbQ", // 当前应用的唯一标识
        //         "province" => "",
        //         "union_id" => "3f0cea32-cd7d-5b90-a47e-b6d25dce4563", // 开发账户下的唯一标识
        //     ],
        //     "message" => "success",
        // ];
        //
        // // ## 查询绑定意愿
        // $result = $dyUser->get_auth_info_open($client_token['data']['access_token'], $info['data']['open_id']);
        // print_r($result);
        // die;





        // $handleChannelIds = [];
        // $channelIds = (new \app\common\model\Setting())->getSetting('handle_new_imeil_white_channel');
        // if(empty($channelIds)){
        //     $handleChannelIds = explode(',', $channelIds);
        // }
        // dump($channelIds, $handleChannelIds);
        //
        // $param = input();
        // $res = Env::get('ali_oss.access_keysid');
        //
        // // $res = regidterHandle('127.0.0.1', '00000000-1645-d5b5-ffff-ffffef05ac4a-wen');
        // dump($res);

        // $res = Db::query("UPDATE `new_sdk`.`cy_members` SET `amount` = 4.5 WHERE `id` = 153196");
        // dump($res);


        // ## 订单号
        // dump(makeOrderid());

        // $params = [
        //     "mer_id" => "55282",
        //     "notify_url" => "http://xxx.xxx.com",
        //     "amt_type" => "RMB",
        //     "goods_inf" => "测试商品",
        //     "order_id" => rand(0,
        //         999999999),
        //     "mer_date" => date("Ymd"),
        //     "amount" => "1",
        //     // "user_ip" => "127.0.0.1",
        //     "user_ip" => "**************", // 要真实IP地址
        //     "open_id" => "o0jap60RiBbxuKoiM1tWm2oyurxU",
        //     "app_id" => "wx117849d0c0632d22",
        //     "scancode_type" => "WECHAT",
        //     "mer_flag" => "KMER",
        // ];
        // $service = new UmfService("55282");
        // $res = $service->miniProgramPayMap($params); // 小程序
        // // $res = $service->H5AutoConnectMap($params); // H5直连-范围URL地址
        // // $res = $service->H5ConnectMap($params); // H5直连-返回请求参数
        // dump($res);


        // $payData = [
        //     'productname' => '测试',
        //     'order_id' => makeOrderid(),
        //     'amount' => '1',
        //     'open_id' => $param['open_id'],
        // ];
        // // $result = (new LdysPay())->getWxmpPay($payData);
        // // $result = (new LdysPay())->getWxToekn($payData);
        // $result = (new LdysPay())->generateWxmpPayUrl();
        // dump($result);


        // $res = Db::query("UPDATE `new_sdk`.`cy_memberstwo` SET `realname` = '', `idcard` = '' WHERE `id` = 56557");
        // dump($res);
        // $jule = new Jule();
        // $data = [
        //     'account' => '{"user_code":"*********","user_name":"jolo666","game_code":"*************","session_id":"********","id":"*************"}',
        //     'accountSign' => '13DersLTir22ofrzbxii4+N44gByUIZi6vg/ulP8ru9vid0GOY3sVvgSoAmBkNezKiUWEqei/gPrPERYUccbpM05yh1I4Bs81nunLx1GrlDl6FU3nPRzC4a8mtDbCcU3aZ+lzSXk/8ieymPXdrOMtq6ncmqgVf/jk2A8K151oXQ='
        // ];
        // $res = $jule->getSpecialParam($data);
        // dump($res);

        // ## 统计需要分包的相关游戏
        // 订单
        // $res = Db::query("select p.channel_id,p.gameid,g.nickname,SUM(p.amount) as sum_amount FROM cy_pay as p LEFT JOIN cy_game as g ON g.id=p.gameid where p.create_time BETWEEN ********** AND ********** GROUP BY p.gameid ORDER BY sum_amount desc");
        // 登陆
        // $res = Db::query("select * FROM nw_subaccount limit 1");
        // dump($res);


        // // APP 加固
        // $rsa = new RsaSign();
        // $str = '6f8ffbbc02059d05be0816953102f6cc425599';
        // $jiaMiRes = $rsa->openssl_public_encrypt($str);
        // $jieMiRes = $rsa->openssl_private_decrypt($jiaMiRes);
        //
        // // $jiaMiRes = $rsa->openssl_sign($str);
        // // $jieMiRes = $rsa->openssl_verify($str, $jiaMiRes);
        // dump($str, $jiaMiRes, $jieMiRes);


        // dump(json_encode(input()));
        // $sdk = new Yueyou();
        // $res = $sdk->getSpecialParam(['channel_token' => 'RT_7D535DFD85F94A2FBB01072C58610E68', 'time' => **********], ['param' => '{"app_id":"1011","server_key":"rqejOC0Jep"}']);
        // dump($res);

        // 发送分包请求
        // $redis = Cache::init()->handler();  // 获取redis句柄
        // // $redis_value ='{"filename":"mubaoANDROID","channel_id":"5215","apktype":0,"extend":{"channel_version":13,"pinyin":"mubaoANDROID","channel_id":"5215","admin_id":1,"512pngUrl":"https:\/\/cdn.' . QM_DOMAIN_URL . '\/image\/game\/icons\/2023091416266502c3cc46898.png","bundleID":"com.qimeng.mubao","gameChineseName":"\u6bcd\u5305ANDROID"},"target_filename":"6601454dc8b90.apk","finish_notice_url":"http:\/\/admin.7dgames.cn\/upload_notify\/index\/game_id\/10\/channel_id\/5215\/adminid\/1\/apktype\/0.html"}';
        // $redis_value = '{"filename":"aaaaav1anzhuo","channel_id":"5215","apktype":0,"extend":{"channel_version":17,"pinyin":"aaaaav1anzhuo","channel_id":"5215","admin_id":1,"512pngUrl":"http:\/\/cdn.7dgame.cn\/","bundleID":"aaa.xxcc","gameChineseName":"aaaaa_v1(\u5b89\u5353)"},"target_filename":"66b46056b1b1d.apk","finish_notice_url":"http:\/\/admin.7dgames.cn\/upload_notify\/index\/game_id\/271\/channel_id\/5215\/adminid\/1\/apktype\/0.html","sign_ver":1}';
        // $res = $redis->lpush('aoyou:apk:subpackage:task', $redis_value);
        // dump($res);

        // ## 分包通知-ws
        // http://admin.7dgames.cn/upload_notify/makeExcelComplete?action=payList&adminid=1&file_path=https://static.7dgames.cn/excel_dev/20240806/充值记录管理导出_20240806144943_1.csv&time=1722926977
        // $ws = new Websocket();
        // $gamename = 'test'.'-'.'9527';
        // $str = '{"error_code":0,"data":{"action":"subpackageFinish","data":{"code":0,"gamename":"'.$gamename.'", "message":"分包完成！"}}}';
        // $res = $ws->sayToUid('1', $str);
        // dump($res);

        // $res = Db::query("SELECT * FROM `new_sdk`.`cy_members` where username='18947335139 '");
        // dump($res);

        // ## 代金卷使用通知
        // $res = (new \app\common\logic\YqlHandle())->notifyCoupon('206'); -- 弃用
        // $res = (new \app\api\controller\YqlData())->notifyCoupon('206');
        // dump($res);

        // 176 解密/加密
        // $a = strEncrypt("13","Su)sjKsu23G0slow");
        // var_dump('# $a: ', $a);
        // $a = 'd7afaff8a880a0fe535318299fdf80b5';
        // var_dump('# $b: ', strDecrypt($a,"Su)sjKsu23G0slow"));


        // $res = RSACryptUtil::encrypt(['xxx' => 'xxx']);
        // dump($res);

        // $signStr = sprintf('token=%s&userID=%s&key=%s', 'db733736e9613e42f81f69b4309b9dca', '********', '0adc300369494b5f9e195024b2bdefee');
        // $res = md5($signStr);
        // dump($signStr, $res);


        // 支付回调 - 微信H5
        // $res = '{"charset":"UTF-8","req_data":"{\"order_no\":\"1010317672024080718032424646\",\"pay_scene\":\"H5\",\"order_desc\":\"\u9053\u5177\",\"order_name\":\"\u9053\u5177\",\"pay_time\":\"**************\",\"ch_order_no\":\"4200002304202408074418206630\",\"payer_account\":\"oAVOM6yBC6zrc8MdVXQAMQ9p4gyE\",\"out_trade_no\":\"**********************\",\"total_amount\":10,\"reserved\":\"\",\"ch_out_trade_no\":\"****************\",\"trade_status\":\"SUCCESS\",\"pay_channel\":\"WX\",\"payer_id\":\"oAVOM6yBC6zrc8MdVXQAMQ9p4gyE\"}","method":"trade.order.notify","app_id":"A17222309452323464","version":"1.0.1","sign_type":"RSA2","timestamp":"**************"}';
        // 支付回调 - 支付宝H5
        // $res = '{"charset":"UTF-8","req_data":"{\"order_no\":\"1010317672024080718071324962\",\"pay_scene\":\"H5\",\"order_desc\":\"\u9053\u5177\",\"order_name\":\"\u9053\u5177\",\"pay_time\":\"**************\",\"ch_order_no\":\"2024080722001456531440275398\",\"payer_account\":\"ahj***@163.com\",\"out_trade_no\":\"**********************\",\"total_amount\":10,\"reserved\":\"\",\"ch_out_trade_no\":\"705150165776240807\",\"trade_status\":\"SUCCESS\",\"pay_channel\":\"ALI\",\"payer_id\":\"****************\"}","method":"trade.order.notify","app_id":"A17222309452323464","version":"1.0.1","sign_type":"RSA2","timestamp":"**************"}';
        // dump(json_decode($res));

        // ## TC-喜钛游 支付
        // $pay = new PayService();
        // $data = [
        //     "orderid" => makeOrderid(),
        //     "productname" => '道具',
        //     "real_amount" => '0.01',
        //     "notify_url" => Config::get('xty_pay')['notify']['notify_url'],
        //     "ip" => request()->ip(),
        // ];
        // $payInfoRes = $pay->getXtyPayParam($data, 'payWxH5');
        // // $payInfoRes = $pay->getXtyPayParam($data, 'payAliH5');
        // dump($payInfoRes);


        // $randomList = [
        //     ['pay_scene' => 'wx_h5', 'game_id' => 10, 'version' => 'v3.4.4'],
        //     ['pay_scene' => 'wx_h5', 'game_id' => 266, 'version' => 'v3.4.4'],
        //     ['pay_scene' => 'wx_h5', 'game_id' => 256, 'version' => 'v3.4.4'],
        //     ['pay_scene' => 'wx_h5', 'game_id' => 20, 'version' => 'v3.4.3'],
        //     ['pay_scene' => 'wx_h5', 'game_id' => 259, 'version' => 'v3.4.3'],
        // ];
        // $countNum = ['qzl_wx_h5' => 0, 'ldys_wx_h5' => 0, 'ybzf_wxmp_h5' => 0, 'yyyb_wx_h5' => 0, 'xty_wx_h5' => 0, 'qmf_wxmp_h5' => 0];
        // for ($i = 0; $i < 100; $i++) {
        //     // $info =
        //     // 随机获取$randomList数据总数中的一条
        //     $data = $randomList[array_rand($randomList)];
        //     $newPayType = (new PayHandle())->getPayChannel($data['pay_scene'], $data['game_id'], $data['version']);
        //     $countNum[$newPayType['data']['paytype']] = $countNum[$newPayType['data']['paytype']] + 1;
        //     echo "<br />" . $newPayType['data']['paytype'] . ' - ' . $data['game_id'];
        // }
        // dump($countNum);
    }

    // shop 测试订单生成
    public function wxPay()
    {
        $money = input('customMoney');
        if (!$money) {
            return json_encode(['code' => 0, 'msg' => '请输入金额！', 'data' => '']);
        }
        $title = input('title', '购买商品');

        $pay = new PayService();
        $notify_url = Config::get('wxpay-h5')['notify_url'];
        $ip = $_SERVER['REMOTE_ADDR'] ? $_SERVER['REMOTE_ADDR'] : '127.0.0.1';
        $payInfoRes = $pay->getWxpayH5Param(makeUniqueid('API'), $money, $title, $ip, $notify_url);
        if($payInfoRes['error'] == true){
            return json_encode(['data' => '', 'code' => 0, 'msg' => $payInfoRes['msg']]);
        }
        $payInfoRes['data']['mweb_url'] = 'http://sdkapi.'.QM_DOMAIN_URL.'/jump?key=' . base64_encode($payInfoRes['data']['mweb_url']);
        return json_encode(['data' => $payInfoRes['data'], 'code' => 1, 'msg' => '订单生成成功']);
    }

    // 会长数据迁移
    public function migrate_hz(){
        $qy_id = input('qy_id', ''); // 迁移的会长
        $js_id = input('js_id', ''); // 接收会长

        if(!$qy_id || !$js_id){
            return json_encode(["code" => 100, "message" => "缺少迁移或接收会长", "data" => ""]);
        }

        // 迁移会长
        $qy_info = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id' => $qy_id, 'level' => 1])->find();
        $js_info = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id' => $js_id, 'level' => 1])->find();
        // $id_path = $info['id_path'].$info['id'].',';


        // ## 数据统计 ##
        // 子会长
        $qy_child_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $qy_id, 'level' => 2])->count();
        $js_child_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $js_id, 'level' => 2])->count();
        // dump($qy_child_count, $js_child_count);
        $qy_child_id_path = $qy_info['id_path'].$qy_info['id'].',';
        $js_child_id_path = $js_info['id_path'].$js_info['id'].',';
        // dump($qy_child_id_path, $js_child_id_path);
        // 推广员
        $qy_promoter_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id_path' => ['like', '%'.$qy_child_id_path.'%'], 'level' => 3])->count();
        $js_promoter_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id_path' => ['like', '%'.$js_child_id_path.'%'], 'level' => 3])->count();
        // dump($qy_promoter_count, $js_promoter_count);
        $statisticsData = [
            // 迁移
            'qy' => [
                'child_count' => $qy_child_count,
                'promoter_count' => $qy_promoter_count,
            ],
            // 接收
            'js' => [
                'child_count' => $js_child_count,
                'promoter_count' => $js_promoter_count,
            ],
            // 汇总
            'hz' => [
                'child_count' => $qy_child_count+$js_child_count,
                'promoter_count' => $qy_promoter_count+$js_promoter_count,
            ],
            // 实际数据(处理后)
            'sj' => [
                'child_count' => 0,
                'promoter_count' => 0,
            ],
            // 程序处理
            'cx' => [
                'child_count' => 0,
                'promoter_count' => 0,
            ],
        ];
        // dump($statisticsData);


        // 子会长
        $child_list = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $qy_info['id'], 'level' => 2])->select();
        $child_list_ids = array_column($child_list, 'id');


        $child_sql = [];
        $child_count = 0;
        $promoter_count = 0;

        $id_path = $js_info['id_path'].$js_info['id'].',';
        foreach ($child_list as $v){
            $child_count++;
            $child_sql[] = [
                'id' => $v['id'],
                'parent_id' => $js_info['id'],
                'id_path' => $id_path,
            ];

            // $v['parent_ids'] = $js_info['id'];
            // $v['id_paths'] = $id_path;
            // $child_sql[] = $v;

            // 推广员
            $promoter_list = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => ['in',  $child_list_ids], 'level' => 3])->select();
            $vp_id_path = $id_path.$v['id'].',';
            foreach ($promoter_list as $vp){
                $promoter_count++;
                $child_sql[] = [
                    'id' => $vp['id'],
                    'parent_id' => $v['id'],
                    'id_path' => $vp_id_path,
                ];
            }
        }
        // return (json_encode($child_sql));

        // 启动事务
        // Db::startTrans();
        // try {
        //     $channel = new Channel();
        //     if (!$res = $channel->saveAll($child_sql)) {
        //         // 回滚事务
        //         Db::rollback();
        //         throw new \Exception('迁移失败！');
        //     }
        //     Cache::clear('channel_cache');
        //
        //     // 提交事务
        //     Db::commit();
        //
        // } catch (\Exception $e) {
        //     // 回滚事务
        //     Db::rollback();
        //     return json_encode(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        // }


        // ## 数据统计
        $js_child_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $js_id, 'level' => 2])->count();
        $js_child_id_path = $js_info['id_path'].$js_info['id'].',';
        // 推广员
        $js_promoter_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id_path' => ['like', '%'.$js_child_id_path.'%'], 'level' => 3])->count();
        $statisticsData['sj'] = [
            'child_count' => $js_child_count,
            'promoter_count' => $js_promoter_count,
        ];
        $statisticsData['cx'] = [
            'count' => count($child_sql),
            'child_count' => $child_count,
            'promoter_count' => $promoter_count,
        ];

        return json_encode(['code' => 0, 'msg' => '操作成功', 'data' => $statisticsData]);
    }

    // 商务账号下的会长数据迁移
    public function migrate_sw(){
        $qy_id = input('qy_id', ''); // 迁移的商务
        $js_id = input('js_id', ''); // 接收的商务

        if(!$qy_id || !$js_id){
            return json_encode(["code" => 100, "message" => "缺少迁移或接收商务", "data" => ""]);
        }

        // 迁移的商务
        $qy_info = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id' => $qy_id, 'level' => 0, 'parent_id' => 0])->find();
        $js_info = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id' => $js_id, 'level' => 0, 'parent_id' => 0])->find();
        $qy_path = ','.$qy_info['id'].',';
        $js_path = ','.$js_info['id'].',';
        // dump($qy_path,' - ', $js_path);

        // TODO: 以下流程只是复制上边的，还未完善。等韩辉后续需要再说。

        // 迁移的会长
        $qy_ids = [''];
        $qy_info = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $qy_info['id'], 'level' => 1])->find();
        $js_info = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $js_info['id'], 'level' => 1])->find();
        // $id_path = $info['id_path'].$info['id'].',';


        // ## 数据统计 ##
        // 子会长
        $qy_child_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $qy_id, 'level' => 2])->count();
        $js_child_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $js_id, 'level' => 2])->count();
        // dump($qy_child_count, $js_child_count);
        $qy_child_id_path = $qy_info['id_path'].$qy_info['id'].',';
        $js_child_id_path = $js_info['id_path'].$js_info['id'].',';
        // dump($qy_child_id_path, $js_child_id_path);
        // 推广员
        $qy_promoter_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id_path' => ['like', '%'.$qy_child_id_path.'%'], 'level' => 3])->count();
        $js_promoter_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id_path' => ['like', '%'.$js_child_id_path.'%'], 'level' => 3])->count();
        // dump($qy_promoter_count, $js_promoter_count);
        $statisticsData = [
            // 迁移
            'qy' => [
                'child_count' => $qy_child_count,
                'promoter_count' => $qy_promoter_count,
            ],
            // 接收
            'js' => [
                'child_count' => $js_child_count,
                'promoter_count' => $js_promoter_count,
            ],
            // 汇总
            'hz' => [
                'child_count' => $qy_child_count+$js_child_count,
                'promoter_count' => $qy_promoter_count+$js_promoter_count,
            ],
            // 实际数据(处理后)
            'sj' => [
                'child_count' => 0,
                'promoter_count' => 0,
            ],
            // 程序处理
            'cx' => [
                'child_count' => 0,
                'promoter_count' => 0,
            ],
        ];
        // dump($statisticsData);


        // 子会长
        $child_list = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $qy_info['id'], 'level' => 2])->select();
        $child_list_ids = array_column($child_list, 'id');


        $child_sql = [];
        $child_count = 0;
        $promoter_count = 0;

        $id_path = $js_info['id_path'].$js_info['id'].',';
        foreach ($child_list as $v){
            $child_count++;
            $child_sql[] = [
                'id' => $v['id'],
                'parent_id' => $js_info['id'],
                'id_path' => $id_path,
            ];

            // $v['parent_ids'] = $js_info['id'];
            // $v['id_paths'] = $id_path;
            // $child_sql[] = $v;

            // 推广员
            $promoter_list = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => ['in',  $child_list_ids], 'level' => 3])->select();
            $vp_id_path = $id_path.$v['id'].',';
            foreach ($promoter_list as $vp){
                $promoter_count++;
                $child_sql[] = [
                    'id' => $vp['id'],
                    'parent_id' => $v['id'],
                    'id_path' => $vp_id_path,
                ];
            }
        }
        // return (json_encode($child_sql));

        // 启动事务
        // Db::startTrans();
        // try {
        //     $channel = new Channel();
        //     if (!$res = $channel->saveAll($child_sql)) {
        //         // 回滚事务
        //         Db::rollback();
        //         throw new \Exception('迁移失败！');
        //     }
        //     Cache::clear('channel_cache');
        //
        //     // 提交事务
        //     Db::commit();
        //
        // } catch (\Exception $e) {
        //     // 回滚事务
        //     Db::rollback();
        //     return json_encode(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        // }


        // ## 数据统计
        $js_child_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['parent_id' => $js_id, 'level' => 2])->count();
        $js_child_id_path = $js_info['id_path'].$js_info['id'].',';
        // 推广员
        $js_promoter_count = Db::name('nw_channel')->field('id,name,parent_id,level,id_path')->where(['id_path' => ['like', '%'.$js_child_id_path.'%'], 'level' => 3])->count();
        $statisticsData['sj'] = [
            'child_count' => $js_child_count,
            'promoter_count' => $js_promoter_count,
        ];
        $statisticsData['cx'] = [
            'count' => count($child_sql),
            'child_count' => $child_count,
            'promoter_count' => $promoter_count,
        ];

        return json_encode(['code' => 0, 'msg' => '操作成功', 'data' => $statisticsData]);
    }

    // 空方法
    public function _empty()
    {
        return "url error!";
    }

    // 微信小程序跳转
    public function wxmp()
    {
        $url = input('url');
        if ($url) {
            header("location: " . $url);
        }
    }

    // 魔力宝贝推广页地址 - 2024.05.17
    public function mlbbDownload()
    {
        $code = input('code', '');
        $img_status = true;
        if (!$code) {
            $img_status = false;
        }
        $code = base64_decode($code);
        $code = str_replace('@qm_mlbb', "", $code);
        $url_host = QM_DOMAIN_URL;
        $file_url = "https://sdkdown.{$url_host}/game_mlbb/app/{$code}.plist";

        $this->assign('img_status', $img_status);
        $this->assign('file_url', $file_url);
        return $this->fetch();
    }
    // 批量获取推广标识
    public function getMlbbCode()
    {
        $pix = 'mlbb_';
        // $arr = [];
        // for ($i = 21; $i <= 100; $i++) {
        //     $paddedNumber = str_pad($i, 4, '0', STR_PAD_LEFT);
        //
        //     // if (intval($i / 10) = 0) {
        //     //
        //     // }
        //     $code = $pix . $paddedNumber;
        //     // echo $code . ' = https://t.'.QM_DOMAIN_URL.'/mlbb/' . base64_encode($code . '@qm_mlbb') . "<br />";
        //     echo 'https://t.'.QM_DOMAIN_URL.'/mlbb/' . base64_encode($code . '@qm_mlbb') . "<br />";
        //     // if (intval($i % 10) === 0) {
        //     //     echo $i . "<br />";
        //     // }
        //
        //
        //     // $arr[$i] = [
        //     //     'title' => $code,
        //     //     'code' => base64_encode($code . '@qm_mlbb'),
        //     // ];
        // }
        //
        // dump($arr);
        $name = input('name', '');
        $code = $pix . $name;

        // 范例文件：public/mubao/mlbb_1001.plist
        return 'https://t.'.QM_DOMAIN_URL.'/mlbb/' . base64_encode($code . '@qm_mlbb');
    }

}
