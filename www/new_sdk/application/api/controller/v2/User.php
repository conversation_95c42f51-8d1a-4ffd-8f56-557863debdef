<?php

namespace app\api\controller\v2;

use app\api\controller\Api;
use app\common\model\Channel;
use app\common\model\Members;
use app\common\model\MembersTwo;
use app\common\model\Game;
use app\common\service\FkStrategiesService;
use app\service\CommonService;
use app\service\GameService;
use think\Cache;
use think\Db;
use think\Env;
use think\Session;
use app\common\model\Setting;

class User extends Api
{
    private $_passwordSalt = '';      // 密码加密的盐
    private $_encryptMethod = '';       // 密码加密方法
    private $_passwordEncrypt = null;    // 加密密码
    private $_password = null;    // 密码明文

    /**
     * 用户详情
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function userInfo()
    {
        //用户信息
        $userInfo = model('Members')->where(['id' => $this->input['userid']])->field('mobile,email,nickname,username,id,imeil,dy_uid')->find();
        //实用认证信息
        $membersTwoInfo = model('MembersTwo')->where(['userid' => $this->input['userid']])->find();
        //游戏信息
        $gameInfo = model('Game')->where(['id' => $this->input['gameid']])->find();
        $isVisitor = '未认证';

        if ($gameInfo['game_auth_type'] == 1 || $gameInfo['game_auth_type'] == 2) {  //中宣部实名认证
            $gameAccountInfo = model('Subaccount')->where(['game_id' => $this->input['gameid'], 'member_id' => $this->input['userid']])->find();
            if ($gameAccountInfo) {
                if ($gameAccountInfo['auth_status'] == 3) {
                    $membersTwoInfo['realname'] = trim($gameAccountInfo['real_name']);
                    $membersTwoInfo['idcard'] = trim($gameAccountInfo['idcard_num']);
                    $isVisitor = '已认证';
                } else if ($gameAccountInfo['auth_status'] == 1) {
                    vendor('authSdk.Authentication', '.class.php');
                    $auth = new \Authentication();
                    $result = $auth->query(trim($gameInfo['game_bizid']), $gameAccountInfo['member_id'] . '-' . $gameAccountInfo['id']);
                    if ($result['errcode'] == 0) {
                        $resultData = $result['data']['result'];
                        if ($resultData['status'] == 0 && isset($resultData['pi']) && trim($resultData['pi']) <> '') {
                            $updAccountData = array();
                            $updAccountData['auth_type'] = 1;
                            $updAccountData['auth_status'] = 3;
                            $updAccountData['auth_pi'] = $resultData['pi'];
                            $updAccountData['last_query_time'] = time();
                            $updResult = model('Subaccount')->where(['id' => $gameAccountInfo['id']])->update($updAccountData);
                            if ($updResult) {
                                $membersTwoInfo['realname'] = trim($gameAccountInfo['real_name']);
                                $membersTwoInfo['idcard'] = trim($gameAccountInfo['idcard_num']);
                                $isVisitor = '已认证';
                            } else {
                                $membersTwoInfo['realname'] = '';
                                $membersTwoInfo['idcard'] = '';
                                $isVisitor = '未认证';
                            }
                        } else if ($resultData['status'] == 1) {
                            $updAccountData = array();
                            $updAccountData['auth_type'] = 1;
                            $updAccountData['last_query_time'] = time();
                            $updResult = model('Subaccount')->where(['id' => $gameAccountInfo['id']])->update($updAccountData);
                            $membersTwoInfo['realname'] = trim($gameAccountInfo['real_name']);
                            $membersTwoInfo['idcard'] = trim($gameAccountInfo['idcard_num']);
                            $isVisitor = '未认证';
                        } else if ($resultData['status'] == 2) {
                            $updAccountData = array();
                            $updAccountData['auth_type'] = 1;
                            $updAccountData['auth_status'] = 2;
                            $updAccountData['last_query_time'] = time();
                            $updResult = model('Subaccount')->where(['id' => $gameAccountInfo['id']])->update($updAccountData);
                            $membersTwoInfo['realname'] = '';
                            $membersTwoInfo['idcard'] = '';
                            $isVisitor = '未认证';
                        }
                    }
                } else {
                    $isVisitor = '未认证';
//                    if (empty($userInfo['mobile']) && empty($userInfo['email'])) {
//                        $isVisitor = '游客';
//                    } else {
//                        $isVisitor = '已认证';
//                    }
                }
            } else {
                $isVisitor = '未认证';
            }
        } else {   //平台实名认证
            if ($membersTwoInfo['realname'] && $membersTwoInfo['idcard']) {
                $isVisitor = '已认证';
            } else {
                $isVisitor = '未认证';
            }
        }
        if ($userInfo['mobile']) {
            $userInfo['mobile'] = substr_replace($userInfo['mobile'], '****', 3, 4);
        }
        if ($membersTwoInfo['realname'] && $membersTwoInfo['idcard']) {
            $userInfo['realname'] = mb_substr($membersTwoInfo['realname'], 0, 1, "UTF-8") . '**';
            $userInfo['idcard'] = substr_replace($membersTwoInfo['idcard'], '*************', 3, 13);
        } else {
            $userInfo['realname'] = $userInfo['idcard'] = '';
        }
        $userInfo['isVisitor'] = $isVisitor;
        // $kefu = model('common/Kefu')->getKefuList();
        $kefuList = $this->getKefuList($gameInfo['id']);
        $result = [
            'userInfo' => $userInfo,
            'm_url' => MOBILE_SITE_DOMAIN,
            'customer_service' => $kefuList,
            'is_mobile' => 1, // 手机号是否必绑定：1=否、2=是
            'is_new_imei' => 1, // 是否新设备：1=否、2=是
        ];

        // 抖音用户必绑手机号
        if(!empty($userInfo['dy_uid'])){
            $result['is_mobile'] = 2;
        }
        // 手机号是否必绑
        $handleGameIds = (new Setting())->getSetting('handle_register_phone_games');
        if ($handleGameIds && $result['is_mobile'] != 2) {
            if($handleGameIds == 'all'){
                $result['is_mobile'] = 2;
            }else{
                $gameIds = explode(',', $handleGameIds);
                if (in_array($this->input['gameid'], $gameIds)) {
                    $result['is_mobile'] = 2;
                }
            }
        }

        // 是否新设备 - 指定新版本的包才判断；指定渠道不判断；
        $handleChannelIds = [];
        $channelIds = (new Setting())->getSetting('handle_new_imeil_white_channel');
        if(!empty($channelIds)){
            $handleChannelIds = explode(',', $channelIds);
        }
        if($userInfo['imeil'] != $this->input('imeil') && $this->input('version') >= 'v3.5.3' && !in_array($this->input('channel_id'), $handleChannelIds)){
            $result['is_new_imei'] = 2;
        }

        // 发行部测试账户，去掉新设备效验
        if(in_array($userInfo['username'], ['qidian001', 'qidian002'])){
            $result['is_new_imei'] = 1;
        }

        $result['system_message_count'] = model('SystemMessage')
            ->where('is_show', 1)
            ->where('member_id', $this->input['userid'])
            ->count();

        $this->jsonResult($result, 1, '获取成功');
    }

    /**
     * 获取相关游戏的客服，没有则获取公共客服
     * @param $game_id 游戏ID
     *
     * @return mixed
     */
    private function getKefuList($game_id = '')
    {
        $list = model('common/Kefu')->getKefuByGameId(['game_id' => $game_id]);
        if(!$list){
            $list = model('common/Kefu')->getKefuByGameId(['game_id' => 0]);
        }

        return $list;
    }

    /**
     * 修改密码
     *
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updatePwd()
    {


        $checkResult = $this->validate($this->input,
            [
                ['oldpassword', 'require|min:6|max:15', '密码不能为空|密码最少6位字符最多15个字符|密码最少6位字符最多15个字符'],
                ['password', 'require|min:6|max:15', '密码不能为空|密码最少6位字符最多15个字符|密码最少6位字符最多15个字符'],
                ['repassword', 'require|confirm:password', '密码不能为空|两次输入的密码不一致'],
            ]);

        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }

        $data['oldpassword'] = $this->input['oldpassword'];
        $data['password'] = $this->input['password'];
        $data['repassword'] = $this->input['repassword'];
        if ($data['oldpassword'] == $data['password']) $this->jsonResult([], 0, '新密码不能与旧密码相同');

        $info = model('Members')->field('id,username,email,mobile,password')->where(["id" => $this->input['userid'], 'flag' => 0])->find();

        $this->_password = $data['oldpassword'];
        $this->_passwordEncrypt = $this->getPassword();
        if ($this->_passwordEncrypt != $info['password']) $this->jsonResult([], 0, '输入的旧密码错误');

        $this->_password = $data['password'];
        $this->_passwordEncrypt = '';
        $this->_passwordEncrypt = $this->getPassword();

        $res = model('Members')->save(['password' => $this->_passwordEncrypt], ['id' => $this->input['userid']]);
        model('MemberHistory')->insertGetId(['password' => $this->_passwordEncrypt, 'userid' => $this->input['userid'], 'ip' => request()->ip(), 'create_time' => NOW_TIMESTAMP]);

        if ($res) {
            $this->jsonResult([], 1, '修改成功');
        }

        $this->jsonResult([], 0, '修改失败');
    }

    /**
     * 绑定手机号-获取验证码
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getBindPhoneCode()
    {
        $checkResult = $this->validate($this->input, [
                ['type', 'require|integer|in:1,2', '类型不能为空|请输入类型|请输入类型'], // 1 绑定手机  2换绑手机
                ['phone', 'require|integer|length:11', '手机号不能为空|请输入正确的手机号|请输入正确的手机号'],
        ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }
        if (isset($this->input['captcha'], $this->input['random']) && $this->input['captcha'] && $this->input['random']) {
            $captcha = new \app\common\library\Captcha();
            if (!$captcha->check($this->input['captcha'], $this->input['random'])) {
                $this->jsonResult('', 0, '图形验证码错误', 'json');
            }
        }
        if(isVirtualMobile($this->input['phone'])){
            $this->jsonResult('',0,'禁止使用虚拟手机号！');
        }

        $info = model('Members')->field('id,username,email,mobile')->where(["id" => $this->input['userid'], 'flag' => 0])->find();
        if (!$info) {
            $this->jsonResult([], 0, '账号不存在');
        }
        if ($info['mobile'] && $this->input['type'] == 1) {
            $this->jsonResult([], 0, '已绑定手机号');
        } else {
            if ($info['mobile'] == $this->input['phone']) {
                $this->jsonResult([], 0, '换绑手机号不能与原手机号一样');
            }
        }
        $result = (new \app\common\library\Sms)->sendCode($this->input['phone'], config('ALI_SMS.bindMobile'));
        if (isset($result) && $result['status']) {
            $this->jsonResult([], 1, '发送成功');
        } else {
            $this->jsonResult([], 0, $result['msg']);
        }
    }

    // 新设备手机号验证码获取
    public function getNewImeiCode()
    {
        $checkResult = $this->validate($this->input, [
            ['captcha', 'require', '图形验证码有误！'],
            ['random', 'require', '图形验证码标识有误！'],
        ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }
        if (isset($this->input['captcha'], $this->input['random']) && $this->input['captcha'] && $this->input['random']) {
            $captcha = new \app\common\library\Captcha();
            if (!$captcha->check($this->input['captcha'], $this->input['random'])) {
                $this->jsonResult('', 0, '图形验证码错误', 'json');
            }
        }

        $mobile = model('Members')->where(["id" => $this->input['userid'], 'flag' => 0])->value('mobile');
        if (!$mobile) {
            $this->jsonResult([], 0, '当前账户不存在或者未绑定手机号');
        }
        $result = (new \app\common\library\Sms)->sendCode($mobile, config('ALI_SMS.bindMobile'));
        if (isset($result) && $result['status']) {
            $this->jsonResult([], 1, '发送成功');
        } else {
            $this->jsonResult([], 0, $result['msg']);
        }
    }
    // 新设备验证手机号验证码
    public function newImeilValid(){
        $code = $this->input('code');
        if(empty($code)){
            $this->jsonResult('', 0, '请输入验证码！');
        }

        $mobile = model('Members')->where(["id" => $this->input['userid'], 'flag' => 0])->value('mobile');
        if (!$mobile) {
            $this->jsonResult([], 0, '当前账户不存在或者未绑定手机号');
        }

        $codeResult = (new \app\common\library\Sms)->checkCode($mobile, $code);
        if(!$codeResult['status']) {
            $this->jsonResult('', 0, $codeResult['msg']);
        }

        $result = model('Members')->save(['imeil' => $this->input['imeil'], 'update_time' => time()], ['id' => $this->input['userid']]);
        if(!$result){
            $this->jsonResult('', 0, '效验失败！');
        }

        $this->jsonResult('', 1, 'success');
    }

    /**
     * 绑定手机号
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function bindPhone()
    {

        $checkResult = $this->validate($this->input,
            [
                ['type', 'require|integer|in:1,2', '类型不能为空|类型错误|类型错误'],
                ['code', 'require|integer|length:6', '验证码不能为空|请输入验证码|请输入验证码'],
                ['phone', 'require|integer|length:11', '手机号不能为空|请输入正确的手机号|请输入正确的手机号'],
                // ['password', 'requireIf:type,2|min:6|max:15', '密码不能为空|密码最少6位字符最多15个字符|密码最少6位字符最多15个字符'],
            ]);

        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }
        $data['type'] = $this->input['type'];
        $data['code'] = $this->input['code'];
        $data['phone'] = $this->input['phone'];


        $info = model('Members')->field('id,username,email,mobile,password')->where(["id" => $this->input['userid'], 'flag' => 0])->find();
        if (!$info) {
            $this->jsonResult([], 0, '用户信息不存在');
        }
        if ($info['mobile']) {
            $this->jsonResult([], 0, '已绑定手机号');
        }
        if ($data['type'] == 2) {
            if(empty($this->input['password'])){
                $this->jsonResult([], 0, '密码不能为空');
            }
            $data['password'] = $this->input['password'];
            $this->_password = $data['password'];
            $this->_passwordEncrypt = $this->getPassword();
            if ($this->_passwordEncrypt != $info['password']) {
                $this->jsonResult([], 0, '密码错误');
            }
        }

        $result = (new \app\common\library\Sms)->checkCode($data['phone'], $data['code']);
        if (isset($result) && $result['status']) {
            model('Members')->save(['mobile' => $data['phone'], 'imeil' => $this->input['imeil'], 'update_time' => time()], ['id' => $this->input['userid']]);
            model('MemberHistory')->insertGetId(['mobile' => $data['phone'], 'userid' => $this->input['userid'], 'ip' => request()->ip(), 'create_time' => NOW_TIMESTAMP]);

            $this->jsonResult([], 1, '绑定成功');
        }
        $this->jsonResult([], 0, $result['msg']);
    }

    /**
     * 换绑手机号-原手机号验证码获取
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function unbindingPhoneCode()
    {

        if (isset($this->input['captcha'], $this->input['random']) && $this->input['captcha'] && $this->input['random']) {
            $captcha = new \app\common\library\Captcha();
            if (!$captcha->check($this->input['captcha'], $this->input['random'])) {
                $this->jsonResult('', 0, '验证码错误', 'json');
            }
        }

        $info = model('Members')->field('id,username,email,mobile')->where(["id" => $this->input['userid'], 'flag' => 0])->find();

        if (!$info) {
            $this->jsonResult([], 0, '手机号不存在');
        }

        if (!$info['mobile']) {
            $this->jsonResult([], 0, '手机号不存在');
        }
        $result = (new \app\common\library\Sms)->sendCode($info['mobile'], config('ALI_SMS.bindMobileVLMobile'));

        if (isset($result) && $result['status']) {
            $this->jsonResult([], 1, '发送成功');
        } else {
            $this->jsonResult([], 0, $result['msg']);
        }
    }

    /**
     * 换绑原手机验证码验证
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function checkUnbindingPhoneCode()
    {


        $checkResult = $this->validate($this->input,
            [
                ['code', 'require|integer|length:6', '验证码不能为空|请输入验证码|请输入验证码'],
            ]);

        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }

        $data['code'] = $this->input['code'];
        $info = model('Members')->field('id,username,email,mobile')->where(["id" => $this->input['userid'], 'flag' => 0])->find();

        if (!$info) {
            $this->jsonResult([], 0, '手机号不存在');
        }

        $result = (new \app\common\library\Sms)->checkCode($info['mobile'], $data['code']);
        if (isset($result) && $result['status']) {
            $cacheStr = md5('v2:User:checkUnbindingPhoneCode:phone:' . $info['mobile'] . '_' . time());
            Cache::store('default')->set('v2:User:checkUnbindingPhoneCode:' . $cacheStr, $info['id'], 15 * 60);
            $res = [
                'checkToken' => $cacheStr
            ];
            $this->jsonResult($res, 1, '发送成功');
        } else {
            $this->jsonResult([], 0, $result['msg']);
        }
    }

    /**
     * 换绑手机号
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function unbindingPhone()
    {
        $checkResult = $this->validate($this->input,
            [
                ['checkToken', 'require', 'token不能为空'],
                ['code', 'require|integer|length:6', '验证码不能为空|请输入验证码|请输入验证码'],
                ['phone', 'require|integer|length:11', '手机号不能为空|请输入正确的手机号|请输入正确的手机号'],
            ]);

        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }

        $data['checkToken'] = $this->input['checkToken'];
        $data['code'] = $this->input['code'];
        $data['phone'] = $this->input['phone'];

        $id = Cache::store('default')->get('v2:User:checkUnbindingPhoneCode:' . $data['checkToken']);
        if (!$id) {
            $this->jsonResult([], 0, '验证信息已过期，请重新发起验证');
        }

        $info = model('Members')->field('id,username,email,mobile')->where(["id" => $this->input['userid'], 'flag' => 0])->find();
        if (!$info) {
            $this->jsonResult([], 0, '手机号不存在');
        }

        $result = (new \app\common\library\Sms)->checkCode($data['phone'], $data['code']);
        if (isset($result) && $result['status']) {
            model('Members')->save(['mobile' => $data['phone']], ['id' => $this->input['userid']]);
            model('MemberHistory')->insertGetId(['mobile' => $info['mobile'].' 改为 '.$data['phone'], 'userid' => $this->input['userid'], 'ip' => request()->ip(), 'create_time' => NOW_TIMESTAMP]);

            $this->jsonResult([], 1, '绑定成功');
        }
        $this->jsonResult([], 0, $result['msg']);
    }

    /**
     * 获取加密密码
     * @return false|string
     */
    public function getPassword()
    {
        $password = (string)$this->_passwordEncrypt;
        if (empty($password)) {
            switch (strtoupper($this->_encryptMethod)) {
                case 'MD5':
                    $password = $this->_md5Code($this->_password, $this->_passwordSalt);
                    break;

                default:
                    $password = $this->_authCode($this->_password, $this->_passwordSalt);
                    break;
            }

            $this->_passwordEncrypt = $password;
        }

        return $password;
    }

    /**
     * 账号验证
     */
    public function verifyUserName()
    {

        $checkResult = $this->validate($this->input,
            [
                ['username', 'require', '账号不能为空'],
            ]);
        $data['username'] = $this->input['username'];                 //游戏id

        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }
        $user = (new Members())->getUserName($data);
        if ($user) {
            $this->jsonResult($user, 1, '成功');
        } else {
            $this->jsonResult([], 0, '账号不存在');
        }
    }


    private function _md5Code($password, $salt = '')
    {
        return md5($password . $salt);
    }

    private function _authCode($password, $salt = '')
    {
        return auth_code($password, 'ENCODE', $salt);
    }

    /**
     * 实名认证
     */
    public function personalIdentity()
    {
        $checkResult = $this->validate($this->input, [
            ['realname', ['require', 'length' => ':2,100', 'regex' => "/^[\p{L}\p{M}\p{N}'\-·.\s]{2,100}$/u"], '请输入姓名|姓名最少两位以上|姓名格式有误'],
            // '请输入姓名|姓名只能是2-10位的汉字|姓名只能是2-10位的汉字'],
            ['idcard', 'require|validateIDCard', '请输入身份证号码|身份证号码格式不正确'],
        ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult, 'json');
        }
        $data = [
            'realname' => $this->input['realname'],
            'idcard' => $this->input['idcard'],
            'update_time' => time()
        ];
        $member_id = $this->input['userid'];

        // 通过身份证验证年龄
        if (!isMeetAgeByIDCard($data['idcard'], 18, 200)) $this->jsonResult([], 0, '您未满足年龄要求（18岁），无法通过认证');

        $gameInfo = (new Game())->where(['id' => $this->input['gameid']])->find();
        $memberRes = model('MembersTwo')->updateInfo($data, $member_id);

        // FIXME: 封禁某个实名信息的账户使用 - 处理实名后的账户封禁
        if ($this->input['idcard'] == '******************') {
            return $this->jsonResult('', 0, '认证失败，当前账户已被封禁！');
        }

        $membersTwoModel = new MembersTwo();
        if ($gameInfo['game_auth_type'] == 1) {  //中宣部实名认证
            if (!trim($gameInfo['game_bizid'])) {
                return $this->jsonResult('', 0, '该游戏未配置游戏备案识别码');
            }
            $gameAccountInfo = model('Subaccount')->where(['game_id' => $this->input['gameid'], 'member_id' => $member_id])->find();
            if ($gameAccountInfo) {
                if ($gameAccountInfo['auth_status'] <> 3) {
                    vendor('authSdk.Authentication', '.class.php');
                    $auth = new \Authentication();

                    $result = $auth->check(trim($gameInfo['game_bizid']), $gameAccountInfo['member_id'] . '-' . $gameAccountInfo['id'], $data['realname'], $data['idcard']);
                    if ($result['errcode'] <> 0) {
                        if ($result['errcode'] == 2004) {
                            return $this->jsonResult('', 0, '不能频繁调用中宣部实名认证接口，请稍后再重新发起认证');
                        } else {
                            return $this->jsonResult('', 0, '调用接口异常:' . $result['errmsg']);
                        }
                    } else {
                        $resultData = $result['data']['result'];
                        if ($resultData['status'] == 0) {
                            $updAccountData = array();
                            $updAccountData['auth_type'] = 1;
                            $updAccountData['real_name'] = trim($data['realname']);
                            $updAccountData['idcard_num'] = trim($data['idcard']);
                            $updAccountData['auth_status'] = 3;
                            $updAccountData['auth_pi'] = $resultData['pi'];
                            $updAccountData['last_auth_time'] = time();
                            $updResult = model('Subaccount')->where(['id' => $gameAccountInfo['id']])->update($updAccountData);
                            
                            // 更新用户扩展信息的实名信息
                            $membersTwoModel->update($data, ['userid' => $member_id]);
                            // 插入玩家历史记录
                            Db::table('cy_member_history')->insert([
                               'userid' => $member_id,
                               'password' => $data['realname'] . ' - ' . $data['idcard'] . ' - ' . $this->input['username'],
                               'mobile' => '',
                               'ip' => request()->ip(),
                               'create_time' => time(),
                           ]);
                            if ($updResult) {
                                $mark = $this->checkPlayLogin($this->input['idcard']);
                                return $this->jsonResult(["mark" => $mark], 1, '认证成功');
                            } else {
                                return $this->jsonResult('', 0, '实名认证结果保存异常');
                            }
                        } else if ($resultData['status'] == 1) {
                            $updAccountData = array();
                            $updAccountData['auth_type'] = 1;
                            $updAccountData['real_name'] = trim($data['realname']);
                            $updAccountData['idcard_num'] = trim($data['idcard']);
                            $updAccountData['auth_status'] = 1;
                            $updAccountData['auth_pi'] = $resultData['pi'];
                            $updAccountData['last_auth_time'] = time();
                            $updResult = model('Subaccount')->where(['id' => $gameAccountInfo['id']])->update($updAccountData);
                            if ($updResult) {
                                $mark = $this->checkPlayLogin($this->input['idcard']);
                                return $this->jsonResult(["mark" => $mark], 2, '您的实名认证请求已提交审核，请耐心等待认证结果');
                            } else {
                                return $this->jsonResult('', 0, '您的实名认证请求已提交审核，保存异常');
                            }
                        } else {
                            return $this->jsonResult('', 0, '未通过实名认证');
                        }
                    }
                } else {
                    $mark = $this->checkPlayLogin($this->input['idcard']);
                    
                    // 处理游戏绑定渠道管理
                    (new CommonService())->handleBelongToBinding($this->input['userid'], $this->input['member_channel_id'], $this->input['gameid'], $this->input['realname'], $this->input['idcard']);
                    return $this->jsonResult(["mark" => $mark], 1, '您已实名认证成功');
                }
            } else {
                return $this->jsonResult('', 0, '您尚未创建游戏账号');
            }
        } else if ($gameInfo['game_auth_type'] == 2) {  //通过上级调用中宣部认证
            $gameAccountInfo = model('Subaccount')->where(['game_id' => Session::get('game_id'), 'member_id' => $member_id])->find();
            if ($gameAccountInfo) {
                return $this->jsonResult('', 0, '该认证方式尚未开放');
            } else {
                return $this->jsonResult('', 0, '您尚未创建游戏账号');
            }
        } else {   //平台实名认证
            //实名认证数据更新
            if ($membersTwoModel->where(['userid' => $member_id])->find()) {
                $flag = $membersTwoModel->update($data, ['userid' => $member_id]);
            } else {
                $data['userid'] = $member_id;
                $data['username'] = $this->input['username'];
                $data['create_time'] = time();
                $flag = $membersTwoModel->insert($data);
            }

            // 插入玩家历史记录
            Db::table('cy_member_history')->insert([
                'userid' => $member_id,
                'password' => $data['realname'] . ' - ' . $data['idcard'] . ' - ' . $this->input['username'],
                'mobile' => '',
                'ip' => request()->ip(),
                'create_time' => time(),
            ]);

            if ($flag) {
                $mark = $this->checkPlayLogin($this->input['idcard'], '', 'trim');
                
                // 处理游戏绑定渠道管理
                $handleResult = (new CommonService())->handleBelongToBinding($this->input['userid'], $this->input['member_channel_id'], $this->input['gameid'], $this->input['realname'], $this->input['idcard']);
                
                return $this->jsonResult(["mark" => $mark, 'handle' => $handleResult], 1, '认证成功');
            } else {
                return $this->jsonResult('', 0, '认证失败');
            }
        }
        
        if ($memberRes){
            $this->jsonResult($memberRes, 1, '成功');
        }
        $this->jsonResult('', 0, '认证失败');
    }

    /***
     * 检测
     */
    public function checkPlayLogin($idcard)
    {
        $flag = 0;
        if (isMeetAgeByIDCard($idcard, 12, 18)) {
            $flag = 1;
        }
        return $flag;
    }

    // 常见问题列表
    public function problem()
    {

        $checkResult = $this->validate($this->input,
            [
                ['page', 'require|integer|egt:1', '页数不能为空|页数必须是整数|页数必须大于1'],
                ['pageSize', 'require|integer|egt:10', '页码不能为空|页码必须是整数|页码必须大于10'],

            ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult);
        }
        $data['page'] = $this->input['page'];
        $data['pageSize'] = $this->input['pageSize'];

        $list = model('CpsProblem')->alias('a')
            ->field('id,title,answer')
            ->order('order asc, create_time asc')
            ->paginate(['list_rows' => $data['pageSize'], 'page' => $data['page']])
            ->toArray();
        $this->jsonResult($list, 1, '获取成功');
    }

    /**
     * 心跳获取
     *
     * @return void
     */
    public function heartbeat()
    {
        $this->jsonResult([], 1, '上传成功');
    }

    /**
     * 获取176的渠道推广信息
     *
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getYqlChannel()
    {
        $setInfo = model('Common/Setting')->where(['name' => 'yql_tg_value'])->value('value');
        $result = ['tg_url' => '', 'value' => $setInfo];
        if (model('Common/Setting')->where(['name' => 'yql_tg_status'])->value('value') === 0) {
            return $this->jsonResult($result, 1, '未开启176推广');
        }

        $channel_id = $this->input('channel_id');
        $yql_id = (new Channel())->where(['id' => $channel_id, 'status' => 1, 'yql_id' => ['<>', 0]])->value('yql_id');
        if (empty($yql_id)) {
            return $this->jsonResult($result, 1, '当前渠道所属的176的推广信息为空');
        }

        $app_url = Env::get("yql.app_url");
        $app_hash_key = Env::get("yql.app_hash_key");
        $code = strEncrypt($yql_id, $app_hash_key);
        $result['tg_url'] = $app_url . "#/pages/invite/invite?cid=" . $code;
        $this->jsonResult($result, 1, 'success');
    }
}
