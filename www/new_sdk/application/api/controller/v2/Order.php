<?php

namespace app\api\controller\v2;

use app\api\controller\Api;
use app\common\model\Members;
use app\common\model\MembersTwo;
use app\common\model\Game;
use think\Cache;
use think\Config;
use think\Db;
use think\Model;
use app\common\model\PayType;
use app\common\model\Payrule;
use think\Request;

class Order extends Api
{

    private $status = [
        0 => '待支付',
        1 => '支付成功',
        2 => '已取消',
    ];
    private $payType = [];

    public function _initialize()
    {
        parent::_initialize();
        $this->payType = config('order_pay_type');
    }
    /**
     * 订单列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function orderList()
    {
        $checkResult = $this->validate($this->input,
            [
                ['page', 'require|integer|egt:1', '页数不能为空|页数必须是整数|页数必须大于1'],
                ['pageSize', 'require|integer|egt:10', '页码不能为空|页码必须是整数|页码必须大于10'],
                ['month', 'dateFormat:Y-m', '日期格式错误'],
                ['type', 'require|integer|in:1,2', '类型不能为空|请输入类型|请输入类型'],
            ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult);
        }

        $data['page'] = $this->input['page'];
        $data['pageSize'] = $this->input['pageSize'];
        $data['month'] = $this->input['month'];
        $data['type'] = $this->input['type']; //1 游戏充值 2 平台币充值

        $where = [
            'userid' => $this->input['userid'],
        ];
        if ($data['month']) {
            $where['a.create_time'] = ['between', [strtotime(getMonthRange($data['month'])), strtotime(getMonthRange($data['month'], false))]];
        }

        if ($data['type'] == 1) { //充值游戏
            $platform = model('GameInfo')->field('platform')->cache('order:orderlist:' . $this->input['gameid'], 180)->where(['game_id' => $this->input['gameid']])->find();

            if ($platform['platform'] == 0) {
                $gameBand = model('gameBand')->cache('order:orderlist:gameBand:' . $this->input['gameid'], 180)->where('android_game_id', $this->input['gameid'])->find();
                if ($gameBand) {
                    $where['gameid'] = ['in', [$gameBand['android_game_id'], $gameBand['ios_game_id']]];
                } else {
                    $where['gameid'] =  $this->input['gameid'];
                }
            } else {
                $gameBand = model('gameBand')->cache('order:orderlist:gameBand:' . $this->input['gameid'], 180)->where('ios_game_id', $this->input['gameid'])->find();
                if ($gameBand) {
                    $where['gameid'] = ['in', [$gameBand['android_game_id'], $gameBand['ios_game_id']]];
                } else {
                    $where['gameid'] =  $this->input['gameid'];
                }
            }

            $gameInfo = (new Game())->getGameInfoList();
            $list = model('pay')->alias('a')
                ->field('a.amount,a.gameid,a.create_time,a.status,a.orderid,a.paytype')
                ->where($where)
                ->where('a.create_time', '>', strtotime('-3 month'))
                ->order('a.id desc')
                ->paginate(['list_rows' => $data['pageSize'], 'page' => $data['page']])
                ->toArray();
            foreach ($list['data'] as $k => $v) {
                $list['data'][$k]['game_name'] = isset($gameInfo[$v['gameid']]['nickname']) ? $gameInfo[$v['gameid']]['nickname'] : (isset($gameInfo[$v['gameid']]['nname']) ? $gameInfo[$v['gameid']]['name'] : '');
                $list['data'][$k]['logo_img'] = isset($gameInfo[$v['gameid']]['mobileicon']) ? STATIC_DOMAIN . '/' . $gameInfo[$v['gameid']]['mobileicon'] : '';
                $list['data'][$k]['statusStr'] = isset($this->status[$v['status']]) ? $this->status[$v['status']] : '未知';
                $list['data'][$k]['payTypeStr'] = isset($this->payType[$v['paytype']]) ? $this->payType[$v['paytype']] : '未知';
            }
        } else { // 充值平台币

            $list = model('memberCoinPay')->alias('a')
                ->field('a.amount,a.create_time,a.status,a.orderid,a.paytype,a.place_user_id,a.userid')
                ->where($where)
                ->order('a.id desc')
                ->paginate(['list_rows' => $data['pageSize'], 'page' => $data['page']])
                ->toArray();
            foreach ($list['data'] as $k => $v) {
                $list['data'][$k]['gameid'] = 0;
                $list['data'][$k]['game_name'] = '平台币';
                $list['data'][$k]['logo_img'] = config('PTB_LOGO');
                $list['data'][$k]['statusStr'] = isset($this->status[$v['status']]) ? $this->status[$v['status']] : '未知';

                if ($v['place_user_id'] == $v['userid']) {
                    $list['data'][$k]['payTypeStr'] = isset($this->payType[$v['paytype']]) ? $this->payType[$v['paytype']] : '未知';
                } else {
                    $list['data'][$k]['payTypeStr'] = '未知';
                }
                unset($list['data'][$k]['place_user_id'], $list['data'][$k]['userid']);
            }
        }
        $this->jsonResult($list, 1, '发送成功');
    }

    /**
     * 订单详情
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function orderInfo()
    {

        $checkResult = $this->validate($this->input,
            [
                ['orderid', 'require', '订单号不能为空'],
                ['type', 'require|integer|in:1,2', '类型不能为空|请输入类型|请输入类型'],
            ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult);
        }

        $data['orderid'] = $this->input['orderid'];
        $data['type'] = $this->input['type']; //1 游戏充值 2 平台币充值

        $where = [
            'a.userid' => $this->input['userid'],
        ];
        if ($data['type'] == 1) {
            $gameInfo = (new Game())->getGameInfoList();
            $info = model('pay')->alias('a')
                ->field('a.amount,a.gameid,a.create_time,a.status,a.orderid,a.paytype,b.status as cp_status')
                ->join('cy_paycpinfo b', 'a.orderid=b.orderid', 'left')
                ->where(['a.orderid' => $data['orderid']])
                ->where($where)
                ->find();
            if (!$info) {
                $this->jsonResult('', 0, '当前订单号不存在！');
            }

            $info['game_name'] = isset($gameInfo[$info['gameid']]['nickname']) ? $gameInfo[$info['gameid']]['nickname'] : (isset($gameInfo[$info['gameid']]['nname']) ? $gameInfo[$info['gameid']]['name'] : '');
            $info['logo_img'] = STATIC_DOMAIN . '/' . $gameInfo[$info['gameid']]['mobileicon'];
            $info['statusStr'] = isset($this->status[$info['status']]) ? $this->status[$info['status']] : '未知';
            $info['payTypeStr'] = isset($this->payType[$info['paytype']]) ? $this->payType[$info['paytype']] : '未知';
            $info['cpStatusStr'] = $info['cp_status'] == 1 ? '已发货' : '未发货';
        } else {
            $info = model('memberCoinPay')->alias('a')
                ->field('a.amount,a.create_time,a.status,a.orderid,a.paytype,a.place_user_id,a.userid')
                ->where(['a.orderid' => $data['orderid']])
                ->where($where)
                ->find();
            if (!$info) {
                $this->jsonResult('', 0, '当前订单号不存在！');
            }

            $info['gameid'] = 0;
            $info['game_name'] = '平台币';
            $info['logo_img'] = config('PTB_LOGO');
            $info['statusStr'] = isset($this->status[$info['status']]) ? $this->status[$info['status']] : '未知';
            if ($info['place_user_id'] == $info['userid']) {
                $info['payTypeStr'] = isset($this->payType[$info['paytype']]) ? $this->payType[$info['paytype']] : '未知';
            } else {
                $info['payTypeStr'] = '未知';
            }
            $info['cpStatusStr'] = $info['status'] == 1 ? '已发货' : '未发货';
            unset($info['place_user_id'], $info['userid']);
        }


        $this->jsonResult($info, 1, '发送成功');
    }

    /**
     * 取消订单
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function cancelOrder()
    {

        $checkResult = $this->validate($this->input,
            [
                ['orderid', 'require', '订单号不能为空'],
                ['type', 'require|integer|in:1,2', '类型不能为空|请输入类型|请输入类型'],
            ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult);
        }

        $data['orderid'] = $this->input['orderid'];
        $data['type'] = $this->input['type']; //1 游戏充值 2 平台币充值

        $where = [
            'userid' => $this->input['userid'],
        ];

        if ($data['type'] == 1) {
            $info = model('pay')
                ->field('status,orderid,paytype,real_ptb,real_coin,coupon_amount,coupon_member_id')
                ->where(['orderid' => $data['orderid']])
                ->where($where)
                ->find();
            if ($info['status'] == 0) {
                if (model('MemberCoinInfo')->releaseCoin($this->input['userid'], $data['orderid'])) {
                    $this->jsonResult([], 1, '取消成功');
                } else {
                    $this->jsonResult([], 0, '取消失败,请稍后再试');
                }

            } else {
                $this->jsonResult([], 0, '取消失败，订单不是待支付状态');
            }

        } else {
            $info = model('memberCoinPay')
                ->field('orderid,paytype,status')
                ->where(['orderid' => $data['orderid']])
                ->where($where)
                ->find();

            if ($info['status'] == 0) {
                if (model('common/MemberCoinPay')
                    ->where(['orderid' => $data['orderid']])
                    ->where($where)->update(['status' => 2])) {
                    $this->jsonResult([], 1, '取消成功');
                } else {
                    $this->jsonResult([], 0, '取消失败');
                }
            } else {
                $this->jsonResult([], 0, '取消失败，订单不是待支付状态');
            }
        }


        $this->jsonResult($info, 1, '发送成功');
    }

    /**
     * 平台币明细
     */
    public function getMemberCoinList()
    {
        $checkResult = $this->validate($this->input,
            [
                ['page', 'require|integer|egt:1', '页数不能为空|页数必须是整数|页数必须大于1'],
                ['pageSize', 'require|integer|egt:10', '页码不能为空|页码必须是整数|页码必须大于10'],
                ['month', 'dateFormat:Y-m', '日期格式错误'],
                ['type', 'require|integer|in:0,1,2,3', '类型不能为空|请输入类型|请输入类型'],
            ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult);
        }
        $data['page'] = $this->input['page'];
        $data['pageSize'] = $this->input['pageSize'];
        $data['month'] = $this->input['month'];
        $data['type'] = $this->input['type']; //0 全部 类型1充值2消费3回退
        $where = [
            'a.userid' => $this->input['userid'],
        ];
        if ($data['type'] > 0) {
            $where['a.type'] = ['in', [$data['type']]];
        }
        if ($data['month']) {
            $where['a.create_time'] = ['between', [strtotime(getMonthRange($data['month'])), strtotime(getMonthRange($data['month'], false))]];
        }

        $sql = Db::field('id,orderid,amount,type,"0" as gameid,create_time')
            ->table('cy_member_coin_info a')->where($where)->where(['a.type' => 3])
            ->union(function ($query) use ($where) {
                $query->field('a.id,a.orderid,a.amount,a.type,"0" as gameid,a.create_time')->table('cy_member_coin_info a')
                    ->where($where)->where(['a.type' => 1]);
            })
            ->union(function ($query) use ($where) {
                $query->field('a.id,a.orderid,a.amount,a.type,b.gameid,a.create_time')->table('cy_member_coin_info a')
                    ->join('cy_pay b', 'a.orderid=b.orderid')
                    ->where($where)->where(['a.type' => 2]);
            })->buildSql();

        $list = Db::table($sql . ' a')->order('id desc')->paginate(['list_rows' => $this->input['pageSize'], 'page' => $this->input['page']])->toArray();

        $gameInfo = (new Game())->getGameInfoList();

        foreach ($list['data'] as $k => $v) {

            $list['data'][$k]['create_time'] = date('Y-m-d H:i:s', $v['create_time']);
            if ($v['type'] == 2) {
                $list['data'][$k]['game_name'] = isset($gameInfo[$v['gameid']]['name']) ? $gameInfo[$v['gameid']]['name'] : '';
                $list['data'][$k]['logo_img'] = isset($gameInfo[$v['gameid']]['mobileicon']) ? APK_DOWN_DOMAIN . '/' . $gameInfo[$v['gameid']]['mobileicon'] : '';
                $list['data'][$k]['typeStr'] = '消费';
            } else if ($v['type'] == 3) {
                $list['data'][$k]['orderid'] = str_ireplace('_T', '', $v['orderid']);
                $list['data'][$k]['game_name'] = '平台币';
                $list['data'][$k]['logo_img'] = config('PTB_LOGO');
                $list['data'][$k]['typeStr'] = '未消费释放';
            } else {
                $list['data'][$k]['game_name'] = '平台币';
                $list['data'][$k]['logo_img'] = config('PTB_LOGO');
                $list['data'][$k]['typeStr'] = '充值';
            }
        }
        $this->jsonResult($list, 1, '发送成功');
    }

    // 获取支付配置
    public function getPayInfo()
    {
        $list['pay_type'] = $this->payType();
        $list['member_coin'] = $this->getCoin();
        $list['member_zs_coin'] = $this->getZsCoin();
        $list['coupon'] = $this->getPayCoupon();
        $this->jsonResult($list, 1, '发送成功');
    }

    private function payType()
    {

        $gameModel = new Game;
        $payRuleModel = new Payrule;
        $payTypeModel = new PayType;
        $sdkModel = model('common/AndroidSdk');

        $game_id = $this->input('gameid');                   //游戏ID
        $version = $this->input('version');                  //sdk版本号   [v3.0.2 参数]

        $headerDevice = Request::instance()->header('device', 0);
        if($headerDevice == 4){
            return [
                ["paytype" => "wx_h5", "payname" => "微信支付", "pay_scene" => "wx_h5", "sort" => 1],
                ["paytype" => "ali_h5", "payname" => "支付宝", "pay_scene" => "ali_h5", "sort" => 3],
                ["paytype" => "coinpay", "payname" => "平台币", "pay_scene" => "ptb", "sort" => 4],
            ];
        }
        if (version_compare($version, config('pay_handle_version'), '>')) {
            return [
                ["paytype" => "ybzf_wxmp_h5", "payname" => "微信支付", "pay_scene" => "wx_h5", "sort" => 1],
                ["paytype" => "zfb", "payname" => "支付宝(快捷)", "pay_scene" => "ali_app", "sort" => 2],
                ["paytype" => "old-zfb-wap", "payname" => "支付宝", "pay_scene" => "ali_h5", "sort" => 3],
                ["paytype" => "coinpay", "payname" => "平台币", "pay_scene" => "ptb", "sort" => 4],
            ];
        }

        // TODO：萌战无双游戏微信支付申请-01.02
        if ($game_id == Config::get('gb_game')) {
            return [["paytype" => "ybzf_wxmp_h5", "payname" => "微信"]];
        }

        if (empty($game_id)) {
            $this->jsonResult('', 0, '游戏ID不能为空');
        } elseif (!($gameInfo = $gameModel->field('id,android_sdk_id,name,game_kind')->where(['id' => $game_id, 'isdelete' => 0])->find())) {
            $this->jsonResult('', 0, '传入游戏参数错误');
        }

        //游戏禁止的支付方式
        $denyPayType = $payRuleModel->where(["game_id" => $game_id])->column('deny_paytype');

        //专服游戏禁用平台币支付
        if ($gameInfo['game_kind'] == 1) {
            $denyPayType[] = 'ptb';
        }

        if (!empty($version)) {
            $android_sdk_id = $sdkModel->where(['version' => $version])->value('id');
            if (empty($android_sdk_id)) {
                $this->jsonResult('', 0, 'sdk版本信息不存在');
            }

            $payType = $payTypeModel->field('paytype,payname')->where(['status' => 1, 'paytype' => ['not in', $denyPayType], 'used_sdk_id' => ['like', '%,' . $android_sdk_id . ',%']])->order('sort asc')->select();
        } else {

            $payType = $payTypeModel->field('paytype,payname')->where(['status' => 1, 'paytype' => ['not in', $denyPayType], 'used_sdk_id' => ['like', '%,' . $gameInfo['android_sdk_id'] . ',%']])->order('sort asc')->select();
        }

        return $payType;
    }

    // 获取玩家平台币和折扣
    private function getCoin()
    {
        $userid = $this->input('userid');         //用户ID

        if (empty($userid)) {
            $this->jsonResult('', 0, '用户ID不能为空');
        }
        // 待支付订单，交易取消释放平台币
        model('MemberCoinInfo')->releaseCoin($userid);
        $memberInfo = model('Members')->field('id,amount,channel_id')->where(['id' => $userid])->find();
        if ($memberInfo) {
            $discount = getDiscount($memberInfo['id'], $this->input('gameid'), $memberInfo['channel_id']);
            $memberInfo['discount'] = $discount;
            $memberInfo['discount_str'] = ($discount * 10) . '折';

            return $memberInfo;
        } else {
            $this->jsonResult('', 0, '用户不存在');
        }
    }

    // 获取玩家游戏专属币
    private function getZsCoin()
    {
        $game_id = $this->input('gameid', 0);       //游戏ID
        $userid = $this->input('userid');         //用户ID
        //	$game_id = 20;
        //	$userid = 2;

        if (empty($game_id)) {
            $this->jsonResult('', 0, '游戏ID不能为空');
        }

        if (empty($userid)) {
            $this->jsonResult('', 0, '用户ID不能为空');
        }

        $memberInfo = model('Members')->field('id,username')->where(['id' => $userid])->find();
        $gameInfo = model('Game')->field('id,name')->where(['id' => $game_id])->find();
        if ($memberInfo && $gameInfo) {
            $userCoinInfo = model('MemberZscoin')->field('id,userid,username,game_id,amount,status')->where(['userid' => $userid, 'game_id' => $game_id])->find();
            $retData = array();
            $retData['userid'] = $userid;
            $retData['game_id'] = $game_id;
            if ($userCoinInfo) {
                $retData['amount'] = floatval($userCoinInfo['amount']);
                $retData['status'] = $userCoinInfo['status'];
            } else {
                $retData['amount'] = 0;
                $retData['status'] = 1;
            }
            return $retData;
        } else {
            $this->jsonResult('', 0, '用户或游戏不存在');
        }
    }

    // 用户代金券列表
    private function getPayCoupon()
    {
        $checkResult = $this->validate($this->input,
            [
                ['money', 'require|float|egt:0', '金额不能为空|金额数据有误|金额必须大于0'],
                ['gameid', 'require|integer|gt:1', '游戏ID不能为空|游戏ID必须是整数|游戏ID必须大于1'],
            ]);
        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult);
        }

        $list = model('common/CouponMember')->getPayCoupon($this->input['userid'], $this->input['gameid'], $this->input['money']);
        return $list;
    }

}
