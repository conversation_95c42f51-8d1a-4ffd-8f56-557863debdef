<?php

/**
 * 验证码
 */

namespace app\api\controller\v2;


use app\common\controller\Base;

class Captcha extends Base
{
    public function getCaptcha()
    {
        $rand = input('random');
        $config =    [
            // 验证码字体大小
            'fontSize'    =>    30,
            // 验证码位数
            'length'      =>    5,
            // 关闭验证码杂点
            'useNoise'    =>    false,
        ];
        $captcha = new \app\common\library\Captcha($config);

        log_message('Captcha.getCaptcha: random=' . $rand, 'log', LOG_PATH . 'apilog/');

        // 便于测试
        if($rand == 'eb55f905cbd4220b3c06b0e995a441d41_wen'){
            return json_encode([
                'code' =>0,
                'msg' => 'success',
                'time' => request()->server('REQUEST_TIME'),
                'data' => $captcha->entryJson($rand),
            ]);
        }
        $img = $captcha->entry($rand);
        // type = 方便测试返回json用的
        if(input('type', 0) == 1){
            return json_encode(['code' =>1, 'msg'=>'success', 'data' => ['img_str' => "data:image/png;base64,".base64_encode($img->getContent()), 'random' => $rand]]);
        }
        return $img;
    }

    public function vCaptcha()
    {
        $captcha = new \app\common\library\Captcha();
        var_dump($captcha->check(input('code'), input('random')));
    }
}