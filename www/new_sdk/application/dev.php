<?php
/**
 * 开发环境
 */

use think\Env;

return [
    // +----------------------------------------------------------------------
    // | 应用设置
    // +----------------------------------------------------------------------

    // 应用命名空间
    'app_namespace'             => 'app',
    // 应用调试模式
    'app_debug'                 => Env::get('app_debug', false),
    // 应用Trace
    'app_trace'                 => Env::get('app_trace', false),
    // 应用模式状态
    'app_status' => 'dev',

    // 视图输出字符串内容替换
    'view_replace_str'      => [
        '__STATIC__' => '/static', // 模板变量替换css、js等文件根目录
        '__OBS_URL__'=> STATIC_DOMAIN,
    ],

    /************************************************************************
    // 以下自定义设置
     ************************************************************************/

    // +----------------------------------------------------------------------
    // | 邮箱设置
    // +----------------------------------------------------------------------
    'mail'           => [
        'host'      => 'smtp.exmail.qq.com',
        'port'      => '465',
        'username'  => '',
        'password'  => '',
    ],

    // 管理员类型
    'CHANNEL'        => '2',
    'ADMIN'          => '1',

    // 登录模式
    'LOGIN_NOMAL'    => 1,
    'LOGIN_SMS'      => 2,

    // 顶级渠道ID（运营部）
    'TOP_CHANNEL_ID' => 10,

    //新平台默认空渠道(100)
    'EMPTY_CHANNEL_ID' => 100,

    // 报表导出 缓存key
    'MAKE_REPORT_KEY'				=> 'ayam:task:make-report',
    'MAKE_REPORT_UNIQUE_KEY_PRE'	=> 'ayam:task:make-report:unique-id:',

    // websocket 注册中心地址
    'WEBSOCKET_REGISTER_ADDR'       => Env::get('ws.register_addr'),
    // websocket 连接地址
    'WEBSOCKET_WS_ADDR' => Env::get('ws.ws_addr'),

    //母包的默认渠道ID
    'initial_channel_id' => 100,

    //麻花网络自己使用的聚合渠道标识（自己聚合自己时，为了固定支付回调地址）
    'self_complex_channel_mark' => 'xkhyn',

    //自己聚合自己时，固定支付回调地址
    'self_complex_callback_url' => 'http://devjhgame.' . QM_DOMAIN_URL . '/xkhyn/pay',

    // 特殊处理的 IOS游戏id 数组
    'gameIOS' => [],

    // 特殊CP游戏不支持下划线需做特别处理
    'complexSpecialGameIds' => [],

    // 推广后台判断游戏活动文章和渠道是否为旧数据的时间点   旧数据默认文章已读  2019-03-26 00:00:00
    'ARTICLE_AND_CHANNEL_TIMELINE' => 1553529600,

    // 微信公众号文章详情页网址( 加斜杠 )
    'WX_ARTICLE_LINK' => 'http://wx.' . QM_DOMAIN_URL . '/article/s/',

    // 官网相关站点网址
    'WEILONG_WEBSITE' => array(
        'HOME' => 'http://devwww.' . QM_DOMAIN_URL,
        'MOBILE' => 'http://devm.' . QM_DOMAIN_URL,
        'STATIC' => 'http://static.' . QM_DOMAIN_URL,
    ),

    // 腾讯滑块验证参数
    'SLIDER_VERIFICATION' => array(
        'VERIFYURL'    => 'https://ssl.captcha.qq.com/ticket/verify',
        'APPID'        => '2063267852',
        'AppSecretKey' => '0gvdDrugzUmThr1aaLJKokg**',
    ) ,

];
