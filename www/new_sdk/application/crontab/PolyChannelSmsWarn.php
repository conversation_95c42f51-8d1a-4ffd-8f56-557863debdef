<?php
/**
 * 聚合渠道游戏短信预警定时器脚本
 *
 */
namespace app\crontab;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Cache;
use app\common\library\Sms;
use app\common\logic\Complex;
use think\Env;

class PolyChannelSmsWarn extends Command {

    const TABLE_POLYCHANNEL_DEPOSIT     = 'cy_polychannel_deposit'; // 聚合渠道预付款配置表
    const TABLE_POLYCHANNEL_GAME_FROZEN = 'cy_polychannel_game_frozen'; // 聚合游戏冻结配置表

    protected function configure() {
        $this->setName('PolyChannelSmsWarn')->setDescription('聚合渠道游戏短信预警脚本');
    }

    protected function execute(Input $input, Output $output) {
        $list = Db::table(self::TABLE_POLYCHANNEL_DEPOSIT)->field('channel_id,mobile,total_advance,warn_limit,warn_amount')->select();

        $output->writeln(date('Y-m-d H:i:s')." start\r\n");

        if ( ! empty($list) ) {
            $complexLogic = new Complex;

            $redis = Cache::store('default');

            foreach ( $list as $row ) {
                $sms = new Sms();

                $channel_id    = $row['channel_id'];
                $mobile        = $row['mobile'];
                $total_advance = $row['total_advance'];
                $warn_limit    = $row['warn_limit'];
				$warn_amount   = $row['warn_amount'];
                $channel_name  = Db::table('nw_complex_channel')->where(['id'=>$channel_id])->value('remark');

                if ( empty($mobile) ) {
                    continue;
                }

                if ( 0 >= $total_advance || 0 > $warn_amount ) {
                    continue;
                }

                if ( is_object($redis) ) {
                    $key       = "wlsh:polyChannelSmsWarn:{$channel_id}_{$mobile}";

					$todayTimes = intval(date('H')/6);
                    $key_today = "wlsh:polyChannelSmsWarn:{$channel_id}_{$mobile}_".date('Ymd')."_".$todayTimes;

                //  $warn_amount = bcmul($total_advance, $warn_limit/100, 2);
					$totalWarnAmount = $total_advance - $warn_amount;
                    if ( $complexLogic->getChannelTotalAmount($channel_id) < $totalWarnAmount ) {
                        (int)$redis->get($key) && $redis->rm($key);
                        continue;
                    }

                    $is_send = false; // 是否发送过短信

                    $count_today = (int)$redis->get($key_today);
                    $count = (int)$redis->get($key);

					$output->writeln(date('H:i:s')."当前发送: {$count_today}_发送总数:{$count}\r\n");

                    if ( 1 <= $count_today || 12 <= $count) {
                         $is_send = true;
                    }

                    if ( ! $is_send ) {
                    //    $send = $sms->sendNotify($mobile, '您好！渠道:['.$channel_name.']的预付款余额已不足，请及时联系我方商务。');
						$remainAmount = floatval($total_advance - $complexLogic->getChannelTotalAmount($channel_id));
						$template = '存在聚合子渠道预付款余额不足，子渠道名：'.$channel_name.'，剩余额度：'.$remainAmount.' 元，请多加关注，并通知相关渠道！时间：'.date('Y-m-d H:i:s');
						$ddurl = Env::get('operat_url');
						curlDD($template, $ddurl,true);

						$params = array('name'=>'渠道：'.$channel_name,'amount'=>$remainAmount);
						if(date('Hi') >= '08:00' && date('Hi') < '22:00'){
							$send = $sms->sendNotify($mobile, config('ALI_SMS.channelAdvanceMobile'),$params);
						}
						else{
							$send = array();
							$send['status'] = 1;
							$send['msg'] = '休息时间不发送短信';
						}

                        if ( $send['status'] ) {
                             $redis->set($key_today, 1, 24*3600);

                             $value = (int)$redis->get($key)+1;
                             if ( '1' == $value ) {
                                 $redis->set($key, $value, 9000*24*3600);
                             } else {
                                $redis->inc($key);
                             }
                             $output->writeln(date('H:i:s')." {$channel_id}_{$mobile}短信发送成功\r\n");
                         } else {
                             $output->writeln(date('H:i:s')." {$channel_id}_{$mobile}短信发送失败[{$send['msg']}]\r\n");
                         }
                    } else {
                        $output->writeln(date('H:i:s')." {$channel_id}_{$mobile}今日短信已发送或已达上限\r\n");
                    }
                } else {
                    $output->writeln(date('H:i:s')." redis 实例化出错\r\n");
                }
            }
        }

        $output->writeln(date('Y-m-d H:i:s')." end\r\n");
    }
}