<?php
/**
 * 聚合渠道游戏流水统计定时器脚本
 * 
 */
namespace app\crontab;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

class PolyChannelFlowStats extends Command {

    const TABLE_PAY                     = 'nw_complex_pay'; // 聚合游戏消费表
    const TABLE_POLYCHANNEL_GAME_FROZEN = 'cy_polychannel_game_frozen'; // 聚合游戏冻结配置表

    protected function configure() {
        $this->setName('PolyChannelFlowStats')->setDescription('聚合渠道游戏流水统计脚本');
    }

    protected function execute(Input $input, Output $output) {
        $list = Db::table(self::TABLE_POLYCHANNEL_GAME_FROZEN)->field('id,game_id,channel_id,last_sum_time')->select();

        $output->writeln(date('Y-m-d H:i:s')." start\r\n");

        if ( ! empty($list) ) {
            foreach ( $list as $row ) {
                $id            = (int)$row['id'];
                $game_id       = (int)$row['game_id'];
                $channel_id    = (int)$row['channel_id'];
                $last_sum_time = (int)$row['last_sum_time'];

                if ( empty($id) || empty($game_id) || empty($channel_id) ) {
                    continue;
                }

                $total_amount   = (int)Db::table(self::TABLE_PAY)->where("channel_id='{$channel_id}' AND gameid='{$game_id}' AND `status`=1 AND create_time>'{$last_sum_time}'")->sum('amount');
                $last_sum_time  = (int)Db::table(self::TABLE_PAY)->where("channel_id='{$channel_id}' AND gameid='{$game_id}' AND `status`=1 AND create_time>'{$last_sum_time}'")->max('create_time');
                if ( 0 < $total_amount && 0 < $last_sum_time ) {
                    $update = Db::table(self::TABLE_POLYCHANNEL_GAME_FROZEN)->where("id='{$id}'")->update([
                        'total_amount'  => Db::raw("total_amount+{$total_amount}"),
                        'last_sum_time' => $last_sum_time
                    ]);

                    if ( $update ) {
                        $output->writeln(date('H:i:s')." 渠道游戏总流水更新结果：success\r\n");
                    } else {
                        $output->writeln(date('H:i:s')." 渠道游戏总流水更新结果：fail\r\n");
                    }
                }
            }
        }

        $output->writeln(date('Y-m-d H:i:s')." end\r\n");
    }
}