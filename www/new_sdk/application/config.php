<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\Env;

return [
    // +----------------------------------------------------------------------
    // | 应用设置
    // +----------------------------------------------------------------------

    // 应用命名空间
    'app_namespace'          => 'app',
    // 接口加密秘钥
    'aes_key'                => Env::get('aes_key', ''),
    'aes_key_h5'             => Env::get('aes_key_h5', ''),
    // 应用调试模式
    'app_debug'              => Env::get('app_debug', true),
    // 应用Trace
    'app_trace'              => Env::get('app_trace', false),
    // 应用模式状态
    'app_status'             => APP_STATUS,
    // 是否支持多模块
    'app_multi_module'       => true,
    // 入口自动绑定模块
    'auto_bind_module'       => false,
    // 注册的根命名空间
    'root_namespace'         => [],
    // 扩展函数文件
    'extra_file_list'        => [THINK_PATH . 'helper' . EXT],
    // 默认输出类型
    'default_return_type'    => 'html',
    // 默认AJAX 数据返回格式,可选json xml ...
    'default_ajax_return'    => 'json',
    // 默认JSONP格式返回的处理方法
    'default_jsonp_handler'  => 'jsonpReturn',
    // 默认JSONP处理方法
    'var_jsonp_handler'      => 'callback',
    // 默认时区
    'default_timezone'       => 'PRC',
    // 是否开启多语言
    'lang_switch_on'         => false,
    // 默认全局过滤方法 用逗号分隔多个
    'default_filter'         => '',
    // 默认语言
    'default_lang'           => 'zh-cn',
    // 应用类库后缀
    'class_suffix'           => false,
    // 控制器类后缀
    'controller_suffix'      => false,

    // +----------------------------------------------------------------------
    // | 模块设置
    // +----------------------------------------------------------------------

    // 默认模块名
    'default_module'         => 'admin',
    // 禁止访问模块
    'deny_module_list'       => ['common'],
    // 默认控制器名
    'default_controller'     => 'Index',
    // 默认操作名
    'default_action'         => 'index',
    // 默认验证器
    'default_validate'       => '',
    // 默认的空控制器名
    'empty_controller'       => 'Error',
    // 操作方法后缀
    'action_suffix'          => '',
    // 自动搜索控制器
    'controller_auto_search' => false,

    // +----------------------------------------------------------------------
    // | URL设置
    // +----------------------------------------------------------------------

    // PATHINFO变量名 用于兼容模式
    'var_pathinfo'           => 's',
    // 兼容PATH_INFO获取
    'pathinfo_fetch'         => ['ORIG_PATH_INFO', 'REDIRECT_PATH_INFO', 'REDIRECT_URL'],
    // pathinfo分隔符
    'pathinfo_depr'          => '/',
    // URL伪静态后缀
    'url_html_suffix'        => 'html',
    // URL普通方式参数 用于自动生成
    'url_common_param'       => false,
    // URL参数方式 0 按名称成对解析 1 按顺序解析
    'url_param_type'         => 0,
    // 是否开启路由
    'url_route_on'           => true,
    // 路由使用完整匹配
    'route_complete_match'   => false,
    // 路由配置文件（支持配置多个）
    'route_config_file'      => ['route'],
    // 是否强制使用路由
    'url_route_must'         => false,
    // 域名部署
    'url_domain_deploy'      => true,
    // 域名根，如thinkphp.cn
    'url_domain_root'        => '',
    // 是否自动转换URL中的控制器和操作名
    'url_convert'            => false,
    // 默认的访问控制器层
    'url_controller_layer'   => 'controller',
    // 表单请求类型伪装变量
    'var_method'             => '_method',
    // 表单ajax伪装变量
    'var_ajax'               => '_ajax',
    // 表单pjax伪装变量
    'var_pjax'               => '_pjax',
    // 是否开启请求缓存 true自动缓存 支持设置请求缓存规则
    'request_cache'          => false,
    // 请求缓存有效期
    'request_cache_expire'   => null,
    // 全局请求缓存排除规则
    'request_cache_except'   => [],

    // +----------------------------------------------------------------------
    // | 模板设置
    // +----------------------------------------------------------------------

    'template'               => [
        // 模板引擎类型 支持 php think 支持扩展
        'type'         => 'Think',
        // 模板路径
        'view_path'    => '',
        // 模板后缀
        'view_suffix'  => 'html',
        // 模板文件名分隔符
        'view_depr'    => DS,
        // 模板引擎普通标签开始标记
        'tpl_begin'    => '{',
        // 模板引擎普通标签结束标记
        'tpl_end'      => '}',
        // 标签库标签开始标记
        'taglib_begin' => '{',
        // 标签库标签结束标记
        'taglib_end'   => '}',
    ],

    // 视图输出字符串内容替换
    'view_replace_str'       => [],
    // 默认跳转页面对应的模板文件
    'dispatch_success_tmpl'  => THINK_PATH . 'tpl' . DS . 'dispatch_jump.tpl',
    'dispatch_error_tmpl'    => THINK_PATH . 'tpl' . DS . 'dispatch_jump.tpl',

    // +----------------------------------------------------------------------
    // | 异常及错误设置
    // +----------------------------------------------------------------------

    // 异常页面的模板文件
    'exception_tmpl'         => THINK_PATH . 'tpl' . DS . 'think_exception.tpl',

    // 错误显示信息,非调试模式有效
    'error_message'          => '页面错误！请稍后再试～',
    // 显示错误信息
    'show_error_msg'         => false,

    // 异常处理handle类 留空使用 \think\exception\Handle
    'exception_handle'       => '\\app\\common\\exception\\Http',

    // +----------------------------------------------------------------------
    // | 日志设置
    // +----------------------------------------------------------------------

    'log' => [
        // 日志记录方式，内置 file socket 支持扩展
        'type'  => 'File',
        // 日志保存目录
        'path'  => LOG_PATH,
        // 日志记录级别
        'level' => ['log', 'error', 'notice', 'info', 'debug', 'sql'],
        'max_files'	=> 30,
        'realtime_write' => true,
    ],

    // +----------------------------------------------------------------------
    // | Trace设置 开启 app_trace 后 有效
    // +----------------------------------------------------------------------
    'trace'                  => [
        // 内置Html Console 支持扩展
        'type' => 'Html',
    ],

    // +----------------------------------------------------------------------
    // | 缓存设置
    // +----------------------------------------------------------------------
    'cache' => [
        // 驱动方式
        'type'    => 'complex',

        // 默认使用的缓存redis缓存
        'default' => [
            // 驱动方式
            'type'   => 'redis',
            // 服务器地址
            'host'   => Env::get('redis.host', '127.0.0.1'),
            //端口号
            'port'   => Env::get('redis.port', 6379),
            //密码
            'password' => Env::get('redis.password', ''),
            // 缓存前缀
            'prefix' => Env::get('redis.prefix', 'newsdk_'),
            //缓存redis库
            'select' => Env::get('redis.select', 0),
            'expire' => 60 * 60 * 24 * 7,
        ],

        // 文件缓存
        'File'    => [
            // 驱动方式
            'type'   => 'File',
            // 缓存保存目录
            'path'   => CACHE_PATH,
            // 缓存前缀
            'prefix' => '',
            // 缓存有效期 0表示永久缓存
            'expire' => 60 * 60 * 24 * 30,
        ],
        // redis缓存
        'redis'   => [
            // 驱动方式
            'type' => 'redis',
            // 服务器地址
            'host'   => Env::get('redis.host', '127.0.0.1'),
            //端口号
            'port'   => Env::get('redis.port', 6379),
            //密码
            'password' => Env::get('redis.password', ''),
            // 缓存前缀
            'prefix' => Env::get('redis.prefix', 'newsdk_'),
            //缓存redis库
            'select' => Env::get('redis.select', 0),
            'expire' => 60 * 60 * 24 * 7,
        ],
    ],

    // +----------------------------------------------------------------------
    // | 会话设置
    // +----------------------------------------------------------------------
    'session'               => [
        'id'             => '',
        // SESSION_ID的提交变量,解决flash上传跨域
        'var_session_id' => '',
        // SESSION 前缀
        'prefix'         => 'think_',
        // 驱动方式 支持redis memcache memcached
        'type'           => 'redis',
        // 是否自动开启 SESSION
        'auto_start'     => true,
        // 服务器地址
        'host'   => Env::get('redis.host', '127.0.0.1'),
        //端口号
        'port'   => Env::get('redis.port', 6379),
        //密码
        'password' => Env::get('redis.password', ''),
        //缓存redis库
        'select' => Env::get('redis.select', 0),
        // 是否长连接
        'persistent'     => false,
        //session过期时间
        'expire'         => 60 * 60 * 5 * 7,

        'cache_expire'   => 60 * 60 * 5 * 7,
    ],

    // +----------------------------------------------------------------------
    // | Cookie设置
    // +----------------------------------------------------------------------
    'cookie'         => [
        // cookie 名称前缀
        'prefix'    => 'think_',
        // cookie 保存时间
        'expire'    => 2 * 60 * 60 * 7,
        // cookie 保存路径
        'path'      => '/',
        // cookie 有效域名
        'domain'    => '',
        //  cookie 启用安全传输
        'secure'    => false,
        // httponly设置
        'httponly'  => '',
        // 是否使用 setcookie
        'setcookie' => true,
    ],

    //分页配置
    'paginate'               => [
        'type'      => 'bootstrap',
        'var_page'  => 'page',
        'list_rows' => 15,
    ],

    // +----------------------------------------------------------------------
    // | 数据库设置
    // +----------------------------------------------------------------------
    'database'               => [
        // 数据库类型
        'type'            => 'mysql',
        // 服务器地址
        'hostname'        => Env::get('database.hostname', '127.0.0.1'),
        // 数据库名
        'database'        => Env::get('database.database', '46px'),
        // 用户名
        'username'        => Env::get('database.username', 'root'),
        // 密码
        'password'        => Env::get('database.password', ''),
        // 端口
        'hostport'        => Env::get('database.hostport', 3306),
        // 连接dsn
        'dsn'             => '',
        // 数据库连接参数
        'params'          => [],
        // 数据库编码默认采用utf8
        'charset'         => 'utf8mb4',
        // 数据库表前缀
        'prefix'          => '',
        // 数据库调试模式
        'debug'           => false,
        // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
        'deploy'          => 0,
        // 数据库读写是否分离 主从式有效
        'rw_separate'     => false,
        // 读写分离后 主服务器数量
        'master_num'      => 1,
        // 指定从服务器序号
        'slave_no'        => '',
        // 是否严格检查字段是否存在
        'fields_strict'   => true,
        // 数据集返回类型
        'resultset_type'  => 'array',
        // 自动写入时间戳字段
        'auto_timestamp'  => false,
        // 时间字段取出后的默认时间格式
        'datetime_format' => 'Y-m-d H:i:s',
        // 是否需要进行SQL性能分析
        'sql_explain'     => false,
        // 开启断线重连
        'break_reconnect' => true,
    ],
    'database_slave'               => [
        // 数据库类型
        'type'            => 'mysql',
        // 服务器地址
        'hostname'        => Env::get('database.hostname', '127.0.0.1'),
        // 数据库名
        'database'        => Env::get('database.database', '46px'),
        // 用户名
        'username'        => Env::get('database.username', 'root'),
        // 密码
        'password'        => Env::get('database.password', ''),
        // 端口
        'hostport'        => Env::get('database.hostport', 3306),
        // 连接dsn
        'dsn'             => '',
        // 数据库连接参数
        'params'          => [],
        // 数据库编码默认采用utf8
        'charset'         => 'utf8mb4',
        // 数据库表前缀
        'prefix'          => '',
        // 数据库调试模式
        'debug'           => false,
        // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
        'deploy'          => 0,
        // 数据库读写是否分离 主从式有效
        'rw_separate'     => false,
        // 读写分离后 主服务器数量
        'master_num'      => 1,
        // 指定从服务器序号
        'slave_no'        => '',
        // 是否严格检查字段是否存在
        'fields_strict'   => true,
        // 数据集返回类型
        'resultset_type'  => 'array',
        // 自动写入时间戳字段
        'auto_timestamp'  => false,
        // 时间字段取出后的默认时间格式
        'datetime_format' => 'Y-m-d H:i:s',
        // 是否需要进行SQL性能分析
        'sql_explain'     => false,
    ],

    // +----------------------------------------------------------------------
    // | 多数据库配置
    // +----------------------------------------------------------------------
    //游戏角色数据库
    'db_config_role' => [
        // 数据库类型
        'type'     => 'mysql',
        // 服务器地址
        'hostname' => '',
        // 数据库名
        'database' => '',
        // 数据库用户名
        'username' => '',
        // 数据库密码
        'password' => '',
        // 端口
        'hostport' => '3306',
        // 数据库编码默认采用utf8
        'charset'  => 'utf8mb4',
        // 数据库表前缀
        'prefix'   => '',
    ],
    // 微信公众号管理后台数据库
    'db_config_wechat' => [
        // 数据库类型
        'type'     => 'mysql',
        // 服务器地址
        'hostname' => '',
        // 数据库名
        'database' => '',
        // 数据库用户名
        'username' => '',
        // 数据库密码
        'password' => '',
        // 数据库编码默认采用utf8
        'charset'  => 'utf8mb4',
        // 数据库表前缀
        'prefix'   => '',
    ],



    'h5_alipay_config' => [
        'zfbAppId'           => '',
        'key'       => '',
        'notify_url'        => 'http://' . HTTP_HOST_URL . '/v1.pay_notify/zfbxjzfipaynow.html',
        'notify_coin_url'        => 'http://' . HTTP_HOST_URL . '/v1.pay_notify_coin/zfbxjzfipaynow.html',
        'return_url'        => 'http://' . HTTP_HOST_URL . '/v1.pay_return/pay_success_xjzf.html',
    ],

    /*支付宝wap支付参数*/
    'alipay_wap_config' => [
        'partner'           => '',
        'seller_id'         => '', //和上面一样
        'key'               => 'qktcd2jirrv4zkwwlegvyndfynyrra1f',
        'notify_url'        => 'http://' . HTTP_HOST_URL . '/v1.pay/alipay_wap_notify.html',
        'return_url'        => 'http://' . HTTP_HOST_URL . '/v1.pay/alipay_wap_return.html',
        'notify_coin_url'   => 'http://' . HTTP_HOST_URL . '/v1.pay_coin/alipay_wap_notify.html',
        'sign_type'         => strtoupper('MD5'),
        'input_charset'     => strtolower('utf-8'),
        'cacert'            => getcwd() . '\\cacert.pem', //需要放在根目录
        'transport'         => 'http',
        'payment_type'      => '1',
        'service'           => 'alipay.wap.create.direct.pay.by.user'
    ],

    //支付宝配置(使用麻花网络SDK中的新支付宝账号的参数)
    'alipay'    => [
        //签名方式
        'sign_type'         => 'RSA',

        //合作身份者ID，签约账号，以2088开头由16位纯数字组成的字符串
        'partner'           => '',

        //卖家支付宝账号
        'seller_id'         => '',

        //商户的私钥,此处填写原始私钥去头去尾，RSA公私钥生成：https://doc.open.alipay.com/doc2/detail.htm?spm=a219a.7629140.0.0.nBDxfy&treeId=58&articleId=103242&docType=1
        'private_key'       => VENDOR_PATH . 'alipay/key/rsa_private_key.pem',

        //支付宝的公钥，查看地址：https://b.alipay.com/order/pidAndKey.htm
        'alipay_public_key' => VENDOR_PATH . 'alipay/key/rsa_public_key.pem',

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url'        => "http://" . HTTP_HOST_URL . "/v1.pay_notify/alipay.html",

        //字符编码格式 目前支持 gbk 或 utf-8
        'input_charset'     => 'utf-8',

        //ca证书路径地址，用于curl中ssl校验
        'cacert'            => VENDOR_PATH . 'alipay/cacert.pem',

        // 支付类型 ，无需修改
        'payment_type'      => "1",

        //访问模式,根据自己的服务器是否支持ssl访问，若支持请选择https；若不支持请选择http
        'transport'         => 'http',
    ],

    //支付宝配置(使用麻花网络SDK中的新支付宝账号的参数) -- 准备弃用
    'alipayAop'    => [
        //签名方式
        'sign_type'         => 'RSA2',

        //合作身份者ID，签约账号，以2088开头由16位纯数字组成的字符串
        'appId'                => '2021004118630047',

        //商户的私钥,此处填写原始私钥去头去尾，RSA公私钥生成：https://doc.open.alipay.com/doc2/detail.htm?spm=a219a.7629140.0.0.nBDxfy&treeId=58&articleId=103242&docType=1
        'private_key'       => VENDOR_PATH . 'alipayAop/key/rsa_private_key.txt',

        //支付宝的公钥，查看地址：https://b.alipay.com/order/pidAndKey.htm
        'alipay_public_key' => VENDOR_PATH . 'alipayAop/key/rsa_public_key.txt',

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url'        => "http://" . HTTP_HOST_URL . "/v1.pay_notify/alipayAop.html",

        'notify_coin_url'        => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/alipayAop.html",
    ],

    // 支付宝公共配置(多个应用)
    'ali_pay_config'    => [
        'sign_type'         => 'RSA2',
        'notify_url'        => "http://" . HTTP_HOST_URL . "/v1.pay_notify/alipayAop.html", // 发起支付（游戏内购买道具）
        'notify_coin_url'        => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/alipayAop.html", // 发起充值（祈盟充值）

        // 相关支付类型使用的应用
        'default' => [
            'zfb' => '2021002136682180', // 支付宝-app/扫码
            'old-zfb-wap' => '2021004118630047', // 支付宝-H5
        ],

        // 相关应用的配置
        'configs' => [
            // ## 主体 - 应用名(支持的支付类型) ##

            // ## 浙江祈点网络科技有限公司 ##
            // ### 应用2.0 - H5/APP
            '2021002136682180' => [
                'app_id' => '2021002136682180',
                'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAryfPajm71/7PCyRMfiEz/3G98K6Zsv/7IGflMtEWzayUPaNNt9AkvBmVmTQaCH2P1zjtatE/bfYA56j2EOU1qlZxZddNlFiJK4+RzZ1N9XzLXlr0yybhgEB/mxkPK7063F9eCYYTkEbVG7zzN9QmR/0s/rZv9lN3U2bWi837m+ynNYg7C+xthx/LGRnkXc1UuBiVHK0jTZwcggalUrlZ1JLnL3sc4S38+DGK5e5eQDU3pmnYM0A44QzoKrkHF2IXkgyOGwcHz1evN8lfURqK3W18RQQ/Bo5CwkXanbvw3/seCy+/rEPTm++oS/3HM8ZRH1GU84YwcDBeDt0+caTcbwIDAQAB',
                'private_key' => 'MIIEowIBAAKCAQEAqWk0DcM3DAM2mqW0Oyis9IyL5BnDV8QWifR5+WSR30Fv0bjrPDnMbNFyX6zxV1KHQxFL/kuh/Meotz8YrIV0IF6X7rZCysl/7avi8iSTz3PpiD2CRHfBeFnGWdc+9k/pQV2QFdVQCD6JUoWIl4YIgUIWGwBKC0sYlhVp3JeeQq2BvyTcaX4frC/oZjh0WEvj8g7iaE6MurAtxDo7u9IFH3cmRNDO0qyZ4UBfKl23+i1viCyRTUjOoLJ1/N11q0fYc/SWv+J9iGGsjdCGFdE3bcxA+G/gZ/CFXN2x+nh9If9CTt+egW1epXM5FOldsLj8ejSV5vgC6NvHHUzTsyGVzwIDAQABAoIBAG8irdxNcWI3BW5EQE0C7RtszCSyZfzAjYyozg+2Ux+jvOhXKDKVNC86444yVQzU/hkYUsMa+MRscpjsEpKy5bCKfZ9C/+QqdfsD5Ni2+CSCxPP0be3ephP+yOmNFCff2oRgDbLVAjC2Xb2+DhJhAllRclD6SpjIBfQHTjBBYrgYK20QAH18T/kfScaZb1SxmqiDJKc/kmua2MZk0cb01HgkXGJIeZReT9wMhmEPKRaFodCvLxSn+fHr5H0o2LFwfKMF1LfQpiqetYCoQJgBUWyh/8J3kCpts1QnVL/UWvRaGfFXantmZ4u7zgYY632MHFoPgSCLkbXAXtwjXFTEJjECgYEA2NaRmRPWL7+Nw0aZ4JDwK4MQeN+S8G5IZOS+8qci6Au9whNPM/g2MftJVuKR6Yc0ha41P9WnSJ0qh04H5Gps7JI55hOh4ys6fxt7KCbTPd2dUFe3g4BxlOVwiewvzVRldnplY1PSB38O/e56nx9hiWH7HMMXjNxBae2MAq08QNcCgYEAyAHa9pvG4TnlmQQLCDw6EFIXkBlLON0por7CH+g04kbl1pk3RTABQamexdfUy/razkS8R/uZaPHaaTs4cq/55umm4A9/wxQwFzLWKpMoMgMDNsrYlayTyY4URNa0U6XRlnUh7v3xKY/TNKqTAm8R8yNlWz+bx+jBtRngDQBkG8kCgYBLAAxkMiRHZ64U+PqIXhl0M2UjKdGdL8gCfcw+Celc8ZK06hDfHN9B8nSvhLaLW6SjCFqKtc+iohnvquG388exG/LCzc+Uz4LwLAUxIhy2rUyYWX60EV037xQ54GO4VZSyoisna/qddSkuU6o+F5CZADbm/+PJaPWHOg7PbjQFCQKBgGqx2q4twbWKnjm3l28IOe1U7a74X1FxYfGok4RT6ko/igKl738gMbtmkgV2stUx+CGLN2mrBVaflmUGq4E68TRGtrfWbNCTZ2govWku4YV0Z/WF1m5aBujBu3xRkRcCYUFDMRv+KfWC/SeeSxhESi4LVtmLoD3Po+5JZGVrLPxJAoGBALx1P0wxDTqeJW/oTxiKvsz9dG00S42fdm9inG3jG3HOMychYWcS52bUGQreZI2hyIsCbo0REP1Htc1rXjmYfD48PL3D9Y5uFhzjr8mpyr5BsL/VwrTYiBDmpZ6T3gDu7IWZNgYNDYPIzg11vvl04kKajHN/xD0aKjklmE4btlqf',
            ],
            // #### 祈盟SDK支付 - H5
            '2021004118630047' => [
                'app_id' => '2021004118630047',
                'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAryfPajm71/7PCyRMfiEz/3G98K6Zsv/7IGflMtEWzayUPaNNt9AkvBmVmTQaCH2P1zjtatE/bfYA56j2EOU1qlZxZddNlFiJK4+RzZ1N9XzLXlr0yybhgEB/mxkPK7063F9eCYYTkEbVG7zzN9QmR/0s/rZv9lN3U2bWi837m+ynNYg7C+xthx/LGRnkXc1UuBiVHK0jTZwcggalUrlZ1JLnL3sc4S38+DGK5e5eQDU3pmnYM0A44QzoKrkHF2IXkgyOGwcHz1evN8lfURqK3W18RQQ/Bo5CwkXanbvw3/seCy+/rEPTm++oS/3HM8ZRH1GU84YwcDBeDt0+caTcbwIDAQAB',
                'private_key' => 'MIIEowIBAAKCAQEAirgWDviEvfLIqfpqEZsBsNaeq3v/Pwwb1PJulMHdq20olb/dNt7UXqgQOLVlCJQUKMhoZktFrouduqIYd78dOo9z56ZpYQGs+YQzbE771m4giJjeRlwTnCDvcwHGoiQYHCoZ1KjIX6RfDxd0l9v4Dh/LuC9l62ikGsM3n6iepQmSlophJ8gvkQ7P2NmvGKq6md+Ck6/mfMfjq4THyI/PTU+Os9yA0l0wcv6cXiUjMLkbxpI5YX7UXovO4FPvKSqQFqwq++LJSksWVTL77HNR/2N5CkePC2hYlYvthW/CY1roIc5zTAsEae8M77Pp/SipR+9kJ1nEGCSlQtMQ2q+7nwIDAQABAoIBAH73qXEAdPcrDhHOm2EL4ItMlaRd9S1u1tadIyvTGiK64efW2hJL+FO/PkG5fgVRC/acIDpo8RmmvrLBhOWtroIYJT1FKrTYhMtI/oxG6ujEmdCNIv2Bb41688/vMzDbGppEyqnD5srrtvooPNZ2RuAeqCcocv/6IiJAxlvDSZnB/9208NDZUVVWriX5Ws4Qtec8Jre+w8wQVCmlPoPOtr9vibt0adu2DoRudwcTZ2GBLfvEmIIlLqsb6biHxBepl1+ZmwnG8EzjXXyEK5KdodnthU226Hqlu4OT0+X4L7RrSXeJizrJ8brmEWGeZBd5ekGLXvV1/db+pdMR9L5EFKkCgYEA4Pcios4lyeagZitXwA5cKuiDnUIBzDqwVN4tE0qEwApsw8+NVNOxAedhIo8cuL263fLffJTy2KR8T0P1YfxV9mu4YrdDL7qsJFzGBo2iTzUSVyPJY5pRY84VCTkDYYDxaf4DjDBwn4++1INxVBp//57DJyB2WLfawGs0Ki2V09UCgYEAndsU8FJprIUnEBC8Bu76dzr695tTiODpMkt6XezozwXQPFhlMBDRwQQj6WdD7oKlEKyvMsgXta76oUbudx81jwpRsxgdhwxfV3DIDHFSCiDfnkYfkf421Mha2/oxOqOAX17Fyn/x//FBnmbXTwdeEXO3fg0CLPtfKkIWuzZu76MCgYEA4EdiP39YCy6i8Nj7NdzbJGgEdu5MjohgLWwG8jmTmUWLY7fzRgN6b9R2cZbN4pw8xXOSPUjdxwBNclTlOVfxd6ey/q+ICviHEAVx4yynCWJ2s2tZJrS9ucvGCxjM9xMvGyWD8eqFDX6omIOmo1t8CqMEGmjxs8cwDi+SpQ4nVjUCgYBLtDO4hW2UhU8Uj1H9aULJR4nwkZdgP6zWqAgJX4LPNhSFvEba5FYT4S5v00xq6FDNTGPBUOC+Cyq7ic73ZgpDjUldQKV4jOB0iOwHu5kCrn1P5IVjeK1HIscgVD6jxqHcGxF0aiPfAQ7dLqj3h6r/Z6WKWCbA99TN6WAJbLOLTQKBgBWZRlQ3fB6gLDOcLmD2pbXMwo2yTmTec0PhCDcTcTxWTrcxYbDiEGaehS/l6VxSiDNlMDh+A9exSW2XXC0C4WNJ4dv2c+XDEHM4stoFkuIPx5P0yZWLxvYwoM4TslmrtoyGzZvb0iSW9QmSUDtCoyM3HqvvOyHnkjbcNUndwlvS',
            ],

            // ## 杭州祈盟文化传媒有限公司 ## 不能使用，IPC审核未通过
            // // ### 应用2.0 - H5/APP
            // '2021004105643269' => [
            //     'app_id' => '2021004105643269',
            //     'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1eEPzFViqibSmxgYnVw9abkUl/9Ak72H3DwFqFTqJqo+AZuNS07HbRsFk9ALafoNFeFC4YOo7E89lzGxXiKSOPTRCh9dJkFZqptphxv7uHnVVULwkpOaqnXm2rx5hKSqA2kD7NeXTGuwPD+8NM4vBDGXAMRg8fx0MG2MGWqeaByiLqkJbo1Em4aBomjQaF/MiJbMA2CgY7h46YiVgELq+JquktO7lssV/VByRaotwAPLFWd7RjoBViLIaDLUnmK25s7DLWX2xuMPg5TkpOMHx6p+pnow/ddseN4hRJABygPSc8xqKQoSw7/+atsgDWUZEP8qclR/88CmgL1EeOFN6QIDAQAB',
            //     'private_key' => 'MIIEogIBAAKCAQEAibZSYJPnm9qcXB8N8lvNDX8RN3k7aOJs1ofnj/5EztL83OxZNwx11VTRe5kamNve1LtMtAqZtCJRzztOLbt7NTPFAIIGgnwEU8pvdh5PXuMUow4YmAsDS39LYqCy3DN9p/OLbxCYZD9Ep9uxvPIGG8NxU1MEX0zxPPlFVsYoKsPD5cf8/87jUrLGoEJ5xSSDJbqal+yLNkTpg++00fW3IMh8qRnxtPmiaCrnj8gxcuUvE0XKbx+CIP1bW8SWedQ58bDV3IOGG5TcXIppUBMjLWCja4fBI27ETe8/My9yWOez4DyFrwVVro/PZ7qYJM5bWNGyEAyWEU864BUrkkQJTQIDAQABAoIBACJSjKmBc4JGtB3Jp6I1+x9YAiR/VK1JcbLuYGplByU9NMSjK4Eq29t1rlhNynE6q4j9vZpJ4hEhVJ5/u3ZuecDCzGDaB2FSI4n2tF0OJN4TXrtc8ghDBmk0qsdEIg/7zV+ktaXSzwh9NFFZ0iyzbY5R4dbQpBBUjvORdRSxgqVVk8hmaTKras4CcywfVojj3lO3oWQgAMbOPVm56Rp8tChVIGIXzzH9m3cUcjmMN5GERYZHzmLuxfNMDC/g+KSWAsgWlhEu05nnj1yCh/dYBmEdAiyU6ROpowHYOyxE+aRff1MPfQoNdeIrY5imCWpTNopq7cFHNyLDeiAPe4JfBokCgYEAwGNS/wAWPY0mXpoNpzYGbAbhRL+8ucXHqnlkJ3trw6sWoCe3+ZdstMirn37VyqNN78hbh4XoXOY1+FFXZBYdso5rmwbUgpZ6gMTxOSSOmN1cXnGZiXj3LCs6fBU8mIZOZ867TfYipBXuT2KlGZnfpocqKk5SuUxOi050GEg3R5MCgYEAtz73fY8OKq+uTcdpuUJwkrELuMZ0Y2dJ/mApZVWyEDgEijw7LKdlaO0UPzoQwg/yKigx0CSY5hEm37QdIcB8ZH+d531+ioOkkoI46w4FiDHPHk1bDg+rzj8N9Lq0uaQQYlCnTRNnaYnQ1fAOiNmJml53qatkiHM2Ve1yYIb6N58CgYATe7uTAYI/F4VUPUu2rFz4IbY8jSJiuenflM2UxgYC2de/vmX+S9yBbnUbAPBLxkAFUYgbcG84wUOj8zDjfO3jAFNbbtSkqPKdCdTYEaCzUG+jTOPcrThy66nPmSL621cjYidL3OpjLalltrq7nf6tUbDueeIcy+KEbxyIvP+EKwKBgGEfzq2OFUQquYggGblHrcMSc9QL28SUCnP5I/fB5A8UodNi+TpBv05N2l1ouVkieog9palcvG20yZXoMUiibUwXF+qKaXehLqk4vFJZrDTzsYOuHITnaXh3kz4Td5atjsGNXh9MGZOmqDm8cve11WksiOA1J6Pq7PG+WNEwuY6hAoGATWnSxgJpp/W0LDPmqy1pUmAoeoxTxyfQmUVmOAqoLY+Mxg8UW22sxqH0konozQYO891bwIF0RSFAOxwkUCiGXxotojTyC0Dpow9g6bgyYu8Aq/L26kbfSl9d+JuU0KSfhNQulhoBXBkFdHlXh1ABpgy4bGZiIIHy26vFMAL4HcE=',
            // ],
            // // ### 祁盟网络 - H5
            // '2021004108688386' => [
            //     'app_id' => '2021004108688386',
            //     'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1eEPzFViqibSmxgYnVw9abkUl/9Ak72H3DwFqFTqJqo+AZuNS07HbRsFk9ALafoNFeFC4YOo7E89lzGxXiKSOPTRCh9dJkFZqptphxv7uHnVVULwkpOaqnXm2rx5hKSqA2kD7NeXTGuwPD+8NM4vBDGXAMRg8fx0MG2MGWqeaByiLqkJbo1Em4aBomjQaF/MiJbMA2CgY7h46YiVgELq+JquktO7lssV/VByRaotwAPLFWd7RjoBViLIaDLUnmK25s7DLWX2xuMPg5TkpOMHx6p+pnow/ddseN4hRJABygPSc8xqKQoSw7/+atsgDWUZEP8qclR/88CmgL1EeOFN6QIDAQAB',
            //     'private_key' => 'MIIEogIBAAKCAQEAibZSYJPnm9qcXB8N8lvNDX8RN3k7aOJs1ofnj/5EztL83OxZNwx11VTRe5kamNve1LtMtAqZtCJRzztOLbt7NTPFAIIGgnwEU8pvdh5PXuMUow4YmAsDS39LYqCy3DN9p/OLbxCYZD9Ep9uxvPIGG8NxU1MEX0zxPPlFVsYoKsPD5cf8/87jUrLGoEJ5xSSDJbqal+yLNkTpg++00fW3IMh8qRnxtPmiaCrnj8gxcuUvE0XKbx+CIP1bW8SWedQ58bDV3IOGG5TcXIppUBMjLWCja4fBI27ETe8/My9yWOez4DyFrwVVro/PZ7qYJM5bWNGyEAyWEU864BUrkkQJTQIDAQABAoIBACJSjKmBc4JGtB3Jp6I1+x9YAiR/VK1JcbLuYGplByU9NMSjK4Eq29t1rlhNynE6q4j9vZpJ4hEhVJ5/u3ZuecDCzGDaB2FSI4n2tF0OJN4TXrtc8ghDBmk0qsdEIg/7zV+ktaXSzwh9NFFZ0iyzbY5R4dbQpBBUjvORdRSxgqVVk8hmaTKras4CcywfVojj3lO3oWQgAMbOPVm56Rp8tChVIGIXzzH9m3cUcjmMN5GERYZHzmLuxfNMDC/g+KSWAsgWlhEu05nnj1yCh/dYBmEdAiyU6ROpowHYOyxE+aRff1MPfQoNdeIrY5imCWpTNopq7cFHNyLDeiAPe4JfBokCgYEAwGNS/wAWPY0mXpoNpzYGbAbhRL+8ucXHqnlkJ3trw6sWoCe3+ZdstMirn37VyqNN78hbh4XoXOY1+FFXZBYdso5rmwbUgpZ6gMTxOSSOmN1cXnGZiXj3LCs6fBU8mIZOZ867TfYipBXuT2KlGZnfpocqKk5SuUxOi050GEg3R5MCgYEAtz73fY8OKq+uTcdpuUJwkrELuMZ0Y2dJ/mApZVWyEDgEijw7LKdlaO0UPzoQwg/yKigx0CSY5hEm37QdIcB8ZH+d531+ioOkkoI46w4FiDHPHk1bDg+rzj8N9Lq0uaQQYlCnTRNnaYnQ1fAOiNmJml53qatkiHM2Ve1yYIb6N58CgYATe7uTAYI/F4VUPUu2rFz4IbY8jSJiuenflM2UxgYC2de/vmX+S9yBbnUbAPBLxkAFUYgbcG84wUOj8zDjfO3jAFNbbtSkqPKdCdTYEaCzUG+jTOPcrThy66nPmSL621cjYidL3OpjLalltrq7nf6tUbDueeIcy+KEbxyIvP+EKwKBgGEfzq2OFUQquYggGblHrcMSc9QL28SUCnP5I/fB5A8UodNi+TpBv05N2l1ouVkieog9palcvG20yZXoMUiibUwXF+qKaXehLqk4vFJZrDTzsYOuHITnaXh3kz4Td5atjsGNXh9MGZOmqDm8cve11WksiOA1J6Pq7PG+WNEwuY6hAoGATWnSxgJpp/W0LDPmqy1pUmAoeoxTxyfQmUVmOAqoLY+Mxg8UW22sxqH0konozQYO891bwIF0RSFAOxwkUCiGXxotojTyC0Dpow9g6bgyYu8Aq/L26kbfSl9d+JuU0KSfhNQulhoBXBkFdHlXh1ABpgy4bGZiIIHy26vFMAL4HcE=',
            // ],

            // ## 杭州禹米网络科技有限公司 ##
            // ### APPSDK支付 - APP/H5/电脑网站支付
            '2021004121658022' => [
                'app_id' => '2021004121658022',
                'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwi5X6Lq9141MQmTVb1zL73U9n9xqnmLttmZbyphPTwkdZIE5k+Hj63fKnit6cRrJOtmm3D7h2uBlTYiak12q/WgEugfFcqLyjHTN48ttAX3+kmNPFP1AKeIMNICTKaudeEUzC39OiQIDoEKxahEkaRMOi+cMJLyqZ1iNSu5icyGqds/ZKWIBxPl7HwK3P2LEoN/hchX9QlJreLR6lAMcbooPf8q09YtBxyluDyK3ojjZ4MNQJ4pwZFhX0rRYxbM0xd8xvbLhbzc823DHhi7JMhDvqXBsLrG8nMmqEnZ+iiex/h+ebvIR5KLYKT0Wv/p9CUwamtWryXJr4gUEHnX6BwIDAQAB',
                'private_key' => 'MIIEogIBAAKCAQEAibZSYJPnm9qcXB8N8lvNDX8RN3k7aOJs1ofnj/5EztL83OxZNwx11VTRe5kamNve1LtMtAqZtCJRzztOLbt7NTPFAIIGgnwEU8pvdh5PXuMUow4YmAsDS39LYqCy3DN9p/OLbxCYZD9Ep9uxvPIGG8NxU1MEX0zxPPlFVsYoKsPD5cf8/87jUrLGoEJ5xSSDJbqal+yLNkTpg++00fW3IMh8qRnxtPmiaCrnj8gxcuUvE0XKbx+CIP1bW8SWedQ58bDV3IOGG5TcXIppUBMjLWCja4fBI27ETe8/My9yWOez4DyFrwVVro/PZ7qYJM5bWNGyEAyWEU864BUrkkQJTQIDAQABAoIBACJSjKmBc4JGtB3Jp6I1+x9YAiR/VK1JcbLuYGplByU9NMSjK4Eq29t1rlhNynE6q4j9vZpJ4hEhVJ5/u3ZuecDCzGDaB2FSI4n2tF0OJN4TXrtc8ghDBmk0qsdEIg/7zV+ktaXSzwh9NFFZ0iyzbY5R4dbQpBBUjvORdRSxgqVVk8hmaTKras4CcywfVojj3lO3oWQgAMbOPVm56Rp8tChVIGIXzzH9m3cUcjmMN5GERYZHzmLuxfNMDC/g+KSWAsgWlhEu05nnj1yCh/dYBmEdAiyU6ROpowHYOyxE+aRff1MPfQoNdeIrY5imCWpTNopq7cFHNyLDeiAPe4JfBokCgYEAwGNS/wAWPY0mXpoNpzYGbAbhRL+8ucXHqnlkJ3trw6sWoCe3+ZdstMirn37VyqNN78hbh4XoXOY1+FFXZBYdso5rmwbUgpZ6gMTxOSSOmN1cXnGZiXj3LCs6fBU8mIZOZ867TfYipBXuT2KlGZnfpocqKk5SuUxOi050GEg3R5MCgYEAtz73fY8OKq+uTcdpuUJwkrELuMZ0Y2dJ/mApZVWyEDgEijw7LKdlaO0UPzoQwg/yKigx0CSY5hEm37QdIcB8ZH+d531+ioOkkoI46w4FiDHPHk1bDg+rzj8N9Lq0uaQQYlCnTRNnaYnQ1fAOiNmJml53qatkiHM2Ve1yYIb6N58CgYATe7uTAYI/F4VUPUu2rFz4IbY8jSJiuenflM2UxgYC2de/vmX+S9yBbnUbAPBLxkAFUYgbcG84wUOj8zDjfO3jAFNbbtSkqPKdCdTYEaCzUG+jTOPcrThy66nPmSL621cjYidL3OpjLalltrq7nf6tUbDueeIcy+KEbxyIvP+EKwKBgGEfzq2OFUQquYggGblHrcMSc9QL28SUCnP5I/fB5A8UodNi+TpBv05N2l1ouVkieog9palcvG20yZXoMUiibUwXF+qKaXehLqk4vFJZrDTzsYOuHITnaXh3kz4Td5atjsGNXh9MGZOmqDm8cve11WksiOA1J6Pq7PG+WNEwuY6hAoGATWnSxgJpp/W0LDPmqy1pUmAoeoxTxyfQmUVmOAqoLY+Mxg8UW22sxqH0konozQYO891bwIF0RSFAOxwkUCiGXxotojTyC0Dpow9g6bgyYu8Aq/L26kbfSl9d+JuU0KSfhNQulhoBXBkFdHlXh1ABpgy4bGZiIIHy26vFMAL4HcE=',
            ],
            // ### 祁盟sdk - H5/电脑网站支付
            '2021004121680050' => [
                'app_id' => '2021004121680050',
                'public_key' => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwi5X6Lq9141MQmTVb1zL73U9n9xqnmLttmZbyphPTwkdZIE5k+Hj63fKnit6cRrJOtmm3D7h2uBlTYiak12q/WgEugfFcqLyjHTN48ttAX3+kmNPFP1AKeIMNICTKaudeEUzC39OiQIDoEKxahEkaRMOi+cMJLyqZ1iNSu5icyGqds/ZKWIBxPl7HwK3P2LEoN/hchX9QlJreLR6lAMcbooPf8q09YtBxyluDyK3ojjZ4MNQJ4pwZFhX0rRYxbM0xd8xvbLhbzc823DHhi7JMhDvqXBsLrG8nMmqEnZ+iiex/h+ebvIR5KLYKT0Wv/p9CUwamtWryXJr4gUEHnX6BwIDAQAB',
                'private_key' => 'MIIEogIBAAKCAQEAibZSYJPnm9qcXB8N8lvNDX8RN3k7aOJs1ofnj/5EztL83OxZNwx11VTRe5kamNve1LtMtAqZtCJRzztOLbt7NTPFAIIGgnwEU8pvdh5PXuMUow4YmAsDS39LYqCy3DN9p/OLbxCYZD9Ep9uxvPIGG8NxU1MEX0zxPPlFVsYoKsPD5cf8/87jUrLGoEJ5xSSDJbqal+yLNkTpg++00fW3IMh8qRnxtPmiaCrnj8gxcuUvE0XKbx+CIP1bW8SWedQ58bDV3IOGG5TcXIppUBMjLWCja4fBI27ETe8/My9yWOez4DyFrwVVro/PZ7qYJM5bWNGyEAyWEU864BUrkkQJTQIDAQABAoIBACJSjKmBc4JGtB3Jp6I1+x9YAiR/VK1JcbLuYGplByU9NMSjK4Eq29t1rlhNynE6q4j9vZpJ4hEhVJ5/u3ZuecDCzGDaB2FSI4n2tF0OJN4TXrtc8ghDBmk0qsdEIg/7zV+ktaXSzwh9NFFZ0iyzbY5R4dbQpBBUjvORdRSxgqVVk8hmaTKras4CcywfVojj3lO3oWQgAMbOPVm56Rp8tChVIGIXzzH9m3cUcjmMN5GERYZHzmLuxfNMDC/g+KSWAsgWlhEu05nnj1yCh/dYBmEdAiyU6ROpowHYOyxE+aRff1MPfQoNdeIrY5imCWpTNopq7cFHNyLDeiAPe4JfBokCgYEAwGNS/wAWPY0mXpoNpzYGbAbhRL+8ucXHqnlkJ3trw6sWoCe3+ZdstMirn37VyqNN78hbh4XoXOY1+FFXZBYdso5rmwbUgpZ6gMTxOSSOmN1cXnGZiXj3LCs6fBU8mIZOZ867TfYipBXuT2KlGZnfpocqKk5SuUxOi050GEg3R5MCgYEAtz73fY8OKq+uTcdpuUJwkrELuMZ0Y2dJ/mApZVWyEDgEijw7LKdlaO0UPzoQwg/yKigx0CSY5hEm37QdIcB8ZH+d531+ioOkkoI46w4FiDHPHk1bDg+rzj8N9Lq0uaQQYlCnTRNnaYnQ1fAOiNmJml53qatkiHM2Ve1yYIb6N58CgYATe7uTAYI/F4VUPUu2rFz4IbY8jSJiuenflM2UxgYC2de/vmX+S9yBbnUbAPBLxkAFUYgbcG84wUOj8zDjfO3jAFNbbtSkqPKdCdTYEaCzUG+jTOPcrThy66nPmSL621cjYidL3OpjLalltrq7nf6tUbDueeIcy+KEbxyIvP+EKwKBgGEfzq2OFUQquYggGblHrcMSc9QL28SUCnP5I/fB5A8UodNi+TpBv05N2l1ouVkieog9palcvG20yZXoMUiibUwXF+qKaXehLqk4vFJZrDTzsYOuHITnaXh3kz4Td5atjsGNXh9MGZOmqDm8cve11WksiOA1J6Pq7PG+WNEwuY6hAoGATWnSxgJpp/W0LDPmqy1pUmAoeoxTxyfQmUVmOAqoLY+Mxg8UW22sxqH0konozQYO891bwIF0RSFAOxwkUCiGXxotojTyC0Dpow9g6bgyYu8Aq/L26kbfSl9d+JuU0KSfhNQulhoBXBkFdHlXh1ABpgy4bGZiIIHy26vFMAL4HcE=',
            ],
        ],
    ],


    //微信汇付宝h5支付
    'wxwappay-h5'   => [
        //商户号
        'mch_id'    => '',
        //密钥
        'key'       => '',
        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/wxwapnotify.html",
        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/wxwapnotify.html",

        'return_url' => "http://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_wxwappay.html",

        'pay_url' => "https://Pay.Heepay.com/DirectPay/applypay.aspx",
        //APP里调起H5支付，需要在webview中手动设置referer
        'referer'   => WEBSITE_DOMAIN,
    ],

    //微信官方h5支付 - 注意支付时的链接为，用于判断Darwin，http://sdkapi.7dgame.cn/jump
    'wxpay-h5'   => [

        // 公众账号或者小程序的 AppID - 当前为小程序
        'app_id'     => 'wx1e1d56e679395ef4',

        // 商户号
        'mch_id' => '1650347988',

        // PEM 格式的商户 API 私钥 - APIV2
        'key' => 'ASD1fwefefdgdsgsdvdvsd3rtqqwfqwf',

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/wxpayh5.html",           // 支付
        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/wxpayh5.html", // 平台币

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer' => 'http://sdkapi.' . QM_DOMAIN_URL,

        // 'apiclient_key'   => ROOT_PATH .'application/common/config/pay/wx_cert/apiclient_key.pem',
        // 'apiclient_cert'   => ROOT_PATH .'application/common/config/pay/wx_cert/apiclient_cert.pem',
    ],

    //米花微信h5支付
    'wxpay-h5-mihua'   => [
        // 支付后返回地址
        'return_url' => "http://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_mihua.html",

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/wxpayh5mihua.html",

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer'   => WEBSITE_DOMAIN,
    ],

    //快接微信h5支付
    'wxpay-h5-kj'   => [
        // 支付后返回地址
        'return_url' => "http://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_kj.html",

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/wxpayh5kj.html",

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer'   =>WEBSITE_DOMAIN,
    ],

    //米花支付宝h5支付
    'zfb-h5-mihua'   => [
        // 支付后返回地址
        'return_url' => "http://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_mihua_zfb.html",

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/zfbh5mihua.html",

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer'   => WEBSITE_DOMAIN,
    ],

    //快接支付宝h5支付
    'zfb-h5-kj'   => [
        // 支付后返回地址
        'return_url' => "http://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_kj_zfb.html",

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/zfbh5kj.html",

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer'   => WEBSITE_DOMAIN,
    ],

    //商盟微信h5支付
    'wxpay-h5-sumpay'   => [
        // 支付后返回地址
        'return_url' => "http://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_sumpay.html",

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/wxpayh5sumpay.html",

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer'   => WEBSITE_DOMAIN,
    ],
    //酷点h5支付
    'kdh5'   => [
        // 支付后返回地址
        'return_url' => "http://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_kdh5zhifu.html",

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/kdh5zhifu.html",

        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/kdh5zhifu.html",

        'pay_url' => WEBSITE_DOMAIN . "/coin/payHtml.html",

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer' => "yiyouweixin",
    ],


    // ## 全民付/银联 - 未对接
    'qmf_pay' => [
        /**
         *  支付宝H5支付：
        测试环境：https://test-api-open.chinaums.com/v1/netpay/trade/h5-pay
        生产环境：https://api-mop.chinaums.com/v1/netpay/trade/h5-pay
        微信H5支付：
        测试环境：https://test-api-open.chinaums.com/v1/netpay/wxpay/h5-pay
        生产环境：https://api-mop.chinaums.com/v1/netpay/wxpay/h5-pay
        银联云闪付：
        测试环境：https://test-api-open.chinaums.com/v1/netpay/uac/order
                生产环境：https://api-mop.chinaums.com/v1/netpay/uac/order
            微信H5转小程序支付：
                测试环境：https://test-api-open.chinaums.com/v1/netpay/wxpay/h5-to-minipay
                生产环境：https://api-mop.chinaums.com/v1/netpay/wxpay/h5-to-minipay
         */
        // 接口地址
        'api_url' => [
            "zfb-h5" => "https://test-api-open.chinaums.com/v1/netpay/trade/h5-pay",
            "wx-h5" => "https://api-mop.chinaums.com/v1/netpay/wxpay/h5-pay",
            "ysf" => "https://api-mop.chinaums.com/v1/netpay/uac/order",
        ],
        "app_id" => "xxxx",
        "app_key" => "xxxx",
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/qmf_pay.html",
        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/qmf_pay.html",
    ],
    // ## 易宝支付 - 新壹玖柒玖（北京）信息技术有限公司 - 已对接
    'ybzf_pay' => [
        // 'polling_id' => '9sz9fqkO90AzfX8I',                                                   // 轮询ID - 易宝
        'polling_id' => '64j32560lX8mMJMR',                                                   // 轮询ID -富友
        'private_key' => 'fdee11fc-65d3-4f69-8a2a-1d0040f50eb0',                              // 商户密钥
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/ybzf_pay.html",           // 支付回调
        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/ybzf_pay.html", // 充值回调
        'return_url' => '',                                                                   // 页面回跳地址

        // 支付方式
        // 'order_type' => [
        //     'ybzf_wxmp_h5' => 41,      // 微信H5跳小程序
        //     'coin-ybzf_wxmp_h5' => 41, // 平台币-微信H5跳小程序
        // ],
    ],

    // ## 联动优势支付 - 联动优势电子商务有限公司 - 支付宝-mp
    'ldys_pay' => [
        'mer_id' => '55282',                                                                  // 商户编号
        'app_id' => 'wx4c5b7ade4949bcc9',                                                     // 微信小程序APPID
        // 'scancode_type' => '',                                                                // 扫码类型: WECHAT:微信、ALIPAY:支付宝、UNION:银联支付、STAGING:扫码分期、NETSUNION:网联二维码
        'goods_id' => '',                                                                     // 商品号
        'private_file' => EXTEND_PATH . "LdzfPay/cert/55282_private_key.pem",                        // 私钥地址
        'public_file' => EXTEND_PATH . "LdzfPay/cert/55282_public_key.pem",                        // 公钥地址

        'notify' => [
            'wxmp' => [
                'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/ldzf_wx_pay.html",           // 异步通知地址 - 支付回调
                'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/ldzf_wx_pay.html", // 异步通知地址 - 充值回调
            ],
            'zfbmp' => [
                'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/ldzf_zfb_pay.html",           // 异步通知地址 - 支付回调
                'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/ldzf_zfb_pay.html", // 异步通知地址 - 充值回调
            ]
        ],
    ],

    // ## 趣智连支付 - 微信-H5
    'qzl_pay' => [
        "mch_no" => "M8822024010410000400",                                                  // 商户号
        "pay_key" => "6ad78f6c659e4b3bb55e5488e78c9530",                                     // 密钥

        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/qzl_pay.html",           // 异步通知地址 - 支付回调
        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/qzl_pay.html", // 异步通知地址 - 充值回调
        'referer' => WEBSITE_DOMAIN,
    ],

    // ## 快接支付
    'kjzf_pay' => [],

    // ## 优亿(易宝)(原趣智连)支付
    'yyyb_pay' => [
        'mch_no' => 'M1715933151',                                                                         // 商户号
        'app_id' => '66470fdfe4b0e246c4d2f98e',                                                            // 应用ID
        'app_secret' => 'v81tnbio9mo254st6os8lsun3dec4nxsl2yhtspe02tkjpdg2lelhbisvu786tbrobgrwd7y7v82nqk1c8d6u7ghwpo6dechy49kjyq2d5zph4l0jy23oj26dbonexvc',                                                      // app_secret
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/yyyb_pay.html",                        // 异步通知地址 - 支付回调
        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/yyyb_pay.html",              // 异步通知地址 - 充值回调
    ],
    // ## 喜钛游支付(Tc) - 喜钛游网络技术（厦门）有限公司
    'xty_pay' => [
        'app_id' => 'A17222309452323464',                                                                                   // 应用ID
        'private_file' => file_get_contents(EXTEND_PATH . "XiTaiYouPay/cert/private_key.pem"),                              // 应用私钥
        'public_file' => file_get_contents(EXTEND_PATH . "XiTaiYouPay/cert/public_key.pem"),                                // 平台公钥

        'notify' => [
            'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/xty_pay.html",             // 异步通知地址 - 支付回调
            'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/xty_pay.html",   // 异步通知地址 - 充值回调
        ],
    ],



    //短信模板-阿里云
    'ALI_SMS' => [
        'registerMsgCode' => 'SMS_462250586',      //'【麻花网络游戏】您正在进行注册，验证码：[%s]，有效期10分钟，请勿向任何人提供您收到的验证码。';      // 手机号注册短信验证码格式
        'findPwdMsgCode' => 'SMS_462250586',       //'【麻花网络游戏】您正在找回密码，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 忘记密码-手机验证身份
        'bindEmailVLMobile' => 'SMS_462250586',    //'【麻花网络游戏】您正在换绑邮箱，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定邮箱-手机验证身份
        'bindEmail' => 'SMS_462250586',            //'【麻花网络游戏】您正在绑定邮箱，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定邮箱
        'bindMobile' => 'SMS_462250586',           //'【麻花网络游戏】您正在绑定手机，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定手机
        'bindMobileVLMobile' => 'SMS_462250586',   //'【麻花网络游戏】您正在换绑手机，验证码：[%s]，有效期10分钟。请勿向任何人提供您收到的验证码。';      // 绑定手机-手机验证身份
        'channelAdvanceMobile' => 'SMS_462250586', //【倾枫】您好！渠道：%s的预付款余额已不足，剩余额度：%s元，请及时联系我方商务。  // 渠道预付款提示
    ],
    //短信签名  倾枫 的模板
    'ALI_SMS_SIGN' => [
        'SMS_462250586'
    ],
    'PTB_LOGO' => STATIC_DOMAIN.'sdk/v2/qm_ico_108.png', //平台币logo
    'haiwai_game_ids' => [],                                     //海外游戏id限制登录,
    'haiwai_game_ids_arr' => [],                                 //海外游戏id,
    'welfare_task' => 'welfare:task:',                           //福利队列缓存名
    'fk_task' => 'fk_task:',                           // 风控

    //php 使用版本
    "PHP_VERSION" => 72,
    // 支付可处理版本（用于是否随机支付）
    'pay_handle_version' => Env::get('pay_handle_version', 'v3.3.0'),

    // cps 后台地址
    'self_cps_url' => Env::get('HTTP_HOST_URL_CPS', ''),

    // +----------------------------------------------------------------------
    // | 支付配置
    // +----------------------------------------------------------------------
    'new_pay_type' => [
        'wx' => '微信支付',
        'zfb' => '支付宝支付',
        'coin' => '平台币',
        'coupon' => '代金券',
    ],
    // 客户端接口的支付方式
    'order_pay_type' => [
        'ptb' => '专属平台币',
        'coinpay' => '平台币',
        'coupon' => '代金券',

        // 支付方式
        'zfb' => '支付宝(快捷)',
        'old-zfb-wap' => '支付宝',
        'wxpay-h5' => '微信',
        'ybzf_wxmp_h5' => '微信',
        'qmf_wxmp_h5' => '微信',
        'qzl_wx_h5' => '微信',
        'yyyb_wx_h5' => '微信',
        'yyyb_ali_h5' => '支付宝',
        'xty_wx_h5' => '微信',
        'xty_ali_h5' => '支付宝',
        'ldys_wx_h5' => '微信',

        // 混合平台币
        'coin-zfb' => '平台币混合支付宝',
        'coin-old-zfb-wap' => '平台币混合支付宝',
        'coin-wxpay-h5' => '平台币混合微信',
        'coin-ybzf_wxmp_h5' => '平台币混合微信',
        'coin-qmf_wxmp_h5' => '平台币混合微信',
        'coin-qzl_wx_h5' => '平台币混合微信',
        'coin-yyyb_wx_h5' => '平台币混合微信',
        'coin-xty_wx_h5' => '平台币混合微信',
        'coin-xty_ali_h5' => '平台币混合支付宝',
        'coin-ldys_wx_h5' => '平台币混合微信',
        'coin-yyyb_ali_h5' => '平台币混合支付宝',

        // 混合代金券
        'mix-zfb' => '代金券混合支付宝H5',
        'mix-old-zfb-wap' => '代金券混合支付宝',
        'mix-wxpay-h5' => '代金券混合微信',
        'mix-ybzf_wxmp_h5' => '代金券混合微信',
        'mix-qmf_wxmp_h5' => '代金券混合微信',
        'mix-qzl_wx_h5' => '代金券混合微信',
        'mix-yyyb_wx_h5' => '代金券混合微信',
        'mix-xty_wx_h5' => '代金券混合微信',
        'mix-xty_ali_h5' => '代金券混合支付宝',
        'mix-ldys_wx_h5' => '代金券混合微信',
        'mix-yyyb_ali_h5' => '代金券混合支付宝',
    ],
    // 后台的支付方式
    'paytype' => [
        // 在用
        // 'ptb' => '平台币',

        // 支付方式
        'coinpay' => '平台币',
        'coupon' => '代金券',
        // ---- 支付宝
        'zfb' => '支付宝-APP',
        'old-zfb-wap' => '支付宝-H5',
        'ldys_wx_h5' => '联动-微信',
        'ldys_zfb' => '联动-支付宝',
        'dh_zfb' => '电魂-支付宝',
        'dh_wx' => '电魂-微信',

        'wxpay-h5' => '微信-H5',
        'ybzf_wxmp_h5' => '易宝-微信',
        'ybzf_pay' => '易宝-微信',
        'qzl_wx_h5' => '趣智连-微信',
        'yyyb_wx_h5' => '优亿-微信',
        'yyyb_ali_h5' => '优亿-支付宝',
        'qmf_wxmp_h5' => '全民付-微信',
        'xty_wx_h5' => '喜钛游-微信',
        'xty_ali_h5' => '喜钛游-支付宝',

        // ## 混合平台币
        'coin-zfb' => '平台币-支付宝-APP',
        'coin-old-zfb-wap' => '平台币-支付宝-H5',
        'coin-ldys_zfb' => '平台币-联动&支付宝',
        'coin-ybzf_pay' => '平台币-易宝&支付宝',
        'coin-yyyb_wx_h5' => '平台币-优亿&支付宝',
        'coin-qmf_wxmp_h5' => '平台币-全民付&支付宝',
        'coin-wxpay-h5' => '平台币-微信H5',
        'coin-ybzf_wxmp_h5' => '平台币-易宝-微信H5',
        'coin-qzl_wx_h5' => '平台币-趣智连-微信',
        'coin-xty_wx_h5' => '平台币-喜钛游-微信',
        'coin-xty_ali_h5' => '平台币-喜钛游-支付宝',
        'coin-ldys_wx_h5' => '平台币-联动-微信',
        'coin-yyyb_ali_h5' => '平台币-优亿-微信',

        // ## 混合代金券
        'mix-zfb' => '代金券-支付宝-APP',
        'mix-old-zfb-wap' => '代金券-支付宝-H5',
        'mix-ybzf_pay' => '代金券-易宝&微信-h5',
        'mix-yyyb_wx_h5' => '代金券-优亿&微信-h5',
        'mix-qmf_wxmp_h5' => '代金券-全民付&微信-h5',
        //  'mix-wxpay-h5' => '代金券-微信H5',
        'mix-ybzf_wxmp_h5' => '代金券-易宝-微信H5',
        'mix-xty_wx_h5' => '代金券-喜钛游-微信',
        'mix-xty_ali_h5' => '代金券-喜钛游-支付宝',
        'mix-ldys_wx_h5' => '代金券-联动-微信',
        'mix-yyyb_ali_h5' => '代金券-优亿-微信',


        // ## 未使用（未对接）
        // 'qmf_wxmp_h5' => '全民付-微信mp-h5',
        // 'kjzf_wxmp_h5' => '快接付-微信mp-h5',

        // 'zfb-h5' => '支付宝H5支付',
        // 'zfb-wap' => '支付宝WAP-H5支付',
        // 'wx-wap' => '微信WAP-H5支付',
        // 'zfbsmzf' => '支付宝扫码支付',
        // 'wxsmzf' => '微信扫码支付',
        // 'xzzfbzf' => '新支付宝支付',
        // 'kdh5' => '酷点H5平台支付',
        // 'airwallex' => '空中云汇',
        // 'airwallexh5' => '空中云汇h5',
        //
        // 'kdsm' => '酷点微信扫码平台支付',
        // 'coin-zfb-wap' => '支付宝WAP-H5混合支付',
        // 'coin-wx-wap' => '微信WAP-H5混合支付',
        // 'coin-zfbsmzf' => '支付宝扫码混合支付',
        // 'coin-wxsmzf' => '微信扫码混合支付',
        // 'coin-xzzfbzf' => '新支付宝混合支付',
        // 'coin-old-zfb-wap' => '老支付宝混合支付',
        // 'coin-kdh5' => '酷点H5平台混合支付',
        // 'coin-kdsm' => '酷点微信扫码平台混合支付',
        // 'coin-airwallex' => '空中云汇混合支付',
        // 'coin-airwallexh5' => '空中云汇h5混合支付',
        //	'mix-zfb'        => '支付宝混合支付',
        //	'mix-wxpay-h5'   => '微信官方H5混合支付',
        // 'wxpay-h5-mihua' => '米花微信H5支付',
        //	'mix-wxpay-h5-mihua'=> '米花微信H5混合支付',
        // 'wxpay-h5-kj'	 => '快接微信H5支付',
        //	'mix-wxpay-h5-kj'=> '快接微信H5混合支付',
        //'zfb-h5-mihua'	 => '米花支付宝H5支付',
        //	'mix-zfb-h5-mihua'=>'米花支付宝H5混合支付',
        //'zfb-h5-kj'		 => '快接支付宝H5支付',
        //	'mix-zfb-h5-kj'	 => '快接支付宝H5混合支付',
        //'wxpay-h5-sumpay' => '商盟微信H5支付',
        //	'mix-wxpay-h5-sumpay'=> '商盟微信H5混合支付',

    ],

    // IOS 微信H5支付成功后跳转APP的配置
    'ios_jump_game' => [
        // '游戏ID' => 'IOS手机的游戏应用跳转地址',
        '20' => 'qimeng://',                    // 祁盟demo母包
        '261' => 'com.qimeng.xdjt://',          // 秀逗军团（ios）
        '258' => 'com.qimeng.shjh://',          // 山海镜花(ios)
        '259' => 'com.qimeng.xjqxz.qm://',      // 仙剑奇侠传3D(ios)
        '265' => 'com.qimeng.hazc://',          // 黑暗之潮：契约(ios)
        '282' => 'com.qidian.xfdg://',          // 仙风道骨（iOS）
        '283' => 'com.qdyx.kdzz.qimeng://',     // 昆顿之杖（iOS）
        '292' => 'com.qdxx.fx.qm://',         // 拂晓（iOS）
    ],

    // 钉钉通知
    'dd_notice' => [
        'server_name' => Env::get('dingtalk.server_name', '祈盟'),
        // 负责人钉钉
        'develop' => '13121776520',

        // 通知类型
        'notice_type' => [
            'warning' => 'https://oapi.dingtalk.com/robot/send?access_token=5f3224edc1f6ede121f7d0780c804649d491aa50a74e5823ac0655eaafeefc42', // 紧急-P0
            'weelfar' => 'https://oapi.dingtalk.com/robot/send?access_token=8aeff33f23b901f0b27b748d35f36d4f17feca343cae6f45e569970787c9d25f', // 异常-P0
            'notic' => 'https://oapi.dingtalk.com/robot/send?access_token=8aeff33f23b901f0b27b748d35f36d4f17feca343cae6f45e569970787c9d25f', // 通知-P1
            'operat' => 'https://oapi.dingtalk.com/robot/send?access_token=8aeff33f23b901f0b27b748d35f36d4f17feca343cae6f45e569970787c9d25f', // 操作-P2
        ],

        // 通知模板
        'msg_template' => [
            // 默认模板
            'defaults' => "Title: %s  \n  - error.msg：%s  \n - error.mark: %s  \n  - error.time: %s", // 标题、说明、标识、时间

            // 祈盟平台
            'pay_coin' => "Title: %s  \n  - error.msg：%s  \n - data.app_id: %s  \n  - data.out_trade_no: %s  \n  - data.time: %s",
            // 'pay_notify_coin' => "pay_notify_coin.alipayAop.fail：  \n  - error.msg：%s  \n  - data.app_id: %s \n  - data.out_trade_no: %s  \n  - data.time: %s",
            //
            // // 易宝支付
            // 'pay_notify_ybzf' => "pay_notify_ybzf.fail：  \n  - error.msg：易宝支付异常  \n - data.paytype: %s  \n  - data.out_trade_no: %s  \n  - data.time: %s",
            // 'pay_notify_coin_ybzf' => "pay_notify_coin_ybzf.fail：  \n  - error.msg：易宝支付异常   \n  - data.paytype: %s  \n  - data.out_trade_no: %s  \n  - data.time: %s",
        ],
    ],

    // sql查询缓存时间 - 一小时
    'QUERY_RESULT_CACHE_TIME' => 3600,

    // 微信小程序配置
    'wxmp_config' => [
        'app_id' => 'wx117849d0c0632d22',                                                       // 应用ID - 祈点服务支持(杭州祈点)
        'mch_id' => '617891483',                                                                // 商户号 - 杭州祈点
        'secret' => '8ad81d17988947391944f717ef5b53ea',                                         // API密钥

        // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
        'notify_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify/wxpayh5.html",              // 支付
        'notify_coin_url' => "http://" . HTTP_HOST_URL . "/v1.pay_notify_coin/wxpayh5.html",    // 平台币

        //APP里调起H5支付，需要在webview中手动设置referer
        'referer' => 'http://sdkapi.' . QM_DOMAIN_URL,

        'certPath' => ROOT_PATH . 'application/common/config/pay/wxmp_cert/apiclient_cert.pem',
        'keyPath' => ROOT_PATH . 'application/common/config/pay/wxmp_cert/apiclient_key.pem',
    ],

    "zb_str" => [ // 直播平台交互
        'sign_key' => "8Srs3dTs4dFIvtbG",
        'dev_url' => "http://devghapi." . QM_DOMAIN_URL,
        'pro_url' => "http://ghapi." . QM_DOMAIN_URL,
    ],

    // 中宣实名
    "zxsm" => [
        "app_id" => '394a2dd2a3ae46ca8c9de80e8c7a112a',
        "secret_key" => '99dac2942a73c72504258989c4cbab5c',
    ],

    // 平台相关RSA签名的配置
    "rsa_sign" => [
        "private_key" => APP_PATH . 'common/config/rsa_sign/rsa_private_key.pem',
        "public_key" => APP_PATH . 'common/config/rsa_sign/rsa_public_key.pem',
    ],
];
