<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\common\validate;

use app\common\library\ValidateExtend;

class Coupon extends ValidateExtend
{
    protected $rule = [
        'name' => 'require',
        'page' => 'require|number|gt:0',
        'pageSize' => 'require|number|gt:0',
        'type_id' => 'require|integer|in:1,2',
        'start_time' => 'requireIf:type_id,1|dateFormat:Y-m-d',
        'end_time' => 'requireIf:type_id,1|dateFormat:Y-m-d',
        'day' => 'requireIf:type_id,2|integer|gt:0',
        'money' => 'require|integer|gt:0',
        'min_money' => 'require|integer|egt:0',
        'state' => 'require|integer|in:1,2',
        'is_examine' => 'require|integer|in:1,2',

    ];
    protected $message = [
        'name.require' => '名称不能为空',



        'page.require' => '页码不能为空',
        'page.number' => '页码必须为数值类型',
        'page.gt' => '页码必须大于0',
        'pageSize.require' => '分页数不能为空',
        'pageSize.number' => '分页数必须为数值类型',
        'pageSize.gt' => '分页数必须大于0',

        'type_id.require' => '类型不能为空',
        'type_id.integer' => '类型数值整数',
        'type_id.in' => '类型数值类型错误',

        'start_time.dateFormat' => '开始时间格式错误',
        'start_time.requireIf'=>'开始时间必填',

        'end_time.dateFormat' => '结束时间格式错误',
        'end_time.requireIf'=>'结算时间必填',

        'day.requireIf' => '天数不能为空',
        'day.integer' => '天数数值类型',
        'day.gt' => '天数必须大于0',

        'money.require' => '面值不能为空',
        'money.number' => '面值必须为数值类型',
        'money.gt' => '面值必须大于0',

        'min_money.require' => '最低使用金额不能为空',
        'min_money.number' => '最低使用金额必须为数值类型',
        'min_money.gt' => '最低使用金额必须大于等于0',

        'state.require' => '状态不能为空',
        'state.integer' => '状态数值类型',
        'state.in' => '状态数值类型',

        'is_examine.require' => '发放是否需要审核不能为空',
        'is_examine.integer' => '发放是否需要审核数值类型',
        'is_examine.in' => '发放是否需要审核数值类型',

    ];

    protected $scene = [
        'add' => ['name', 'game_id', 'type_id', 'start_time', 'end_time', 'money','min_money','state','is_examine','day'],
        'edit' => ['id','name', 'type_id', 'start_time', 'end_time', 'money','min_money','state','is_examine','day'],
    ];
}
