<?php
/**
 * Created by PhpStorm.
 * User: edison
 * Date: 2018/5/14
 * Time: 上午10:31
 */

namespace app\common\logic;

use app\common\model\Operatelog;

class Log {

    /**
     * 写入操作日志
     *
     * @param string $node
     * @param string $content
     * @param int $type
     * @param string $admin_id
     * @param string $username
     * @return mixed
     */
    public static function insertOperateLog($node = '', $content = '', $type = 0, $admin_id = '', $username = '')
    {
        $model = new Operatelog;
        
        $data['username']    = $username;
        $data['node']        = $node;
        $data['type']        = $type;
        $data['content']     = $content;
        $data['admin_id']    = $admin_id;
        $data['create_time'] = NOW_TIMESTAMP;
        $rs = $model->addLog($data);

        return $rs;
    }
}
