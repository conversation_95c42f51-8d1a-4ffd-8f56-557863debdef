<?php

/**
 * 聚合渠道相关公共业务逻辑类
 * 
 */

namespace app\common\logic;

use think\Db;
use app\common\model\Polychannel;
use app\common\model\PolyChannelGameFrozen;
use app\common\model\ComplexChannelFrozen;
use app\common\model\PolyChannelDeposit;

class Complex {

     /**
     * 是否被登录冻结、是否注册冻结
     * 
     * @param int $channel_id 聚合渠道ID
     * @param int $game_id 游戏ID
     * @param string $type 冻结项目(register、login、consume)
     * 
     * @return boolean true:冻结；false:不冻结
     */
    public function isFrozen($channel_id,$game_id,$type)
    {
        $PolychannelModel = new Polychannel;
        $frozenModel        = new PolyChannelGameFrozen;
        $channelFrozenModel = new ComplexChannelFrozen;

        //返回值
        $result = false;
        
        //判断聚合渠道是否冻结
        if($result==false){
            $polyChannelInfo = $PolychannelModel->field('login,register,consume')->where(['channel_id'=>$channel_id])->find();
            if(!empty($polyChannelInfo)){
                switch($type){
                    case 'register':    //新增
                        $result = $polyChannelInfo['register'];
                        break;
                    case 'login':       //登录
                        $result = $polyChannelInfo['login'];
                        break;
                    case 'consume':     //充值消费
                        $result = $polyChannelInfo['consume'];
                        break;
                }
            }
        }

		//聚合渠道判断不冻结时，再判断预警是否冻结
		if($result==false){
			//预警冻结记录
			$frozenInfo = $frozenModel->field('depf_login,depf_register,depf_consume,status')->where(['channel_id'=>$channel_id,'game_id'=>$game_id])->find();
			if($frozenInfo){
				
				//预付状态开启时
				if($frozenInfo['status']==1){
					
					$depositModel = new PolyChannelDeposit;
					
					//预付款配置信息
					$depositInfo = $depositModel->field('total_advance,over_limit,over_limit_amount')->where(['channel_id' => $channel_id])->find();
					
					//预付款配置信息为空时
					if(empty($depositInfo)){
						$result = true;
					}
					else{
						//实际分成总流水
						$real_total_amount = $this->getChannelTotalAmount($channel_id);
					   
						//超过预付款可超额度时
						if($real_total_amount>=($depositInfo['total_advance']+$depositInfo['over_limit_amount']))
						{
							switch($type){
								
								case 'register':    //新增
									
									$result = $frozenInfo['depf_register'];
									break;
									
								case 'login':       //登录
									
									$result = $frozenInfo['depf_login'];
									break;
									
								case 'consume':     //充值消费
									
									$result = $frozenInfo['depf_consume'];
									break;
									
							}
						}
					}
				}
				else{
					switch($type){
						
						case 'register':    //新增
							
							$result = $frozenInfo['depf_register'];
							
							break;
							
						case 'login':       //登录
							
							$result = $frozenInfo['depf_login'];
							
							break;
							
						case 'consume':     //充值消费
							
							$result = $frozenInfo['depf_consume'];
							
							break;
					}
					
				}
			}
        }

        //预警判断不冻结时，再判断渠道冻结表的状态
        if($result==false)
        {
            $channelFrozenInfo = $channelFrozenModel->field('member_login,register,consume')->where(['channel_id'=>$channel_id,'game_id'=>$game_id])->find();
            
            if(!empty($channelFrozenInfo)){
           
                switch($type){
                    
                    case 'register':    //新增
                        
                        $result = $channelFrozenInfo['register'];
                        
                        break;
                        
                    case 'login':       //登录
                        
                        $result = $channelFrozenInfo['member_login'];
                        
                        break;
                        
                    case 'consume':     //充值消费
                        
                        $result = $channelFrozenInfo['consume'];
                        
                        break;
                }
            }
        }
        
        return $result;
    }
    
   /**
    * 获取渠道实际总流水(即分成以后的流水)
    *
    * @param int $channel_id 聚合渠道ID
    * 
    * @return float
    */
    public function getChannelTotalAmount($channel_id) {
        
        $query = Db::query("SELECT SUM(total_amount*divide_point) as total FROM cy_polychannel_game_frozen WHERE `channel_id`={$channel_id} AND `status`=1");
        
        $total_amount = isset($query[0]) ? (float)$query[0]['total'] : 0;
        
        return bcdiv($total_amount, 10, 2);
    }
}
