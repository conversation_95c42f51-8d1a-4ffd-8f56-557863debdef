<?php

namespace app\common\logic;

use think\Db;

/**
 * mg权限认证类
 */
class Auth
{

    //默认配置
    protected $_config = [];

    public function __construct()
    {
    }

    /**
     * 检查权限
     * @param $name string|array  需要验证的规则列表,支持逗号分隔的权限规则或索引数组
     * @param $uid  int           认证用户的id
     * @param $relation string    如果为 'or' 表示满足任一条规则即通过验证;如果为 'and'则表示需满足所有规则才能通过验证
     * @return boolean           通过验证返回true;失败返回false
     */
    public function check($uid, $name, $relation = 'or')
    {
        if (empty($uid)) {
            return false;
        }
        if ($uid == 1) {
            return true;
        }

        if (is_string($name)) {
            $name = strtolower($name);
            if (strpos($name, ',') !== false) {
                $name = explode(',', $name);
            } else {

                $findAuthRuleCount = Db::name('nw_admin_auth_rule')->where([
                    'auth_name' => $name
                ])->count();

                if ($findAuthRuleCount == 0) {//没有规则时,失败!
                    //return false;
                    return true;
                }
            }
        }

        $list   = []; //保存验证通过的规则名
        $roleInfo = Db::name('nw_admin_role_user')
            ->alias("a")
            ->join('nw_admin_role b', 'a.role_id = b.id')
            ->where(["a.user_id" => $uid, "b.status" => 1])
            ->column("role_id, auth_ids");

//        if (in_array(1, $groups)) {
//            return true;
//        }

//        $groups = array_keys($roleInfo);
        $authIds = array_values($roleInfo);

        $uniqueAuthIdsTemp = [];
        foreach ($authIds as $ids) {
            $idsArr = explode(',', $ids);
            foreach ($idsArr as $id) {
                array_push($uniqueAuthIdsTemp, $id);
            }
        }

        $uniqueAuthIds = array_unique($uniqueAuthIdsTemp);

//        if (empty($groups)) {
//            return false;
//        }

        if (empty($uniqueAuthIds)) {
            return false;
        }


        $rules = Db::name('nw_admin_auth_rule')
            ->where(["id" => ["in", $uniqueAuthIds]])
            ->select();

        foreach ($rules as $rule) {
            if (strtolower($rule['auth_name']) === strtolower($name) ) {
                return true;
            }
        }

        return false;
    }
}
