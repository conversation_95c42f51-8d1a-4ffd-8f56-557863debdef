<?php
namespace app\common\logic;

// 176
use think\Env;
use think\Request;
use think\response\Json;

class YqlHandle
{
    protected $api_url;
    protected $api_sign_key;

    public function __construct()
    {
        $this->api_url = Env::get("yql.api_url");
        $this->api_sign_key = Env::get("yql.api_sign_key");
        trace(json_encode(input()), 'YqlHandle@__construct.input');
    }

    /**
     * sign
     *
     * @param $data
     *
     * @return string
     */
    private function sign($data)
    {
        unset($data['sign']);

        $data['key'] = $this->api_sign_key;
        $string = '';
        foreach ($data as $k => $v) {
            if ($string) {
                $string .= '&' . $k . "=" . $v;
            } else {
                $string = $k . "=" . $v;
            }
        }

        // dump(md5($string), $string);
        return md5($string);
    }


    /**
     * 代金卷使用通知 - 请求176
     *
     * @param $coupon_member_id 祈盟平台的用户代金卷ID
     *
     * @return Json
     */
    public function notifyCoupon($coupon_member_id)
    {
        if (!$coupon_member_id) {
            return json(["code" => 0, "message" => "非法操作：代金卷不存在！", "data" => []]);
        }
        // 176 平台的用户代金卷ID
        $yql_id = model('common/CouponMember')->where(['id' => $coupon_member_id])->value('yql_id');
        if (empty($yql_id)) {
            return json(["code" => 0, "message" => "代金卷不存在！", "data" => []]);
        }
        $data = ['id' => $yql_id];
        $data['sign'] = $this->sign($data);
        $url = $this->api_url . '/serve/v1/notify/vouchers';
        $res = getHttpPost($url.'?'.http_build_query($data), [], ['Content-Type' => 'application/json']);
        trace("url: ".$url." - body: ".json_encode($data).' - res:'.json_encode($res), '<EMAIL>');
        if ($res) {
            return json(["code" => 1, "message" => "通知成功！", "data" => []]);
        }
        return json(["code" => 0, "message" => "通知失败！", "data" => []]);
    }
}
