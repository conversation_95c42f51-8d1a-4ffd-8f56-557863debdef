<?php
namespace app\common\library;
use think\Env;

/**
 * 抖音广告队列类
 * Class MakeReport
 */
class Tiktok
{
    private $_redis = null;

    public function __construct()
    {
        $this->_redis = new \Redis();
        $this->_redis->connect(Env::get('redis.host', '127.0.0.1'), Env::get('redis.port', 6379));

        if (Env::get('redis.password', '')) {
            $this->_redis->auth(Env::get('redis.password', ''));
        }

        $this->_redis->select(Env::get('redis.seelect_go', ''));
    }

    /**
     * 添加任务
     * @param $action string  和go协商好的名称用来识别哪个方法
     * @param $uid int 用户id
     * @param $extend array 扩展参数
     * @param $count int 重试次数
     * @param int $expire 相同任务多久内不重复生成
     *
     * @return boolean true:提交任务成功  ；false:提交失败
     */
    public function addTask($action, $uid, $extend = [], $count = 0, $expire = 60)
    {

        $task_data = array(
            'Action' => $action,
            'Uid' => $uid,
            'Time' => getMillisecond(),
            'Extend' => $extend,
            'Count' => $count
        );

        $task_unique_id = strtolower(md5(sprintf(
            '%s|%s|%s',
            $task_data['Uid'],
            $task_data['Action'],
            json_encode($task_data['Extend'])
        )));
        $task_unique_id = 'tiktok:'. $task_unique_id;
        $has_task = $this->_redis->get($task_unique_id);

        // 防止重复生成报表
        if (empty($has_task)) {
            $this->_redis->zAdd('tiktok:task',getMillisecond(), json_encode($task_data));
            $this->_redis->setex($task_unique_id, $expire, 1);

            return true;
        } else {
            return false;
        }
    }
}
