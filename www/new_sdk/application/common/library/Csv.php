<?php

namespace app\common\library;

Class Csv
{

    public static function createCsv($head = array(), $data = array(), $filename = null, $with_head = true)
    {
        if (empty($filename)) {
            $filename = date('Y-m-d') . '.csv';
        } else {
            $filename = $filename . '.csv';
        }

        header("Content-Type: application/csv");
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $head = eval('return ' . iconv('utf-8', 'GBK', var_export($head, true)) . ';');
        foreach ($data as &$v) {
            foreach ($v as &$vv) {
                $vv = iconv('utf-8', 'GBK', $vv);
            }
        }


        $fp = fopen('php://output', 'w');
        if ($with_head) foreach ($head as $line) {
            if (is_array($line)) {
                fputcsv($fp, $line);
            } else {
                fputcsv($fp, $head);
                break;
            }
        }
        $head = array_keys($head);

        $i = 0;
        foreach ($data as $k => $line) {
            $new_line = [];
            foreach ($head as $col) {
                $new_line[] = $line[ $col ];
            }

            fputcsv($fp, $new_line);
            unset($data[ $k ]);
            $i++;
            if ($i > 100) {
                ob_flush();
                flush();
                $i = 0;
            }
        }
//        if (empty($data)) {
//            fputcsv($fp, [iconv('utf-8', 'GBK', '无数据')]);
//        }
        fclose($fp);
    }
}