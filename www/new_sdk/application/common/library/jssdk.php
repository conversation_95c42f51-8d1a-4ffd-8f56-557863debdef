<?php
/**
 * 微信JS-SDK(微信官网下载的demo)
 * 
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421141115
 * 
 */
namespace app\common\library;

class jssdk {
  private $appId;
  private $appSecret;
  protected $errMsg;

  public function __construct($appId, $appSecret) {
    $this->appId = $appId;
    $this->appSecret = $appSecret;
  }

  public function getSignPackage() {
    $jsapiTicket = $this->getJsApiTicket();
    
    if(empty($jsapiTicket)){
        return false;
    }

    // 注意 URL 一定要动态获取，不能 hardcode.
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $url = "$protocol$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

    $timestamp = time();
    $nonceStr = $this->createNonceStr();

    // 这里参数的顺序要按照 key 值 ASCII 码升序排序
    $string = "jsapi_ticket=$jsapiTicket&noncestr=$nonceStr&timestamp=$timestamp&url=$url";

    $signature = sha1($string);

    $signPackage = array(
      "appId"     => $this->appId,
      "nonceStr"  => $nonceStr,
      "timestamp" => $timestamp,
      "url"       => $url,
      "signature" => $signature,
      "rawString" => $string
    );
    return $signPackage; 
  }

  private function createNonceStr($length = 16) {
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    $str = "";
    for ($i = 0; $i < $length; $i++) {
      $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
  }

  private function getJsApiTicket() {
      
      $ticket = false;
      
      if (session('jssdk') && isset(session('jssdk')['jsapi_ticket']) && session('jssdk')['ticket_expire_time'] > time()) {
          $ticket = session('jssdk')['jsapi_ticket'];
      } else {
          $accessToken = $this->getAccessToken();
          
          if(empty($accessToken)){
              return false;
          }
          
          // 如果是企业号用以下 URL 获取 ticket
          //$url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=$accessToken";
          $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi&access_token=$accessToken";
          $res = json_decode($this->httpGet($url));
          
          if(isset($res->ticket)){
              
              $ticket = $res->ticket;
              
              if ($ticket) {
                  $data['jsapi_ticket']         = $ticket;// session
                  $data['ticket_expire_time']   = time() + 7000;
                  
                  session('jssdk',$data);
              }
          }
          else{
              $this->errMsg = 'jsapi_ticket获取失败'.$res->errcode;
          }
      }
      return $ticket;
  }

  private function getAccessToken() {
      
      $access_token = '';
      
      if (session('jssdk') && isset(session('jssdk')['access_token']) && session('jssdk')['token_expire_time'] > time()) {
          return session('jssdk')['access_token'];
      } else {
          $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$this->appId&secret=$this->appSecret";
          $res = json_decode($this->httpGet($url));

          if(isset($res->access_token)){
              
              $access_token = $res->access_token;
              
              if ($access_token) {
                  
                  session('jssdk')['access_token'] = $access_token;
                  session('jssdk')['token_expire_time'] = time() + 7000;
                  
                  return $access_token;
              }
          }
          else{
              $this->errMsg = 'access_token获取失败'.$res->errcode;
          }
      }
      
      return $access_token;
  }

  private function httpGet($url) {
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_TIMEOUT, 500);
    // 为保证第三方服务器与微信服务器之间数据传输的安全性，所有微信接口采用https方式调用，必须使用下面2行代码打开ssl安全校验。
    // 如果在部署过程中代码在此处验证失败，请到 http://curl.haxx.se/ca/cacert.pem 下载新的证书判别文件。
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2);
    curl_setopt($curl, CURLOPT_URL, $url);

    $res = curl_exec($curl);
    curl_close($curl);

    return $res;
  }
  
  /**
   * 获取错误信息
   * @return string
   */
  public function getError()
  {
      return $this->errMsg;
  }
}

