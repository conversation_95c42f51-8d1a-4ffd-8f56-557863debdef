<?php
namespace app\common\library;

/**
 * 微信支付类
 */
class WeixinPay {
	
	var $url;              //统一下单接口链接
	var $order_query_url;  //查询订单链接
	var $curl_timeout;     //curl超时时间
	
	
	public function __construct(){
	    
	    $this->url = "https://api.mch.weixin.qq.com/pay/unifiedorder";
	    
	    $this->order_query_url = "https://api.mch.weixin.qq.com/pay/orderquery";
	}
	
	/**
	 * 生成签名
	 * @param $params array 签名用参数
	 * @param $key string 微信支付的key
	 * 
	 * @return string 签名
	 */
	public function MakeSign($params,$key)
	{
	    //签名步骤一：按字典序排序数组参数
	    ksort($params);
	    $string = $this->ToUrlParams($params);
	    //签名步骤二：在string后加入KEY
	    $string = $string . "&key=" . $key;
	    //签名步骤三：MD5加密
	    $string = md5($string);
	    //签名步骤四：所有字符转为大写
	    $result = strtoupper($string);
	    
	    return $result;
	}
	
	/**
	 * 将参数拼接为url: key=value&key=value
	 * @param   $params
	 * @return  string
	 */
	public function ToUrlParams($params)
	{
	    $string = '';
	    if (!empty($params)) {
	        $array = array();
	        foreach ($params as $key => $value) {
	            $array[] = $key . '=' . $value;
	        }
	        $string = implode("&", $array);
	    }
	    
	    return $string;
	}

	/**
	 * 	作用：以post方式提交xml到对应的接口url
	 * 
	 * @param $xml string xml的报文内容
	 * @param $second int 请求的超时时间
	 * 
	 */
	public function postXmlCurl($xml,$url='',$second=30){
	    
	    if($url==''){
	        
	        $url = $this->url;
	    }
	    
        //初始化curl
       	$ch = curl_init();
		//超时时间
		curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,$second);
		curl_setopt($ch,CURLOPT_TIMEOUT,$second);
        //这里设置代理，如果有的话
        //curl_setopt($ch,CURLOPT_PROXY, '*******');
        //curl_setopt($ch,CURLOPT_PROXYPORT, 8080);
		curl_setopt($ch,CURLOPT_URL, $url);
        curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,FALSE);
        //curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,1);
        //curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,2); //php5.2以上版本
		curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,0); //php5.5以上版本
		//设置header
		curl_setopt($ch, CURLOPT_HEADER, FALSE);
		//设定传输xml格式
		curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: text/xml" ));
		//要求结果为字符串且输出到屏幕上
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
		//post提交方式
		curl_setopt($ch, CURLOPT_POST, TRUE);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
		//运行curl
        $data = curl_exec($ch);
		//返回结果
		if($data){
			curl_close($ch);
			return $data;
		}else{
			$error = curl_errno($ch);
			curl_close($ch);
			
			throw new \Exception("curl出错，错误码:$error");
			
			return false;
		}
	}
	
	/**
	 * post方式提交xml微信订单查询接口
	 * 
	 * @param $xml string xml的报文内容
	 * 
	 */
	public function queryPostXmlCurl($xml)
	{
	    return $this->postXmlCurl($xml,$this->order_query_url);
	}

	/**
	 * 将xml转为array
	 * @param string $xml
	 * 
	 * @return array
	 */
	public function xmlToArray($xml)
	{
	    if (!$xml) {
	        return false;
	    }
	    
	    $data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
	    
	    return $data;
	}
}

