<?php
/**
 * 华为云obs对象存储处理类
 * 【除PutObject,DeleteObject方法外，其他方法还未调试，暂时无法使用】
 *
 * @see https://bbs.huaweicloud.com/forum/thread-9056-1-1.html
 *
 */
namespace app\common\library;

vendor('huawei-obs.obs-autoloader');

use Obs\S3\ObsClient;
use Obs\Common\ObsException;
use function GuzzleHttp\json_encode;

class HuaweiObs
{
    protected $obsClient;
    protected $bucketName;  //bucket名称

    function __construct(){

        $this->obsClient = ObsClient::factory ( array (
            'key'       => HW_OBS_ACCESS_KEYID,
            'secret'    => HW_OBS_ACCESS_KEYSECRET,
            'endpoint'  => HW_OBS_ENDPOINT
        ) );

        $this->bucketName = HW_OBS_BUCKET;
    }

    // create bucket
    function CreateBucket() {
        global $obsClient;
        global $bucketName;
        echo "create bucket start...\n";
        try {
            $resp = $this->obsClient->createBucket ( array (
                'Bucket' => $bucketName,
                'ACL' => ObsClient::AclLogDeliveryWrite,
                'LocationConstraint' => '',
                'StorageClass' => ObsClient::StorageClassWarm
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "Location:%s\n", $resp ['Location'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch (ObsException $e ) {
            echo $e;
        }
    }

    //list buckets
    function ListBuckets() {
        global $obsClient;
        echo "list bucket start...\n";
        try {
            $resp = $this->obsClient->listBuckets ();
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            $i = 0;
            foreach ( $resp ['Buckets'] as $bucket ) {
                printf ( "Buckets[$i][Name]:%s,Buckets[$i][CreationDate]:%s\n", $bucket ['Name'], $bucket ['CreationDate'] );
                $i ++;
            }
            printf ( "Owner[ID]:%s,Owner[Name]:%s\n", $resp ['Owner'] ['ID'], $resp ['Owner'] ['DisplayName'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //delete bucket
    function DeleteBucket() {
        global $obsClient;
        global $bucketName;
        echo "delete bucket start...\n";
        try {
            $resp = $this->obsClient->deleteBucket ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //list objects
    function ListObjects() {
        global $obsClient;
        global $bucketName;
        echo "list objects start...\n";
        try {
            $resp = $this->obsClient->listObjects ( array (
                'Bucket' => $bucketName,
                'Delimiter' => '',
                'Marker' => '',
                'MaxKeys' => '',
                'Prefix' => ''
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "IsTruncated:%d,Marker:%s,NextMarker:%s,Name:%s\n", $resp ['IsTruncated'], $resp ['Marker'], $resp ['NextMarker'], $resp ['Name'] );
            printf ( "Prefix:%s,Delimiter:%s,MaxKeys:%d\n", $resp ['Prefix'], $resp ['Delimiter'], $resp ['MaxKeys'] );
            $i = 0;
            foreach ( $resp ['CommonPrefixes'] as $CommonPrefixe ) {
                printf ( "CommonPrefixes[$i][Prefix]:%s\n", $CommonPrefixe ['Prefix'] );
                $i ++;
            }
            $i = 0;
            foreach ( $resp ['Contents'] as $content ) {
                printf ( "Contents[$i][ETag]:%s,Contents[$i][Size]:%d,Contents[$i][StorageClass]:%s\n", $content ['ETag'], $content ['Size'], $content ['StorageClass'] );
                printf ( "Contents[$i][Key]:%s,Contents[$i][LastModified]:%s\n", $content ['Key'], $content ['LastModified'] );
                printf ( "Contents[$i][Owner][ID]:%s,Contents[$i][Owner][DisplayName]:%s\n", $content ['Owner'] ['ID'], $content ['Owner'] ['DisplayName'] );
                $i ++;
            }
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //list versions
    function ListVersions() {
        global $obsClient;
        global $bucketName;
        echo "list versions start...\n";
        try {
            $resp = $this->obsClient->listVersions ( array (
                'Bucket' => $bucketName,
                'Delimiter' => '',
                'KeyMarker' => '',
                'MaxKeys' => '',
                'Prefix' => '',
                'VersionIdMarker' => ''
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "IsTruncated:%d,KeyMarker:%s,VersionIdMarker:%s,NextKeyMarker:%s\n", $resp ['IsTruncated'], $resp ['KeyMarker'], $resp ['VersionIdMarker'], $resp ['NextKeyMarker'] );
            printf ( "NextVersionIdMarker:%s,Name:%s,Prefix:%s,Delimiter:%s,MaxKeys:%s\n", $resp ['NextVersionIdMarker'], $resp ['Name'], $resp ['Prefix'], $resp ['Delimiter'], $resp ['MaxKeys'] );
            $i = 0;
            foreach ( $resp ['CommonPrefixes'] as $CommonPrefixe ) {
                printf ( "CommonPrefixes[$i][Prefix]:%s\n", $CommonPrefixe ['Prefix'] );
                $i ++;
            }
            $i = 0;
            foreach ( $resp ['Versions'] as $version ) {
                printf ( "Versions[$i][ETag]:%s,Versions[$i][Size]:%d,Versions[$i][StorageClass]:%s\n", $version ['ETag'], $version ['Size'], $version ['StorageClass'] );
                printf ( "Versions[$i][Key]:%s,Versions[$i][VersionId]:%s,Versions[$i][IsLatest]:%d,Versions[$i][LastModified]:%s\n", $version ['Key'], $version ['VersionId'], $version ['IsLatest'], $version ['LastModified'] );
                printf ( "Versions[$i][Owner][ID]:%s,Versions[$i][Owner][DisplayName]:%s\n", $version ['Owner'] ['ID'], $version ['Owner'] ['DisplayName'] );
                $i ++;
            }
            $i = 0;
            foreach ( $resp ['DeleteMarkers'] as $deleteMarker ) {
                printf ( "DeleteMarkers[$i][Key]:%s,DeleteMarkers[$i][VersionId]:%s,DeleteMarkers[$i][IsLatest]:%d,DeleteMarkers[$i][LastModified]:%s\n", $deleteMarker ['Key'], $deleteMarker ['VersionId'], $deleteMarker ['IsLatest'], $deleteMarker ['LastModified'] );
                printf ( "DeleteMarkers[$i][Owner][ID]:%s,DeleteMarkers[$i][Owner][DisplayName]:%s\n", $deleteMarker ['Owner'] ['ID'], $deleteMarker ['Owner'] ['DisplayName'] );
                $i ++;
            }
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //head bucket
    function HeadBucket() {
        global $obsClient;
        global $bucketName;
        echo "head bucket start...\n";
        try {
            $resp = $this->obsClient->headBucket ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    // get bucket metadata
    function GetBucketMetadata() {
        global $obsClient;
        global $bucketName;
        echo "get bucket metatdata start...\n";
        try {
            $resp = $this->obsClient->getBucketMetadata ( array (
                "Bucket" => $bucketName,
                "Origin" => "www.example.com",
                "RequestHeader" => "header1"
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "StorageClass:%s\n", $resp ["StorageClass"] );
            printf ( "AllowOrigin:%s\n", $resp ["AllowOrigin"] );
            printf ( "MaxAgeSeconds:%s\n", $resp ["MaxAgeSeconds"] );
            printf ( "ExposeHeader:%s\n", $resp ["ExposeHeader"] );
            printf ( "AllowHeader:%s\n", $resp ["AllowHeader"] );
            printf ( "AllowMethod:%s\n", $resp ["AllowMethod"] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket location
    function GetBucketLocation() {
        global $obsClient;
        global $bucketName;
        echo "get bucket location start...\n";
        try {
            $resp = $this->obsClient->getBucketLocation ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "Location:%s\n", $resp ['Location'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket storageinfo
    function GetBucketStorageInfo() {
        global $obsClient;
        global $bucketName;
        echo "get bucket storage info start...\n";
        try {
            $resp = $this->obsClient->getBucketStorageInfo ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "Size:%d,ObjectNumber:%d\n", $resp ['Size'], $resp ['ObjectNumber'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket quota
    function SetBucketQuota() {
        global $obsClient;
        global $bucketName;
        echo "set bucket quota start...\n";
        try {
            $resp = $this->obsClient->setBucketQuota ( array (
                'Bucket' => $bucketName,
                'StorageQuota' => 1048576
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket quota
    function GetBucketQuota() {
        global $obsClient;
        global $bucketName;
        echo "get bucket quota start...\n";
        try {
            $resp = $this->obsClient->getBucketQuota ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "StorageQuota:%s\n", $resp ['StorageQuota'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket storage policy
    function SetBucketStoragePolicy() {
        global $obsClient;
        global $bucketName;
        echo "set bucket storage policy start...\n";
        try {
            $resp = $this->obsClient->setBucketStoragePolicy ( array (
                'Bucket' => $bucketName,
                'StorageClass' => ObsClient::StorageClassCold
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket storage policy
    function GetBucketStoragePolicy() {
        global $obsClient;
        global $bucketName;
        echo "get bucket storage policy start...\n";
        try {
            $resp = $this->obsClient->getBucketStoragePolicy ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "StorageClass:%s\n", $resp ['StorageClass'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket acl
    function SetBucketAcl() {
        global $obsClient;
        global $bucketName;
        echo "set bucket ACL start...\n";
        try {
            $resp = $this->obsClient->setBucketAcl ( array (
                'Bucket' => $bucketName,
                'ACL' => '',
                'Owner' => array (
                    'DisplayName' => 'ownername',
                    'ID' => 'ownerid'
                ),
                'Grants' => array (
                    0 => array (
                        'Grantee' => array (
                            'ID' => 'userid',
                            'Type' => 'CanonicalUser'
                        ),
                        'Permission' => ObsClient::PermissionRead
                    ),
                    1 => array (
                        'Grantee' => array (
                            'ID' => 'userid',
                            'Type' => 'CanonicalUser'
                        ),
                        'Permission' => ObsClient::PermissionWrite
                    ),
                    2 => array (
                        'Grantee' => array (
                            'URI' => ObsClient::GroupLogDelivery,
                            'Type' => 'Group'
                        ),
                        'Permission' => ObsClient::PermissionWrite
                    ),
                    3 => array (
                        'Grantee' => array (
                            'URI' => ObsClient::GroupAuthenticatedUsers,
                            'Type' => 'Group'
                        ),
                        'Permission' => ObsClient::PermissionRead
                    ),
                    4 => array (
                        'Grantee' => array (
                            'URI' => ObsClient::GroupAllUsers,
                            'Type' => 'Group'
                        ),
                        'Permission' => ObsClient::PermissionRead
                    )
                )
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket acl
    function GetBucketAcl() {
        global $obsClient;
        global $bucketName;
        echo "get bucket ACL start...\n";
        try {
            $resp = $this->obsClient->getBucketAcl ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "Owner[ID]:%s,Owner[DisplayName]:%s\n", $resp ['Owner'] ['ID'], $resp ['Owner'] ['DisplayName'] );
            $i = 0;
            foreach ( $resp ['Grants'] as $grant ) {
                printf ( "Grants[$i][Grantee][DisplayName]:%s,Grants[$i][Grantee][ID]:%s,Grants[$i][Grantee][URI]:%s\n", $grant ['Grantee'] ['DisplayName'], $grant ['Grantee'] ['ID'], $grant ['Grantee'] ['URI'] );
                printf ( "Grants[$i][Permission]:%s\n", $grant ['Permission'] );
                $i ++;
            }
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket logging configuration
    function SetBucketLoggingConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "set bucket logging configuration start...\n";
        try {
            $resp = $this->obsClient->setBucketLoggingConfiguration ( array (
                'Bucket' => $bucketName,
                'LoggingEnabled' => array (
                    'TargetBucket' => 'bucket003',
                    'TargetPrefix' => 'bucket.log',
                    'TargetGrants' => array (
                        0 => array (
                            'Grantee' => array (
                                'DisplayName' => 'username',
                                'ID' => 'userid',
                                'Type' => 'CanonicalUser',
                                'URI' => ''
                            ),
                            'Permission' => ObsClient::PermissionRead
                        ),
                        1 => array (
                            'Grantee' => array (
                                'URI' => ObsClient::GroupAuthenticatedUsers,
                                'Type' => 'Group'
                            ),
                            'Permission' => ObsClient::PermissionRead
                        )
                    )
                )
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket logging configuration
    function GetBucketLoggingConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "get bucket logging configuration start...\n";
        try {
            $resp = $this->obsClient->getBucketLoggingConfiguration ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "LoggingEnabled[TargetBucket]:%s,LoggingEnabled[TargetPrefix]:%s\n", $resp ['LoggingEnabled'] ['TargetBucket'], $resp ['LoggingEnabled'] ['TargetPrefix'] );
            if (is_array ( $resp ['LoggingEnabled'] ['TargetGrants'] )) {
                $i = 0;
                foreach ( $resp ['LoggingEnabled'] ['TargetGrants'] as $grant ) {
                    printf ( "LoggingEnabled[$i][TargetGrants][Permission]:%s\n", $grant ['Permission'] );
                    printf ( "LoggingEnabled[$i][TargetGrants][Grantee][ID]:%s,LoggingEnabled[$i][TargetGrants][Grantee][DisplayName]:%s,LoggingEnabled[$i][TargetGrants][Grantee][URI]:%s\n", $grant ['Grantee'] ['ID'], $grant ['Grantee'] ['DisplayName'], $grant ['Grantee'] ['URI'] );
                    $i ++;
                }
            }
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket policy
    function SetBucketPolicy() {
        global $obsClient;
        global $bucketName;
        echo "set bucket policy start...\n";
        try {
            $resp = $this->obsClient->setBucketPolicy ( array (
                'Bucket' => $bucketName,
                'Policy' => '{"Version":"2008-10-17", "Id": "Policy1375342051334", "Statement": [{"Sid": "Stmt1375240018061", "Action": ["s3:GetBucketPolicy"], "Effect": "Allow", "Resource": "arn:aws:s3:::bucket001", "Principal": { "AWS": ["*"] } }]}'
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket policy
    function GetBucketPolicy() {
        global $obsClient;
        global $bucketName;
        echo "get bucket policy start...\n";
        try {
            $resp = $this->obsClient->getBucketPolicy ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "Policy:%s\n", $resp ['Policy'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //delete bucket policy
    function DeleteBucketPolicy() {
        global $obsClient;
        global $bucketName;
        echo "delete bucket policy start...\n";
        try {
            $resp = $this->obsClient->deleteBucketPolicy ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket lifycycle configuration
    function SetBucketLifecycleConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "set bucket lifecycle configuration start...\n";
        try {
            $resp = $this->obsClient->setBucketLifecycleConfiguration ( array (
                'Bucket' => $bucketName,
                'Rules' => array (
                    0 => array (
                        'ID' => '',
                        'Prefix' => 'ok',
                        'Status' => 'Enabled',
                        'Transitions' => array(
                            0 => array(
                                'StorageClass' => ObsClient::StorageClassWarm,
                                'Date' => '2018-02-01T00:00:00Z'
                            ),
                            1 => array(
                                'StorageClass' => ObsClient::StorageClassCold,
                                'Date' => '2018-03-01T00:00:00Z'
                            )
                        ),
                        'Expiration' => array (
                            'Date' => '2018-04-01T00:00:00Z'
                        ),
                        'NoncurrentVersionTransitions' => array(
                            0 => array(
                                'StorageClass' => ObsClient::StorageClassWarm,
                                'NoncurrentDays' => 5
                            ),
                            1 => array(
                                'StorageClass' => ObsClient::StorageClassCold,
                                'NoncurrentDays' => 10
                            )
                        ),
                        // 'Expiration'=>array('Days'=>5),
                        'NoncurrentVersionExpiration' => array (
                            'NoncurrentDays' => 20
                        )
                    )
                )
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket lifycycle configuration
    function GetBucketLifecycleConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "get bucket lifecycle configuration start...\n";
        try {
            $resp = $this->obsClient->getBucketLifecycleConfiguration ( array (
                'Bucket' => $bucketName
            ) );
            $i = 0;
            foreach ( $resp ['Rules'] as $rule ) {
                foreach ($rule['Transitions'] as $index => $transition){
                    printf ( "Rules[$i][Transitions][$index][Date]:%s,Rules[$i][Transitions][$index][StorageClass]:%s\n", $transition ['Date'], $transition ['StorageClass']);
                }
                printf ( "Rules[$i][Expiration][Date]:%s,Rules[$i][Expiration][Days]:%d\n", $rule ['Expiration'] ['Date'], $rule ['Expiration'] ['Days'] );
                printf ( "Rules[$i][NoncurrentVersionExpiration][NoncurrentDays]:%s\n", $rule ['NoncurrentVersionExpiration'] ['NoncurrentDays'] );
                foreach ($rule['NoncurrentVersionTransitions'] as $index => $noncurrentVersionTransition){
                    printf ( "Rules[$i][NoncurrentVersionTransitions][$index][NoncurrentDays]:%d,Rules[$i][NoncurrentVersionTransitions][$index][StorageClass]:%s\n", $noncurrentVersionTransition ['NoncurrentDays'], $noncurrentVersionTransition ['StorageClass']);
                }
                printf ( "Rules[$i][ID]:%s,Rules[$i][Prefix]:%s,Rules[$i][Status]:%s\n", $rule ['ID'], $rule ['Prefix'], $rule ['Status'] );
                $i ++;
            }
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //delete bucket lifycycle configuration
    function DeleteBucketLifecycleConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "delete bucket lifecycle configuration start...\n";
        try {
            $resp = $this->obsClient->deleteBucketLifecycleConfiguration ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket website configuration
    function SetBucketWebsiteConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "set bucket website configuration start...\n";
        try {
            $resp = $this->obsClient->setBucketWebsiteConfiguration ( array (
                'Bucket' => $bucketName,
                // 'RedirectAllRequestsTo'=>array('HostName'=>'obs.hostname','Protocol'=>'http'),
                'IndexDocument' => array (
                    'Suffix' => 'index.html'
                ),
                'ErrorDocument' => array (
                    'Key' => 'error.html'
                ),
                'RoutingRules' => array (
                    0 => array (
                        'Condition' => array (
                            'KeyPrefixEquals' => 'docs/',
                            'HttpErrorCodeReturnedEquals' => 404
                        ),
                        'Redirect' => array (
                            'ReplaceKeyPrefixWith' => 'documents/',
                            'HostName' => 'obs.hostname',
                            'Protocol' => 'http',
                            'HttpRedirectCode' => 308
                        )
                    )
                )
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket website configuration
    function GetBucketWebsiteConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "get bucket website configuration start...\n";
        try {
            $resp = $this->obsClient->GetBucketWebsiteConfiguration ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            printf ( "RedirectAllRequestsTo[HostName]:%s,RedirectAllRequestsTo[Protocol]:%s\n", $resp ['RedirectAllRequestsTo'] ['HostName'], $resp ['RedirectAllRequestsTo'] ['Protocol'] );
            printf ( "IndexDocument[Suffix]:%s\n", $resp ['IndexDocument'] ['Suffix'] );
            printf ( "ErrorDocument[Key]:%s\n", $resp ['ErrorDocument'] ['Key'] );
            $i = 0;
            foreach ( $resp ['RoutingRules'] as $rout ) {
                printf ( "RoutingRules[$i][Condition][HttpErrorCodeReturnedEquals]:%s,RoutingRules[$i][Condition][KeyPrefixEquals]:%s\n", $rout ['Condition'] ['HttpErrorCodeReturnedEquals'], $rout ['Condition'] ['KeyPrefixEquals'] );
                printf ( "RoutingRules[$i][Redirect][Protocol]:%s,RoutingRules[$i][Redirect][HostName]:%s,RoutingRules[$i][Redirect][ReplaceKeyPrefixWith]:%s,RoutingRules[$i][Redirect][ReplaceKeyWith]:%s,RoutingRules[$i][Redirect][HttpRedirectCode]:%s\n", $rout ['Redirect'] ['Protocol'], $rout ['Redirect'] ['HostName'], $rout ['Redirect'] ['ReplaceKeyPrefixWith'], $rout ['Redirect'] ['ReplaceKeyWith'], $rout ['Redirect'] ['HttpRedirectCode'] );
                $i ++;
            }
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //delete bucket website configuration
    function DeleteBucketWebsiteConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "delete bucket website configuration start...\n";
        try {
            $resp = $this->obsClient->deleteBucketWebsiteConfiguration ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket versioning configuration
    function SetBucketVersioningConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "set bucket versioning configuration start...\n";
        try {
            $resp = $this->obsClient->setBucketVersioningConfiguration ( array (
                'Bucket' => $bucketName,
                'Status' => 'Suspended'
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket versioning configuration
    function GetBucketVersioningConfiguration() {
        global $obsClient;
        global $bucketName;
        echo "get bucket versioning configuration start...\n";
        try {
            $resp = $this->obsClient->getBucketVersioningConfiguration ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "Status:%s\n", $resp ['Status'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //set bucket cors
    function SetBucketCors() {
        global $obsClient;
        global $bucketName;
        echo "set bucket cors start...\n";
        try {
            $resp = $this->obsClient->setBucketCors ( array (
                'Bucket' => $bucketName,
                'CorsRule' => array (
                    0 => array (
                        'ID' => '123456',
                        'AllowedMethod' => array (
                            0 => "PUT",
                            1 => "POST",
                            2 => "GET",
                            3 => "DELETE"
                        ),
                        'AllowedOrigin' => array (
                            0 => "obs.hostname1"
                        ),
                        'AllowedHeader' => array (
                            0 => "header-1",
                            1 => "header-2"
                        )
                    )
                )
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //delete bucket cors
    function DeleteBucketCors() {
        global $obsClient;
        global $bucketName;
        echo "delete bucket cors start...\n";
        try {
            $resp = $this->obsClient->deleteBucketCors ( array (
                'Bucket' => $bucketName
            ) );
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //get bucket cors
    function GetBucketCors() {
        global $obsClient;
        global $bucketName;
        echo "get bucket cors start...\n";
        try {
            $resp = $this->obsClient->getBucketCors( array (
                'Bucket' => $bucketName
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf ( "RequestId:%s\n", $resp ['RequestId'] );
            print_r( $resp ['CorsRule'] );
        } catch ( ObsException $e ) {
            echo $e;
        }
    }

    //options bucket
    function OptionsBucket(){
        global $obsClient;
        global $bucketName;
        echo "options bucket start...\n";
        try {
            $resp = $this->obsClient->optionsBucket(array(
                'Bucket'=>$bucketName,
                'Origin'=>'obs.hostname1',
                'AccessControlRequestMethods' => array(
                    0=>"PUT",
                    1=>"POST",
                ),
                'AccessControlRequestHeaders'=>array(
                    0=>"header-1",
                    1=>"header-2"
                )
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            print_r($resp);
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //set bucket tagging
    function SetBucketTagging()
    {
        global $obsClient;
        global $bucketName;
        echo "set bucket tagging start...\n";
        try{
            $resp = $this->obsClient -> setBucketTagging(array(
                'Bucket' => $bucketName,
                'TagSet' => array(
                    0 => array(
                        'Key' => 'testKey1',
                        'Value' => 'testValue1'
                    ),
                    1 => array(
                        'Key' => 'testKey2',
                        'Value' => 'testValue2'
                    )
                )
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n", $resp["RequestId"] );
        }catch (ObsException $e){
            echo $e;
        }
    }

    //get bucket tagging
    function GetBucketTagging(){
        global $obsClient;
        global $bucketName;
        echo "get bucket tagging start...\n";
        try{
            $resp = $this->obsClient -> getBucketTagging(array(
                'Bucket' => $bucketName
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n", $resp["RequestId"] );
            foreach ($resp["TagSet"] as $tag){
                printf("Tag[%s:%s]\n", $tag["Key"],$tag["Value"] );
            }
        }catch (ObsException $e){
            echo $e;
        }
    }

    //delete bucket tagging
    function DeleteBucketTagging(){
        global $obsClient;
        global $bucketName;
        echo "delete bucket tagging start...\n";
        try{
            $resp = $this->obsClient -> deleteBucketTagging(array(
                'Bucket' => $bucketName
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n", $resp["RequestId"] );
        }catch (ObsException $e){
            echo $e;
        }
    }

    //set bucket notification
    function SetBucketNotification(){
        global $obsClient;
        global $bucketName;
        echo "set bucket notification start...\n";
        try{
            $resp = $this->obsClient -> setBucketNotification(array(
                'Bucket' => $bucketName,
                'TopicConfigurations' => array(
                    0 => array(
                        'ID' => '001',
                        'Topic' => 'urn:smn:region3:35667523534:topic1',
                        'Event' => array(
                            0 => 's3:ObjectCreated:*'
                        ),
                        'Filter' => array(
                            0 => array(
                                'Name' => 'prefix',
                                'Value' => 'smn/'
                            ),
                            1 => array(
                                'Name' => 'suffix',
                                'Value' => '.jpg'
                            )
                        )
                    )
                )
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n", $resp["RequestId"] );
        }catch (ObsException $e){
            echo $e;
        }
    }

    //get bucket notification
    function GetBucketNotification(){
        global $obsClient;
        global $bucketName;
        echo "get bucket notification start...\n";
        try{
            $resp = $this->obsClient -> getBucketNotification(array(
                'Bucket' => $bucketName,
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n", $resp["RequestId"]);
            print_r($resp['TopicConfigurations']);
        }catch (ObsException $e){
            echo $e;
        }
    }

    /**
     * delete object
     *
     * @param string $fileName 不用绝对地址，按照bucket相对路劲
     * @param string $bucket,为空时使用默认配置的bucket
     *
     */
    function DeleteObject($fileName,$bucket='')
    {
        $bucket = !empty($bucket) ? $bucket : $this->bucketName;

        try {
            $resp = $this->obsClient->deleteObject(array(
                'Bucket'=>$bucket,
                'Key'   =>$fileName,
            ));
//            log_message('HttpStatusCode:' . $resp['HttpStatusCode'], 'log', LOG_PATH . 'package/');
//            log_message('RequestId:' . $resp['RequestId'], 'log', LOG_PATH . 'package/');
//            log_message('VersionId:' . $resp['DeleteMarker'].' '.$resp['VersionId'], 'log', LOG_PATH . 'package/');
            /*
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("DeleteMarker:%s,VersionId:%s\n",$resp['DeleteMarker'],$resp['VersionId']);
            */
        } catch (ObsException $e) {
//            log_message('exceotion:' .$e->getMessage(), 'log', LOG_PATH . 'package/');
            return false;
        }

        return $resp;
    }

    //options object
    function OptionsObject(){
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "options bucket start...\n";
        try {
            $resp = $this->obsClient->optionsObject(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
                'Origin'=>'obs.hostname1',
                'AccessControlRequestMethods'=>array("PUT","GET"),
                'AccessControlRequestHeaders'=>array(
                    0=>"header-1",
                    1=>"header-2"
                )
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            print_r($resp);
        } catch (ObsException $e) {
            echo $e;
        }
    }


    //delete objects
    function DeleteObjects()
    {
        global $obsClient;
        global $bucketName;
        echo "delete objects start...\n";
        try {
            $resp = $this->obsClient->deleteObjects(array(
                'Bucket'=>$bucketName,
                'Objects'=>array(
                    0=>array('Key'=>'test'),
                    1=>array('Key'=>'file.log')
                ),
                'Quiet'=> false,
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            $i = 0;
            foreach ($resp['Errors'] as $error)
            {
                printf("Errors[$i][Key]:%s,Errors[$i][VersionId]:%s，Errors[$i][Code]:%s，Errors[$i][Message]:%s\n",
                    $error['Key'],$error['VersionId'],$error['Code'],$error['Message']);
                $i++;
            }
            $i = 0;
            foreach ($resp['Deleteds'] as $delete)
            {
                printf("Deleteds[$i][Key]:%s,Deleted[$i][VersionId]:%s，Deleted[$i][DeleteMarker]:%s，Deleted[$i][DeleteMarkerVersionId]:%s\n",
                    $delete['Key'],$delete['VersionId'],$delete['DeleteMarker'],$delete['DeleteMarkerVersionId']);
                $i++;
            }
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //set object acl
    function SetObjectAcl()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "set object ACL start...\n";
        try {
            $resp = $this->obsClient->setObjectAcl(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
                'Grants'=>array(
                    0 => array (
                        'Grantee' => array (
                            'ID' => 'userid',
                            'Type' => 'CanonicalUser'
                        ),
                        'Permission' => ObsClient::PermissionWrite
                    ),
                    1 => array (
                        'Grantee' => array (
                            'URI' => ObsClient::GroupLogDelivery,
                            'Type' => 'Group'
                        ),
                        'Permission' => ObsClient::PermissionWrite
                    ),
                    2 => array (
                        'Grantee' => array (
                            'URI' => ObsClient::GroupAuthenticatedUsers,
                            'Type' => 'Group'
                        ),
                        'Permission' => ObsClient::PermissionRead
                    ),
                ),
                'Owner'=>array(
                    'DisplayName' => 'ownername',
                    'ID' => 'ownerid'
                ),
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
        } catch (ObsException $e) {
            echo  $e;
        }
    }

    //get object acl
    function GetObjectAcl()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "get bucket ACL start...\n";
        try {
            $resp = $this->obsClient->getObjectAcl(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
            ));
            printf ( "HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("Owner[ID]:%s,Owner[DisplayName]:%s\n",$resp['Owner']['ID'],$resp['Owner']['DisplayName']);
            $i = 0;
            foreach ($resp['Grants'] as $grant)
            {
                printf("Grants[$i][Grantee][DisplayName]:%s,Grants[$i][Grantee][ID]:%s,Grants[$i][Grantee][URI]:%s\n",
                    $grant['Grantee']['DisplayName'],$grant['Grantee']['ID'],$grant['Grantee']['URI']);
                printf("Grants[$i][Permission]:%s\n",$grant['Permission']);
                $i++;
            }
        } catch (ObsException $e) {
            echo  $e;
        }
    }

    //restore object
    function RestoreObject()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "restore object start...\n";

        try{
            $resp = $this->obsClient -> restoreObject(array(
                "Bucket" => $bucketName,
                "Key" => $objectKey,
                "VersionId" => NULL,
                "Days" => 1,
                "Tier" => ObsClient::RestoreTierExpedited
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n", $resp["RequestId"] );
        }catch (ObsException $e){
            echo $e;
        }
    }

    /**
     * put object
     * @param string $sourceFile 源文件
     * @param string $newFileName OBS上重命名的文件名
     * @param string $dir OBS上的存储目录【如：my/】
     */
    function PutObject($sourceFile,$newFileName,$dir='')
    {
        try {
            $resp =$this->obsClient->putObject(array(
                'Bucket'    => $this->bucketName,
                'Key'       => $dir.$newFileName,
                'SourceFile'=> $sourceFile,
            ));

            /*
            printf ("HttpStatusCode:%s\n", $resp['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("ETag:%s,VersionId:%s\n",$resp['ETag'],$resp['VersionId']);
            */
            return $resp;
        } catch (ObsException $e) {
            //echo $e;
            return false;
        }
    }


    //get object metadata
    function GetObjectMetadata()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "get object metadata start...\n";
        try {
            $resp = $this->obsClient->getObjectMetadata(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("Expiration:%s,LastModified:%s,ContentLength:%d,StorageClass:%s\n",$resp['Expiration'],$resp['LastModified'],$resp['ContentLength'], $resp['StorageClass']);
            printf("ETag:%s,VersionId:%s,WebsiteRedirectLocation:%s\n",$resp['ETag'],$resp['VersionId'],$resp['WebsiteRedirectLocation']);
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //get object
    function GetObject()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "get object start...\n";
        try {
            $resp = $this->obsClient->getObject(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
                'Range'=>'bytes=0-10',
                'SaveAsFile' => '/temp/test.txt'
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("Metadata:%s\n",json_encode($resp['Metadata']));
            printf("DeleteMarker:%s,Expiration:%s,LastModified:%s\n",$resp['DeleteMarker'],$resp['Expiration'],$resp['LastModified']);
            printf("ContentLength:%d,ETag:%s,VersionId:%s,SaveAsFile:%s\n",$resp['ContentLength'],$resp['ETag'],$resp['VersionId'],$resp['SaveAsFile']);
            printf("Expires:%s,WebsiteRedirectLocation:%s\n",$resp['Expires'],$resp['WebsiteRedirectLocation']);
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //copy object
    function CopyObject()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "copy object start...\n";
        try {
            $resp = $this->obsClient->copyObject(array(
                'Bucket'=>$bucketName,
                'Key'=> $objectKey,
                'CopySource'=>'bucket003/test',
                'Metadata'=>array('test'=>"value"),
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("ETag:%s,VersionId:%s,LastModified:%s,CopySourceVersionId:%s\n",$resp['ETag'],$resp['VersionId'],$resp['LastModified'],$resp['CopySourceVersionId']);
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //initiate multipart upload
    function InitiateMultipartUpload()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "initiate mutipart upload start...\n";
        try {
            $resp = $this->obsClient->initiateMultipartUpload(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("Bucket:%s,Key:%s,UploadId:%s\n",$resp['Bucket'],$resp['Key'],$resp['UploadId']);
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //abort multipart upload
    function AbortMultipartUpload()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "abort mutipart upload start...\n";
        try {
            $resp = $this->obsClient->abortMultipartUpload(array(
                'Bucket'=>$bucketName,
                'Key' => $objectKey,
                'UploadId'=>'uploadid'
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
        } catch (ObsException $e) {
            echo  $e;
        }
    }

    //list multipart uploads
    function ListMultipartUploads()
    {
        global $obsClient;
        global $bucketName;
        echo "list mutipart upload start...\n";
        try {
            $resp = $this->obsClient->listMultipartUploads(array(
                'Bucket'=>$bucketName,
                'MaxUploads'=>1000
            ));
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("Bucket：%s,KeyMarker:%s,UploadIdMarker:%s,NextKeyMarker:%s\n",
                $resp['Bucket'],$resp['KeyMarker'],$resp['UploadIdMarker'],$resp['NextKeyMarker']);
            printf("Prefix:%s,Delimiter:%s,NextUploadIdMarker:%s,MaxUploads:%d,IsTruncated:%s\n",
                $resp['Prefix'],$resp['Delimiter'],$resp['NextUploadIdMarker'],$resp['MaxUploads'],$resp['IsTruncated']);
            $i = 0;
            foreach ($resp['CommonPrefixes'] as $common)
            {
                printf("CommonPrefixes[$i][Prefix]:%s\n",$common['Prefix']);
                $i++;
            }
            $i = 0;
            foreach ($resp['Uploads'] as $upload)
            {
                printf("Uploads[$i][Key]:%s,Uploads[$i][UploadId]:%s,Uploads[$i][StorageClass]:%s,Uploads[$i][Initiated]:%s\n",
                    $upload['Key'],$upload['UploadId'],$upload['StorageClass'],$upload['Initiated']);
                printf("Uploads[$i][Initiator][ID]:%s,Uploads[$i][Initiator][DisplayName]:%s\n",
                    $upload['Initiator']['ID'],$upload['Initiator']['DisplayName']);
                printf("Uploads[$i][Owner][ID]:%s,Uploads[$i][Owner][DisplayName]:%s\n",
                    $upload['Owner']['ID'],$upload['Owner']['DisplayName']);
                $i++;
            }
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //upload part
    function UploadPart()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "upload part start...\n";
        try {
            $resp = $this->obsClient->uploadPart(array(
                'Bucket'=>$bucketName,
                'Key' => $objectKey,
                'UploadId'=>'uploadid',
                'PartNumber'=>1,
                // 				'Body' => 'test',
                'SourceFile'=>'/temp/test.txt'
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("ETag:%s\n",$resp['ETag']);
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //copry part
    function CopyPart()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "copy part start...\n";
        try {
            $resp = $this->obsClient->copyPart(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
                'UploadId'=>'uploadid',
                'PartNumber'=>1,
                'CopySource'=>'bucket003/test',
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("ETag:%s,LastModified:%s\n",$resp['ETag'],$resp['LastModified']);
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //list parts
    function ListParts()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "list parts start...\n";
        try {
            $resp = $this->obsClient->listParts(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
                'UploadId'=>'uploadid',
                'MaxParts'=>500,
                'PartNumberMarker'=>0,
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("Bucket：%s,Key:%s,UploadId:%s,PartNumberMarker:%d\n",$resp['Bucket'],$resp['Key'],$resp['UploadId'],$resp['PartNumberMarker']);
            printf("NextPartNumberMarker:%d,MaxParts:%d,IsTruncated:%d,StorageClass:%s\n",$resp['NextPartNumberMarker'],$resp['MaxParts'],$resp['IsTruncated'],$resp['StorageClass']);
            printf("Initiator[ID]:%s,Initiator[DisplayName]:%s\n",$resp['Initiator']['ID'],$resp['Initiator']['DisplayName']);
            printf("Owner[ID]:%s,Owner[DisplayName]:%s\n",$resp['Owner']['ID'],$resp['Owner']['DisplayName']);
            $i = 0;
            foreach ($resp['Parts'] as $part)
            {
                printf("Parts[$i][PartNumber]:%s,Parts[$i][LastModified]:%s,Parts[$i][ETag]:%s,Parts[$i][Size]:%d\n",
                    $part['PartNumber'],$part['LastModified'],$part['ETag'],$part['Size']);
                $i++;
            }
        } catch (ObsException $e) {
            echo $e;
        }
    }

    //merge parts
    function  CompleteMultipartUpload()
    {
        global $obsClient;
        global $bucketName;
        global $objectKey;
        echo "complete multipart upload start...\n";
        try {
            $resp = $this->obsClient->completeMultipartUpload(array(
                'Bucket'=>$bucketName,
                'Key'=>$objectKey,
                'UploadId'=>'uploadid',
                'Parts'=>array(0=>array('PartNumber'=>1,'ETag'=>'etagvalue'),
                ),
            ));
            printf ("HttpStatusCode:%s\n", $resp ['HttpStatusCode'] );
            printf("RequestId:%s\n",$resp['RequestId']);
            printf("Bucket:%s,Key:%s,ETag:%s,VersionId:%s,Location:%s\n",$resp['Bucket'],$resp['Key'],$resp['ETag'],$resp['VersionId'],$resp['Location']);
        } catch (ObsException $e) {
            echo $e;
        }
    }
}



//----bucket related apis---
// CreateBucket();
// ListBuckets();
// DeleteBucket();
// ListObjects();
// ListVersions();
// HeadBucket();
// GetBucketMetadata();
// GetBucketLocation();
// GetBucketStorageInfo();
// SetBucketQuota();
// GetBucketQuota();
// SetBucketStoragePolicy();
// GetBucketStoragePolicy();
// SetBucketAcl();
// GetBucketAcl();
// SetBucketLoggingConfiguration();
// GetBucketLoggingConfiguration();
// SetBucketPolicy();
// GetBucketPolicy();
// DeleteBucketPolicy();
// SetBucketLifecycleConfiguration();
// GetBucketLifecycleConfiguration();
// DeleteBucketLifecycleConfiguration();
// SetBucketWebsiteConfiguration();
// GetBucketWebsiteConfiguration();
// DeleteBucketWebsiteConfiguration();
// SetBucketVersioningConfiguration();
// GetBucketVersioningConfiguration();
// SetBucketCors();
// GetBucketCors();
// DeleteBucketCors();
// OptionsBucket();
// SetBucketTagging();
// GetBucketTagging();
// DeleteBucketTagging();
// SetBucketNotification();
// GetBucketNotification();

//-----object related apis--------
// DeleteObject();
// OptionsObject();
// SetObjectAcl();
// GetObjectAcl();
// RestoreObject();
// DeleteObjects();
// PutObject();
// GetObject();
// CopyObject();
// GetObjectMetadata();
// InitiateMultipartUpload();
// ListMultipartUploads();
// AbortMultipartUpload();
// UploadPart();
// ListParts();
// CompleteMultipartUpload();
// CopyPart();








