<?php

namespace app\common\library;

use think\Env;

/**
 * 报表生成服务类 go
 * Class MakeReport
 */
class MakeReportGo
{
    private $_redis = null;

    public function __construct()
    {
        if (extension_loaded('redis')) {
            $this->_redis = new \Redis();
            $this->_redis->connect(Env::get('redis.host', '127.0.0.1'), Env::get('redis.port', 6379));

            if (Env::get('redis.password', '')) {
                $this->_redis->auth(Env::get('redis.password', ''));
            }

            $this->_redis->select(Env::get('redis.seelect_go', 0));
        } else {
            exit('请开启redis扩展');
        }
    }

    /**
     * 添加报表生成任务(go)
     * @param $action string  和go协商好的名称用来识别哪个报表
     * @param $sql
     * @param $uid int 用户id
     * @param $extend array 扩展参数
     * @param $count int sql查询结果总条数
     * @param int $expire 相同任务多久内不重复生成
     *
     * @return boolean true:提交报表任务成功  ；false:提交报表失败
     */
    public function addTask($action, $sql, $uid, $extend = ['IsShow' => false], $count = 0, $expire = 3600)
    {
        $task_data = array(
            'Action' => $action,
            'Sql' => $sql,
            'Uid' => $uid,
            'Time' => request()->time(),
            'Extend' => $extend,
            'Count' => $count
        );

        $task_unique_id = strtolower(md5(sprintf(
            '%s|%s|%s|%s',
            $task_data['Uid'],
            $task_data['Action'],
            $task_data['Sql'],
            json_encode($task_data['Extend'])
        )));

        $task_unique_id = config('MAKE_REPORT_UNIQUE_KEY_PRE') . $task_unique_id;
        // $has_task = $this->_redis->get($task_unique_id);

        // 防止重复生成报表
        // if (empty($has_task)) {
        // MAKE_REPORT_KEY = ayam:task:make-report
            $a = $this->_redis->zAdd(config('MAKE_REPORT_KEY'), time(), json_encode($task_data));
            // $this->_redis->setex($task_unique_id, $expire, 1);
            return true;
        // } else {
        //     return false;
        // }
    }

    // 福利
    public function welfareTask($action, $id, $count = 0, $expire = 3600, $bool = false)
    {
        $task_data = array(
            'Action' => $action,
            'Id' => $id,
            'Time' => request()->time(),
            'Count' => $count
        );

        $task_unique_id = strtolower(md5(sprintf(
            '%s|%s',
            $task_data['Action'],
            $task_data['Id']
        )));
        $task_unique_id = config('welfare_task') . $task_unique_id;
        $has_task = $this->_redis->get($task_unique_id);
        log_message('$has_task:'.$has_task.' Id:'.$task_data['Id'],'info',LOG_PATH .'welfareGrant/');
        // 防止重复生成
        if (empty($has_task)) {
            $tmp = $this->_redis->zAdd(config('welfare_task') . 'list', time(), json_encode($task_data));
            log_message(config('welfare_task') . 'list'.'__'.time().'__'.json_encode($task_data).'  tmp:'.$tmp,'info',LOG_PATH .'welfareGrant/');
            $this->_redis->setex($task_unique_id, $expire, 1);
            $this->_redis->setex(config('welfare_task').':test:'.$id, $expire, 1); //调试
            return true;
        } else {
            if ($bool) {
                $this->_redis->zAdd(config('welfare_task') . 'list', time(), json_encode($task_data));
            }
            return false;
        }
    }
    
}
