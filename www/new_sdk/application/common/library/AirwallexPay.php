<?php

namespace app\common\library;

class AirwallexPay
{

    private $client_id = 'WUeckl4-Q-qxQXOda3uzTw';
    private $api_key = 'acfb93ca8d03524321a2b2b688ea8676d9f22ebaacde4bf11e0da28a50b719d8b312a38e7bdbfd97b676ce6f395ba3b7';
    private $webhooks_key = 'whsec_zuNFByhGiZ4l0JZWTcKtdpxeeprd0nlv';
    private $url = 'https://api.airwallex.com/';


    //测试配置
//    private $client_id = 'BDgsMC2fQjWg9rvl7XmaTg';
//    private $api_key = '635f574fc5e9c30a0edbb1707ff2ac345bba4d9e1ce9dc49dc36112b528cefe418ccb3e66a31fe9eb07bf03b88f4a3d6';
//    private $webhooks_key = 'whsec_6bygt6gr-EWPBZIg8qFj2KkrqY1ZzWRV';
//    private $url = 'https://api-demo.airwallex.com/';

    public function getToken()
    {
//        if(!cache('AirwallexPay:getToken')){
            $action = 'api/v1/authentication/login';
            $header = [
                'Content-Type:application/json',
                'x-client-id:' . $this->client_id,
                'x-api-key:' . $this->api_key,
                'Content-Length: ' . strlen('')
            ];
            log_message('info:'.$this->url . $action .'  '.json_encode($header), 'log', LOG_PATH . 'PayNotify/airwallex/pay/');
            $result = curlHeader($this->url . $action, [], $header);
            log_message('info:'.$result, 'log', LOG_PATH . 'PayNotify/airwallex/pay/');
            $result = json_decode($result,true);
            if(isset($result['token']) && $result['token']){

                cache('AirwallexPay:getToken',$result['token'],25*60);
                return true;
//            }else{
//                return false;
//            }
        }

        return true;

    }

    public function createOrderUrl(){

        $token = cache('AirwallexPay:getToken');

        $action = 'api/v1/pa/payment_links/create';
        $data = [
            'amount' => 0.01,
            'currency'=>'CNY',
//            'expires_at' => date('c',time()+30*60), //到期时间
            'reusable' => true,//链接是否可重复使用

            'title'=>'测试',
        ];

        $param = json_encode($data);
        $header = [
            'Content-Type:application/json',
            'Authorization: ' . $token,
            'Content-Length: ' . strlen($param)
        ];

        $result = curlHeader($this->url . $action, $param, $header);
        echo $result;
        $result = json_decode($result,true);
    }

    public function createOrder($amount,$orderId,$return_url)
    {
        $token = cache('AirwallexPay:getToken');

        $action = 'api/v1/pa/payment_intents/create';
        $data = [
            'amount' =>$amount,
            'currency' => 'USD',
            'merchant_order_id' => $orderId, //订单号
            'request_id' => makeOrderid('AIRWA'),//唯一请求id
            'payment_method_options' => [
                'card' => [
                    'risk_control' => [
                        'skip_risk_processing' => false,
                        'three_domain_secure_action' => 'FORCE_3DS',
                        'three_ds_action' => 'FORCE_3DS',
                    ],
                    'three_ds_action' => 'FORCE_3DS',
                ],
            ],
            'return_url'=>$return_url,
        ];
        $param = json_encode($data);
        $header = [
            'Content-Type:application/json',
            'Authorization: ' . $token,
            'Content-Length: ' . strlen($param)
        ];
        log_message('info:'.$this->url . $action.'  '.$param .'  '.json_encode($header), 'log', LOG_PATH . 'PayNotify/airwallex/pay/');
        $result = curlHeader($this->url . $action, $param, $header,true);
        log_message('info:'.json_encode($result), 'log', LOG_PATH . 'PayNotify/airwallex/pay/');

        if($result['code_qf'] == 201){

            return [
                'id' =>$result['id'],
                'currency' =>$result['currency'],
                'client_secret' =>$result['client_secret'],
                'amount'=>$result['amount'],
            ];
        }else{

            return [];
        }


    }

    public function notify($json,$header){

        if (hash_hmac('sha256', $header['timestamp'] . $json, $this->webhooks_key) != $header['signature']) {
            throw new \Exception('signature invalid');
        }

        //状态处理
        $data = json_decode($json, true);
        if (empty($data)) {
            throw new \Exception('data error');
        }

        if($data['name'] == 'payment_intent.succeeded' && $data['data']['object']['status'] == 'SUCCEEDED'){ //支付处理成功

            return true;
        }

        return false;
    }

}
