<?php
namespace app\common\library;

use app\common\library\AliyunOSS;
use app\common\library\HuaweiObs;
use think\Env;

class OneFileUpload
{
    // 要配置的内容
    
	private $uploadType = UPLOAD_SAVE_PLACE;		//上传的目标地址类型,默认阿里
    
    private $allowExt 	= 'jpg,jpeg,gif,png';		//允许的文件类型

    private $maxsize 	= 10240000;					//默认允许最大10M
    
    private $dir		= '';						//上传文件的存放目录(非完整路径)

    private $errorMess 	= "";						//错误信息
    
    private $absoluteUrl= false;                    //是否返回图片URL的全路径(false:否   true:是)

    /**
     * 用于设置成员属性($allowExt, $maxsize, $dir)
     * 可以通过连贯操作一次设置多个属性值
     * 
     * @param $key string 成员属性
     * @param $val string 为成员属性设置的值            
     * @return object 返回自己对象$this, 可以用于连贯操作
     */
    function set($key, $val)
    {
        if (array_key_exists($key, get_class_vars(get_class($this)))) {
            $this->setOption($key, $val);
        }
        return $this;
    }

    /**
     * 调用该方法上传文件
     * 
     * @param $file :THINKPHP的文件上传对象(如：request()->file('thumb'))       
     * @return mixed
     */
    function upload($file)
    {
    	if(empty($file))
    	{
    		$this->errorMess ='文件没有上传';
    		return false;
    	}
    	
    	$file->validate(['size'=>$this->maxsize,'ext'=>$this->allowExt])->check();
    	
    	//没有错误时
    	if(empty($file->getError()))
    	{
            $result = $this->uploadByLocal($file);
    		return $result;
    	}
    	else{
    		$this->errorMess = $file->getError();
    		return false;
    	}
    }

    
    /**
     * 把图片移到指定目录下
     * 
     * @param $file :THINKPHP的文件上传对象(如：request()->file('thumb'))
     */
    public function uploadByLocal($file){
    	
    	if(is_object($file)){
    		//文件保存
    		$upload_path = 'upload/file/';
    		$info = $file->move(ROOT_PATH .'public/'. $upload_path);
    		if($info){
    			//文件相对路径
    			$path = $upload_path.$info->getSaveName();
    			
    			//URL全路径
    			if($this->absoluteUrl)
    			    return STATIC_DOMAIN.'/'.$path;
    			else
    			//URL相对路径
    			    return $path;
    		}else{
    			$this->errorMess = $file->getError();
    			return false;
    		}
    	}else{
    		return false;
    	}
    }
    
    // 上传失败后，调用该方法则返回，上传出错信息
    public function getError()
    {
        return $this->errorMess;
    }
    
    // 为单个成员属性设置值
    private function setOption($key, $val)
    {
        $this->$key = $val;
    }
    
    /**
     * 删除指定文件
     * @param	$fileUrl :文件的URL链接地址
     * 
     * @return boolean
     */
    public function deleteFile($fileUrl){
    	
        if(!empty($fileUrl)){
            
            switch ($this->uploadType){
                
                case 'local':   //本地
                    
                    return $this->deleteLocalFile($fileUrl);
                    
                    break;
                    
                case 'ali':     //阿里云
                    
                    return $this->deleteAliFile($fileUrl);
                    break;
                    
                case 'huawei':  //华为云
                    
                    return $this->deleteHuaweiFile($fileUrl);
                    
                    break;
            }
        }
     
        return false;
    }
    
    /**
     * 删除本地文件
     * @param	$fileUrl :文件的URL链接地址
     * 
     * @return boolean 
     */
    private function deleteLocalFile($fileUrl)
    {
        //url全路径
        if($this->absoluteUrl){
            
            $filename = str_replace(STATIC_DOMAIN, ROOT_PATH.'public', $fileUrl);
        }
        //相对路径
        else{
            $filename = ROOT_PATH.'public/'.$fileUrl;
        }
    	
    	if(!file_exists($filename)){
    	    
    	    $this->errorMess = '文件不存在';
    		return false;
    	}
    	
    	unlink($filename);
    	return true;
    }
    
    
    /**
     * 删除阿里oss文件
     * @param	$fileUrl :文件的URL链接地址
     *
     * @return boolean
     */
    private function deleteAliFile($fileUrl)
    {
        
        //url全路径
        if($this->absoluteUrl){
            
            $fileName = str_replace(Env::get('ali_oss.access_endpoint').'/','',mb_strstr($fileUrl,Env::get('ali_oss.access_endpoint').'/',false,'UTF-8'));
        }
        //相对路径
        else{
            $fileName = $fileUrl;
        }
        
        
        if(!empty($fileName))
        {
            $exist = AliyunOSS::doesObjectExist($fileName);
            
            if($exist)
            {
                $result = AliyunOSS::deleteObject($fileName);
                
                if(empty($result))
                {
                    $this->errorMess = '文件删除失败';
                    return false;
                }

                return true;
            }
            else{
                $this->errorMess = '文件不存在';
                return false;
            }
        }
        else{
            $this->errorMess = '文件不存在';
            
            return false;
        }
    }
    
    /**
     * 删除华为obs文件
     * @param	$fileUrl :文件的URL链接地址
     *
     * @return boolean
     */
    private function deleteHuaweiFile($fileUrl)
    {
        //url全路径
        if($this->absoluteUrl){
            
            $fileName = parse_url($fileUrl)['path'];
            
            if(!empty($fileName)){
                $fileName = substr($url_path,1);       //去掉开头的斜杆
            }
        }
        //相对路径
        else{
            $fileName = $fileUrl;
        }
        
        
        if(!empty($fileName))
        {
            $obs = new HuaweiObs();
            
            $result = $obs->DeleteObject($fileName);

            if(empty($result))
            {
                $this->errorMess = '文件删除失败';
                return false;
            }
                
            return true;
        }
        else{
            $this->errorMess = '文件不存在';
            
            return false;
        }
        
    }
}