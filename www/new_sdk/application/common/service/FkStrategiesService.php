<?php

namespace app\common\service;

// 风控策略管理
use app\common\model\FkRosterLists;
use think\Cache;
use think\Db;
use think\Exception;
use think\Log;

class FkStrategiesService
{
    const CACHE_PREFIX = 'fk_strategies:';
    private $fkStrategiesDetailsService;
    private $redis;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->fkStrategiesDetailsService = new FkStrategiesDetailsService();
        $this->redis = Cache::store('default')->handler();
        $this->redis->select(1);
    }

    /**
     * 类方法返回
     *
     * @param bool   $status
     * @param string $msg
     *
     * @return array
     */
    public function result(bool $status=true, string $msg='success'){
        return ['status' => $status, 'msg' => $msg];
    }

    /**
     * 实时任务处理
     *
     * @param array  $eventData // 需要验证的数据
     * @param string $execution_type 执行类型：realtime/scheduled
     * @param string $strategy_code 策略编码
     *
     * @return array
     */
    public function processEvent(array $eventData){

        try {
            // 1. 数据验证
            // $this->validateEventData($eventData, $eventData['event_type'] ?? 'login');

            // 2. 检查黑白名单
            $rosterResult = $this->checkInspectRosterList($eventData);
            if ($rosterResult == 3) {
                return $this->result(true, '当前账户黑名单');
            } else if ($rosterResult == 2) {
                return $this->result(false, '当前账户白名单');
            }

            return $this->result(false, '当前账户无异常');
        } catch (Exception $e) {
            // 记录错误日志
            Log::error('风控处理异常@error：' . $e->getMessage().' - '.$e->getFile().':'.$e->getLine());
            Log::error('风控处理异常@getTraceAsString：' . $e->getTraceAsString());
            return $this->result(false, '风控系统异常:'. $e->getMessage());
        }
    }

    /**
     * 验证事件数据
     *
     * @param array  $eventData
     * @param string $type 类型：login|register
     *
     * @return void
     * @throws Exception
     */
    private function validateEventData(array $eventData, string $type = 'login')
    {
        if($type == 'login'){
            $requiredFields = ['user_name', 'ip', 'imei', 'phone'];
        }else{
            $requiredFields = ['user_name', 'ip', 'imei', 'phone', 'duration', 'id_card'];
        }
        foreach ($requiredFields as $field) {
            if (!isset($eventData[$field]) || empty($eventData[$field])) {
                return "缺少必要字段：{$field}";
                // throw new Exception("缺少必要字段：{$field}");
            }
        }
    }

    /**
     * 检查黑名单状态
     *
     * @param array $eventData
     *
     * @return array 是否在黑名单中: true=在黑名单中，false=不在黑名单中
     */
    public function handleBlackList(array $eventData)
    {
        // ## 检查IP
        if(isset($eventData['ip'])){
            if($this->checkInList($eventData['ip'], 'ip', 'black')){
                return $this->result(true, '当前IP在黑名单中');
            }
        }

        // ## 检查IMEI
        if(isset($eventData['imei'])){
            if($this->checkInList($eventData['imei'], 'imei', 'black')){
                return $this->result(true, "当前IMEI在黑名单中");
            }
        }

        // ## 检查用户名
        if(isset($eventData['user_name'])){
            if($this->checkInList($eventData['user_name'], 'user_name', 'black')){
                return $this->result(true, "当前用户名在黑名单中");
            }
        }

        // ## 手机号
        if(isset($eventData['phone'])){
            if($this->checkInList($eventData['phone'], 'phone', 'black')){
                return $this->result(true, "当前手机号在黑名单中");
            }
        }

        // ## 身份证号
        if(isset($eventData['id_card'])){
            if($this->checkInList($eventData['id_card'], 'id_card', 'black')){
                return $this->result(true, "当前身份证号在黑名单中");
            }
        }
        return $this->result(false);
    }

    /**
     * 检查名单状态
     * @param array $eventData
     * @return string 状态：1=待处理、2=正常、3=拒绝
     */
    private function checkInspectRosterList(array $eventData)
    {
        // ## 检查IP
        if(isset($eventData['ip'])){
            if ($this->checkInList($eventData['ip'], 'ip', 'black')) {
                return 3;
            }
            if ($this->checkInList($eventData['ip'], 'ip', 'white')) {
                return 2;
            }
        }

        // ## 检查IMEI
        if(isset($eventData['imei'])){
            if ($this->checkInList($eventData['imei'], 'imei', 'black')) {
                return 3;
            }
            if ($this->checkInList($eventData['imei'], 'imei', 'white')) {
                return 2;
            }
        }

        // ## 检查用户名
        if(isset($eventData['username'])){
            if ($this->checkInList($eventData['username'], 'username', 'black')) {
                return 3;
            }
            if ($this->checkInList($eventData['username'], 'username', 'white')) {
                return 2;
            }
        }

        // ## 手机号
        if(isset($eventData['mobile'])){
            if ($this->checkInList($eventData['mobile'], 'mobile', 'black')) {
                return 3;
            }
            if ($this->checkInList($eventData['mobile'], 'mobile', 'white')) {
                return 2;
            }
        }

        // ## 身份证号
        if(isset($eventData['id_card'])){
            if ($this->checkInList($eventData['id_card'], 'id_card', 'black')) {
                return 3;
            }
            if ($this->checkInList($eventData['id_card'], 'id_card', 'white')) {
                return 2;
            }
        }

        return 1;
    }
    // 添加名单
    public function addRoster($value, $type, $listType){
        $cacheKey = self::CACHE_PREFIX . 'inspect_list:';
        $key = $cacheKey . $listType . ':' . $type . ':' . $value;
        $this->redis->set($key, 1, 604800); // 缓存一周
    }
    // 删除名单
    public function delRoster($value, $type, $listType){
        $cacheKey = self::CACHE_PREFIX . 'inspect_list:';
        $key = $cacheKey . $listType . ':' . $type . ':' . $value;
        $this->redis->del($key);
    }
    /**
     * 检查是否在名单中
     * @param string $value 对象值
     * @param string $type 对象类型
     * @param string $listType 名单类型：black/white
     * @param string $cacheKey
     * @return bool
     */
    private function checkInList($value, $type, $listType)
    {
        if((new FkRosterLists())->getCacheInfo(['object_value' => $value, 'object_type' => $type, 'list_type' => $listType])){
            return true;
        }

        return false;
    }
}