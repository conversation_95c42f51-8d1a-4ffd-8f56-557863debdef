<?php

namespace app\common\exception;

use Exception;
use http\Header;
use think\exception\Handle;
use think\exception\HttpException;
use think\Log;
use think\Request;

class Http extends Handle
{

    public function render(Exception $e)
    {
        // // 参数验证错误
        // if ($e instanceof ValidateException) {
        //     return json($e->getError(), 422);
        // }
        //
        // // 请求异常
        // if ($e instanceof HttpException && request()->isAjax()) {
        //     return response($e->getMessage(), $e->getStatusCode());
        // }
        $request = Request::instance();

        $traces = [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'code' => $this->getCode($e),
            'message' => $this->getMessage($e),
            'source' => $this->getSourceCode($e),
            'method' => $request->method(),
            'param' => $request->param(),
        ];

        // Log::record(get_class($e), 'error');
        Log::record($traces, 'error');

        $template = $request->method()  .$traces['message']. "\n" . $e->getMessage() . "\n" .
            $e->getFile() . ' ' . $e->getLine() . "\n" . $request->url() . "\n" . $request->ip() . "\n" .
            json_encode($request->param());

        // 请求异常
        if ($e instanceof HttpException) {
            if(APP_STATUS == 'stable' && $request->isGet() && ($request->module() == 'home' || $request->module() == 'api')){
                // Log::record($traces, 'error');
                log_message('Response:' . $template, 'log', LOG_PATH . 'exception/');
            }else{
                // $ddurl = WARNING_DINGDING_URL;
                // $result = curlDD($template, $ddurl, true);
                // Log::record($template.'======='.$ddurl.'======='.$result, 'error');
            }
        }else{
            // $ddurl = WARNING_DINGDING_URL;
            // $result = curlDD($template, $ddurl, true);
            // Log::record($template.'======='.$ddurl.'======='.$result, 'error');
        }
        //TODO::开发者对异常的操作
        //可以在此交由系统处理
        return parent::render($e);
    }

}
