<?php
namespace app\common\factory\pay;

use Exception;
use think\Config;

/**
 * 趣智连 支付
 */
class QzlPay extends Pay
{
    // private $url = "https://test.quzhilian.net"; // 测试
    private $url = "https://game.quzhilian.net"; // 正式

    private $handleSign;

    // 下单
    public function pay($data, $type = 'payWxH5')
    {
        try {
            $res = $this->$type($data);
        } catch (Exception $e) {
            return ['error' => true, 'data' => [], "msg" => '支付平台的支付方式有误_qzl_001-' . $e->getMessage()];
        }

        return $res;
    }

    // 下单-微信H5
    public function payWxH5($data)
    {
        $this->action = 'pay';
        $pay_config = Config::get('qzl_pay');

        $wap_url = 'http://' . HTTP_HOST_URL; // 支付成功后打开的网址跳回游戏
        if ($data['gameid']) {
            $wap_url .= '?gameid=' . $data['gameid'];
        }

        $extra = [
            "notifyUrl" => $data['notify_url'],      // 后台通知地址
            "callbackUrl" => $wap_url, // 页面返回地址
            "clientIp" => $data['ip'],               // IP
            // "memberId" => $data['userid'], // 买家用户标识
            // "bankType" => "", // 银行编码
            // "cardType" => "", // 银行卡类型
            // "merchantName" => "祈盟支付", // 商户展示名称
            // "orderPeriod" => "5", // 订单有效时长
        ];
        $bodyData = [
            "tradeType" => "cs.pay.submit",                            // 交易类型
            "version" => "2.0",                                        // 版本号
            "channel" => "wx_h5",                                      // 支付渠道
            "mchNo" => $pay_config['mch_no'],                          // 商户号
            "body" => $data['productname'],                            // 商品描述
            "mchOrderNo" => $data['orderid'],                          // 商户支付订单号
            "amount" => strval(formatYuanToFen($data['real_amount'])), // 交易金额(分)，要转string类型
            "currency" => "CNY",                                       // 货币类型
            "timePaid" => date("YmdHis", time()),                      // 订单提交支付时间
            // "timeExpire" => date("YmdHis",strtotime( "+30 seconds")), // xxxx
            // "remark" => 'qzl_wx_pay', // 支付描述
        ];

        $params = array_merge($bodyData, $extra);
        $bodyData['sign'] = $this->formatRequestParams($params, $pay_config['pay_key']);
        $bodyData['extra'] = json_encode($extra); // 扩展参数

        $result = get_http_response($this->url . '/gateway/api/trade', $bodyData, 'post', array('Content-Type: application/x-www-form-urlencoded'));
        $return = json_decode($result, true);
        if ($return['resultCode'] == '0' && $return['status'] == '0') {
            return ['error' => false, 'data' => ['mweb_url' => $return['codeUrl']]];
        }
        return ['error' => true, 'data' => [], "msg" => '支付平台下单失败::qzl_002-' . $return['errMsg']];
    }

    // 回调
    public function notify($input)
    {
        $this->action = 'notify';
        $pay_config = Config::get('qzl_pay');

        $data['status'] = strval($input['status']);//返回是int类型，注意要转成字符串
        $data['mchNo'] = $input['mchNo'];
        $data['resultCode'] = strval($input['resultCode']);//返回是int类型，注意要转成字符串
        $data['cpOrderNo'] = $input['cpOrderNo'];          // 平台订单号
        $data['mchOrderNo'] = $input['mchOrderNo'];        // 商户订单号
        $data['amount'] = strval($input['amount']);
        $data['payResult'] = $input['payResult'];
        $data['traceTime'] = $input['traceTime'];
        $data['message'] = $input['message'];
        $sign = $input['sign'];

        //首先对获得的商户号进行比对
        if ($data['mchNo'] != $pay_config["mch_no"]) {
            ddMsg("warning", '', ['回调-qzl_pay', '回调商户号不存在', json_encode(['orderid' => $input['mchNo']], JSON_UNESCAPED_UNICODE)]);
            return ['error' => true, 'result' => $this->returnNotify('fail'), 'msg' => $data['mchNo'] . " - fail; 当前商户号不存在！"];
        }
        //验签
        $return = $this->getSignVeryfy($data, $sign, $pay_config["pay_key"]);
        if ($return) {
            return ['error' => false, 'result' => $this->returnNotify('success'), "msg" => $data['mchNo'] . " - success"];
        }

        ddMsg("warning", '', ['回调-qzl_pay', '回调验签失败', json_encode(['orderid' => $input['mchNo'], 'input' => $sign, 'handle' => $this->handleSign], JSON_UNESCAPED_UNICODE)]);
        return ['error' => true, 'result' => $this->returnNotify('fail'), 'msg' => $data['mchNo'] . ' - fail; sign: ' . $sign . '(input) - ' . $this->handleSign . '(handle)'];
    }



    // ----------------------------  下单处理  ------------------------------------
    //生成签名
    public function formatRequestParams($params, $app_secret) {
        ksort($params);
        $stringToBeSigned = "";
        foreach ($params as $k => $v)
        {
            if(is_string($v) && strlen($v) > 0)
            {
                $stringToBeSigned .= "$k=$v&";
            }
        }
        unset($k, $v);
        $stringToBeSigned = substr($stringToBeSigned, 0, -1);
        $stringToBeSigned .= "&paySecret=".$app_secret;
        $sign = strtoupper(md5($stringToBeSigned));
        return $sign;
    }


    // ------------------------------  回调处理  ----------------------------------
    function getSignVeryfy($para_temp, $sign,$key) {
        //除去待签名参数数组中的空值和签名参数
        $para_filter = $this->paraFilter($para_temp);
        //对待签名参数数组排序
        $para_sort = $this->argSort($para_filter);
        //把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
        $prestr = $this->createLinkstring($para_sort);

        $isSgin = $this->md5Verify($prestr, $sign, $key);
        return $isSgin;
    }

    function paraFilter($para) {
        $para_filter = array();
        while (list ($key, $val) = fun_adm_each ($para)) {
            if($key == "sign" || $val == "")continue;
            else	$para_filter[$key] = $para[$key];
        }
        return $para_filter;
    }

    function argSort($para) {
        ksort($para);
        reset($para);
        return $para;
    }

    function createLinkstring($para) {
        $arg  = "";
        while (list ($key, $val) = fun_adm_each ($para)) {
            if($arg){
                $arg .= "&".$key."=".$val;
            }else{
                $arg.=$key."=".$val;
            }
        }
        //如果存在转义字符，那么去掉转义
        if(get_magic_quotes_gpc()){$arg = stripslashes($arg);}
        return $arg;
    }

    function md5Verify($prestr, $sign, $key) {
        $prestr = $prestr ."&paySecret=". $key;
        $mysgin = strtoupper(md5($prestr));
        $this->handleSign = $mysgin;

        if($mysgin == $sign) {
            return true;
        }else{
            return false;
        }
    }
}