<?php
namespace app\common\factory\pay;

use Exception;
use LdzfPay\Api\MerToPlat;
use LdzfPay\Api\ReqDataUtil;
use LdzfPay\UmfPayService\UmfService;
use think\Config;
use app\common\logic\LdysPay as LogicLdysPay;


/**
 * 联动优势 支付
 */
class LdysPay extends Pay
{

    private $params = [
        "service" => "",
        "charset" => "UTF-8",
        "sign_type" => "RSA",
        "sign" => "",
        "res_format" => "HTML",
        "version" => "4.0",
    ];

    public function pay($data, $type = 'payZfbH5')
    {
        try {
            $res = $this->$type($data);
        } catch (Exception $e) {
            return ['error' => true, 'data' => [], "msg" => '支付平台的支付方式有误::qzl_001-' . $e->getMessage()];
        }

        return $res;
    }

    // 1. 记录订单
    public function recordOrder($data, $type = '')
    {

    }
    // 2. 发起支付
    // 3. 支付回调


    /**
     * 微信H5支付（使用自己的小程序）
     *
     * @param $data
     *
     * @return array
     */
    public function payWxmpH5($data)
    {
        if (empty($data['orderid'])) {
            return ['error' => true, 'data' => [], "msg" => 'ldys_支付参数有误！'];
        }
        $payData = [
            'title' => $data['productname'],
            'amount' => $data['real_amount'],
            'game_id' => $data['gameid'],
            'is_coin' => $data['is_coin'],
        ];
        $result = (new LogicLdysPay())->recordOrder($data['orderid'], $payData);
        if ($result['error'] == true) {
            return ['error' => true, 'data' => [], "msg" => 'ldys_支付下单失败！'];
        }
        if (empty($result['data']['pay_url'])) {
            return ['error' => true, 'data' => [], "msg" => 'ldys_支付下单失败！！'];
        }

        $pay_url = API_DOMAIN . '/index/wxmp?url=' . $result['data']['pay_url'];
        return ['error' => false, 'data' => ['mweb_url' => $pay_url], 'msg' => 'success'];
    }

    /**
     * 微信H5支付（使用联动官方小程序）
     *
     * @param $data
     *
     * @return array\
     */
    public function payWxmpH5V2($data)
    {
        if (empty($data['orderid'])) {
            return ['error' => true, 'data' => [], "msg" => 'ldys_支付参数有误！'];
        }
        $result = (new LogicLdysPay())->getWxH5Pay('wxmp', $data);
        if ($result['code'] != 200) {
            return ['error' => true, 'data' => [], "msg" => 'ldys_支付下单失败！'];
        }
        return ['error' => false, 'data' => ['mweb_url' => $result['data']['pay_url']], 'msg' => 'success'];
    }

    public function payZfbH5($data)
    {
        $mer_id = Config::get('ldys_pay.mer_id');

        $wap_url = 'http://' . HTTP_HOST_URL; // 支付成功后打开的网址跳回游戏
        if ($data['gameid']) {
            $wap_url .= '?gameid=' . $data['gameid'];
        }

        // dump($data);
        $params = $this->params;
        $params['service'] = "appprepay_order";
        $bodyData = [
            "notify_url" => $data['notify_url'],               // 异步通知地址
            "mer_id" => $mer_id,                               // 商户编号
            "order_id" => $data['orderid'],                    // 商户唯一订单号
            "mer_date" => date("Ymd", $data['create_time']),   // 商户订单日期(商户生成订单的日期，格式YYYYMMDD)
            "amount" => formatYuanToFen($data['real_amount']), // 付款金额
            "user_ip" => $data['ip'],                          // 用户IP地址
            "goods_inf" => $data['productname'],               // 商品描述信息
            "pay_type" => "1",                                 // 支付类型(固定值："1" 或 " 2" 业务含义：1代表支付宝；2代表微信)


            // "goods_id" => "", // 商品号
            // "mer_priv" => "xxxx", // 商户私有域
            "expire_time" => "30",                             // 订单过期时长
            "page_order_type" => "H5",                         // 支付方式(填固定值 H5)
            // "app_id" => "xxxx", // APPID标识(商户微信小程序)
            // "mer_flag" => "xxxx", // 商户标识
            "consumer_id" => $data['userid'],                  // 消费者ID(mer_flag不为空时必填)
            "return_url" => $wap_url,                          // 支付完成跳转链接(支付完成跳转链接跳转类型为内跳，或者外跳H5时 return_url格式为 https://xxxxx.com;跳转类型为外跳小程序时格式为 pages/index/index&appid=2021003156682566)
        ];
        // $data = array_merge($params, $data);

        $service = new UmfService($mer_id);
        $res = $service->H5FrontPageMaps($bodyData);
        dump($res);

        // $util = new ReqDataUtil();
        // $reqData = $util->makeRequestData($data,'post');
        // dump($reqData);

        $reqDataPost = MerToPlat::makeRequestDataByPost($bodyData);
        $post_url = $reqDataPost->getUrl();
        $mapfield = $reqDataPost->getField();
        dump($post_url, $mapfield);

        $result = get_http_response('https://pccp.everwings.online/matrix/getPollingPayInfo', $bodyData, 'post');
        dump($result);

    }

    public function pays($data)
    {
        $mer_id = Config::get('ldys_pay.mer_id');

        $params = [
            "service" => "appprepay_order",
            "charset" => "UTF-8",
            "sign_type" => "RSA",
            "sign" => "",
            "res_format" => "HTML",
            "version" => "4.0",
        ];
        $service = new UmfService("55282");
        $h5_pay_get_url = $service->H5FrontPageMap($params);


        $params = [
            "mer_id" => $mer_id,
            "notify_url" => $data['notify_url'],
            // "ret_url" => $notify_url,
            "goods_id" => $data['productname'],                                // 商品号
            // "goods_inf" => $data['productdesc'],            // 商品描述信息
            // "media_id" => "",                 // 媒介标识
            // "media_type" => "",               // 媒介类型[返回值范围：MOBILE（手机号）EMAIL（邮箱地址）]
            "order_id" => $data['orderid'],                                    // 原商户订单号（商户订单号和U付订单号必传其一，如都传递则需两者互相匹配）
            "mer_date" => date("Ymd", $data['create_time']),                   // 商户订单日期
            "amount" => formatYuanToFen($data['pay_amount']),                  // 付款金额（分）
            // "mer_priv" => "",                 // 商户私有域[联动优势支付平台原样返回，用于商户的私有信息]
            // "expand" => "",                   // 业务扩展信息
            "user_ip" => $data['ip'],                                          // 用户IP地址[必须取用户终端真实的公网IP，尽量保证用户IP不同，否则将大大影响支付成功率]
            "expire_time" => "30",                                             // 订单过期时长[单位为分钟，默认1440分钟（24小时）]

            "pay_type" => "1",                     // 固定值："1" 或 " 2" 业务含义：1代表支付宝；2代表微信
            "page_order_type" => "1",              // 填固定值 H5 或者 APP 默认为APP支付
        ];

        // $service = new UmfService("55282", EXTEND_PATH."LdzfPay/cert/55282_.key.pem");
        // $service = new UmfService("55282", "../cert/55282_.key.pem");
        $service = new UmfService("55282");
        $res = $service->mobileOrderMap($params);
        if($res['ret_code'] != '0000'){
            return ['error' => true, 'msg' => $res['ret_msg']];
        }
        return ['error' => false, 'data' => ['mweb_url' => $res]];
    }

    public function notify($data)
    {
        $result = (new LogicLdysPay())->notify();
        if ($result['error'] == true) {
            ddMsg("warning", '', ['回调-qzl_pay', '回调验签失败', json_encode(['orderid' => $data['order_id'], 'input' => '', 'handle' => ''], JSON_UNESCAPED_UNICODE)]);
            return ['error' => true, 'result' => $result['data'], 'msg' => 'LdysPay_回调验签失败: '.$data['order_id'] . ' - fail'];
        }

        return ['error' => false, 'result' => $result['data'], 'msg' => $data['order_id'] . ' - fail'];
    }

    // 支付回调通知返回
    public function returnNotify($res)
    {
        return $res;
    }
}