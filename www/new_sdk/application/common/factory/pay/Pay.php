<?php

namespace app\common\factory\pay;

use app\common\contracts\PayInterface;
use think\Config;

/**
 * 支付公共方法
 */
abstract class Pay implements PayInterface
{
    const DEFAULT_TIMEOUT = 10;

    protected $type;        // 支付方式类型（第三方渠道标识）
    protected $action;      // 操作类型(pay/notify/query/...)
    protected $config = []; // 支付方式类型的配置
    protected $msg;         // 日志记录内容

    // 支付回调返回标识
    protected $success = "success";
    protected $fail = "fail";

    public function __construct($type)
    {
        // 获取当前调用方法名
        $this->type = $type;
        // $this->action = request()->action();
        if (!empty(Config::get($type))) {
            $this->config = Config::get($type);
        }
    }

    // 下单
    public function pay($data, $type){}

    // 支付回调
    public function notify($data)
    {
    }

    // 查询订单
    public function query($data)
    {
    }

    // 退款
    public function refund($data)
    {
    }

    // 支付回调返回
    public function returnNotify($type)
    {
        if ($type == 'success') {
            return $this->success;
        }
        return $this->fail;
    }


    /**
     * 记录日志
     *
     * @param $orderNum 订单号
     * @param $sign     请求的签名
     * @param $oldSign  计算的签名
     * @param $result   状态
     *
     * @return void
     */
    public function recordLog($msg = '')
    {
        if (!$msg) {
            $msg = $this->msg;
        }
        if (!$msg) {
            return false;
        }
        //写入日志
        log_message("## " . $this->type . ": " . $msg, 'log', RUNTIME_PATH . 'pay/' . $this->type . "/");
    }
}