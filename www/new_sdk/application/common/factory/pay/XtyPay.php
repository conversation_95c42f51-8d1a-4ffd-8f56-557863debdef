<?php
namespace app\common\factory\pay;
use XiTaiYouPay\XiTaiYouPay;

/**
 * 喜钛游 支付
 */
class XtyPay extends Pay
{
    public function pay($data, $type)
    {
        try {
            switch ($type){
                case "payWxH5":
                    $pay_channel = 'WX';
                    $return_type = 2;
                    break;
                case "payAliH5":
                    $pay_channel = 'ALI';
                    $return_type = 3;
                    break;
                case "payUniH5":
                    $pay_channel = 'UNI';
                    $return_type = 2;
                    break;
                default:
                    return ['error' => true, 'data' => [], "msg" => 'xty-支付发起失败：支付类型有误！'];
            }

            if(empty($data['orderid']) || empty($data['productname']) || empty($data['real_amount']) || empty($data['ip']) ){
                return ['error' => true, 'data' => [], "msg" => 'xty-支付发起失败：支付参数有误！'];
            }
            $params = [
                'out_trade_no' => $data['orderid'], // 订单号
                'order_name' => $data['productname'], // 订单名称
                'total_amount' => formatYuanToFen($data['real_amount']), // 金额(分)
                'order_desc' => $data['productname'], // 订单描述
                'notify_url' => $data['notify_url'], // ip
                'return_url' => $data['return_url']??"", // ip
                'ip' => $data['ip'], // ip
            ];
            $result = (new XiTaiYouPay())->payH5($pay_channel, $return_type, $params);
            if($result['code'] == 200){
                return ['error' => false, 'data' => ['mweb_url' => $result['data'], 'referer' => 'com.yiyou.zfb'], "msg" => ''];
            }
            return ['error' => true, 'data' => [], "msg" => 'xty-支付发起失败：'. $result['msg']];
        } catch (Exception $e) {
            return ['error' => true, 'data' => [], "msg" => 'xty-支付发起异常: ' . $e->getMessage()];
        }
    }

    public function notify($data)
    {
        if (empty($data['req_data'])) {
            return ['error' => false, 'result' => [], 'msg' => 'fail:渠道方回调数据有误！'];
        }
        try {
            $signData = json_decode($data["req_data"], true);
            if (empty($data['req_data'])) {
                return ['error' => false, 'result' => [], 'msg' => 'fail:渠道方订单号不存在！'];
            }
            $result = (new XiTaiYouPay())->orderQuery($signData['order_no'], $signData['out_trade_no']);
            if ($result['code'] == 200) {
                return ['error' => true, 'result' => $this->returnNotify('success'), 'msg' => 'success'];
            }
            ddMsg("warning", '', ['回调-xty_pay', '回调验签失败', json_encode(['orderid' => $signData['out_trade_no'], 'input' => '', 'handle' => ''], JSON_UNESCAPED_UNICODE)]);
        } catch (\Exception $e) {
            return ['error' => false, 'result' => 'failL' . $e->getMessage(), 'msg' => ''];
        }

        return ['error' => false, 'result' => $this->returnNotify('success') . ' : ' . $result['data'], 'msg' => ''];
    }

    // 支付回调通知返回
    public function returnNotify($res)
    {
        return $res;
    }
}