<?php

namespace app\common\factory;

use app\common\factory\pay\LdysPay;
use app\common\factory\pay\QzlPay;
use app\common\factory\pay\XtyPay;
use app\common\factory\pay\YyYbPay;

class Pay
{
    /**
     * 工厂方法示例
     *
     * @param string $way 支付方式类型
     *
     * @return mixed 支付方式实例
     * @throws \Exception
     */
    public static function createPayment($way)
    {
        // 根据支付方式类型返回不同的实例
        switch ($way) {
            case 'ldys_pay':
                return new LdysPay($way);
            case 'qzl_pay':
                return new QzlPay($way);
            case 'yyyb_pay':
                return new YyYbPay($way);
            case 'xty_pay':
                return new XtyPay($way);
            // 其他支付方式...
            default:
                throw new \Exception('当前支付方式未配置！');
        }
    }

    /**
     * 调用支付方式的支付方法
     *
     * @param string $way 支付方式类型
     *
     * @return mixed
     * @throws \Exception
     */
    public static function makePay($way, $data, $type = '')
    {
        $payment = self::createPayment($way);
        return $payment->pay($data, $type);
    }

    /**
     * 调用支付方式的支付回调方法
     *
     * @param string $way 支付方式类型
     * @param string $data 数据
     *
     * @return bool|void
     * @throws \Exception
     */
    public static function makeNotify($way, $data)
    {
        $payment = self::createPayment($way);
        return $payment->notify($data);
    }

    /**
     * 调用支付方式的查询订单方法
     * @param string $way 支付方式类型
     *
     * @return void
     * @throws \Exception
     */
    public static function makeQuery($way)
    {
        $payment = self::createPayment($way);
        return $payment->query();
    }

    // 调用支付方式的退款方法
    public static function makeRefund($way)
    {
        $payment = self::createPayment($way);
        return $payment->refund();
    }

    /**
     * 调用支付方式的返回方法
     *
     * @param string $way        支付方式标识
     * @param string $returnType 状态标识: success/fail
     *
     * @return string
     * @throws \Exception
     */
    public static function makeReturnNotify($way, $returnType)
    {
        $payment = self::createPayment($way);
        $result = $payment->returnNotify($returnType);

        log_message('## '.$way.'回调返回: '. $returnType, 'log', LOG_PATH . 'PayNotify/');
        return $result;
    }
}