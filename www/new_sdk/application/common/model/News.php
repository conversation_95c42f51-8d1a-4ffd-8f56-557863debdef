<?php

namespace app\common\model;

use think\Cache;
use think\Db;
use think\Config;

/**
 * 新闻资讯表
 * <AUTHOR>
 */
class News extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_article';
    protected $dateFormat = false;          //时间戳格式不自动转换
	protected $connection = 'database_slave';  //官网使用从库

    private $catgList = array(
        array('id'=>1, 'name'=>'新游资讯'),
        array('id'=>4, 'name'=>'新游攻略'),
    );
    private $_catgMapList = array(
        1 => '新游资讯',
        2 => '麻花网络资讯',
        3 => '新游评测',
        4 => '新游攻略',
        5 => '精彩视频',
    );
    private $position = array(
        '首页资讯' => 'a',
        '首页攻略' => 'b',
        '新游评测' => 'c',
        '新闻热点' => 'd',
        '新游视频' => 'e',
    );

    private $mobileCatgList = array(
        array('id'=>1, 'name'=>'资讯'),
        array('id'=>4, 'name'=>'攻略'),
    );
    private $_mobileCatgMapList = array(
        1 => '资讯',
        4 => '攻略',
    );


    public function getPosition($name) {
        return $this->position[$name];
    }
    public function getCatgList() {
        return $this->catgList;
    }
    public function getCatgName($type) {
        return $this->_catgMapList[$type];
    }
    public function getMobileCatgList() {
        return $this->mobileCatgList;
    }
    public function getMobileCatgName($type) {
        return $this->_mobileCatgMapList[$type];
    }
}
