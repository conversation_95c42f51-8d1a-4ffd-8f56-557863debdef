<?php
namespace app\common\model;

use think\Db;
use think\Config;
/**
 * 游戏扩展表
 * <AUTHOR>
 */
class GameInfo extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_gameinfo';
    protected $dateFormat = false;          //时间戳格式不自动转换
    
    
    /**
     ** 官网中，根据cy_game表中的lanmu字段，获取热门游戏、最新游戏、本周推荐信息
     *@param $condition array 查询条件
     *
     *@return $list array
     */
    public function getGameInfoByLanmu($condition,$limit = 6)
    {
        $condition['g.cooperation_status']  = ['in','1,2'];
        $condition['g.is_show']             = 1;


        $list = Db::connect(config('database_slave'))->table('cy_gameinfo info,cy_game g')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->join('cy_gametype t', 'g.type = t.id','left')
				->join('cy_gamesubject s', 'g.subject = s.id','left')
				->join('cy_sdkgamelist sdk', 'g.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
				->field('g.nickname,g.id,info.androidurl,info.platform,info.show_style,g.pinyin,info.mobileicon,g.type,g.subject,t.name as typename,s.name as subjectname,info.description,sdk.filename')
				->where('info.game_id=g.id')        
				->where($condition)
                ->limit(0,$limit)
                ->order('g.xulie DESC,g.create_time desc')
                ->select();

		foreach ( $list as &$game ) {
			$game['icon'] = getGameIcon($game['mobileicon']);
			$game['download'] = getDownload($game);
			$game['typename'] = getTypename($game['typename']);
			$game['subjectname'] = getSubjectname($game['subjectname']);
			$game['description'] = getDeveloper($game['description']);
		}
        return $list;
    }

    /**
     ** 官网中，根据cy_game表中的lanmu字段，获取热门游戏、最新游戏、本周推荐信息
     *@param $condition array 查询条件
     *
     *@return $list array
     */
    public function getMobileGameInfoByLanmu($condition,$limit=3)
    {
        $condition['g.cooperation_status']  = ['in','1,2'];
        $condition['g.is_show']             = 1;
        
        $list = Db::connect(config('database_slave'))
				->table('cy_game g')
				->join('cy_gameinfo info', 'g.id = info.game_id')
				->join('cy_gametype t', 'g.type = t.id','left')
				->join('cy_gamesubject s', 'g.subject = s.id','left')
				->join('cy_sdkgamelist sdk', 'g.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
				->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->field('g.id,g.nickname,g.power,info.androidurl,info.platform,g.pinyin,info.mobileicon,g.type,g.subject,t.name as typename,s.name as subjectname,sdk.filename')
                ->where($condition)
                ->limit(0,$limit)
                ->order('g.xulie DESC,g.create_time desc')
                ->select();
        
        return $list;
    }
}