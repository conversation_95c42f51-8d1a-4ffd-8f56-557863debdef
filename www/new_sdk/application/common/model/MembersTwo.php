<?php
namespace app\common\model;

/**
 * 前台用户扩展表
 * <AUTHOR>
 */
class MembersTwo extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_memberstwo';

    /**
     * 更新用户资料副表
     * @param      $data    需要更新的资料
     * @param      $userid  用户id
     * @param bool $check   是否检查用户在cy_memberstwo有记录
     * @return bool
     */
    public function updateInfo($data,$userid,$check = true){
        if ($check){
            $info = $this->where('userid',$userid)->find();
            if ( empty($info)) {
                $data['userid']   = $userid;
                $data['username'] = session('front_account');
                $res              = model('MembersTwo')->insertGetId($data);
                return $res ? true : false;
            }
        }
        $res = $this->save($data , ['userid' => $userid]);
        return $res ? true : false;
    }
}