<?php

namespace app\common\model;

use think\Cache;
use think\Db;
use think\Config;

/**
 * 游戏表
 * <AUTHOR>
 */
class Game extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_game';
    protected $dateFormat = false;          //时间戳格式不自动转换
    const  CACHE_KEY = "clearable_game_list";
    protected $resultSetType = 'collection';

    protected static function init()
    {
        Game::afterInsert(function () {
            Cache::clear(self::CACHE_KEY);
        });

        Game::afterUpdate(function () {
            Cache::clear(self::CACHE_KEY);
        });
        Game::afterWrite(function () {
            Cache::clear(self::CACHE_KEY);
        });
    }

    public function getAllByCondition($field = 'id,name', $condition = [],$order='id desc',$type = 'all')
    {
        $prefix                = 'clearable_game_list';
        // $condition['isdelete'] = 0;
        $cacheKey              = $prefix . md5(json_encode($condition) . $field.$order.$type);
        $list                  = Cache::get($cacheKey);
        if (empty($list)) {
            if ($type == 'self') {
                $list = Db::table($this->table)->field($field)->where($condition)->where("is_default = 0")->order($order)->select();
            }else{
                $list = Db::table($this->table)->field($field)->where($condition)->order($order)->select();
            }

            if (!empty($list)) {
                Cache::tag('clearable_game_list')->set($cacheKey, $list, 300);
            }
        }
        return $list;
    }
    // 获取非外部游戏列表 考虑到游戏管理更新后的及时性 不做缓存
    public function getSelfAllByCondition($field = 'id,name', $condition = [],$order='id desc')
    {
        // $prefix                = 'self_game_list';
        $condition['isdelete'] = 0;
        $condition['is_default'] = 0;
        // $cacheKey              = $prefix . md5(json_encode($condition) . $field.$order);
        // $list                  = Cache::get($cacheKey);
        // if (empty($list)) {
            $list = Db::table($this->table)->field($field)->where($condition)->whereor('is_default','null')->order($order)->select();
            // if (!empty($list)) {
                // Cache::tag('self_game_list')->set($cacheKey, $list, 3600 * 24);
            // }
        // }
        return $list;
    }
    public function getInitial($id)
    {
        $info = $this->field('initial')->find($id);
        if (!empty($info)) {
            return $info['initial'];
        }
        return $info;
    }

    public function getName($id)
    {
        return $this->where('id', $id)->value('name');
    }

    public function getOriginName($id)
    {
        return $this->where('id', $id)->value('origin_name');
    }

    public function getApkInfo($apk_url,$pinyin,$type='',$platform=0) {
        $auth     = md5($pinyin.'_app_info');
		$cacheKey = Config::get('DATA_CACHE_PREFIX').':'.$auth;
        $apk_info = Cache::get($cacheKey);
        if ( empty($apk_info) ) {
            $pinyin   = urlencode($pinyin);
			if($platform == 1){
				$api_url  = Config::get('WEILONG_WEBSITE')['DOWNLOAD'].'/getIosPackageInfo.php';
			}
			else{
				$api_url  = Config::get('WEILONG_WEBSITE')['DOWNLOAD'].'/getPackageInfo.php';
			}
            $apk_info = __getUrlContent($api_url,array('pinyin'=>$pinyin,'auth'=>$auth));
            $apk_info = json_decode($apk_info, true);
            if ( empty($apk_info['error']) ) {
                Cache::set($cacheKey, json_encode($apk_info), 300);
            }
        } else {
            $apk_info = json_decode($apk_info, true);
        }

        if ( empty($apk_info['error']) && $type ) {
			if($apk_info['size'] >= 1024*1024*1024){
				$apk_info['size'] = round($apk_info['size']/pow(1024, 3), 2).'GB';
			}
			else if($apk_info['size'] >= 1024*1024){
				$apk_info['size'] = round($apk_info['size']/pow(1024, 2), 2).'MB';
			}
			else{
				$apk_info['size'] = '';
			}
            $apk_info['version'] = isset($apk_info['version'])&&$apk_info['version'] ? 'V'.$apk_info['version'] : '';
            $apk_info['updtime'] = isset($apk_info['updtime'])&&$apk_info['updtime'] ? date('Y-m-d', $apk_info['updtime']) : '';
            return $apk_info[$type];
        }

        return '';
    }

    /**
     * 获取已接单游戏id
     */
    public function getTakingGame($channelId){
        $account_level = Db::name('nw_channel')->where(['id' => $channelId])->value('level');
        switch ($account_level) {
            case '1'://B账号
                $list = model('SdkGameList')->alias('sdk')
                    ->join('cy_game game', 'game.id = sdk.gameid AND game.cooperation_status != 3 AND (game.is_default = 0)')
                    ->join('nw_channel channel', 'sdk.channel_id = channel.id', 'left')
                    ->join('nw_channel p_channel', 'channel.parent_id = p_channel.id AND p_channel.level = 2', 'left')
                    ->where(['channel.id_path' => ['LIKE', '%,' . $channelId . ',%']])->column('game.id');
                break;
            case '2'://B-
                $list = model('SdkGameList')->alias('sdk')
                    ->join('cy_game game', 'game.id = sdk.gameid AND game.cooperation_status != 3 AND (game.is_default = 0)')
                    ->join('nw_channel channel', 'sdk.channel_id = channel.id', 'left')
                    ->where(['channel.id_path' => ['LIKE', '%,' . $channelId . ',%']])->column('game.id');
                break;
            case '3'://C
                $list = model('SdkGameList')->alias('sdk')
                    ->join('cy_game game', 'game.id = sdk.gameid AND game.cooperation_status != 3 AND (game.is_default = 0)')
                    ->join('nw_channel channel', 'sdk.channel_id = channel.id', 'left')
                    ->field(['game.id'])->where('channel.id = '.$channelId)->column('game.id');
                break;
            default:
                $list = [-1];//不存在接单游戏
                break;
        }
        return $list;
    }

    /**
     * 获取可自由支付的游戏
     */
    public function getGameListPay(){
        return $this->where('free_pay', 2)->field('id,name,name as text')->select();
    }

    public function getGameInfoList(){
        return $this->alias('a')->join('cy_gameinfo b', 'a.id=b.game_id')
            ->cache('v2:order:gameInfo', 180)->column('a.name,b.mobileicon,a.nickname', 'a.id');
    }
}
