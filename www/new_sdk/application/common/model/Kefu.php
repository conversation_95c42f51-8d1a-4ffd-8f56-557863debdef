<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/10/31
 * Time: 16:56
 */
namespace  app\common\model;

use think\Cache;
use think\Model;
use think\Config;

class Kefu extends Model
{
    protected $pk = 'id';
    protected $table = 'cy_kefu';
    const  CACHE_KEY = "kefu:getKefuList";

    protected static function init()
    {
        Kefu::afterInsert(function () {
            Cache::rm(self::CACHE_KEY);
        });

        Kefu::afterUpdate(function () {
            Cache::rm(self::CACHE_KEY);
        });
        Kefu::afterWrite(function () {
            Cache::rm(self::CACHE_KEY);
        });
    }

    /*
     *获取列表内容
     */
    public function getList()
    {
        return $this->field('id,qq,nickname,game_id')->paginate();
    }

    public function getKefuList()
    {
        return $this->cache(self::CACHE_KEY, Config::get('QUERY_RESULT_CACHE_TIME'))->field('id,qq,nickname,"09:30-22:00" as time')->select();
    }

    // 根据游戏ID查询带有缓存的数据
    public function getKefuByGameId($where = [])
    {
        $cacheName = self::CACHE_KEY;
        if(!empty($where['game_id'])){
            $cacheName .= ":".$where['game_id'];
        }
        return $this->cache($cacheName, Config::get('QUERY_RESULT_CACHE_TIME'))->where($where)->field('id,qq,nickname,"09:30-22:00" as time')->select();
    }
}