<?php
namespace app\common\model;

/**
 * 推广落地页短链表
 * <AUTHOR>
 */
class PromotionShortLink extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'nw_promotion_short_link';
    protected $dateFormat = false;          //时间戳格式不自动转换


    /**
     * 生成推广的短链
     *
     * @param string $game_id 游戏ID
     * @param  string $channel_id 渠道ID
     * @param  string $package_type
     * @param  string $type 类型：推广/推广页
     *
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function insertData($game_id,$channel_id,$package_type, $type=1)
    {
        if(!$this->where(['game_id'=>$game_id,'channel_id'=>$channel_id,'package_type'=>$package_type, 'type' => $type])->find()){
            $this->insert(['game_id'=> $game_id,'channel_id'=> $channel_id,'package_type'=>$package_type,'create_time'=>time(),'short_link'=>$this->createShortLink(), 'type' => $type]);
            return true;
        }
        else{
            return false;
        }
    }

    /**
     * 生成推广落地页短链接参数字段值
     */
    private function createShortLink()
    {
        $shortLink = strtolower(random(7));
        
        while ($this->field('id')->where(['short_link' => $shortLink])->find()) {
            $shortLink = strtolower(random(7));
            if (preg_match('/^\d+$/', $shortLink)) {
                $shortLink = strtolower(random(7));
            }
        }
        
        return $shortLink;
    }
}