<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/10/31
 * Time: 16:56
 */

namespace  app\common\model;
use think\Db;
use think\Env;
use think\Model;

class CscAppeal extends Model
{
    protected $table = 'cy_csc_appeal';
    // private $_AUTH_KEY = 'CWF8CKN1u4LDnxXZKvc30nMKqftziD8nSlbDeddre_ddsdd@ao@aoyousdk@2020';
    /*
     *获取申诉列表
     */
    public function getList($condition = [],$score = 0)
    {
       
        return $this->where('reason = 1 or (reason = 2 and score< :score)',['score'=>$score])
                ->where($condition)->field('id,username,mobile,mail,code,status,reason,score,create_time,notice_time')
                ->order('status asc,create_time desc')
                ->paginate();
    }
    /*
     * 获取用户申诉信息
     */
    public function getAppealResult($where)
    {
        $data = $this->where($where)->find(); //申诉信息
        $crTime = strtotime($data['create_time']); // 申诉时间
        $condition['username'] = $data['username'];
        $member = model('Members')->where($condition)->find(); //用户注册信息
        if (empty($member)) {
            return false;
        }
        $member['reggamename'] = $this->getGameName($member['gameid']);
        $member['channelname'] = $this->getChannelName($member['channel_id']);
        $user_id = $member['id'];
        //实例化用户历史数据表
        $historyModel = model('MemberHistory');
        $historyinfo = $historyModel->where(['userid'=>$user_id,'create_time'=> ['elt',$crTime] ])->select(); //历史信息
        $reginfo = $historyModel->where(['userid'=>$user_id,'create_time'=> ['elt',$crTime] ])->order('create_time asc')->field('mobile,email')->find(); //历史信息
        $bond = model('MemberChannelGame')->where('member_id',$user_id)->select(); //绑定游戏
        $member['reg_time'] = date("Y-m-d",$member['reg_time']);
        $data = $this->getArr($data);//反序列化得到申诉信息
        // 实例化pay模型
        $payModel = model('Pay');
        $payhistory = $payModel->where(['userid'=>$user_id,'create_time'=> ['elt',$crTime] ])->field('amount,create_time')->select();
        $totalpay = $payModel->where(['userid'=>$user_id,'status'=>1,'pay_time'=> ['elt',$crTime]])->sum('amount');
        $infos = $this->getInfos($bond,$historyinfo,$payhistory); //用户信息
        $config = $this->getConfig();  //配置值
        $firstrechargegameid = $payModel->where('userid',$user_id)->order('create_time asc')->value('gameid');
        $firstrechargegamename = $this->getGameName($firstrechargegameid);
        $recharge = $this->getRecharge($data);
        //ip转换地址
        $member['addr'] = $this->GetIpLookup($member['ip']);
        //判断/审核
        $result = $this->getResult($data, $member, $firstrechargegameid, $infos, $totalpay, $recharge,$reginfo,$user_id);

        //系统得分
        $score = $this->getScore($result,$infos,$config);

        $sum = array_sum($score);

        $array = [
            'recharge'  => $recharge,
            'score'     => $score,
            'sum'       => $sum,
            'totalpay'  => $totalpay,
            'gamename'  => $firstrechargegamename,
            'infos'     => $infos,
            'data'      => $data,
            'member'    => $member,
            'result'    => $result,
            'reginfo'   => $reginfo,
            'config'    => $config,
        ];

        return $array;
    }

    /**
     * 获取用户申诉总得分
     * @param  [array] $data 申诉详情
     * @return [int]         总得分
     */
    public function getAppealScore($data)
    {
        $sum = 0;
        $condition['username'] = $data['username'];
        $member = model('Members')->where($condition)->field('id,reg_time,ip,imeil,gameid,channel_id')->find(); //用户注册信息
        if (empty($member)) {
            return $sum;
        }
        $user_id = $member['id'];
        $crTime = strtotime($data['create_time']); // 申诉时间
        //实例化用户历史数据表
        $historyModel = model('MemberHistory');
        $historyinfo = $historyModel->where(['userid'=>$user_id,'create_time'=> ['elt',$crTime] ])->select(); //历史信息
        $reginfo = $historyModel->where(['userid'=>$user_id,'create_time'=> ['elt',$crTime] ])->order('create_time asc')->field('mobile,email')->find(); //历史信息
        $bond = model('MemberChannelGame')->where('member_id',$user_id)->select(); //绑定游戏
        $member['reg_time'] = date("Y-m-d",$member['reg_time']);
        $data = $this->getArr($data);//反序列化得到申诉信息
        // 实例化pay模型
        $payModel = model('Pay');
        $payhistory = $payModel->where(['userid'=>$user_id,'create_time'=> ['elt',$crTime] ])->field('amount,create_time')->select();
        $totalpay = $payModel->where(['userid'=>$user_id,'status'=>1,'pay_time'=> ['elt',$crTime]])->sum('amount');
        $infos = $this->getInfos($bond,$historyinfo,$payhistory); //用户信息
        $config = $this->getConfig();  //配置值
        $firstrechargegameid = $payModel->where('userid',$user_id)->order('create_time asc')->value('gameid');
        //ip转换地址
        $member['addr'] = $this->GetIpLookup($member['ip']);

        $recharge = $this->getRecharge($data);
        //判断/审核
        $result = $this->getResult($data, $member, $firstrechargegameid, $infos, $totalpay, $recharge,$reginfo,$user_id);
        //系统得分
        $score = $this->getScore($result,$infos,$config);

        $sum = array_sum($score);

        return $sum;

    }

    //新浪ip转换地址
    function GetIpLookup($ip = ''){
        if(empty($ip)){
            return '请输入IP地址';
        }

        $res = @file_get_contents('http://ip.taobao.com/service/getIpInfo.php?ip=' . $ip);
        if(empty($res)){
            return false;
        }

        $jsonMatches = array();
        preg_match('#\{.+?\}#', $res, $jsonMatches);
        // 经过上面的过滤步骤，会丢失 } ，故补上
        $jsonMatches[0] = $jsonMatches[0].'}';
        if(!isset($jsonMatches[0])){
            return false;
        }
        $json = json_decode($jsonMatches[0], true);
        if(isset($json['code']) && $json['code'] == 0){
            $json['data']['ip'] = $ip;
            unset($json['code']);
        }else{
            return false;
        }

        return $json['data']['country'].$json['data']['region'].$json['data']['city'];
    }

    /*
     * 获取游戏昵称
     */
    public function getGameName($gameid)
    {
        $gameName = model('game')->where('id',$gameid)->value('nickname');
        return $gameName;
    }

    /*
     * 获取用户注册渠道
     */
    public function getChannelName($channel_id)
    {
        $ChannelName = model('Channel')->where('id',$channel_id)->value('name');
        return $ChannelName;
    }
    

    /*
     * 配置分数的键值对
     */
    public function getConfig()
    {
        $list = model('CscSetting')->column('value','name');
        return $list;
    }

    /*
     * 判断正确
     */
    public function getResult($data, $member, $firstrechargegameid, $infos, $totalpay, $recharge,$reginfo,$id)
    {
        $time = strtotime($data['regtime']) - strtotime($member['reg_time']);
        $registed_time = time() - strtotime($member['reg_time']);
        if($registed_time <= 90 * 86400){ //注册时间
            $result['regtime'] = abs($time) <= 15 * 86400 ? 1 : 0;
        }
        if($registed_time > 90 * 86400){ //注册时间
            $result['regtime'] = abs($time) <= 30 * 86400 ? 1 : 0;
        }

        $result['regmobile'] = $data['regmobile'] === $reginfo['mobile'] ? 1 : 0;
        
        if($data['imei'] === $member['imeil']){ //注册imei
            $result['imei'] = 1;
        }
        if(empty($data['imei']) && empty($member['imeil'])){
            $result['imei'] = 1;
        }
        $result['imei'] = isset($result['imei']) ? 1 : 0;

        $result['addr'] = $data['addr']  === $member['addr'] ? 1 : 0; //注册地址

        $result['reggameid'] = $data['reggameid'] == $member['gameid'] ? 1 : 0;  //注册来源游戏
        if (!$result['reggameid']){
            $reggame_1 = $this->getGameName($member['gameid']);
            if ($reggame_1 == $data['reggamename']){
                $result['reggameid'] = 1;
            }
        }

        $result['firstrechargegameid'] = $data['firstrechargegameid'] == $firstrechargegameid ? 1 : 0; //首次充值游戏
        if (!$result['firstrechargegameid']){
            $firstrechargegame_1 = $this->getGameName($firstrechargegameid);
            if ($firstrechargegame_1 == $data['firstrechargegamename']){
                $result['firstrechargegameid'] = 1;
            }
        }

        if(intval($data['totalrecharge']) >= intval($totalpay)*0.9  &&  intval($data['totalrecharge']) <= intval($totalpay)*1.1){ //累计充值金额
            $result['totalrecharge'] = 1;
        }else{
            $result['totalrecharge'] = 0;
        }

        $result['regchannelid'] = $data['regchannelid'] === $member['channel_id'] ? 1 : 0;  //注册渠道id
       
        foreach ($data['oldpwd'] as $oldpwd){  //历史密码
            if(in_array($oldpwd,$infos['oldpwd'])){
                $result['oldpwd'][] = 1;
            }else{
                $result['oldpwd'][] = 0;
            }
        }

        foreach ($data['oldmail'] as $oldmail){ //历史邮箱
            if(in_array($oldmail,$infos['oldmail'])){
                $result['oldmail'][] = 1;
            }else{
                $result['oldmail'][] = 0;
            }
        }
        foreach ($data['oldmobile'] as $oldmobile){ //历史手机
            if(in_array($oldmobile,$infos['oldmobile'])){
                $result['oldmobile'][] = 1;
            }else{
                $result['oldmobile'][] = 0;
            }
        }
        foreach ($data['boundgameid'] as $boundgameid){ //绑定过的游戏
            $boundgame = $this->getGameName($boundgameid);
            if(in_array($boundgame,$infos['boundgameid'])){
                $result['boundgameid'][] = 1;
            }else{
                $result['boundgameid'][] = 0;
            }
        }

        // 历史充值记录判断，已经参与判断正确的历史记录，不再参与后续的判断，避免重复判断
        $arr = [];
        foreach($recharge as $key => $pay){
            $condition['amount'] = $pay['rechargeamount'];
            $condition['userid'] = $id;
            $times = model('Pay')->where($condition)->field('create_time,id')->select();
            foreach ($times as $time){
                $first_time = strtotime($time['create_time']) - 3600;
                $last_time  = strtotime($time['create_time']) + 3600;

                if(!in_array($time['id'],$arr) && strtotime($pay['rechargetime']) > $first_time && strtotime($pay['rechargetime']) < $last_time){
                    $result['recharge'][$key] = 1;
                    $arr[] = $time['id'];
                    break;
                }else{
                    $result['recharge'][$key] = 0;
                }
            }
            /*if( isset($result['recharge'][$key]) && $result['recharge'][$key] == 1){ //由于当前正确一个就给全部分数,为节省资源,出现正确就跳出循环
                break;
            }*/
        }
        return $result;
    }

    //获取申诉各项信息得分情况
    public function getScore($result,$infos,$config){
        $count['result']['oldpwd'] =   isset($result['oldpwd']) ? ( in_array(1,$result['oldpwd']) ? count($result['oldpwd']) : 0 ) : 0;
        $count['result']['oldmail'] = isset($result['oldmail']) ? (in_array(1,$result['oldmail']) ? count($result['oldmail']) : 0 ) : 0;
        $count['result']['oldmobile'] = isset($result['oldmobile']) ? (in_array(1,$result['oldmobile']) ? count($result['oldmobile']) : 0 ) : 0;
        $count['result']['boundgameid'] = isset($result['boundgameid']) ? (in_array(1,$result['boundgameid']) ? count($result['boundgameid']) : 0 ) : 0;
        $count['result']['recharge'] = isset($result['recharge']) ? (in_array(1,$result['recharge']) ? count($result['recharge']) : 0 ) : $result['recharge']=[];

        $count['infos']['pay'] = isset($infos['pay']) ? count($infos['pay']) : 0;
        $count['infos']['oldpwd'] = isset($infos['oldpwd']) ? count($infos['oldpwd']) : 0;
        $count['infos']['oldmail'] = isset($infos['oldmail']) ? count($infos['oldmail']) : 0;
        $count['infos']['oldmobile'] = isset($infos['oldmobile']) ? count($infos['oldmobile']) : 0;
        $count['infos']['boundgameid'] = isset($infos['boundgameid']) ? count($infos['boundgameid']) : 0;

        //单选分数
        $score['regmobile'] = isset($result['regmobile']) ? ($result['regmobile'] * $config['MARK_REGISTERED_MOBILE_PHONE']) : 0;
        $score['addr'] = isset($result['addr']) ? ($result['addr'] * $config['MARK_REGISTRATION_ADDRESS']) : 0;
        $score['regtime'] = isset($result['regtime']) ? ($result['regtime'] * $config['MARK_REGISTRATION_TIME']) : 0;
        $score['imei'] = isset($result['imei']) ? ($result['imei'] * $config['MARK_REGISTRATION_IEMI']) : 0;
        $score['reggameid'] = isset($result['reggameid']) ? ($result['reggameid'] * $config['MARK_SOURCE_OF_REGISTRATION']) : 0;
        $score['firstrechargegameid'] = isset($result['firstrechargegameid']) ? ($result['firstrechargegameid'] * $config['MARK_FIRST_CHARGE']) : 0;
        $score['totalrecharge'] = isset($result['totalrecharge']) ? ($result['totalrecharge'] * $config['MARK_ACCUMULATED_AMOUNT'] ) : 0;
        $score['regchannelid'] = isset($result['regchannelid']) ? ($result['regchannelid'] * $config['MARK_REGISTRATION_CHANNE']) : 0;

        //多选分数(对一个就满分)
        $score['oldpwd'] = empty($count['result']['oldpwd']) ? 0 : $config['MARK_HISTORY_PASSWORD'];
        $score['oldmail'] = empty($count['result']['oldmail']) ? 0 : ($config['MARK_HISTORY_MAILBOX']);
        $score['oldmobile'] = empty($count['result']['oldmobile']) ? 0 : ($config['MARK_HISTORY_MOBILE_PHONE']);
        $score['boundgameid'] = empty($count['result']['boundgameid']) ? 0 : ($config['MARK_BINDING_GAME']);

        // 充值记录（对几个，得几分）
        $num = 0;
        foreach ($result['recharge'] as $v){
            if ($v == 1) $num++;
        }
        $score['recharge']  = empty($count['result']['recharge']) ? 0 : ($config['MARK_RECHARGE_RECORD'] * $num);

        return $score;
    }


    //获取用户申诉数据
    public function getArr($data){
        $data['oldpwd'] = unserialize($data['oldpwd']);
        foreach ($data['oldpwd'] as $password){
            $pwd[] = auth_code($password,"ENCODE", Env::get('auth_key'));
        }
        $data['reggamename'] = $this->getGameName($data['reggameid']);
        $data['firstrechargegamename'] = $this->getGameName($data['firstrechargegameid']);
        $data['channelname'] = $this->getChannelName($data['regchannelid']);
        $data['oldpwd'] = $pwd;
        $data['oldmail'] = unserialize($data['oldmail']);
        $data['oldmobile'] = unserialize($data['oldmobile']);
        $data['boundgameid'] = unserialize($data['boundgameid']);
        $data['regtime'] = date("Y-m-d",$data['regtime']);
        $data['rechargetime'] = unserialize($data['rechargetime']);
        $data['rechargeamount'] = unserialize($data['rechargeamount']);
        return $data;
    }

    //获取用户系统保留数据
    public function getInfos($bond,$historyinfo,$payhistory){
        $boundgame = $pwd = $mail = $mobile = $pay = [];

        foreach ($bond as $bondinfo){
            $boundgame[] = $this->getGameName($bondinfo['game_id']);
        }
        foreach($historyinfo as $history){
            $pwd[] = $history['password'];
            $mail[] = $history['email'];
            $mobile[] = $history['mobile'];
        }

        foreach ($payhistory as $value) {
            $pay[] = $value['create_time'].'充值'.$value['amount'];
        }
        $infos = array();
        $infos['oldpwd'] = array_flip(array_flip(array_filter($pwd)));
        $infos['oldmail'] = array_flip(array_flip(array_filter($mail)));
        $infos['oldmobile'] = array_flip(array_flip(array_filter($mobile)));
        $infos['boundgameid'] = array_flip(array_flip(array_filter($boundgame)));
        $infos['payhistory'] = array_flip(array_flip(array_filter($pay)));

        return $infos;
    }

    //获取历史充值数据结果
    public function getRecharge($data){
        $recharge = [];

        if(is_array($data['rechargetime'])){
            foreach ($data['rechargetime'] as $key => $rechargeinfo){
                $recharge[$key] = array(
                    'rechargetime' => $rechargeinfo,
                    'rechargeamount' => isset($data['rechargeamount'][$key]) ? $data['rechargeamount'][$key] : '',
                    'result' => 0,
                );
            }
        }
        return $recharge;
    }
    
    
    /**
     * @param $id id
     * @param $data    数据
     * @return false|int
     */
    public function updateAppeal($data, $id = 0)
    {
        return $this->save($data, ['id'=>$id]);
    }

}