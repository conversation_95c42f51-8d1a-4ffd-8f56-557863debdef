<?php

namespace app\common\model;


class SystemMessage extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_system_message';
    protected $dateFormat = false;          //时间戳格式不自动转换

    public function getCreateTimeAttr($value)
    {
        if ($value > 0) {
            return date('Y-m-d H:i:s', $value);
        } else {
            return '';
        }

    }

    public function getUpdateTimeAttr($value)
    {
        if ($value > 0) {
            return date('Y-m-d H:i:s', $value);
        } else {
            return '';
        }
    }
}
