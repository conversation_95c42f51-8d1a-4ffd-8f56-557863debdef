<?php

namespace app\common\model;
use think\Model;

/**
 * 前台用户表
 * <AUTHOR>
 */
class Members extends Model
{
    protected $pk    = 'id';
    protected $table = 'cy_members';

    /**
     * 生成随机字母+id的昵称
     * @param $id
     */
    public function creatNickname($id,$length=3){
        $str = array_merge(range(0,9),range('a','z'),range('A','Z'));
        shuffle($str);
        $str = implode('',array_slice($str,0,$length));

        $nickname = $str.$id;
        $this->save(['nickname'=>$nickname],['id'=>$id]);

        return $nickname;
    }

    public function getUserName($data){
        return $this->where('username',$data['username'])->where('flag',0)->field('username')->find();
    }
    public function getUseridByName($data){
        return $this->where('username',$data['username'])->where('flag',0)->field('id,channel_id')->find();
    }
    public function getUseridById($data){
        return $this->where('id',$data['member_id'])->where('flag',0)->field('id,channel_id')->find();
    }
}