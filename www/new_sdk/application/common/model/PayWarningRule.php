<?php

namespace app\common\model;

use think\Cache;
use think\Db;

/**
 * 充值预警规则表
 * <AUTHOR>
 */
class PayWarningRule extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'nw_pay_warning_rule';
    protected $dateFormat = false;          //时间戳格式不自动转换
    
    const  CACHE_KEY = "clearable_pay_warning_rule";
    
    protected static function init()
    {
        PaySetting::afterInsert(function () {
            Cache::rm(self::CACHE_KEY);
        });
            
        PaySetting::afterUpdate(function () {
            Cache::rm(self::CACHE_KEY);
        });
                
        PaySetting::afterWrite(function () {
            Cache::rm(self::CACHE_KEY);
        });
    }
    /**
     * 获取配置信息
     * @return boolean|unknown
     */
    public function getPaySetting()
    {
        $list = Cache::get(self::CACHE_KEY);
        
        if (empty($list)) {
            $list = Db::table($this->table)->field('id,warning_time,order_count,min_amount,max_amount,is_six')->select();
            
            if (!empty($list)) {
                Cache::set(self::CACHE_KEY, $list, 3600 * 24 * 10);
            }
        }
        
        return $list;
    }
}