<?php
namespace app\common\model;

use think\Db;
use think\Config;
/**
 * 前台用户玩家子账号表
 *
 */
class Subscribelog extends \think\Model
{
    protected $pk   = 'id';
    protected $table= 'cy_subscribelog';
    protected $dateFormat = false;          //时间戳格式不自动转换


    /**
     * 获取通知模板
     * @param $type
     * @param $id   开服开测、活动的id
     * @return string
     */
    public function getContent($type, $id) {
        $content = '';
        if(0 == $type) {    //活动通知内容
            $info = Db::table('cy_activities')->where('id',$id)->find();
            $content = '【遨游网】 您关注的'. $info['title'] . '活动即将开始，详情登录遨游官网进行查看！官网地址：'.$info['url'];
        } elseif(1 == $type) {    //开服通知内容
            $info = Db::table('cy_serverinfo')->alias('a')
                ->join('cy_game b','b.id = a.gameid')
                ->field('a.sertime,a.sername,b.pinyin,b.nickname,a.gameid')
                ->where([
                    'a.id'=>$id,
                   ]
                )->find();

            $url = Config::get('WEILONG_WEBSITE.HOME').'/game'.$info['gameid'].'.html';

            $content = '【遨游网】 您关注的《'. $info['nickname'] . '》手游===%TIME%===开启新服“===%SERVERNAME%===”,立即下载：'.$url;
        } elseif (2 == $type) {    //开测通知内容

            $info = Db::table('cy_serverinfo')->alias('a')
                ->join('cy_game b','b.id = a.gameid')
                ->field('a.sertime,a.sername,a.serstatus,b.pinyin,b.nickname,a.gameid')
                ->where([
                            'a.id'=>$id,
                            ]
                )->find();
            $url = Config::get('WEILONG_WEBSITE.HOME').'/game'.$info['gameid'].'.html';

			if($info['serstatus']==1){
				$serStatus = '预告';
			}
			else if($info['serstatus']==2){
				$serStatus = '开服';
			}
			else if($info['serstatus']==3){
				$serStatus = '删档内测';
			}
			else if($info['serstatus']==4){
				$serStatus = '不删档内测';
			}
			else{
				$serStatus = '公测';
			}

            $content = '【遨游网】 您关注的《'.$info['nickname'].'》手游===%TIME%===开启===%SERVERSTATUS%===,立即下载：'.$url;
        }
        return $content;
    }
}