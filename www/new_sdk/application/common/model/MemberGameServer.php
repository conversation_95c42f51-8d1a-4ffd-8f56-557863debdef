<?php

namespace app\common\model;

/**
 * 玩家-游戏-区服三者的关联关系表
 * <AUTHOR>
 */
class MemberGameServer extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'nw_member_game_server';
    protected $dateFormat = false;          //时间戳格式不自动转换

    /**
     * 获取可自由支付的游戏
     */
    public function getRoleList($data)
    {
        return $this->where('member_id', $data['userid'])->where('game_id', $data['gameid'])->where('serverid', $data['serverid'])->field('roleid,rolename,roleid as id,rolename as text')->select();
    }
}