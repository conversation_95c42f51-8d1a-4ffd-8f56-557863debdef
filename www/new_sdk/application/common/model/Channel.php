<?php

namespace app\common\model;

use think\Cache;
use think\Db;

/**
 * 渠道表
 * <AUTHOR>
 */
class Channel extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'nw_channel';

    protected static function init()
    {
        Channel::afterInsert(function () {
            Cache::clear('channel_cache');
        });

        Channel::afterUpdate(function () {
            Cache::clear('channel_cache');
        });

        Channel::afterWrite(function () {
            Cache::clear('channel_cache');
        });
    }

    /**
     * 根据条件查询渠道信息 （缓存）
     * @param string $field
     * @param array $condition
     * @param string $order 排序
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getAllByCondition($field = 'id,name,parent_id,level', $condition = ['status' => 1], $order = 'id desc')
    {
        $prefix = 'clearable_channel_list_';
        $cacheKey = $prefix . md5(json_encode($condition) . $field . $order);
        $result = Cache::get($cacheKey);
        if (empty($result)) {
            $list = Db::table($this->table)->field($field)->where($condition)->order($order)->select();
            if (!empty($list)) {
                foreach ($list as $v) {
                    $result[$v['id']] = $v;
                }
                unset($list);
                Cache::tag('channel_cache')->set($cacheKey, $result, 3600 * 24);
            }
        }
        return $result;
    }


    /**
     * 获取渠道 id->name 列表
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getChannleList()
    {
        $prefix = 'clearable_channel_list_data';
        $cacheKey = $prefix . md5(json_encode($prefix));
        $result = Cache::get($cacheKey);
        if (empty($result)) {
            $list = $this->getAllByCondition();
            $result = [];
            foreach ($list as $v) {
                $result[$v['id']] = $v['name'];
            }
            unset($list);
            Cache::tag('channel_cache')->set($cacheKey, $result, 3600 * 24);
        }
        unset($list);
        return $result;
    }


    /**
     * 获取所有父类渠道
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getParentChannel()
    {

        $prefix = 'clearable_channel_list_parent';
        $cacheKey = $prefix . md5(json_encode($prefix));
        $result = Cache::get($cacheKey);
        if (empty($result)) {
            $list = $this->getAllByCondition();
            $result = [];
            foreach ($list as $v) {
                if ($v['parent_id'] == config('TOP_CHANNEL_ID') || $v['level'] == 1) {
                    $result[$v['id']] = $v;
                }
            }
            unset($list);
            Cache::tag('channel_cache')->set($cacheKey, $result, 3600 * 24);
        }
        unset($list);
        return $result;
    }


    /**
     * 获取当前渠道下的所有子渠道ID
     * @param pid：当前渠道ID
     *
     */
    public function getChildIds($pid)
    {

        $cacheKey = 'clearable_channel_childIds:' . $pid;
        $result = Cache::get($cacheKey);

        if (empty($result)) {
            $result = $this->getIds($pid);

            Cache::tag('channel_cache')->set($cacheKey, $result, 3600 * 24 * 30);
        }

        if (is_array($result)) {
            return array_unique($result);
        } else
            return $result;

    }


    /**
     * 获取渠道下所有父/子渠道ID
     *
     * @param pid：多个父渠道ID集
     * @param childids：找到的子渠道集
     *
     */
    public function getIds($pid, $childids = [])
    {

        if (!$pid || $pid <= 0) return false;

        $ids = $this->where(['parent_id' => ['in', $pid]])->column('id');

        //未找到,返回已经找到的
        if (empty($ids))
            return $childids;

        //添加到子渠道ID集合中
        $childids = array_merge($childids, $ids);

        //递归查找
        return $this->getIds($ids, $childids);
    }

    public function channelList($channelIdPath)
    {

        $cacheKey = 'clearable_channel_getChannelList:' . md5($channelIdPath);
        $result = Cache::get($cacheKey);

        if (empty($result)) {
            $result = $this->field('id,name as value')->where(['level' => 3, 'id_path' => ['LIKE', $channelIdPath . '%']])->select();

            Cache::tag('channel_cache')->set($cacheKey, $result, 3600 * 24 * 30);
        }

        if (is_array($result)) {
            return array_unique($result);
        } else
            return $result;

    }

    public function channelListById($id)
    {

        $cacheKey = 'clearable_channel_getChannelListById:' . md5($id);
        $result = Cache::get($cacheKey);

        if (empty($result)) {
            $result = $this->field('id,name as value')->where(['level' => 3, 'id' => $id])->select();

            Cache::tag('channel_cache')->set($cacheKey, $result, 3600 * 24 * 30);
        }

        if (is_array($result)) {
            return array_unique($result);
        } else
            return $result;

    }

    public function getChannelIds($channel_id, $level = 3)
    {

        if ($level == 1) { // 会长

            $channel = model('Channel')->cache('business:getChannelIds:level_B' . $channel_id, 60)->whereIn('id', $channel_id)->column('id');
            if ($channel) {
                return $channel;
            } else {
                return -2;
            }

        } else if ($level == 2) { // 子会长

            $where = sprintf(' id_path like "%%,%s', $channel_id) . '%" ';
            $channel = model('Channel')->cache('business:getChannelIds:level_B_' . $channel_id,60)->where('level', $level)->whereRaw($where)->column('id');
            if ($channel) {
                return $channel;
            } else {
                return -2;
            }
        } else {//推广员

            $where = sprintf(' id_path like "%%,%s', $channel_id) . '%" ';

            $channel = model('Channel')->cache('business:getChannelIds:level_C' . $channel_id,60)->where('level', $level)->whereRaw($where)->column('id');
            if ($channel) {
                return $channel;
            } else {
                return -2;
            }
        }


    }
}
