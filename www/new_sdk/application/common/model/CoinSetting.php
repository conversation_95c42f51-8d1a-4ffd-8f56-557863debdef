<?php

namespace app\common\model;

use think\Cache;

/**
 * 平台币发放规则设置表
 * <AUTHOR>
 */
class CoinSetting extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'nw_coin_setting';
    protected $dateFormat = false;          //时间戳格式不自动转换

    
    protected static function init()
    {
        self::afterInsert(function () {
            //删除缓存
            Cache::rm('clearable_coinsetting');
        });
            
        self::afterUpdate(function () {
             //删除缓存
             Cache::rm('clearable_coinsetting');
        });
                
        self::afterWrite(function () {
             //删除缓存
             Cache::rm('clearable_coinsetting');
        });
    }
    
    /**
     * 更新数据
     * 
     * @param $data: 需要更新的数据
     * @param $where:查询条件
     */
    public function updateData($data, $where)
    {
        $info = $this->where($where)->find();
        
        if (!$info) {
            return $this->save($data);              //insert
        } else {
            $this->save($data, $where);             //update
            return true;
        }
    }


}