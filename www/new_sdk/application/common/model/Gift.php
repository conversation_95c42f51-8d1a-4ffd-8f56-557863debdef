<?php
/**
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/1/23
 * Time: 16:22
 */

namespace app\common\model;

use think\Model;

//use traits\model\SoftDelete;

class Gift extends Model
{
    //use SoftDelete;

    protected $pk = 'id';
    protected $table = 'cy_libaoinfo';
    protected $updateTime = false;
    protected $autoWriteTimestamp = true;
    protected $dateFormat = true;

    //protected $deleteTime         = 'isdelete';


    public function giftCode()
    {
        return $this->hasMany('Giftcode', 'infoid');
    }

    /**
     * 获取列表内容
     * @param array $condition
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getList($condition = [], $order = 'cy_libaoinfo.id desc')
    {
        $gameid = $condition['gameid'];
        unset($condition['gameid']);
        if (isset($condition['title']) && !empty($condition['title'])) {
            $condition['cy_libaoinfo.title'] = ['like', "%{$condition['title']}%"];
            unset($condition['title']);
        }
        $tmpGameList = model('Common/Game')->getAllByCondition('id,name');
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }

        return self::view('cy_libaoinfo', 'id,title,gameid,starttime,endtime,consume,is_top,update_time,create_time as createtime,total,used')
            ->where($condition)
            ->where(function($query) use ($gameid){
                if (isset($gameid) && !empty($gameid)) {
                    $query->whereRaw(sprintf('FIND_IN_SET(%s,gameid)', $gameid));
                }

            })
            ->order($order)->paginate()->each(function ($item, $key) use ($gameList) {
                $game_ids = explode(',', $item['gameid']);
                $tmp = [];
                foreach ($game_ids as $k => $v) {
                    $tmp[] = $gameList[$v]['name'];
                }
                $item['name'] = implode('<br>', $tmp);
                return $item;
            });
    }

    /**
     * 新增礼包数据和礼包序列号数据
     * @mark 优化为批量插入并且增加事务
     * @param $data
     * @return bool
     * @throws \think\exception\PDOException
     */
    public function add($data)
    {
        if (empty($data)) {
            return false;
        }
        self::startTrans();
        try {
            $this->allowField(true)->save($data);
            $codes = [];
            foreach ($data['code'] as $v) {
                $codes[] = ['code' => $v];
            }
            $this->find($this->getLastInsID())->giftCode()->saveAll($codes);
            self::commit();
        } catch (\Exception $e) {
            self::rollback();
            $this->error = $e->getMessage();
            return false;
        }
        return true;
    }

    protected function getStarttimeAttr($value)
    {
        return date('Y-m-d', $value);
    }

    protected function getEndtimeAttr($value)
    {
        return date('Y-m-d', $value);
    }
}