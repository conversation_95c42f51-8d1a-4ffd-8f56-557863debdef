<?php
namespace app\common\model;

use think\Cache;

/**
 * 游戏版本表
 *
 * <AUTHOR>
 */
class SdkGameList extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_sdkgamelist';
    protected $dateFormat = false;          //时间戳格式不自动转换
    
    protected static function init()
    {
        self::afterInsert(function () {
            Cache::clear('sdk_game_list_info');
        });
        
        self::afterUpdate(function () {
            Cache::clear('sdk_game_list_info');
        });
        
        self::afterDelete(function () {
            Cache::clear('sdk_game_list_info');
        });
    }
    
    public function getCacheInfo ($where = [], $field = '*')
    {
        return $this->cache(true, 300, "sdk_game_list_info")->where($where)->field($field)->find();
    }
    
    // 获取游戏关联的分包数据
    public function getGameOrCacheInfo ($code, $game_id)
    {
        return $this->alias('sdk_g')
            ->where(['filename' => $code, 'gameid' => $game_id])
            ->join('nw_game_package_upload gpu', 'sdk_g.gameid=gpu.game_id', 'left')
            ->field('sdk_g.channel_id, sdk_g.upload_status, sdk_g.status, gpu.path')
            ->cache(true, 300, "sdk_game_list_or_game_info")
            ->find();
    }
}