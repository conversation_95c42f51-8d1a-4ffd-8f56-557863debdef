<?php

namespace app\common\model;

use think\Cache;
use think\Db;

/**
 * 渠道注册配置表
 * <AUTHOR>
 */
class ChannelConfig extends \think\Model
{
    protected $pk    = 'id';
    protected $table = 'nw_channel_config';

    protected static function init()
    {
        Channel::afterInsert(function () {
            Cache::clear('channelconfig_cache');
        });

        Channel::afterUpdate(function () {
            Cache::clear('channelconfig_cache');
        });

        Channel::afterWrite(function () {
            Cache::clear('channelconfig_cache');
        });
    }
}