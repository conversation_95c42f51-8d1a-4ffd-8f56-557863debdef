<?php
/**
 * 平台币使用记录信息
 */

namespace app\common\model;

use think\Env;
use think\Exception;
use think\Model;
use think\Db;
use think\Config;

class MemberCoinInfo extends Model
{
    protected $table = 'cy_member_coin_info';
    protected $dateFormat = false;


    /**
     * 待支付订单，交易取消释放平台币
     *
     */
    public function releaseCoin($userid, $orderid = '')
    {
        if($orderid){
            $cache = sprintf('MemberCoinInfo:releaseCoin:order:%s:%s',$userid,$orderid);
        }else{
            $cache = sprintf('MemberCoinInfo:releaseCoin:user:%s',$userid);
        }
        
        if(!requestDuplicateCheck($cache,10)){
            return true;
        }
        $payModel = new Pay();
        if ($orderid) {
            $payList = $payModel->field('gameid,real_ptb,real_coin,orderid,id,coupon_amount,coupon_member_id')->where(['orderid' => $orderid, 'userid' => $userid, 'status' => 0])->limit(1)->select();
        } else {
            //下单已经超过30分钟的待支付订单
            $payList = $payModel->field('gameid,real_ptb,real_coin,orderid,id,coupon_amount,coupon_member_id')->where(['create_time' => ['<=', NOW_TIMESTAMP - (60 * 30)], 'userid' => $userid, 'status' => 0])->whereRaw('(real_ptb>0 or real_coin>0 or coupon_amount>0)')->where(['create_time'=>['>', NOW_TIMESTAMP - 86400*7]])->select();
        }

        //订单信息不为空时
        if (!empty($payList)) {
            foreach ($payList as $key => $value) {
                $memberInfo = model('Members')->where(['id' => $userid])->find();
                $gameInfo = model('Game')->where(['id' => $value['gameid']])->find();

                if ($memberInfo && $gameInfo) {
                    // 启动事务
                    Db::startTrans();

                    try {
                        //包含平台币
                        if ($value['real_coin'] > 0) {
                            $members = model('members')->field('id,amount')->where(['id' => $userid])->find();
                            $coinData = array();
                            $coinData['amount'] = Db::raw("amount+" . $value['real_coin']);
                            $coinData['update_time'] = time();
                            $result = model('members')->where(['id' => $members['id']])->update($coinData);
                            if (!$result) {
                                throw new Exception("用户游戏平台币账户金额变动失败");
                            }
                            $amount = priceFormat($members['amount'] + $value['real_coin']);
                            $coinData = [
                                'userid' => $members['id'],
                                'orderid' => $value['orderid'] . "_T",
                                'start_amount' => $members['amount'],
                                'amount' => $value['real_coin'],
                                'result_amount' => $amount,
                                'type' => 3,
                                'remarks' => '平台币未付款退回',
                                'create_time' => NOW_TIMESTAMP
                            ];

                            if (!$this->insertGetId($coinData)) {
                                throw new Exception("订单消费释放状态更改失败,订单保存失败");
                            }
                        }
                        //专属币处理
                        if ($value['real_ptb'] > 0) {
                            $userCoinInfo = model('MemberZscoin')->field('id,userid,username,game_id,amount,status')->where(['userid' => $userid, 'game_id' => $value['gameid']])->find();
                            if (!empty($userCoinInfo)) {
                                $coinData = array();
                                $coinData['amount'] = Db::raw("amount+" . $value['real_ptb']);
                                $coinData['update_time'] = time();
                                $result = model('MemberZscoin')->where(['id' => $userCoinInfo['id'], 'userid' => $userCoinInfo['userid'], 'game_id' => $userCoinInfo['game_id']])->update($coinData);
                                if (!$result) {
                                    throw new Exception("用户游戏平台币账户金额变动失败");
                                }

                                $coinDetData = array();
                                $coinDetData['userid'] = $userid;
                                $coinDetData['username'] = $memberInfo['username'];
                                $coinDetData['game_id'] = $value['gameid'];
                                $coinDetData['type'] = 4;
                                $coinDetData['prev_amount'] = 0;
                                $coinDetData['change_amount'] = $value['real_ptb'];
                                $coinDetData['after_amount'] = $coinDetData['prev_amount'] + $value['real_ptb'];
                                $coinDetData['out_orderid'] = $value['orderid'];
                                $coinDetData['create_time'] = time();
                                $coinDetData['update_time'] = time();

                                $insertDetId = model('MemberZscoinDet')->insertGetId($coinDetData);
                                if (!$insertDetId) {
                                    throw new Exception("添加用户游戏平台币变动明细失败");
                                }

                            } else {
                                $coinData = array();
                                $coinData['userid'] = $userid;
                                $coinData['username'] = $memberInfo['username'];
                                $coinData['game_id'] = $value['gameid'];
                                $coinData['amount'] = $value['real_ptb'];
                                $coinData['status'] = 1;
                                $coinData['create_time'] = time();
                                $coinData['update_time'] = time();
                                $result = model('MemberZscoin')->insertGetId($coinData);
                                if (!$result) {
                                    throw new Exception("添加用户游戏平台币账户失败");
                                }

                                $coinDetData = array();
                                $coinDetData['userid'] = $userid;
                                $coinDetData['username'] = $memberInfo['username'];
                                $coinDetData['game_id'] = $value['gameid'];
                                $coinDetData['type'] = 4;
                                $coinDetData['prev_amount'] = 0;
                                $coinDetData['change_amount'] = $value['real_ptb'];
                                $coinDetData['after_amount'] = $coinDetData['prev_amount'] + $value['real_ptb'];
                                $coinDetData['out_orderid'] = $value['orderid'];
                                $coinDetData['create_time'] = time();
                                $coinDetData['update_time'] = time();

                                $insertDetId = model('MemberZscoinDet')->insertGetId($coinDetData);
                                if (!$insertDetId) {
                                    throw new Exception("添加用户游戏平台币变动明细失败");
                                }
                            }
                        }

                        //代金券处理
                        if ($value['coupon_member_id'] > 0 && $value['coupon_amount'] > 0) {
                            $coupon = model('common/CouponMember')->updateCouponIsUseById($value['coupon_member_id'], 1);
                            if (!$coupon) {
                                throw new Exception("订单消费释放状态更改失败");
                            }
                            log_message('支付代金券取消支付:订单号'.$value['id'].'代金券id'.$value['coupon_member_id'] , 'log', LOG_PATH . 'MemberCoinInfo/releaseCoin/');
                        }

                        //更新订单状态
                        $updPayResult = model('Pay')->where(['id' => $value['id'], 'userid' => $userid, 'status' => 0])->update(['status' => 2,'coupon_member_id'=>0]);
                        if (!$updPayResult) {
                            throw new Exception("订单消费释放状态更改失败");
                        }

                        // 提交事务
                        Db::commit();
                        if ($orderid) {
                            return true;
                        }
                    } catch (\Exception $e) {
                        // 回滚事务
                        Db::rollback();
                        $template = "订单 " . $value['orderid'] . " 平台币消费释放存在异常:" . $e->getMessage() . "。请及时核实，时间：" . date('Y-m-d H:i:s');
                        $ddurl = Env::get('dingtalk.warning_url');
                        $result = curlDD($template, $ddurl, true);
                        if ($orderid) {
                            return false;
                        }
                    }
                }
            }
        }

    }

    public function getCreateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }
}

