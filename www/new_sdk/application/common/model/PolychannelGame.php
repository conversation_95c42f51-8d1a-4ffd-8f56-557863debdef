<?php

namespace app\common\model;

use think\Cache;

/**
 * 聚合渠道游戏配置表
 * <AUTHOR>
 */
class <PERSON>ychannelGame extends \think\Model
{
    protected $pk    = 'id';
    protected $table = 'cy_polychannel_game';
    const CACHE_KEY_ID_INFO = "polychannel_gameid_info:";
    
    /**
     * 根据 游戏ID+渠道ID 获取详情
     * @param $game_id
     * @param $channel_id
     *
     * @return array|bool|\PDOStatement|string|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCacheInfoById($game_id, $channel_id){
        $key = self::CACHE_KEY_ID_INFO.$game_id.'_'.$channel_id;
        return $this->where(['game_id' => $game_id, 'channel_id' => $channel_id])->cache($key, 30)->field('id,param,param_client')->find();
    }
}