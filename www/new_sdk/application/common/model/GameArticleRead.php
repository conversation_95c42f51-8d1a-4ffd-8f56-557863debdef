<?php
/**
 * Created by PhpStorm.
 * User: cqingt
 * Date: 2019/3/1
 * Time: 10:32
 */
namespace app\common\model;

class GameArticleRead extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'nw_game_article_read';

    protected $autoWriteTimestamp = true;

    /** 删除记录
     * @param $article_id  游戏公告id
     * @return int
     */
    public function delRead($article_id)
    {
        return $this->destroy(['game_article_id' => $article_id]);
    }
}