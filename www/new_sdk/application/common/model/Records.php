<?php
/**
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/1/23
 * Time: 16:22
 */

namespace app\common\model;

use think\Model;
use traits\model\SoftDelete;

class Records extends Model
{

    protected $pk                 = 'id';
    protected $table              = 'act_records';



    /**
     * 获取列表内容
     * @param array $condition
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getList($condition = [])
    {
        $list = $this->alias('a')
            ->join('act_info info','info.id = a.info_id','left')
            ->join('cy_game g','g.id = a.game_id','left')
            ->where($condition)
            ->field('a.*,info.name as lottery_name,g.name as game_name')
            ->order('a.id desc')
            ->paginate();

        return  empty($list) ? [] : $list;
    }



}