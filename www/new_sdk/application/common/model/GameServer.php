<?php

namespace app\common\model;

/**
 * 游戏,区服关系表
 * <AUTHOR>
 */
class GameServer extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'nw_game_server';
    protected $dateFormat = false;          //时间戳格式不自动转换

    /**
     * 获取可自由支付的游戏
     */
    public function getServerList($data)
    {
        return $this->where('game_id', $data['gameid'])->field('serverid,servername,serverid as id,servername as text')->select();
    }
}