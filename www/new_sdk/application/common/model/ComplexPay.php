<?php

namespace app\common\model;

/**
 * 聚合玩家消费订单表
 * <AUTHOR>
 */
class ComplexPay extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'nw_complex_pay';
    protected $dateFormat = false;          //时间戳格式不自动转换


    public function getCreateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }
    // public function getAmountAttr($value,$data)
    // {
    //     // if($value > 100){
    //     //     return formatFenToYuan($value);
    //     // }
    //     return $value;
    // }
}