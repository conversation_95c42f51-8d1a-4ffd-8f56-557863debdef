<?php
/**
 * 敏感词 表
 * 
 */
namespace app\common\model;

class SensitiveWord extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_sensitive_word';

    // 判断敏感词是否存在
    public function existsWord($word,$id = 0)
    {
        
        if ( $id) {
            $condition['id'] = ['<>', $id];
        }

        $condition['word'] = $word;

        $count = $this->where($condition)->count();

        return $count ? true : false;
    }
}