<?php

namespace app\common\model;
use think\Model;

/**
 * 用户冻结状态历史记录表
 * <AUTHOR>
 */
class FrozenHistory extends Model
{
    protected $pk    = 'id';
    protected $table = 'cy_frozen_history';

    /**
     * 插入数据
     * @param int $userid  用户ID
     * @param int $status 操作状态： 0 解冻 1冻结
     * @param int $type  操作类型 0: 申请冻结 1：管理员操作
     */
    public function addData($userid,$status,$type = 1)
    {
    	$data = [
    		'admin_id' => $type ? session('ADMIN_ID') : 0,
    		'userid'   => $userid,
    		'status'   => $status == 1 ? 1:0 ,
    		'create_time' => time()
    	];

    	return $this->insert($data);
    }

}