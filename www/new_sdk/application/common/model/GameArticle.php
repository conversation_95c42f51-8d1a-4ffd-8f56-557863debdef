<?php
/**
 * Created by PhpStorm.
 * User: cqingt
 * Date: 2018/6/28
 * Time: 11:28
 */
namespace app\common\model;

class GameArticle extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'nw_game_article';

    protected $autoWriteTimestamp = true;

    /**
     * 获取一条显示状态的，显示时间小于当前时间的最近一条文章
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getRecommend()
    {
        return $this->where(['is_show' => 1, 'show_time' => ['LT', request()->time()] ])
            ->field(['title', 'content'])
            ->order('show_time', 'desc')
            ->find();
    }
}