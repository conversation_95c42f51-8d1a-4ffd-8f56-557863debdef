<?php
/**
 * 后台 agent 表
 * 
 */
namespace app\common\model;

class Agent extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_agent';
    protected $agent = null;

    // 渠道号 自动修改
    /*protected function _makeAgentNo()
    {
        return date('YmdHis').rand(10, 99);
    }*/

    // 插入数据时自动，添加字段默认值
    protected $insert = array(
        'agentname' => 'NOT_USED' ,
        'username' => 'NOT_USED' ,
        'owner' => 'NOT_USED' ,
    );
}