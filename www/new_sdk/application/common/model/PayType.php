<?php

namespace app\common\model;

/**
 * 支付方式表
 * <AUTHOR>
 */
class PayType extends \think\Model
{
    protected $pk    = 'id';
    protected $table = 'cy_paytype';
    protected $resultSetType = 'collection';

    public function getAllByCondition($condition = [])
    {
        $cacheKey = md5($this->table .json_encode( $condition));
        $list     = cache($cacheKey);
        if (empty($list)) {
            $list = $this->where($condition)->order($this->pk . ' desc')->cache($cacheKey, 3600)->select();
        }
        return $list;
    }
}