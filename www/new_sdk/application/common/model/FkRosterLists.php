<?php
namespace app\common\model;
use think\Cache;

/**
 * 名单表
 */
class FkRosterLists extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'fk_roster_lists';
    protected $dateFormat = false;          //时间戳格式不自动转换

    const  CACHE_KEY = "fk_roster_lists:"; // 缓存前缀
    const  CACHE_TAG = "fk_roster_lists"; // 缓存标签

    protected static function init()
    {
        self::afterUpdate(function () {
            Cache::clear(self::CACHE_TAG);
        });

        self::afterDelete(function () {
            Cache::clear(self::CACHE_TAG);
        });
    }

    public static function getCacheInfo($where = [])
    {
        if (empty($where)) {
            return "";
        }

        return self::where($where)->cache(true, 36000, self::CACHE_TAG)->find();
    }


    public static function updateCacheInfo($where = [], $data = [])
    {
        if (empty($where)) {
            return "";
        }

        return self::where($where)->cache(true, 36000, self::CACHE_TAG)->update($data);
    }


    public static function delCacheInfo($where = [])
    {
        if (empty($where)) {
            return "";
        }

        return self::where($where)->cache(true, 36000, self::CACHE_TAG)->delete();
    }

}