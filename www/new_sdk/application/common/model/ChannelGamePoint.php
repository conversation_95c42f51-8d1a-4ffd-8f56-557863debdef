<?php

namespace app\common\model;

/**
 * 渠道游戏点位表
 * <AUTHOR>
 */
class ChannelGamePoint extends \think\Model
{
    protected $pk                 = 'id';
    protected $table              = 'cy_channel_game_point';
    protected $autoWriteTimestamp = true;
    protected $createTime         = 'create_time';
    protected $updateTime         = 'update_time';

    public function getPoint($game_id, $channel_id)
    {
        return $this->where(['game_id'=>$game_id, 'channel_id'=>$channel_id])->value('point');
    }

    public function updateData($data, $where)
    {

        $info = $this->where($where)->find();
        if (!$info) {
            $data                   = array_merge($data, $where);
            $data['create_time'] = NOW_TIMESTAMP;
            return $this->allowField(true)->insert($data, $where);
        } else {
            return $this->update($data, $where);
        }
    }

    public function getAllPoint($game_id, $channel_id)
    {
        return $this->where(['game_id'=>$game_id, 'channel_id'=>$channel_id])->field('point,first_point')->find();
    }

}