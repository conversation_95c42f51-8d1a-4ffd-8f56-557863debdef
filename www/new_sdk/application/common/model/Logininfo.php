<?php
namespace app\common\model;

use think\Cache;

/**
 * 前台用户登录日志表
 *
 */
class Logininfo extends \think\Model
{
    protected $pk               = 'id';
    protected $table            = 'cy_logininfo';
    protected $limitInsertExpire= 20;               //玩家登录日志插入的限制时间
    
    /**
     * 判断一个渠道是否活跃渠道
     * @param int $channel_id
     * @param int $gameid
     * @param int $day_limit
     * @return boolean
     */
    public function isActiveChannel($channel_id, $gameid, $day_limit){
        $map = [];
        $map['channel_id'] = $channel_id;
        $map['gameid']     = $gameid;
        $map['login_time'] = array('gt',NOW_TIMESTAMP - abs($day_limit)*86400);
		/*
        $login_time = $this
            ->where($map)
            ->order('id DESC')
            ->value('login_time');

        if(!empty($login_time)) {
            if((NOW_TIMESTAMP-$login_time) < (abs($day_limit)*86400) ) {
                return true;
            }
        }
		*/
		$LoginInfo = $this->where($map)->find();
	//	var_dump($LoginInfo);
		if(!empty($LoginInfo)){
			return true;
		}
        return false;
    }
    
    /**
     * 限制玩家登录日志插入处理
     * 
     * @param $data: array 插入的数据
     */
    public function limitInsert($data)
    {
        $userid = $data['userid'];
        $gameid = $data['gameid'];
        $sub_id = $data['sub_id'];

        // 限制插入登录日志的redis
        $cache_key = 'logininfo_insert_limit:'.$userid.':'.$gameid.':'.$sub_id;
        
        $redis = Cache::store('default');
        //限制插入登录日志的redis不存在时
        if(!$redis->has($cache_key)) {
            $this->insert($data);
            $redis->set($cache_key, '', $this->limitInsertExpire);
        }
    }
   
}