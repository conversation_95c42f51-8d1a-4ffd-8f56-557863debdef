<?php

namespace app\common\model;


class CouponMember extends \think\Model
{
    protected $pk = 'id';
    protected $table = 'cy_coupon_member';
    protected $dateFormat = false;          //时间戳格式不自动转换
    protected $resultSetType = 'collection';

    public function getUseTimeAttr($value)
    {
        if ($value > 0) {
            return date('Y-m-d H:i:s', $value);
        } else {
            return '';
        }
    }

    public function getUpdateTimeAttr($value)
    {
        if ($value > 0) {
            return date('Y-m-d H:i:s', $value);
        } else {
            return '';
        }
    }

    public function getStartTimeAttr($value)
    {
        if ($value > 0) {
            return date('Y-m-d H:i:s', $value);
        } else {
            return '';
        }

    }

    public function getEndTimeAttr($value)
    {
        if ($value > 0) {
            return date('Y-m-d H:i:s', $value);
        } else {
            return '';
        }
    }

    public function getPayCoupon($userid, $gameid, $money, $coupon_id = 0)
    {
        $where = [];

        if ($coupon_id > 0) {
            $where['a.id'] = $coupon_id;
        }

        //查询参数
        $where['a.member_id'] = $userid;
        $where['a.examine'] = 2;
        $where['a.is_use'] = 1;
        $time = strtotime(date("Y-m-d"));
        $couponMemberModel = model('CouponMember');
        $list = $couponMemberModel
            ->alias('a')
            ->join('cy_coupon c', 'a.coupon_id=c.id')
            ->where($where)
            ->where(function ($query) use ($gameid, $time) {
                $query->where([
                    'c.state' => 1,
                    'a.state' => 1,
                ])->whereRaw(sprintf('(c.type_id =1 and c.end_time>%d) or (c.type_id=2 and a.end_time>%d)', $time, $time))
                ->whereRaw(sprintf('(c.game_id="" or FIND_IN_SET(%s,c.game_id))', $gameid));
            })
            ->field('a.*,c.name,c.money,c.min_money,c.start_time,c.end_time,a.start_time as coupon_start_time,a.end_time as coupon_end_time,c.type_id,c.game_id,c.state as coupon_state')
            ->order('a.id desc')
            ->select()->toArray();

        if ($list) {
            $tmpGameList = model('Common/Game')->getAllByCondition('id,name,nickname');
            $gameList = array();
            foreach ($tmpGameList as $game) {
                $gameList[$game['id']] = $game;
            }
            $trueArr = $falseArr = [];
            foreach ($list as $k => $v) {

                if ($v['type_id'] == 2) {
                    $v['start_time'] = date('Y-m-d H:i:s',$v['coupon_start_time']);
//                    $v['end_time'] = date('Y-m-d H:i:s',$v['coupon_end_time']+86399);
                    $v['end_time'] = date('Y-m-d H:i:s',$v['coupon_end_time']);

                }else{
//                    $v['end_time'] = date('Y-m-d H:i:s',strtotime($v['end_time'])+86399);
                    $v['end_time'] = date('Y-m-d H:i:s',strtotime($v['end_time']));

                }
                if ($v['game_id']) {
                    $game_ids = explode(',', $v['game_id']);
                    $tmp = [];
                    foreach ($game_ids as $kk => $vv) {
                        $tmp[] = $gameList[$vv]['nickname'];
                    }
                    $v['game_name'] = implode(' ', array_unique($tmp));
                } else {
                    $v['game_name'] = '全部游戏';
                }

                //金额判断是否可用
                if ($v['min_money'] <= $money) {
                    $v['is_pay'] = 1;
                    $trueArr[] = $v;
                } else {
                    $v['is_pay'] = 0;
                    $falseArr[] = $v;
                }
            }

            if ($trueArr) {
                $sort = array_column($trueArr, 'money');
                array_multisort($sort, SORT_DESC, $trueArr);
            }
            if ($falseArr) {
                $sort = array_column($falseArr, 'money');
                array_multisort($sort, SORT_DESC, $falseArr);
            }
            $list = array_merge($trueArr, $falseArr);
        }

        return $list;
    }

    public function updateCouponIsUseById($id,$is_use){
        //3已使用
        if($is_use == 3){
            if($this->where('id',$id)->where('is_use',2)->update(['is_use'=>3,'update_time'=>time()])){
                return true;
            }else{
                return false;
            }
        }else{
            if($this->where('id',$id)->where('is_use',2)->update(['is_use'=>1,'update_time'=>time(),'use_time'=>0])){
                return true;
            }else{
                return false;
            }
        }
    }
}
