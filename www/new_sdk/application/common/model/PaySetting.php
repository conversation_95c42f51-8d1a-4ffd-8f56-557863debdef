<?php

namespace app\common\model;

use think\Cache;
use think\Db;

/**
 * 充值预警配置表
 * <AUTHOR>
 */
class PaySetting extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'nw_pay_setting';
    protected $dateFormat = false;          //时间戳格式不自动转换
    
    const  CACHE_KEY = "clearable_pay_setting";
    
    protected static function init()
    {
        PaySetting::afterInsert(function () {
            Cache::rm(self::CACHE_KEY);
        });
            
        PaySetting::afterUpdate(function () {
            Cache::rm(self::CACHE_KEY);
        });
                
        PaySetting::afterWrite(function () {
            Cache::rm(self::CACHE_KEY);
        });
    }
    /**
     * 获取配置信息
     * @return boolean|unknown
     */
    public function getPaySetting()
    {
        $info = Cache::get(self::CACHE_KEY);
        
        if (empty($info)) {
            $info = Db::table($this->table)->field('status,emails,whitelist_channel')->find();
            
            if (!empty($info)) {
                Cache::set(self::CACHE_KEY, $info, 3600 * 24 * 10);
            }
        }
        
        return $info;
    }
}