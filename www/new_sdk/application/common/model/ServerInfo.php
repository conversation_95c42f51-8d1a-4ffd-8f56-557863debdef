<?php

namespace app\common\model;

/**
 * 开服开测表
 * <AUTHOR>
 */
class ServerInfo extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_serverinfo';
    protected $dateFormat = false;          //时间戳格式不自动转换

    public function game()
    {
        return $this->belongsTo('Game', 'gameid', 'id');
    }

    public function getServerList($where,$order='',$whereor='')
    {
        if (empty($order)){
            $order = 'sertime desc';
        }

        if (empty($whereor)){
            return ServerInfo::with(['game'=>function($query){
                $query->field('id,name');
            }])->where($where)->order($order)->paginate(20);
        }else{
            return ServerInfo::with(['game'=>function($query){
                $query->field('id,name');
            }])->where($where)->whereOr($whereor)->order($order)->paginate(20);
        }

    }

}