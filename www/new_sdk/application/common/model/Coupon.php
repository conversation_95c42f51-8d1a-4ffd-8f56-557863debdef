<?php

namespace app\common\model;


class Coupon extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_coupon';
    protected $dateFormat = false;          //时间戳格式不自动转换
    protected $resultSetType = 'collection';
    public function getCreateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }

    public function getUpdateTimeAttr($value)
    {
        return date('Y-m-d H:i:s', $value);
    }
    public function getStartTimeAttr($value)
    {
        if($value>0){
            return date('Y-m-d H:i:s', $value);
        }else{
            return '';
        }

    }

    public function getEndTimeAttr($value)
    {
        if($value>0){
            return date('Y-m-d 23:59:59', $value);
        }else{
            return '';
        }
    }
}
