<?php

namespace app\common\model;

use think\Cache;
use think\db\Query;

/**
 * 系统配置表
 * <AUTHOR>
 */
class GameBindDouyinOpen extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_game_bind_douyin_open';
    protected $dateFormat = false;          //时间戳格式不自动转换

    const  CACHE_KEY = "game_bind_douyin_open:";
    const  CACHE_TAG = "game_bind_douyin_open";

    protected static function init()
    {
        GameBindDouyinOpen::beforeUpdate(function ($model) {
            Cache::clear(self::CACHE_TAG);
        });

        GameBindDouyinOpen::beforeDelete(function () {
            Cache::clear(self::CACHE_TAG);
        });
    }

    /**
     * 根据 游戏ID 获取配置信息
     * @param $game_id
     *
     * @return array|false|mixed
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoByGameId($game_id)
    {
        if (empty($game_id)) {
            return [];
        }

        $cache_key = self::CACHE_KEY.$game_id;
        $info = Cache::get($cache_key);
        if (empty($info)) {
            $info = self::where(['game_id' => $game_id])->find()->toArray();
            if (empty($info)) {
                return false;
            }
            // $info['time'] = time();
            Cache::tag(self::CACHE_TAG)->set($cache_key, $info, 3600);
        }
        return $info;
    }

    /**
     * 根据 app_id 获取配置信息
     * @param $app_id
     *
     * @return array|false|mixed
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getInfoByAppId($app_id)
    {
        if (empty($app_id)) {
            return [];
        }

        $cache_key = self::CACHE_KEY.$app_id;
        $info = Cache::get($cache_key);
        if (empty($info)) {
            $info = self::where(['app_id' => $app_id])->find()->toArray();
            if (empty($info)) {
                return false;
            }
            Cache::tag(self::CACHE_TAG)->set($cache_key, $info, 3600);
        }
        return $info;
    }

}