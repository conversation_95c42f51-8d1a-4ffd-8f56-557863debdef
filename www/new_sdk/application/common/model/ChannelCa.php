<?php

namespace app\common\model;

/**
 * 渠道秘钥表
 * <AUTHOR>
 */
class ChannelCa extends \think\Model
{
    protected $pk         = 'id';
    protected $table      = 'cy_channel_ca';
    protected $dateFormat = false;          //时间戳格式不自动转换

    /**
     * 是否有效的私钥
     * @param $private_key string
     * @return bool
     */
    protected function _isValidPrivatekey($private_key){
        if(empty($private_key)) {
            return false;
        }

        $resource_id = openssl_pkey_get_private($private_key);
        if(empty($resource_id)) {
            return false;
        }
        return true;
    }

    /**
     * 是否有效的公钥
     * @param $public_key string
     * @return bool
     */
    protected function _isValidPublickey($public_key){
        if(empty($public_key)) {
            return false;
        }

        $resource_id = openssl_pkey_get_public($public_key);
        if(empty($resource_id)) {
            return false;
        }
        return true;
    }

    /**
     * 是否有效的密钥对（判断是否能否互相加密解密）
     * @param $private_key string
     * @param $public_key string
     * @return bool
     */
    public function isValidEncryptionKey($private_key, $public_key){
        if(!$this->_isValidPublickey($public_key)) {
            return false;
        }
        if(!$this->_isValidPrivatekey($private_key)) {
            return false;
        }
        $data = NOW_TIMESTAMP;

        // 使用公钥加密
        openssl_public_encrypt($data, $encrypted, openssl_pkey_get_public($public_key));
        $encrypted = base64_encode($encrypted);

        // 使用秘钥看是否能够解密，能的话，说明就是配对的
        openssl_private_decrypt(base64_decode($encrypted), $decrypted, openssl_pkey_get_private($private_key));

        if(NOW_TIMESTAMP != $decrypted) {
            return false;
        }
        return true;
    }
}