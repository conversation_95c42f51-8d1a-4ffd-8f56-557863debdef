<?php
/**
 * 公共控制器类
 *
 */
namespace app\common\controller;

use think\Controller;
use think\exception\ValidateException;
use think\Request;

class Base extends Controller
{

    /**
     * 初始化数据
     */
    protected function _initialize(){}
    
    /**
     * 重构父类的validate方法
     * @access protected
     * @param array        $data     数据
     * @param string|array $validate 验证器名或者验证规则数组
     * @param array        $message  提示信息
     * @param bool         $batch    是否批量验证
     * @param mixed        $callback 回调方法（闭包）
     * @return array|string|true
     * @throws ValidateException
     */
    public function validate($data, $validate, $message = [], $batch = false, $callback = null)
    {
      if (is_array($validate)) {
        $v = new \app\common\library\ValidateExtend;    //调用Validate的扩展类
        $v->rule($validate);
      } else {
          // 支持场景
          if (strpos($validate, '.')) {
              list($validate, $scene) = explode('.', $validate);
          }
          
          $v = \think\Loader::validate($validate);
          
          !empty($scene) && $v->scene($scene);
      }
      // 是否批量验证
      if ($batch || $this->batchValidate) {
        $v->batch(true);
      }
    
      if (is_array($message)) {
        $v->message($message);
      }
    
      if ($callback && is_callable($callback)) {
        call_user_func_array($callback, [$v, &$data]);
      }
       
      if (!$v->check($data)) {
        if ($this->failException) {
          throw new ValidateException($v->getError());
        } else {
          return $v->getError();
        }
      } else {
        return true;
      }
    }
}