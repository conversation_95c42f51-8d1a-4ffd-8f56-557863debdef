<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\Env;
use think\Route;

//正式生产环境
if (APP_STATUS == 'stable') {
    $domain = [
        'sdkapi' => 'api',
        't' => 'api',
        'sdkcpsapi' => 'guildapi', // 后续可以把这个删除了，统一用 cpsapi
        'cpsapi' => 'guildapi',
        'mcpsapi' => 'mcpsapi',
        'jhgame' => 'complex',
        'www' => 'home',
        'admin' => 'admin',
        'm' => 'mobile',
        '*' => 'home',
    ];
}
//测试环境
elseif (APP_STATUS == 'dev') {
    $domain = [
        'devsdkapi' => 'api',
        'devt' => 'api',
        'devsdkcpsapi' => 'guildapi',
        'devcpsapi' => 'guildapi',
        'devmcpsapi' => 'mcpsapi',
        'devjhgame' => 'complex',
        'devadmin' => 'admin',
        'devm' => 'mobile',
        'devwww' => 'home',
        '*' => 'home'
    ];
}
//开发环境
else {
    $domain = [
        'sdkapi' => 'api',
        't' => 'api',
        'devsdkcpsapi' => 'guildapi',
        'cpsapi' => 'guildapi',
        'mcpsapi' => 'mcpsapi',
        'jhgame' => 'complex',
        'admin' => 'admin',
        'www' => 'home',
        'm' => 'mobile',
        '*' => 'home'
    ];
}

define('PC_WEBSITE_DOMAIN', 'www.' . QM_DOMAIN_URL);
define('MOBILE_WEBSITE_DOMAIN', 'm.' . QM_DOMAIN_URL);


// --------- 推广 ---------
// 推广页
if (pathinfo($_SERVER['REQUEST_URI'])['dirname'] == '/tgy') {
    $short_link = pathinfo($_SERVER['REQUEST_URI'])['filename'];
    $arrAlias = db('nw_promotion_short_link')->where(['short_link' => $short_link])->field('short_link,channel_id')->find();
    if ($arrAlias) {
        $routeSetting['[tgy/:cid]'] = [
            '[:0]' => ['api/game/index?cid=' . base64_encode(auth_code($arrAlias['channel_id'], "ENCODE", Env::get('auth_key')))],
        ];
        return $routeSetting;
    }
}


// 推广短链
// TODO: 修复祈盟域名的问题 `|| (strpos($_SERVER['HTTP_HOST'], 't.qmgames.cn') !== false || strpos($_SERVER['SERVER_NAME'], 't.qmgames.cn') !== false)`
if ((strpos($_SERVER['HTTP_HOST'], T_DOMAIN_URL) !== false || strpos($_SERVER['SERVER_NAME'], T_DOMAIN_URL) !== false) || (strpos($_SERVER['HTTP_HOST'], 't.7dgame.cn') !== false || strpos($_SERVER['SERVER_NAME'], 't.7dgame.cn') !== false)) {
    $short_link = pathinfo($_SERVER['REQUEST_URI'])['filename'];
    if (strpos($short_link, '?')) {
        $short_link = substr($short_link, 0, strpos($short_link, '?'));
    }

    if ($short_link != 'index') {
        $shortLinkInfo = db('nw_promotion_short_link')->where(['short_link' => $short_link])->column('short_link,id');

        if (!empty($shortLinkInfo)) {
            $arrAlias = [];
            if (is_numeric($short_link)) {
                Route::get('/:mark', 'api/game_promotion/index');
            } else {
                foreach ($shortLinkInfo as $key => $value) {
                    $arrAlias[$key] = 'api/game_promotion/index';
                }
            }
            $routeSetting['__alias__'] = $arrAlias;
            return $routeSetting;
        } else {
            Route::rule('/:mark', function (\think\Request $request) {
                return "当前短链不存在！";
            });
        }
    }
}
// ------------------



// TODO: 前后端路由冲突配置，解决方案。后续可优化。
if ($_SERVER['HTTP_HOST'] == PC_WEBSITE_DOMAIN || $_SERVER['HTTP_HOST'] == PC_WEBSITE_DOMAIN . ':80') {
    $route_login = ['Home/member/login', []];
    $route_register = ['Home/member/phoneReg', []];

    $route_kefu_cjwt = ['Home/csc/index', []];
    $route_kefu_zhss = ['Home/csc/appeal', []];
    $route_kefu_sscx = ['Home/csc/appealResult', []];
    $route_kefu_wxss = ['Home/csc/wechatAppeal', []];
    $route_gift_index = ['Home/gift/index', []];
    // $route_gift_news = ['Home/gift/news', []];
} else {
    $route_login = ['Home/member/login', ['domain' => PC_WEBSITE_DOMAIN]];
    $route_register = ['Home/member/phoneReg', ['domain' => PC_WEBSITE_DOMAIN]];

    $route_kefu_cjwt = ['Home/csc/index', ['domain' => PC_WEBSITE_DOMAIN]];
    $route_kefu_zhss = ['Home/csc/appeal', ['domain' => PC_WEBSITE_DOMAIN]];
    $route_kefu_sscx = ['Home/csc/appealResult', ['domain' => PC_WEBSITE_DOMAIN]];
    $route_kefu_wxss = ['Home/csc/wechatAppeal', ['domain' => PC_WEBSITE_DOMAIN]];
    $route_gift_index = ['Home/gift/index', ['domain' => PC_WEBSITE_DOMAIN]];
    // $route_gift_news = ['Home/gift/news', ['domain'=>PC_WEBSITE_DOMAIN]];
}


//PC站路由配置
Route::get('game<id>/<type>', 'Home/Game/detail', ['domain' => PC_WEBSITE_DOMAIN], ['id' => '\d+', 'type' => 'index|info|news|gift|kfkc']);
Route::get('game<id>', 'Home/Game/info', ['domain' => PC_WEBSITE_DOMAIN], ['id' => '\d+']);
Route::get('forgotpwd/result<res>', 'Home/member/forgetPwdResult', ['domain' => PC_WEBSITE_DOMAIN], ['res' => '\d+']);
Route::get('user/zhaq/bind/selc/<type>', 'Home/member/toBindOne', ['domain' => PC_WEBSITE_DOMAIN], ['type' => 'mobile|email']);
Route::get('user/zhaq/bind/validate/<type>', 'Home/member/toVelidatePage', ['domain' => PC_WEBSITE_DOMAIN], ['type' => 'mobile|email']);
Route::get('user/zhaq/bindpage', 'Home/member/bindPage', ['domain' => PC_WEBSITE_DOMAIN], []);
// 微信小程序获取openid
// Route::get('three_platform/getWxAuthCode', 'Api/ThreePlatform/getWxAuthCode', [], []);

// \think\Route::get('upload_notify/index' , 'Admin/upload_notify/index' , [] , []);

// \think\Route::alias('t_rally','api/game/index'); // TODO: 推广集合页短链
Route::post('mp/get_wxmp_code', 'Api/ThreePlatform/getWxAuthCode');
Route::post('mp/get_order_info', 'Api/ThreePlatform/getOrderInfo');
Route::post('mp/get_pay', 'Api/ThreePlatform/getPay');
Route::get('mp/get_article_list', 'Api/ThreePlatform/getArticleList');
Route::get('mp/get_article_info', 'Api/ThreePlatform/getArticleInfo');

Route::get('mlbb/:code', 'Api/Index/mlbbDownload');
Route::get('get_mlbb_code', 'Api/Index/getMlbbCode');

// 聚合API
Route::post('/gh/get_role', 'Api/JhApi/getRole');

Route::get('/game/detail', 'Api/Game/detail'); // 推广页 - 游戏详情页

//路由配置
$routeSetting = [
    '__pattern__' => [
        'name' => '\w+',
    ],
    '[hello]' => [
        ':id' => ['index/hello', ['method' => 'get', 'domain' => PC_WEBSITE_DOMAIN], ['id' => '\d+']],
        ':name' => ['index/hello', ['method' => 'post', 'domain' => PC_WEBSITE_DOMAIN]],
    ],
    //PC站路由配置
    'game/:type/:subject' => ['Home/Game/index', ['domain' => PC_WEBSITE_DOMAIN]],
    '[news]' => [
        ':id' => ['Home/News/detail', ['method' => 'get', 'domain' => PC_WEBSITE_DOMAIN], ['id' => '\d+']],
    ],
    '[news/index]' => [
        '[:0]' => ['Home/news/index?type=1', ['domain' => PC_WEBSITE_DOMAIN]],
    ],
    '[news/xyzx]' => [
        '[:0]'   => ['Home/news/index?type=1',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[news/lyzx]'     => [
        '[:0]'   => ['Home/news/index?type=2',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[news/xypc]'     => [
        '[:0]'   => ['Home/news/index?type=3',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[news/xygl]'     => [
        '[:0]'   => ['Home/news/index?type=4',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[news/jcsp]'     => [
        '[:0]'   => ['Home/news/index?type=5',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[gift/index]'     => [
        '[:0]'   => ['Home/gift/index',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[gift/new]'     => [
        '[:0]'   => ['Home/gift/news',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[gift/hot]'     => [
        '[:0]'   => ['Home/gift/index',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[gift]'     => [
        ':id'   => ['Home/gift/detail', ['method' => 'get','domain'=>PC_WEBSITE_DOMAIN],['id' => '\d+']],
    ],
    '[fastgame]'     => [
        '[:0]'   => ['Home/gameaid/index',['domain'=>PC_WEBSITE_DOMAIN]],
    ],
    '[login]'     => [
        '[:0]'   => $route_login,
    ],
    'search/keyword/:keyword'=>['Home/search/index',['domain'=>PC_WEBSITE_DOMAIN]],
    '[register]'     => [
        '[:0]'   => $route_register,
    ],
    'forgotpwd'              => ['Home/member/contactAccount' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'forgotpwd/check'        => ['Home/member/contactWay' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'forgotpwd/check/email'  => ['Home/member/contact?type=email' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'forgotpwd/check/mobile' => ['Home/member/contact?type=phone' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'forgotpwd/resetpwd'     => ['Home/member/forgetPwdToReset' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'syxy'  => ['Home/index/agreement' , ['domain' => PC_WEBSITE_DOMAIN]],
    'kf'  => ['Home/service/index' , ['domain' => PC_WEBSITE_DOMAIN]],
    'kc'  => ['Home/service/kc' , ['domain' => PC_WEBSITE_DOMAIN]],

    'user/userinfo'          => ['Home/member/personalInfo' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'user/userinfo/edit'     => ['Home/member/personalInfoEdit' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'user/rechargeinfo'      => ['Home/member/personalPay' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'user/giftbox'           => ['Home/member/memberGift' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'user/zhaq'              => ['Home/member/personalAccount' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'user/zhaq/changepwd'    => ['Home/member/personalChangePwd' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'user/zhaq/realname'     => ['Home/member/personalIdentityPage' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    'user/mygame'            => ['Home/member/mygame' , ['domain' => PC_WEBSITE_DOMAIN]] ,

    '[kefu/cjwt]'     => [
        '[:type]' => $route_kefu_cjwt
    ] ,
    'kefu/zhss'     => $route_kefu_zhss,
    'kefu/sscx'     => $route_kefu_sscx,
    'kefu/wxss'     => $route_kefu_wxss,
    'gift/index'     =>  $route_gift_index,
    // 'gift/news'     =>  $route_gift_news,

    '[download]'     => [
        ':id'   => ['Home/game/download', ['method' => 'get','domain'=>PC_WEBSITE_DOMAIN],['id' => '\d+']],
    ],
    '[download/fastgame]'     => [
        ':id'   => ['Home/gameaid/download', ['method' => 'get','domain'=>PC_WEBSITE_DOMAIN],['id' => '\d+']],
    ],
    'feedback'            => ['Home/member/feedback' , ['domain' => PC_WEBSITE_DOMAIN]] ,
    //域名路由配置
    '__domain__'=> $domain,
];


// 官网pc站和m站路由冲突解决
$isDebug = false; // 默认为开启调试模式
if (isset($_SERVER['HTTP_HOST'])) {
    $url = 'http://' . $_SERVER['HTTP_HOST'];                              // 获取访问URL
    $debugArr = [MOBILE_SITE_DOMAIN, 'http://' . MOBILE_WEBSITE_DOMAIN];   // 关闭调试模式的模块名，如果做了伪静态则要根据URL来区别对待了
    $i = count($debugArr);
    while ($i) {
        $i--;
        if (strpos($url, $debugArr[$i]) !== false) {
            $isDebug = true;
            break;
        }
    }
}
if ($isDebug) {
    //M站路由配置
    Route::get('game<id>/<type>', 'Mobile/Game/detail', ['domain' => MOBILE_WEBSITE_DOMAIN], ['id' => '\d+', 'type' => 'index|info|news|gift']);
    Route::get('game<id>', 'Mobile/Game/info', ['domain' => MOBILE_WEBSITE_DOMAIN], ['id' => '\d+']);
    Route::post('game<id>/<type>', 'Mobile/Game/detail', ['domain' => MOBILE_WEBSITE_DOMAIN], ['id' => '\d+', 'type' => 'index|info|news|gift']);
    Route::post('game<id>', 'Mobile/Game/info', ['domain' => MOBILE_WEBSITE_DOMAIN], ['id' => '\d+']);
    Route::any('game/type=<type>', 'Mobile/Game/index', ['domain' => MOBILE_WEBSITE_DOMAIN], ['type' => '\d+']);
    Route::any('game/subject=<subject>', 'Mobile/Game/index', ['domain' => MOBILE_WEBSITE_DOMAIN], ['subject' => '\d+']);
    Route::get('user/zhaq/bind/selc/<type>', 'Mobile/member/toBindOne', ['domain' => MOBILE_WEBSITE_DOMAIN], ['type' => 'mobile|email']);
    Route::get('user/zhaq/bind/validate/<type>', 'Mobile/member/toVelidatePage', ['domain' => MOBILE_WEBSITE_DOMAIN], ['type' => 'mobile|email']);
    Route::get('user/zhaq/bindpage', 'Mobile/member/bindPage', ['domain' => MOBILE_WEBSITE_DOMAIN], []);
    Route::get('user/rechargeinfo/detail/<id>', 'Mobile/member/personalPayDetail', ['domain' => MOBILE_WEBSITE_DOMAIN], ['id' => '\d+']);
    //M站路由配置
    $routeMobile = [
        '[tj/rmyx]' => [
            '[:0]' => ['Mobile/Game/gameList?type=hot', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[tj/zxyx]' => [
            '[:0]' => ['Mobile/Game/gameList?type=new', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[tj/bztj]' => [
            '[:0]' => ['Mobile/Game/gameList?type=recommend', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[news]' => [
            ':id' => ['Mobile/News/detail', ['method' => 'get', 'domain' => MOBILE_WEBSITE_DOMAIN], ['id' => '\d+']],
        ],
        '[news/index]' => [
            '[:0]' => ['Mobile/news/index?type=1', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[news/xyzx]' => [
            '[:0]' => ['Mobile/news/index?type=1', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[news/lyzx]' => [
            '[:0]' => ['Mobile/news/index?type=2', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[news/xypc]' => [
            '[:0]' => ['Mobile/news/index?type=3', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[news/xygl]' => [
            '[:0]' => ['Mobile/news/index?type=4', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[news/jcsp]'     => [
            '[:0]'   => ['Mobile/news/index?type=5',['domain'=>MOBILE_WEBSITE_DOMAIN]],
        ],
        'kf'  => ['Mobile/service/index' , ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'kc'  => ['Mobile/service/kc' , ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'search/keyword/:keyword'=>['Mobile/search/search',['domain'=>MOBILE_WEBSITE_DOMAIN]],
        'syxy'  => ['Mobile/index/agreement' , ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'kefu'  => ['Mobile/csc/index' , ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'kefu/zhss'     => ['Mobile/csc/appeal' , ['domain' => MOBILE_WEBSITE_DOMAIN]] ,
        'kefu/sscx'     => ['Mobile/csc/appealResult' , ['domain' => MOBILE_WEBSITE_DOMAIN]] ,
        'kefu/wxss'     => ['Mobile/csc/wechatAppeal' , ['domain' => MOBILE_WEBSITE_DOMAIN]] ,
        'kefu/zhss/check' => ['Mobile/csc/check', ['domain' => MOBILE_WEBSITE_DOMAIN]] ,
        'kefu/zhss/user' => ['Mobile/csc/appealAccount', ['domain' => MOBILE_WEBSITE_DOMAIN]] ,
        'kefu/zhss/history' => ['Mobile/csc/appealHistory', ['domain' => MOBILE_WEBSITE_DOMAIN]] ,
        'kefu/zhss/result' => ['Mobile/csc/appealEnding', ['domain' => MOBILE_WEBSITE_DOMAIN]] ,
        '[gift/index]'     => [
            '[:0]'   => ['Mobile/gift/index',['domain'=>MOBILE_WEBSITE_DOMAIN]],
        ],
        '[gift/new]'     => [
            '[:0]'   => ['Mobile/gift/news',['domain'=>MOBILE_WEBSITE_DOMAIN]],
        ],
        '[gift/hot]'     => [
            '[:0]'   => ['Mobile/gift/index',['domain'=>MOBILE_WEBSITE_DOMAIN]],
        ],
        '[gift]'     => [
            ':id'   => ['Mobile/gift/detail', ['method' => 'get','domain'=>MOBILE_WEBSITE_DOMAIN],['id' => '\d+']],
        ],
        '[fastgame]'     => [
            '[:0]' => ['Mobile/gameaid/index', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[login]' => [
            '[:0]' => ['Mobile/member/login', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        '[register]' => [
            '[:0]' => ['Mobile/member/userReg', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        ],
        'forgotpwd' => ['Mobile/member/contactAccount', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'forgotpwd/check' => ['Mobile/member/contactWay', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'forgotpwd/check/email' => ['Mobile/member/contact?type=email', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'forgotpwd/check/mobile' => ['Mobile/member/contact?type=phone', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'forgotpwd/resetpwd' => ['Mobile/member/forgetPwdToReset', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user' => ['Mobile/member/personalCenter', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/userinfo' => ['Mobile/member/personalInfo', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/userinfo/qq' => ['Mobile/member/editqq', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/userinfo/code' => ['Mobile/member/editzipcode', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/userinfo/address' => ['Mobile/member/editaddress', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        '[user/rechargeinfo]' => [
            '[:status]' => ['Mobile/member/personalPay', ['domain' => MOBILE_WEBSITE_DOMAIN]]
        ],
        'user/giftbox' => ['Mobile/member/giftbox', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/giftbox/gq' => ['Mobile/member/giftboxgp', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        '[kefu/cjwt]' => [
            '[:type]' => ['Mobile/csc/question', ['domain' => MOBILE_WEBSITE_DOMAIN]]
        ],
        'kefu/sscx/result' => ['Mobile/csc/appealResultPage', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'kefu/sscx/resetpwd' => ['Mobile/csc/resetPwdPage', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/zhaq' => ['Mobile/member/personalAccount', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/zhaq/changepwd' => ['Mobile/member/personalChangePwd', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/zhaq/realname' => ['Mobile/member/personalIdentityPage', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'gzgzh' => ['Mobile/csc/subscription', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/userinfo/nickname' => ['Mobile/member/editnickname', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'feedback' => ['Mobile/member/feedback', ['domain' => MOBILE_WEBSITE_DOMAIN]],
        'user/mygame' => ['Mobile/member/mygame', ['domain' => MOBILE_WEBSITE_DOMAIN]],
    ];

    $routeSetting = array_merge($routeSetting, $routeMobile);
}

// ## 以下注释，后续项目推广页没有发现问题，可以删除
// //短链接域名时
// if(isset($_SERVER['HTTP_HOST']) && !isset($_GET['cid']) && ('http://'.$_SERVER['HTTP_HOST']==API_DOMAIN || 'https://'.$_SERVER['HTTP_HOST']==API_DOMAIN || 'http://'.$_SERVER['HTTP_HOST']==API_DOMAIN.':80' || 'https://'.$_SERVER['HTTP_HOST']==API_DOMAIN.':80' || 'http://'.$_SERVER['HTTP_HOST'] == T_DOMAIN  || 'http://'.$_SERVER['HTTP_HOST']==T_DOMAIN.':80' || 'http://'.$_SERVER['SERVER_NAME']==T_DOMAIN || 'https://'.$_SERVER['SERVER_NAME']==T_DOMAIN || 'https://'.$_SERVER['SERVER_NAME']==T_DOMAIN.':80' || 'https://'.$_SERVER['SERVER_NAME']==T_DOMAIN.':67')){
//
//     $short_link = pathinfo($_SERVER['REQUEST_URI'])['filename'];
//     if(strpos($short_link,'?')){
//         $short_link = substr($short_link,0,strpos($short_link,'?'));
//     }
//
//     $arrAlias = db('nw_promotion_short_link')->where(['short_link'=>$short_link])->column('short_link,id');
//     if(!empty($arrAlias)){
//         foreach ($arrAlias as $key => $value){
//             $arrAlias[$key] = 'api/game_promotion/index';
//         }
//     }
//     else{
//         $arrAlias = [];
//     }
//
//     $routeSetting['__alias__'] = $arrAlias;
//
// }


// // 推广短链
// if (isset($_SERVER['HTTP_HOST']) || (strpos($_SERVER['HTTP_HOST'], T_DOMAIN_URL) !== false || strpos($_SERVER['SERVER_NAME'], T_DOMAIN_URL) !== false)) {
//     $short_link = pathinfo($_SERVER['REQUEST_URI'])['filename'];
//     if (strpos($short_link, '?')) {
//         $short_link = substr($short_link, 0, strpos($short_link, '?'));
//     }
//
//     if ($short_link != 'index') {
//         $arrAlias = db('nw_promotion_short_link')->where(['short_link' => $short_link])->column('short_link,id');
//         if (!empty($arrAlias)) {
//             foreach ($arrAlias as $key => $value) {
//                 $arrAlias[(string)$key] = 'api/game_promotion/index';
//             }
//         } else {
//             $arrAlias = [];
//         }
//
//         $routeSetting['__alias__'] = $arrAlias;
//         // dump($routeSetting);
//     }
// }
//
// // 推广页
// if (pathinfo($_SERVER['REQUEST_URI'])['dirname'] == '/tgy') {
//     $short_link = pathinfo($_SERVER['REQUEST_URI'])['filename'];
//     $arrAlias = db('nw_promotion_short_link')->where(['short_link' => $short_link])->field('short_link,channel_id')->find();
//     // dump($arrAlias);
//     if ($arrAlias) {
//         $routeSetting['[tgy/:cid]'] = [
//             '[:0]' => ['api/game/index?cid=' . base64_encode(auth_code($arrAlias['channel_id'], "ENCODE", Env::get('auth_key')))],
//         ];
//     }
// }


return $routeSetting;
