<?php

/**
 * 地衣子渠道对接
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class Diyi extends Complex  {
    
    protected $channel_mark = 'diyi';

    /**
     * 初始化操作
     */
    public function _initialize() {
        log_message('=== '.$this->channel_mark.' --请求参数记录==='.json_encode($_POST),'log',LOG_PATH . '../complexPaylog/');
    }
    
    /**
     * 支付处理
     */
    public function pay()
    {
        if (empty($_POST)) {
            
            die('请求内容为空');
        }
        
		$attach     = $_POST['game_order'];     
        $orderid    = $_POST['out_trade_no'];     //下级渠道的订单号
		$pay_extra  = $_POST['pay_extra'];     
        $user_id    = $_POST['user_id'];
        $pay_status = $_POST['pay_status'];
        $amount     = $_POST['price'];
        $sign       = $_POST['sign'];

        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('订单不存在或订单金额不一致');
        }
        
        $polyChannelGameModel = new PolychannelGame;
        
        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');
        
        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('用于验签的参数还未设置');
        }
        
		$gameid = $this->payInfo['gameid'];
        $param = unserialize($param);
            
        $appkey = $param[$gameid];
        
        $str = $attach.$orderid.$pay_extra.$pay_status.$amount.$user_id;
        $str.= $appkey;
        $thisSign = md5($str);
            
        if ($sign != $thisSign) {
            log_message('签名校验失败,参数:'.$str.' 生成的签名thisSign='.$thisSign.' 对方的签名:'.$sign,'error',LOG_PATH . '../complexPaylog/');
            die('签名校验失败 ' . $thisSign);
        }
        
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,time());
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('订单生成失败'.$e->getMessage());
        }
        
   
        
        // 【产品确认：测试阶段暂时不用通知CP】通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('success');
    }
    /**
     * 二次登录验证
     */
    public function checklogin(){
		/*
		$_POST['user_id'] = '100073';
		$_POST['token'] = '123213213';
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        
        $user_id	= trim($_POST['user_id']);
        $token		= trim($_POST['token']);
        
		$checkUrl = 'https://www.eggyx.cn/sdk/login_notify/login_verify';

        $response = get_http_response($checkUrl, http_build_query(['user_id'=>$user_id,'token'=>$token]));

		die($response);
    }
}
