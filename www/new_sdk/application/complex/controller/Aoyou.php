<?php

/**
 * 支付接口(自己聚合自己的麻花网络渠道)控制器
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class <PERSON>oy<PERSON> extends Complex  {
    
    protected $channel_mark = 'aoyou';
    
    /**
     * 支付处理
     */
    public function pay()
    {
        if (empty($_POST)) {
            
            die('请求内容为空');
        }
        
        $orderid    = $_POST['out_trade_no'];     //下级渠道的订单号
        $user_id    = $_POST['user_id'];
        $signType   = $_POST['signType'];
        $attach		= $_POST['extend'];
        $pay_status = $_POST['pay_status'];
        $amount     = $_POST['price'];
        $sign       = $_POST['sign'];
        
        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('订单不存在或订单金额不一致');
        }
        
        $polyChannelGameModel = new PolychannelGame;
        
        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');
        
        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('用于验签的参数还未设置');
        }
        
		$gameid = $this->payInfo['gameid'];
        $param = unserialize($param);
            
        $appkey = $param[$gameid];
        
        $str = $orderid.$amount.$pay_status.$attach;
        $str.= $appkey;
        $thisSign = md5($str);
            
        if ($sign != $thisSign) {
            log_message('签名校验失败,参数:'.$str.' 生成的签名thisSign='.$thisSign.' 对方的签名:'.$sign,'error',LOG_PATH . '../complexPaylog/');
            die('签名校验失败 ' . $thisSign);
        }
        
		$paytime = time();
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,$paytime);
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('订单生成失败'.$e->getMessage());
        }
        
   
        
        // 【产品确认：测试阶段暂时不用通知CP】通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('success');
    }
    
}
