<?php

/**
 * 自由网络子渠道对接
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class Ziyou extends Complex  {
    
    protected $channel_mark = 'ziyou';
    
    /**
     * 支付处理
     */
    public function pay()
    {
		/*
        $_POST['orderID']		= 'abc2321321312321321';     //下级渠道的订单号
        $_POST['deviceType']	= '1';
        $_POST['channelType']	= '8888';
        $_POST['uid']          = '100001';
        $_POST['amount']       = $amount = 1;
        $_POST['time']			= '12345633632';
        $_POST['gameRoleID']   = 'roleid';
		$_POST['gameServerID'] = 'serverid';
		$_POST['extrasParams'] = 'extdf234324324';
        $_POST['cpOrderID']	= $attach = 'CMXK16082725923145729855';      //这个才是自己聚合层(本级)的订单编号
        $_POST['sign']			= $sign	  = '211111111';
		*/

        if (empty($_POST)) {
            
            die('请求内容为空');
        }
        $params		= array();
        $params['orderID']		= $orderid = $_POST['orderID'];     //下级渠道的订单号
        $params['deviceType']	= $_POST['deviceType'];
        $params['channelType']	= $_POST['channelType'];
        $params['uid']          = trim($_POST['uid']);
        $params['amount']       = $amount  = $_POST['amount'];
        $params['time']			= $paytime = $_POST['time'];
        $params['gameRoleID']   = $_POST['gameRoleID'];
		$params['gameServerID'] = $_POST['gameServerID'];
		$params['extrasParams'] = $_POST['extrasParams'];
        $params['cpOrderID']	= $attach = $_POST['cpOrderID'];      //这个才是自己聚合层(本级)的订单编号
        $params['sign']			= $sign	  = $_POST['sign'];
        
        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('FAIL:订单不存在或订单金额不一致');
        }
        
        $polyChannelGameModel = new PolychannelGame;
        
        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');
        
        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('FAIL:用于验签的参数还未设置');
        }

        $gameid = $this->payInfo['gameid'];
        $param = unserialize($param);
        $appkey = $param[$gameid];
        
        $thisSign = $this->sign($params,$appkey);
            
        if ($sign != $thisSign) {
            log_message('签名校验失败,参数:'.$thisSign.' 生成的签名thisSign='.$thisSign.' 对方的签名:'.$sign,'error',LOG_PATH . '../complexPaylog/');
            die('FAIL:签名校验失败 ' . $thisSign);
        }
        
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,$paytime);
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('FAIL:订单生成失败'.$e->getMessage());
        }
        
   
        
        // 【产品确认：测试阶段暂时不用通知CP】通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('SUCCESS');
    }
    
	/**
     * 二次登录验证
     */
    public function checklogin()
    {
		/*
		$_POST['uid'] = '100073';
		$_POST['token'] = 'fdsfdsfdsfds122211';
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        
		$params					= array();
        $params['uid']			= trim($_POST['uid']);
        $params['token']		= trim($_POST['token']);        
		$checkUrl = 'http://qzf.zugame.com/user/verify';

        $response = get_http_response($checkUrl, http_build_query($params));

		die($response);
    }

	private function sign($param, $key){
		$param = array_filter($param, function ($v, $k) {
			if ($k !== 'sign_type' && $k !== 'sign' && $v !== '') {
				return true;
			}
		}, ARRAY_FILTER_USE_BOTH);
		ksort($param);
		reset($param);
		$signStr = '';
		foreach ($param as $k => $v) {
			if(!empty($v)){
				$signStr .= $k . '=' . $v . '&';
			}
		}
		if (get_magic_quotes_gpc()) {
			$signStr = stripslashes($signStr);
		}
	//	echo $signStr . $key;
		return md5($signStr . $key);
	}

}
