<?php

/**
 * 星落谷控制器
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class <PERSON>zhi extends Complex  {
    
    protected $channel_mark = 'xiaozhi';
    
    /**
     * 支付处理
     */
    public function pay()
    {
        if (empty($_POST)) {
            
            die('请求内容为空');
        }
        
        $orderid    = urldecode($_POST['orderid']);     //下级渠道的订单号
        $username   = urldecode($_POST['username']);
        $gameid     = urldecode($_POST['gameid']);
        $roleid     = urldecode($_POST['roleid']);
        $serverid   = urldecode($_POST['serverid']);
        $paytype    = urldecode($_POST['paytype']);
        $amount     = urldecode($_POST['amount']);
        $paytime    = urldecode($_POST['paytime']);
        $attach     = urldecode($_POST['attach']);      //这个才是自己聚合层(本级)的订单编号
        $sign       = urldecode($_POST['sign']);
        
        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('订单不存在或订单金额不一致');
        }
        
        $polyChannelGameModel = new PolychannelGame;
        
        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');
        
        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('用于验签的参数还未设置');
        }
        
        $param = unserialize($param);
            
        $appkey = $param[$gameid];
        
        $str = "orderid=".urlencode($orderid)."&username=".urlencode($username)."&gameid=".urlencode($gameid)."&roleid=".urlencode($roleid)."&serverid=".urlencode($serverid)."&paytype=".urlencode($paytype)."&amount=".$amount."&paytime=".$paytime."&attach=".urlencode($attach);
        $str.= "&appkey=".urlencode($appkey);
        $thisSign = md5($str);
            
        if ($sign != $thisSign) {
            log_message('签名校验失败,参数:'.$str.' 生成的签名thisSign='.$thisSign.' 对方的签名:'.$sign,'error',LOG_PATH . '../complexPaylog/');
            die('签名校验失败 ' . $thisSign);
        }
        
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,$paytime);
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('订单生成失败'.$e->getMessage());
        }
        
   
        
        // 【产品确认：测试阶段暂时不用通知CP】通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('success');
    }
    

    /**
     * 二次登录验证
     */
    public function checklogin()
    {
		/*
		$_POST['appid'] = '100073';
		$_POST['username'] = '122211';
		$_POST['gameid'] = '123213213';
		$_POST['logintime'] = time();
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        
        $appid		= trim($_POST['appid']);
        $username	= trim($_POST['username']);
        $gameid		= trim($_POST['gameid']);
        $logintime	= trim($_POST['logintime']);

		$appkeyArrs = array("618"=>"7b1312b936c44020941cd84aec9ccb55","2092"=>"7b1312b936c44020941cd84aec9ccb55");
		if(!isset($appkeyArrs{$appid})){
			die('APP秘钥未配置');
		}
		else{
			$appkey = $appkeyArrs{$appid};
		}
		
		$str = "username=".$username."&appkey=".$appkey."&logintime=".$logintime;

        $sign = md5($str);
        
		$checkUrl = 'http://sdk.ijzd.cn/sdkapi/login/verification';

        $response = get_http_response($checkUrl, http_build_query(['appid'=>$appid,'username'=>$username,'gameid'=>$gameid,'logintime'=>$logintime,'sign'=>$sign]));
		die($response);
	//	var_dump($response);
	//	$response = json_decode($response,true);
	//	var_dump($response);
		
		/*
		if ($response  == 'success'){
            die('success');
        }
		else{
			die($response);
        }
		die('failed');
		*/
    }

}
