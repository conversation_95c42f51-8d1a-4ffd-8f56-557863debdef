<?php

/**
 * 支付接口(自己聚合自己的星空互娱渠道)控制器
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class Shunyouios extends Complex  {
    
    protected $channel_mark = 'shunyou';
    
    /**
     * 支付处理
     */
    public function pay()
    {
        if (empty($_POST)) {
            
            die('请求内容为空');
        }
        
        $orderid    = trim($_POST['orderId']);     //下级渠道的订单号
        $uid		= trim($_POST['uid']);
        $amount     = trim($_POST['amount']);
        $serverId   = trim($_POST['serverId']);
        $attach     = trim($_POST['extraInfo']);
        $test		= trim($_POST['test']);
        $paytime    = trim($_POST['orderTime']);
        $billno     = trim($_POST['billno']);      //这个才是自己聚合层(本级)的订单编号
        $sign       = trim($_POST['sign']);
        
        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('订单不存在或订单金额不一致');
        }
        
        $polyChannelGameModel = new PolychannelGame;
        
        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');
        
        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('用于验签的参数还未设置');
        }
        
        $param = unserialize($param);
            
        $appkey = $param[$this->payInfo['gameid']];
        
        $str = $orderid.$uid.$serverId.$amount.$attach.$paytime.$billno.$test;
        $str.= $appkey;
        $thisSign = strtolower(md5($str));
            
        if ($sign != $thisSign) {
            log_message('签名校验失败,参数:'.$str.' 生成的签名thisSign='.$thisSign.' 对方的签名:'.$sign,'error',LOG_PATH . '../complexPaylog/');
            die('签名校验失败 ' . $thisSign);
        }
        
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,$paytime);
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('订单生成失败'.$e->getMessage());
        }
        
   
        
        // 【产品确认：测试阶段暂时不用通知CP】通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('success');
    }
    

    /**
     * 二次登录验证
     */
    public function checklogin()
    {
		/*
		$_POST['appid'] = '100073';
		$_POST['uid'] = '122211';
		$_POST['token'] = '123213213';
		$_POST['time'] = time();
		$_POST['sessid'] = '32423432432';
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        
        $appid		= trim($_POST['appid']);
        $uid		= trim($_POST['uid']);
        $token		= trim($_POST['token']);
        $time		= trim($_POST['time']);
        $sessid		= trim($_POST['sessid']);

		$appkeyArrs = array("100071"=>"4b95e2540f940b1df90463ca060dd53c","100073"=>"10bd288640193feb5c8c470dd215e09f");
		if(!isset($appkeyArrs{$appid})){
			die('APP秘钥未配置');
		}
		else{
			$appkey = $appkeyArrs{$appid};
		}
		
        $sign  = strtolower(md5($appid.$uid.$token.$sessid.$time.$appkey));
        
		$checkUrl = 'http://apisdk.pdl188.com/Api/Member/CheckLogin';

        $response = get_http_response($checkUrl, http_build_query(['appid'=>$appid,'uid'=>$uid,'token'=>$token,'time'=>$time,'sessid'=>$sessid,'sign'=>$sign]));
	//	var_dump($response);
	//	$response = json_decode($response,true);
	//	var_dump($response);
 
		if ($response  == 'success'){
            die('success');
        }
		else{
			die($response);
        }
		die('failed');
    }

}
