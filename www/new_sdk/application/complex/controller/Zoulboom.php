<?php

/**
 * 曜众子渠道对接
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class Zoulboom extends Complex  {
    
    protected $channel_mark = 'zoulboom';
    
    /**
     * 初始化操作
     */
    public function _initialize() {
        log_message('=== '.$this->channel_mark.' --请求参数记录==='.json_encode($_REQUEST),'log',LOG_PATH . '../complexPaylog/');
    }

    /**
     * 支付处理
     */
    public function pay()
    {
        if (empty($_REQUEST)) {
            
            die('请求内容为空');
        }
        $params = array();
		$params['cporderid'] = $attach   = trim($_REQUEST['cporderid']);
        $params['orderid']   = $orderid  =  trim($_REQUEST['orderid']);     //下级渠道的订单号
		$params['appid']	 = $appid	 =  trim($_REQUEST['appid']);     
        $params['uid']		 = trim($_REQUEST['uid']);
		$params['time']		 = $paytime  = trim($_REQUEST['time']);
		$params['extinfo']	 = trim($_REQUEST['extinfo']);   
        $params['amount']	 = $amount	 = trim($_REQUEST['amount']);
        $params['serverid']  = trim($_REQUEST['serverid']);
        $params['charid']    = trim($_REQUEST['charid']);
         
        $sign       = trim($_REQUEST['sign']);
        
        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('订单不存在或订单金额不一致');
        }
        
        $polyChannelGameModel = new PolychannelGame;
        
        $param = $polyChannelGameModel->where(['game_id'=>$this->payInfo['gameid'],'channel_id'=>$this->payInfo['channel_id']])->value('param');
        
        if(empty($param)){
            log_message('后台渠道游戏中，用于该游戏的验签参数还未设置,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('用于验签的参数还未设置');
        }
        
        $param = unserialize($param);
            
        $appkey = $param[$appid];
        
		ksort($params); //对关联数组按照键名进行升序排序
		$signstr = http_build_query($params); 
        $thisSign = md5($signstr.$appkey); 
            
        if ($sign != $thisSign) {
            log_message('签名校验失败,参数:'.$str.' 生成的签名thisSign='.$thisSign.' 对方的签名:'.$sign,'error',LOG_PATH . '../complexPaylog/');
            die('签名校验失败 ' . $thisSign);
        }
        
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,$paytime);
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('订单生成失败'.$e->getMessage());
        }
        
   
        
        // 【产品确认：测试阶段暂时不用通知CP】通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('success');
    }
    

    /**
     * 二次登录验证(安卓)
     */
    public function checkloginadr()
    {
		/*
		$_POST['appid'] = '100073';
		$_POST['uid'] = '122211';
		$_POST['token'] = '123213213';
		$_POST['time'] = time();
		$_POST['sessid'] = '32423432432';
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        
        $appid		= trim($_POST['appid']);
        $sessionid	= trim($_POST['sessionid']);
        $time		= trim($_POST['time']);
		$sdkversion = '1.1';

		$appkeyArrs = array("jTaZHB25JGysZyYJ"=>"aBCNxrR7rhTTQCXb6AyjJMHd5yx4fDNa");
		if(!isset($appkeyArrs{$appid})){
			die('APP秘钥未配置');
		}
		else{
			$appkey = $appkeyArrs{$appid};
		}
		
        $sign  = md5($sessionid.$appid.$time.$sdkversion.$appkey);
        
		$checkUrl = 'http://api.zoulboom.com/?ct=login&ac=user_login';

        $response = get_http_response($checkUrl, http_build_query(['appid'=>$appid,'sdkversion'=>$sdkversion,'sessionid'=>$sessionid,'time'=>$time,'sign'=>$sign]));
		die($response);
    }

    /**
     * 二次登录验证(iOS)
     */
    public function checkloginios()
    {
		/*
		$_POST['token'] = '123213213';
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        $token		= trim($_POST['token']);
       
		$checkUrl = 'http://api.zoulboom.com/?ct=sdk&ac=user_login';

        $response = get_http_response($checkUrl, http_build_query(['token'=>$token]));
		die($response);
    }
}
