<?php

/**
 * 聚思得子渠道(走quick)对接
 * 
 */

namespace app\complex\controller;

use app\common\model\PolychannelGame;
use app\common\logic\PayCallback;
use think\Db;

class Juside extends Complex  {
    
    protected $channel_mark = 'juside';
    
    /**
     * 初始化操作
     */
    public function _initialize() {
        log_message('=== '.$this->channel_mark.' --请求参数记录==='.json_encode($_POST),'log',LOG_PATH . '../complexPaylog/');
    }

    /**
     * 支付处理
     */
    public function pay()
    {
		/*	
		$_POST['nt_data'] = '@116@111@175@158@160@80@168@157@168@166@157@159@164@116@90@101@99@105@87@82@154@163@155@160@156@154@166@154@112@82@134@134@126@93@111@83@84@163@166@153@164@151@149@156@165@165@157@113@87@167@164@84@116@115@116@162@173@154@155@158@166@148@156@145@165@149@170@164@149@151@151@118@114@160@153@163@169@152@159@153@115@117@158@165@148@169@157@164@172@111@105@111@98@153@164@145@172@149@170@165@114@108@149@160@151@161@162@149@162@117@104@112@100@156@157@147@163@163@157@157@118@109@155@155@148@158@159@151@164@143@172@154@152@110@149@158@106@100@150@150@156@106@156@108@101@154@153@105@150@105@109@102@154@104@157@150@99@145@106@107@153@149@111@149@105@99@114@151@94@92@112@95@153@159@153@162@163@158@161@145@170@158@156@111@116@148@160@148@161@158@150@158@151@159@169@149@153@162@112@116@101@150@156@145@164@165@157@160@148@168@167@150@154@167@118@109@159@146@165@152@146@159@163@150@157@162@117@146@168@164@147@155@158@100@106@96@111@110@108@105@105@112@105@102@109@107@116@96@159@146@165@152@146@159@163@150@157@162@117@109@163@162@150@157@168@146@162@159@116@103@104@100@103@105@103@99@101@102@104@101@105@102@107@100@100@101@106@104@108@101@103@98@109@101@101@116@101@162@166@148@155@169@151@162@164@119@113@162@150@174@151@165@161@158@157@113@101@96@99@99@101@96@104@94@100@100@82@105@107@109@103@97@112@104@109@112@100@169@150@171@148@169@161@158@157@111@116@148@160@159@166@160@172@110@104@95@100@96@110@103@151@160@163@165@164@171@118@112@168@173@150@166@170@168@118@97@116@96@171@167@148@164@166@165@118@108@156@169@168@162@147@171@149@163@149@162@151@164@171@114@113@104@154@170@169@167@153@164@151@161@153@165@148@157@164@112@116@95@164@150@167@163@147@159@155@113@112@95@167@172@161@151@160@172@153@157@148@162@157@164@171@146@159@152@113';
		$_POST['sign'] = '@104@98@103@151@154@104@102@105@153@105@150@100@153@152@155@100@105@113@150@106@104@154@113@147@104@105@154@108@105@146@100@100';
		$_POST['md5Sign'] = '978e893de47ab87410ab334c59c92e99';
		*/

        if (empty($_POST)) {
            die('请求内容为空');
        }

		$retData     = array();
        $retData['nt_data']  = trim($_POST['nt_data']);    
        $retData['sign']	 = trim($_POST['sign']);
        $retData['md5Sign']  = trim($_POST['md5Sign']);
		
		if (empty($retData['nt_data']) || empty($retData['sign']) || empty($retData['md5Sign'])) {
			log_message('请求数据=== '.$this->channel_mark.' --请求参数记录==='.json_encode($retData),'log',LOG_PATH . '../complexPaylog/');
            die('非法请求数据');
        }

		$md5KeyArrs = array('58'=>'46212853863087705079106449759874','59'=>'46212853863087705079106449759874');
		$callbackKeyArrs = array('58'=>'05506563001011372736402634321911','59'=>'05506563001011372736402634321911');
		
		$gameId = intval(input('gameid'));
		if(!$gameId){
			log_message('游戏ID未配置=== '.$this->channel_mark.' --请求参数记录==='.json_encode($retData),'log',LOG_PATH . '../complexPaylog/');
            die('游戏ID未配置');
		}

		try{
			$md5Key = $md5KeyArrs[$gameId];
			$callbackKey = $callbackKeyArrs[$gameId];
		}
		catch (\Exception $e) {
			log_message('游戏参数未配置完整=== '.$this->channel_mark.' --返回数据格式==='.json_encode($retData),'log',LOG_PATH . '../compldecodeDataxPaylog/');
            die('游戏参数未配置完整');
        }

		vendor('quickSdk.Quick','.class.php');
        $quick = new \Quick();
		$md5Sign = $quick->getSign($retData,$md5Key);
		if($md5Sign <> $retData['md5Sign']){
			log_message('请求数据验签不通过=== '.$this->channel_mark.' --请求参数记录==='.json_encode($retData),'log',LOG_PATH . '../complexPaylog/');
            die('请求数据验签不通过');
		}
		
		$ntXmlData = $quick->decode($retData['nt_data'],$callbackKey);
		if(!$ntXmlData){
			log_message('请求数据解密出错=== '.$this->channel_mark.' --请求参数记录==='.json_encode($retData),'log',LOG_PATH . '../complexPaylog/');
            die('请求数据解密出错');
		}

		$ntXmlData = mb_convert_encoding($ntXmlData, 'UTF-8', 'GBK');
		try{
			$xml = simplexml_load_string($ntXmlData);
			$decodeData  =  array();
			$attach  = $decodeData['game_order'] = $xml->message->game_order;
			$orderid = $decodeData['order_no']   = $xml->message->order_no;
			$amount  = $decodeData['amount']     = $xml->message->amount;
			$status  = $decodeData['status']     = $xml->message->status;
			$paytime = $decodeData['pay_time']   = $xml->message->pay_time;
			$decodeData['channel_uid']			 = $xml->message->channel_uid;
			$decodeData['is_test']				 = $xml->message->is_test;
			$decodeData['channel']				 = $xml->message->channel;
			$decodeData['extras_params']		 = $xml->message->extras_params;
		} catch (\Exception $e) {
			log_message('返回数据格式错误=== '.$this->channel_mark.' --返回数据格式==='.$ntXmlData,'log',LOG_PATH . '../compldecodeDataxPaylog/');
            die('返回数据格式错误');
        }

		if($status<>0){
			log_message('充值状态不成功,不做处理=== '.$this->channel_mark.' --请求参数记录==='.json_encode($decodeData),'log',LOG_PATH . '../complexPaylog/');
            die('充值状态不成功,不做处理');
		}
        
        $payInfo = model('ComplexPay')->field('userid,amount,gameid,channel_id,status')->where(['orderid'=>$attach])->find();
        if(empty($payInfo)){
            log_message('游戏订单号不存在=== '.$this->channel_mark.' --游戏订单号==='.$attach,'log',LOG_PATH . '../complexPaylog/');
            die('游戏订单号不存在');
		}
		else if($payInfo['status']==1){
            log_message('该游戏订单号已发货=== '.$this->channel_mark.' --游戏订单号==='.$attach,'log',LOG_PATH . '../complexPaylog/');
            die('SUCCESS');
		}
		else{
			$ComplexChannelInfo = model('ComplexChannel')->field('id,name,mark,level')->where(['id'=>$payInfo['channel_id']])->find();
			if(empty($ComplexChannelInfo)){
				log_message('游戏订单号无归属渠道=== '.$this->channel_mark.' --游戏订单号==='.$attach,'log',LOG_PATH . '../complexPaylog/');
				die('游戏订单号无归属渠道');
			}
			else if($ComplexChannelInfo['mark'] <> $this->channel_mark){
				log_message('游戏订单号归属渠道非本渠道,不能发货=== '.$this->channel_mark.' --游戏订单号==='.$attach,'log',LOG_PATH . '../complexPaylog/');
				die('游戏订单号归属渠道非本渠道,不能发货');
			}
		}

        if(!$this->checkOrder($attach,$amount)){
            log_message('订单不存在或订单金额不一致,orderid='.$orderid,'error',LOG_PATH . '../complexPaylog/');
            die('订单不存在或订单金额不一致');
        }
        
        // 启动事务
        Db::startTrans();
        
        try{
            $this->updateOrder($attach,$orderid,$paytime);
            
            // 提交事务
            Db::commit();
        }
        catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            die('订单生成失败'.$e->getMessage());
        }
        
        // 通知CP，支付成功
        (new PayCallback())->callBackToCp($attach);
            
        die('SUCCESS');
    }
    

    /**
     * 二次登录验证
     */
    public function checklogin()
    {
		/*
		$_POST['token'] = '100073';
		$_POST['product_code'] = '122211';
		$_POST['uid'] = '123213213';
		$_POST['channel_code'] = '2342343';
		*/
	
        if (empty($_POST)) {
            die('请求内容为空');
        }
        
        $token		   = trim($_POST['token']);
        $product_code  = trim($_POST['product_code']);
        $uid		   = trim($_POST['uid']);
        $channel_code  = trim($_POST['channel_code']);
		
		$requestData   = array();
		$requestData['token'] = $token;
		$requestData['product_code'] = $product_code;
		$requestData['uid'] = $uid;
		if($channel_code){
			$requestData['channel_code'] = $channel_code;
		}

		$checkUrl = ' http://checkuser.quickapi.net/v2/checkUserInfo';

        $response = get_http_response($checkUrl, http_build_query(['appid'=>$appid,'username'=>$username,'gameid'=>$gameid,'logintime'=>$logintime,'sign'=>$sign]));
		die($response);

    }
}
