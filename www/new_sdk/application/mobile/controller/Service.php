<?php
/** 开服开测 -- 控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/24
 * Time: 10:47
 */

namespace app\mobile\controller;

use think\Db;
use think\Config;
class Service extends Mobile
{

    protected $serstatus = [
        1 => '预告',
        2 => '开服',
        3 => '删档内测',
        4 => '不删档内测', 
        5 => '公测'
    ];

    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 开服
     */
    public function index(){
        $this->getList(0);
        $this->assign('kfkc','kf');
        return $this->fetch();
    }

    /**
     * 开测
     */
    public function kc(){
        $this->getList(1);
        $this->assign('kfkc','kc');
        return $this->fetch('index');
    }

    /**
     * 开服、开测--列表
     */
    public function getList($type) {
        /*$type = $this->request->param('t',1,'intval');*/
        if (!in_array($type,[0,1])) $type = 0;
        $kfkc = '';
        if ($type == 0){
            $kfkc = '开服';
        }else{
            $kfkc = '开测';
        }

        $where = [
            'a.type'=>$type,
            'a.isdelete'=>0,
            'b.is_show' => 1,
            'b.cooperation_status'  => ['in',[0,1,2]]
        ];
        $nowtime = NOW_TIMESTAMP;

        $list = model('ServerInfo')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->alias('a')
            ->join('cy_game b','b.id = a.gameid')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->join('cy_gamesubject s', 'b.subject=s.id', 'left')
            ->join('cy_gametype t', 'b.type=t.id', 'left')
			->join('cy_sdkgamelist sdk', 'b.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
            ->field('a.*,info.mobileicon,info.androidurl,info.platform,b.nickname,b.pinyin,t.name as typename, s.name as subjectname,sdk.filename')
            ->where($where)
            ->where("( ( a.sertime > {$nowtime} and a.sercertype = 1 ) or a.sercertype = 2 )")
            ->order('a.sercertype asc,a.sertime asc')
            ->paginate(10, false, array('query' => input('get.')));

        $arr = [
            'td'  => [] ,
            'tm'  => [] ,
        ];
        $td = date("Y-m-d");
        $tm = date("Y-m-d",strtotime("+1 day"));
        $datTime = strtotime(date("Y-m-d 23:59:59",strtotime("+1 day")));
        foreach ($list as &$v){
            $v['mobileicon'] = "http://static.46yx.com/".$v['mobileicon'];
            $v['filename'] = model('SdkGameList')->where(['package_type'=>0,'gameid'=>$v['gameid'],'channel_id'=>MEMBER_DEFAULT_CHANNEL_ID])->value('filename');
			$gameInfo = array();
			$gameInfo['id'] = $v['gameid'];
			$gameInfo['pinyin'] = $v['pinyin'];
			$gameInfo['androidurl'] = $v['androidurl'];
			$gameInfo['platform'] = $v['platform'];
			$v['typename'] = getTypename($v['typename']);
			$v['subjectname'] = getSubjectname($v['subjectname']);
			$v['size'] = getSize($gameInfo);
            $v['subscribe'] = 1;    // 预约游戏
            $v['serstatusName'] = isset($this->serstatus[$v['serstatus']]) ? $this->serstatus[$v['serstatus']] : '';
            $v['libaonum'] = model('Gift')->where(['isdelete'=>0,'gameid'=>$v['gameid'],'endtime'=>['gt',NOW_TIMESTAMP]])->where('total > used')->count('id');

            if ($v['sercertype'] == 1 && $td == date("Y-m-d",$v['sertime']) ){
                $arr['td'][] = $v;
                if( $v['sertime'] <= $nowtime + 60*60){
                    $v['subscribe'] = 0;
                }
            }
            if ($v['sercertype'] == 1 && $tm == date("Y-m-d",$v['sertime']) ){
                $arr['tm'][] = $v;
            }
            if ($v['sercertype'] == 1 && $datTime < $v['sertime'] ){

                $arr[date('m月d日',$v['sertime']).$kfkc][] = $v;
            }

            if ($v['sercertype'] == 2){
                $arr['fn'][] = $v;
                $v['subscribe'] = 0;
            }
			$v['hsTime'] = date('H:i',$v['sertime']);
        }
        empty($list->total()) ? $arr=[] : [];
		if ($this->request->isAjax()) $this->jsonResult($arr,1);

        $this->assign('today',date('m月d日',$nowtime));
        $this->assign('tomorrow',date("m月d日",strtotime("+1 day")));

		if($type==0){
			$seo['title'] = '最新手游开服表_麻花网络';
		}
		else{
			$seo['title'] = '最新手游开测表_麻花网络';
		}
		$seo['keywords'] = '手机游戏开服表,手机游戏开测表';
		$seo['description'] = '麻花网络提供最新手机游戏开服表和手机游戏开测表,为您获取第一手开服开测信息，让您不错过任意想玩的手机游戏。';
        $this->assign('seo',$seo);

        $this->assign('list',$arr);
        $this->assign('listobj',$list);
        $this->assign('page',$list->render());

    }

    /**
     * 订阅游戏
     * code = 0 非法操作
     * code = 1 订阅成功
     * code = 2 用户未登录
     * code = 3 未绑定手机号
     * code = 4 不可重复订阅
     * code = 5 离开服开测时间不足1小时，无法预约
     */
    public function subscribe(){
        $id = $this->request->param('id',0,'intval');
        $type = $this->request->param('type','');
        if (empty($id) || empty($type) || !in_array($type,['kf','kc'])) $this->error('非法操作！');
        $msg = '';
        if ($type == 'kf'){
            $type = 1;
            $msg = '开服';
        }else{
            $type = 2;
            $msg = '开测';
        }

        $userid = session('front_userid');
        if (!$userid){
            $this->jsonResult(getMobileEncodeUrl('login'),2,'请先登录！');
        }

        //判断用户是否订阅
        $where = array('userid'=>$userid,'related_id'=>$id);
        if( !empty( Db::table('cy_subscribelog')->where($where)->find() ) ) $this->jsonResult([],4,'您已经预约过该'.$msg.'信息。');

        // 判断开服开测时间
        $info = model('ServerInfo')->where('id',$id)->find();
        if (empty($info) || $info['sertime'] < NOW_TIMESTAMP + 60*60)  $this->jsonResult([],5,'离'.$msg.'时间不足1小时，无法预约。');

        //获取用户手机号
        $mobile = model('Members')->where(array('id'=>$userid))->value('mobile');
        if(empty($mobile))  $this->jsonResult(getMobileAccountUrl(2),3,'请先绑定手机号！');

        $content = model('Subscribelog')->getContent($type,$id);

        $data = [
            'userid'      => $userid ,
            'username'    => session('front_account') ,
            'content'     => $content ,
            'mobile'      => $mobile ,
            'related_id'  => $id ,
            'type'        => $type ,
            'create_time' => NOW_TIMESTAMP
        ];
        $res = model('Subscribelog')->insertGetId($data);

        if ($res) $this->jsonResult([],1,'您已成功预约该'.$msg.'信息，届时会以短信形式通知，请留意。');

        $this->jsonResult([],0,'非法操作。');
    }

}
