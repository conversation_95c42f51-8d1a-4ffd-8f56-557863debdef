<?php
/**
 * 前端新闻控制器
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/29
 * Time: 14:11
 */
namespace app\mobile\controller;

use app\common\model\News as NewsModel;
use app\common\model\GameHome as GameModel;
use think\Db;
use think\Exception;
use think\Config;
class News extends Mobile
{
    /**
     * 新闻列表
     */
    public function index() {

        $newsModel = new NewsModel;

        $where = [];
		$type = intval(input('type'));
		if(!$type) $type = 1;
        if($type){
            $where['a.type'] = $type;
        }
        $newsList = $newsModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('a')
                ->join('cy_content b', 'a.id = b.id','left')
                ->field('a.id,a.title,a.image,a.jlink,a.create_time,a.total,a.zhiding,a.type,a.gameid,b.content')
				->where(['a.type'=>['in','1,2,3,4,5'],'a.isdelete' => 0])
                ->where($where)
                ->order('a.zhiding desc,a.id desc')
				->paginate(10, false, array('query' => input('get.')))
				->each(function ($item, $key){
					$item['image'] = getNewsImage($item['image']);
					$item['url'] = getMobileNewsUrl($item['id'],$item['jlink']);
					$item['createtime'] = date('Y-m-d H:i:s',$item['create_time']);
					$item['cutcontent'] = getCutcontent($item['content']);
					return $item;
            });
	//	var_dump($newsList);
		if ($this->request->isAjax()) $this->jsonResult($newsList,1);

        //获取资讯分类列表
        $newsCatgList = model('News')->getMobileCatgList();
        $totalNewsCnt = $newsModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('a')
                ->join('cy_content b', 'a.id = b.id','left')
                ->field('a.id,a.title,a.image,a.jlink,a.create_time,a.total,a.zhiding,a.type,a.gameid,b.content')
				->where(['a.type'=>['in','1,2,3,4,5'],'a.isdelete' => 0])
                ->where($where)
                ->count();

        $this->assign('newsCatgList',$newsCatgList);
        $this->assign('totalNewsCnt',$totalNewsCnt);
        $this->assign('list',$newsList);
		$this->assign('type',$type);

		if($type==1){
			$title = '新游资讯_手机游戏资讯_麻花网络';
		}
		else if($type==2){
			$title = '麻花网络资讯_手机游戏资讯_麻花网络';
		}
		else if($type==3){
			$title = '新游评测_手机游戏资讯_麻花网络';
		}
		else if($type==4){
			$title = '新游攻略_手机游戏资讯_麻花网络';
		}
		else if($type==5){
			$title = '游戏视频_手机游戏资讯_麻花网络';
		}
		else{
			$title = '新游资讯_手机游戏资讯_麻花网络';
		}

        $seo['title'] = $title;
        $seo['keywords'] = '手机游戏资讯,手游新闻,手机游戏攻略,评测,视频';
        $seo['description'] = '麻花网络资讯中心汇聚最热门的手机游戏资讯，最富有深度的手机游戏评测，最专业的手机游戏攻略，最精彩的手机游戏视频。';
        $this->assign('seo',$seo);
        
        return $this->fetch();
    }
    /**
     * 新闻详情
     */
    public function detail() {
		$newsModel = new NewsModel;
        $newsid = intval(input('id'));
        if(!$newsid) {
		//	throw new Exception("资讯不存在");
            $this->_abort404();
        }
        $detail =$newsModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('a')
                ->join('cy_content b', 'a.id = b.id','left')
                ->field('a.id,a.title,a.image,a.jlink,a.create_time,a.total,a.zhiding,a.type,a.gameid,b.content')
                ->where(['a.id'=>$newsid,'a.isdelete' => 0])
                ->find();

        if ( empty($detail)) {
		//	throw new Exception("资讯不存在或无效");
            $this->_abort404();
        }
		else{
			$detail['image'] = getNewsImage($detail['image']);
			$detail['url'] = getMobileNewsUrl($detail['id'],$detail['jlink']);
			$detail['createdate'] = date('Y-m-d',$detail['create_time']);
			$detail['createtime'] = date('H:i:s',$detail['create_time']);
			$detail['cutcontent'] = getCutcontent($detail['content']);
		}
		//类型是“活动（后台）”、“更新（后台）”、“新闻（后台）”、“充值活动（后台）”，则当前页直接跳转至404
		if(in_array($detail['type'],[6,7,8,9])){
		//	throw new Exception("无权查看该新闻");
            $this->_abort404();
		}
       
	    //获取相关资讯列表&游戏信息
		$relNewsList = $relGameInfo = array();
		if(intval($detail['gameid'])){
			$relNewsList = $newsModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
					->alias('a')
					->join('cy_content b', 'a.id = b.id','left')
					->field('a.id,a.title,a.image,a.jlink,a.create_time,a.total,a.zhiding,a.type,a.gameid,b.content')
					->where(['a.gameid'=>intval($detail['gameid']),'a.id'=>array('neq',$newsid),'a.type'=>['in','1,2,3,4,5'],'a.isdelete' => 0])
					->order('a.create_time desc,a.id DESC')
					->limit(5)
					->select();
			foreach ( $relNewsList as &$item ) {
				$item['url'] = getMobileNewsUrl($item['id'],$item['jlink']);
				$item['create_date'] = date('Y-m-d',$item['create_time']);
			}

			$gameModel = new GameModel;
			$relGameInfo = $gameModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
					->alias('game')
					->join('cy_gameinfo info', 'game.id = info.game_id','left')
					->join('cy_gametype t', 'game.type = t.id','left')
					->join('cy_gamesubject s', 'game.subject = s.id','left')
					->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
					->field('game.id,game.nickname,game.power,info.androidurl,info.platform,game.pinyin,info.mobileicon,game.type,game.subject,info.developer,info.bigimage,t.name as typename,s.name as subjectname,sdk.filename')
					->where(['game.id'=>intval($detail['gameid']),'game.cooperation_status' => ['in','1,2'],'game.is_show' => 1])
					->find();
			if (!empty($relGameInfo)) {
				$relGameInfo['icon'] = getGameIcon($relGameInfo['mobileicon']);
				$relGameInfo['size'] = getSize($relGameInfo);
				$relGameInfo['version'] = getVersion($relGameInfo);
				$relGameInfo['download'] = getDownload($relGameInfo);
				if($relGameInfo['platform']==1){
					$relGameInfo['download'] = '';
					$relGameInfo['ios_download'] = getIosDownload($relGameInfo);
				}
				else{
					$relGameInfo['download'] = getDownload($relGameInfo);
					$relGameInfo['ios_download'] = '';
				}
				$relGameInfo['typename'] = getTypename($relGameInfo['typename']);
				$relGameInfo['subjectname'] = getSubjectname($relGameInfo['subjectname']);
			}
		//	var_dump($relGameInfo);
		}
        $this->assign('detail',$detail);
        $this->assign('relNewsList',$relNewsList);
        $this->assign('relGameInfo',$relGameInfo);

		if($relGameInfo){
			$seo['title'] = $detail['title'].'_'.$relGameInfo['nickname'].'_麻花网络';
			$seo['keywords'] = $relGameInfo['nickname'].'、'.$relGameInfo['nickname'].'资讯、'.$detail['title'];
		}
		else{
			$seo['title'] = $detail['title'].'_麻花网络';
			$seo['keywords'] = $detail['title'];
		}
		$seo['description'] = $detail['cutcontent'];
		$this->assign('seo',$seo);

        return $this->fetch();
    }
}
