<?php
/**
 * 游戏加速
 */

namespace app\mobile\controller;

use think\Db;
use think\Config;
use app\common\model\GameBand;
use app\common\model\GameHome as GameModel;
class Gameaid extends Mobile
{

    private $_type = [
        '1' =>  '加速版',
        '2' =>  '跳过版',
    ];
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 加速游戏
     */
    public function index(){

        $where = [
            'b.is_show' => 1,
            'b.cooperation_status'  => ['in',[1,2]]
        ];
        $list = Db::connect(config('database_slave'))->table('cy_gameaid')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->alias('a')
            ->join('cy_game b','b.id = a.gameid')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->field('a.*,b.nickname,info.mobileicon,b.pinyin,info.platform')
            ->where($where)
            ->order('a.create_time desc')
            ->paginate(10, false, array('query' => input('get.')))
            ->each(function ($item, $key){
                $item['filename'] = Db::connect(config('database_slave'))->table('cy_sdkgamelist')->where(['upload_status'=>1,'package_type'=>1,'gameid'=>$item['gameid'],'channel_id'=>MEMBER_DEFAULT_CHANNEL_ID])->value('filename');

                $urlArr = $this->getDownUrl($item);
                $item['android_down_url'] = $urlArr['android_down_url'];
                $item['ios_down_url'] = $urlArr['ios_down_url'];

                $item['mobileicon'] = getGameIcon($item['mobileicon']);

                $item['typeName'] = isset($this->_type[$item['type']]) ? $this->_type[$item['type']] : '';
                return $item;
            });

        if ($this->request->isAjax()) $this->jsonResult($list,1);
        $this->assign('list',$list);
        $this->assign('type',$this->_type);
        $this->assign('page',$list->render());
        return $this->fetch();
    }

    /**
     * 光环包下载
     */
    public function download() {
        $gameModel = new GameModel;
        $gameid = intval(input('id'));
        if(!$gameid) {
            $this->_abort404();
        }

        $detail = $gameModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
            ->alias('game')
            ->join('cy_gameinfo info', 'game.id = info.game_id','left')
            ->join('cy_gametype t', 'game.type = t.id','left')
            ->join('cy_gamesubject s', 'game.subject = s.id','left')
            ->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=1 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
            ->field('game.id,game.nickname,game.power,info.androidurl,game.pinyin,info.mobileicon,game.type,game.subject,info.developer,info.bigimage,t.name as typename,s.name as subjectname,info.description,sdk.filename')
            ->where(['game.id'=>$gameid,'game.cooperation_status' => ['in','0,1,2'],'game.is_show' => 1])
            ->find();

        if ( empty($detail)) {
            //	throw new Exception("游戏不存在或无效");
            $this->_abort404();
        }
        else{
            /*$detail['icon'] = getGameIcon($detail['mobileicon']);
            $detail['size'] = getSize($detail);
            $detail['version'] = getVersion($detail);
            $detail['updatetime'] = getUpdateTime($detail);*/
            $detail['download'] = getDownload($detail);
           /* $detail['screenShot'] = getScreenShot($detail['bigimage']);
            $detail['typename'] = getTypename($detail['typename']);
            $detail['subjectname'] = getSubjectname($detail['subjectname']);
            $detail['developer'] = getDeveloper($detail['developer']);*/
        }

        $this->assign('detail',$detail);
        return $this->fetch();
    }

    /**
     * 获取下载链接
     */
    public function getDownUrl($item,$package_type = 1){
        //安卓游戏
        if($item['platform']==0)
        {
            $ios_down_url = '';

            $gameBandInfo =  Db::connect(config('database_slave'))->table('nw_game_band b,cy_game g')->field('b.ios_game_id,g.id,g.pinyin')
                ->where('b.ios_game_id = g.id')
                ->where(['b.android_game_id'=>$item['gameid']])->find();

            if(!empty($gameBandInfo)){

                //绑定的游戏包名
                $bandFilename = Db::connect(config('database_slave'))->table('cy_sdkgamelist')->where(['gameid'=>$gameBandInfo['id'],'channel_id'=>MEMBER_DEFAULT_CHANNEL_ID,'upload_status'=>1,'package_type'=>$package_type])->value('filename');


                if(!empty($bandFilename)){

                    $ios_down_url = APK_DOWN_DOMAIN.'/sygame/'.$gameBandInfo['pinyin'].'/'.str_replace(".ipa",".plist",$bandFilename);
                }
            }

            $url = [
                'android_down_url'  => (!empty($item['filename']) ? APK_DOWN_DOMAIN.'/sygame/'.$item['pinyin'].'/'.$item['filename'] : ''),
                'ios_down_url'  => (isset($ios_down_url) ? $ios_down_url : ''),
            ];
            return $url;
        }
        //IOS游戏
        else{
            $android_down_url = '';

            $gameBandInfo = Db::connect(config('database_slave'))->table('nw_game_band b,cy_game g')->field('b.android_game_id,g.id,g.pinyin')
                ->where('b.android_game_id = g.id')
                ->where(['b.ios_game_id'=>$item['gameid']])->find();

            if(!empty($gameBandInfo)){

                //绑定的游戏包名
                $bandFilename = Db::connect(config('database_slave'))->table('cy_sdkgamelist')->where(['gameid'=>$gameBandInfo['id'],'channel_id'=>MEMBER_DEFAULT_CHANNEL_ID,'upload_status'=>1,'package_type'=>$package_type])->value('filename');

                if(!empty($bandFilename)){

                    $android_down_url = APK_DOWN_DOMAIN.'/sygame/'.$gameBandInfo['pinyin'].'/'.$bandFilename;
                }
            }

            $url = [
                'android_down_url'  => (isset($android_down_url) ? $android_down_url : ''),
                'ios_down_url'  => (!empty($item['filename']) ? APK_DOWN_DOMAIN.'/sygame/'.$item['pinyin'].'/'.str_replace(".ipa",".plist",$item['filename']) : ''),
            ];
            return $url;
        }

    }

}
