<?php
/**
 * 官网首页
 */
namespace app\mobile\controller;

use app\common\model\GameHome as Game;
use app\common\model\GameInfo;
use app\common\model\News;
use app\common\model\Links;
use app\common\model\SdkGameList;
use think\Db;
use think\Config;

class Index extends Mobile
{
    protected $serstatus = [
        1 => '预告',
        2 => '开服',
        3 => '删档内测',
        4 => '不删档内测',
        5 => '公测'
    ];

    public function index()
    {
        $gameModel     = new Game;
        $gameInfoModel = new GameInfo;

        //获取热门游戏列表
        $hotGameList = $gameInfoModel->getMobileGameInfoByLanmu(['g.lanmu'=>2],3);
		foreach ( $hotGameList as &$game ) {
			$game['mobileicon'] = "http://static.46yx.com/".$game['mobileicon'];
            $game['url'] = '';
//            $game['url'] = getMobileGameUrl($game['id']);
			if($game['platform']==1){
				$game['download'] = '';
				$game['ios_download'] = getIosDownload($game);
			}
			else{
				$game['download'] = getDownload($game);
				$game['ios_download'] = '';
			}
			$game['typename'] = getTypename($game['typename']);
			$game['subjectname'] = getSubjectname($game['subjectname']);
		}

        //获取最新游戏列表
        $newGameList = $gameInfoModel->getMobileGameInfoByLanmu(['g.lanmu'=>3],4);
		foreach ( $newGameList as &$game ) {
			$game['mobileicon'] = "http://static.46yx.com/".$game['mobileicon'];
            $game['url'] = '';
//			$game['url'] = getMobileGameUrl($game['id']);
			if($game['platform']==1){
				$game['download'] = '';
				$game['ios_download'] = getIosDownload($game);
			}
			else{
				$game['download'] = getDownload($game);
				$game['ios_download'] = '';
			}
			$game['typename'] = getTypename($game['typename']);
			$game['subjectname'] = getSubjectname($game['subjectname']);
		}

        //获取本周推荐列表
        $recommendGameList = $gameInfoModel->getMobileGameInfoByLanmu(['g.lanmu'=>4],8);
		foreach ( $recommendGameList as &$game ) {
			$game['mobileicon'] = getGameIcon($game['mobileicon']);
            $game['url'] = '';
//			$game['url'] = getMobileGameUrl($game['id']);
			if($game['platform']==1){
				$game['download'] = '';
				$game['ios_download'] = getIosDownload($game);
			}
			else{
				$game['download'] = getDownload($game);
				$game['ios_download'] = '';
			}
			$game['typename'] = getTypename($game['typename']);
			$game['subjectname'] = getSubjectname($game['subjectname']);
		}

        //手机顶部轮播
        $topBannerList = Db::connect(config('database_slave'))->table('cy_ad')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->field('id,title,url,image')->where(['type'=>21])->order('sort asc,create_time asc')->limit(0,5)->select();

		//排行版
        $rankGameList = $gameModel->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
                ->alias('g')
                ->join('cy_gameinfo info', 'g.id=info.game_id','left')
				->join('cy_gametype t', 'g.type = t.id','left')
				->join('cy_gamesubject s', 'g.subject = s.id','left')
				->join('cy_sdkgamelist sdk', 'g.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
				->field('g.id,g.nickname,g.power,info.androidurl,info.platform,g.pinyin,info.mobileicon,g.type,g.subject,g.publicity,t.name as typename,s.name as subjectname,sdk.filename')
                ->where(['g.cooperation_status' => ['in','1,2'],'g.is_show' => 1])
                ->order('g.power desc')
                ->limit(0,10)
				->select();
	//	var_dump($rankGameList);
		foreach ( $rankGameList as $k=>&$game ) {

			$game['mobileicon'] = "http://static.46yx.com/".$game['mobileicon'];
//			$game['url'] = getMobileGameUrl($game['id']);
            $game['url']  = '';
			if($game['platform']==1){
				$game['download'] = '';
//				$game['ios_download'] = getIosDownload($game);
                $game['ios_download'] =  '';
			}
			else{
                $game['download'] = '';
//				$game['download'] = getDownload($game);
				$game['ios_download'] = '';
			}
			$game['typename'] = getTypename($game['typename']);
			$game['subjectname'] = getSubjectname($game['subjectname']);
			$game['size'] = getSize($game);
		}
	//	var_dump($rankGameList);

        //获取游戏分类列表
        $gameTypeList = Db::connect(config('database_slave'))->table('cy_gametype t')
					->join('cy_game g', 't.id=g.type and g.cooperation_status in (1,2) and g.is_show=1','left')
					->field('t.id,t.name,sum(case when g.id then 1 else 0 end) as cnt')
				//	->where(['g.cooperation_status' => ['in','1,2'],'g.is_show' => 1])
					->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
					->group('t.id')
					->select();
		foreach ( $gameTypeList as &$gameType ) {
			$gameType['typeicon'] = getGameTypeIcon(trim($gameType['name']));
		}


        $td = date("Y-m-d");
        $tm = date("Y-m-d",strtotime("+1 day"));
        $datTime = strtotime(date("Y-m-d 23:59:59",strtotime("+1 day")));
        $nowtime = NOW_TIMESTAMP;
		//开服信息
        $where = [
            'a.type'=>0,
            'a.isdelete'=>0,
            'b.is_show' => 1,
            'b.cooperation_status'  => ['in',[0,1,2]]
        ];
        $kfList = model('ServerInfo')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->alias('a')
            ->join('cy_game b','b.id = a.gameid')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->join('cy_gamesubject s', 'b.subject=s.id', 'left')
            ->join('cy_gametype t', 'b.type=t.id', 'left')
			->join('cy_sdkgamelist sdk', 'b.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
            ->field('a.*,info.mobileicon,b.nickname,b.pinyin,t.name as typename, s.name as subjectname,sdk.filename,info.androidurl,info.platform')
            ->where($where)
            ->where("( ( a.sertime > {$nowtime} and a.sercertype = 1 ) or a.sercertype = 2 )")
            ->order('a.sercertype asc,a.sertime asc')
			->limit(6)
			->select();
        foreach ($kfList as $k=>&$serverGame){
            $serverGame['subscribe'] = 1;    // 预约游戏
            $serverGame['highlight'] = 0;    // highlight显示
            $serverGame['mobileicon'] = "http://static.46yx.com/".$serverGame['mobileicon'];var_dump(1);die();
//			$serverGame['url'] = getMobileGameUrl($serverGame['gameid']);
            $serverGame['url'] = '';
			if($serverGame['platform']==1){
                $serverGame['ios_download'] = $serverGame['download'] = '';
//				$serverGame['ios_download'] = getIosDownload($serverGame);
			}
			else{
//				$serverGame['download'] = getDownload($serverGame);
                $serverGame['download'] = $serverGame['ios_download'] = '';
			}
			$serverGame['typename'] = getTypename($serverGame['typename']);
			$serverGame['subjectname'] = getSubjectname($serverGame['subjectname']);
			$serverGame['size'] = getSize($serverGame);
            $serverGame['libaonum'] = model('Gift')->where(['isdelete'=>0,'gameid'=>$serverGame['gameid'],'endtime'=>['gt',$nowtime]])->where('total > used')->count('id');

            if ($serverGame['sercertype'] == 1 && $td == date("Y-m-d",$serverGame['sertime']) ){
                if( $serverGame['sertime'] <= $nowtime + 60*60){
                    $serverGame['subscribe'] = 0;
                }
				$serverGame['showSertime'] = '今日'.date('H:i',$serverGame['sertime']);
				$serverGame['highlight'] = 1;
            }
            if ($serverGame['sercertype'] == 1 && $tm == date("Y-m-d",$serverGame['sertime']) ){
				$serverGame['showSertime'] = '明日'.date('H:i',$serverGame['sertime']);
            }
            if ($serverGame['sercertype'] == 1 && $datTime < $serverGame['sertime'] ){
				$serverGame['showSertime'] = date('m月d日',$serverGame['sertime']);
            }
            if ($serverGame['sercertype'] == 2){
                $serverGame['subscribe'] = 0;
				$serverGame['showSertime'] = '长期有效';
            }
        }
		//开测信息
		$where['a.type'] = 1;
        $kcList = model('ServerInfo')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->alias('a')
            ->join('cy_game b','b.id = a.gameid')
            ->join('cy_gameinfo info', 'b.id=info.game_id', 'left')
            ->join('cy_gamesubject s', 'b.subject=s.id', 'left')
            ->join('cy_gametype t', 'b.type=t.id', 'left')
			->join('cy_sdkgamelist sdk', 'b.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
            ->field('a.*,info.mobileicon,b.nickname,b.pinyin,t.name as typename, s.name as subjectname,sdk.filename,info.androidurl,info.platform')
            ->where($where)
            ->where("( ( a.sertime > {$nowtime} and a.sercertype = 1 ) or a.sercertype = 2 )")
            ->order('a.sercertype asc,a.sertime asc')
			->limit(6)
			->select();
        foreach ($kcList as &$serverGame){
            $serverGame['subscribe'] = 1;    // 预约游戏
            $serverGame['highlight'] = 0;    // highlight显示
            $serverGame['mobileicon'] = "http://static.46yx.com/".$serverGame['mobileicon'];
//			$serverGame['url'] = getMobileGameUrl($serverGame['gameid']);
            $serverGame['url'] = '';
			if($serverGame['platform']==1){
                $serverGame['ios_download'] = $serverGame['download'] = '';
//				$serverGame['ios_download'] = getIosDownload($serverGame);
			}
			else{
//				$serverGame['download'] = getDownload($serverGame);
                $serverGame['download'] = $serverGame['ios_download'] = '';
			}
			$serverGame['typename'] = getTypename($serverGame['typename']);
			$serverGame['subjectname'] = getSubjectname($serverGame['subjectname']);
			$serverGame['size'] = getSize($serverGame);
            $serverGame['serstatusName'] = isset($this->serstatus[$serverGame['serstatus']]) ? $this->serstatus[$serverGame['serstatus']] : '';
            $serverGame['libaonum'] = model('Gift')->where(['isdelete'=>0,'gameid'=>$serverGame['gameid'],'endtime'=>['gt',$nowtime]])->where('total > used')->count('id');

            if ($serverGame['sercertype'] == 1 && $td == date("Y-m-d",$serverGame['sertime']) ){
                if( $serverGame['sertime'] <= $nowtime + 60*60){
                    $serverGame['subscribe'] = 0;
                }
				$serverGame['showSertime'] = '今日'.date('H:i',$serverGame['sertime']);
				$serverGame['highlight'] = 1;
            }
            if ($serverGame['sercertype'] == 1 && $tm == date("Y-m-d",$serverGame['sertime']) ){
				$serverGame['showSertime'] = '明日'.date('H:i',$serverGame['sertime']);
            }
            if ($serverGame['sercertype'] == 1 && $datTime < $serverGame['sertime'] ){
				$serverGame['showSertime'] = date('m月d日 H:i',$serverGame['sertime']);
            }
            if ($serverGame['sercertype'] == 2){
                $serverGame['subscribe'] = 0;
				$serverGame['showSertime'] = '长期有效';
            }
        }
        // 热搜词
        $trendingSearch = Db::name('cy_keywords')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->where('type',1)->order('create_time desc')->value('title');

        // APP下载链接
        $app_link = model('Setting')->getSetting('APP_LINK');

        $this->assign('hotGameList',$hotGameList);
        $this->assign('newGameList',$newGameList);
        $this->assign('recommendGameList',$recommendGameList);
        $this->assign('topBannerList',$topBannerList);
        $this->assign('rankGameList',$rankGameList);
        $this->assign('gameTypeList',$gameTypeList);
        $this->assign('trendingSearch',$trendingSearch);
        $this->assign('kfList',$kfList);
        $this->assign('kcList',$kcList);
        $this->assign('app_link',$app_link);

        $seo['title'] = '麻花网络_简单生活，快乐游戏';
        $seo['keywords'] = '好玩的手机游戏,手机游戏下载,热门手游,最新手游,手游排行榜,安卓游戏,麻花网络游戏,麻花网络';
        $seo['description'] = '麻花网络手游平台为玩家提供免费手机游戏下载，最新、热门手机游戏排行以及海量免费的手游礼包。麻花网络还提供最新鲜好玩的手机游戏资讯、最精彩的手游攻略、手游评测、手游视频等内容!';
        $this->assign('seo',$seo);

        return $this->fetch();
    }

    public function agreement()
    {
        return $this->fetch();
    }
}
