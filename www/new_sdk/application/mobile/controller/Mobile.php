<?php

/**
 * 官网的公共控制器类
 *
 */

namespace app\mobile\controller;

use app\common\controller\Base;
use think\Db;
use think\Config;

class Mobile extends Base
{
    // 不验证权限的action
    protected $noCheckAuth = [];

    protected $current_node;

    // 登录返回上一页，需跳过的页面url
    protected $noPreviousPage = [];

    // 登录返回上一页，默认url
    protected $defaultPage = MOBILE_SITE_DOMAIN;
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        $this->noPreviousPage = [MOBILE_SITE_DOMAIN.'/login/',MOBILE_SITE_DOMAIN.'/register/',MOBILE_SITE_DOMAIN.'/forgotpwd.html',MOBILE_SITE_DOMAIN.'/forgotpwd/check.html',MOBILE_SITE_DOMAIN.'/forgotpwd/resetpwd.html',MOBILE_SITE_DOMAIN.'/csc/resetpwdresult.html',MOBILE_SITE_DOMAIN.'/csc/resetpwdresult/suc/1.html'];

        // 麻花网络app下载链接
        $link = model('Setting')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->where('name','APP_LINK')->value('value');
        // 用户昵称
        $userinfo = '';
        if (session('front_userid')){
            $userinfo = model('members')->field('nickname,avatar,username,id,amount')->where('id',session('front_userid'))->find();
        }
        $this->assign('link',$link);
        $this->assign('userinfo',$userinfo);
    }

    /**
     * 判断用户是否登录，未登录跳转登录页面
     */
    protected function _isLogin(){
        if (!session('?front_userid')){
            $this->redirect('Mobile/member/login');
            exit;
        }
    }

    /**
     * 返回json数据到H5
     * @param array $data
     * @param int $code
     * @param string $msg
     * @return \think\response\Json
     */
    public function json($data = [], $code = 0, $msg = '')
    {
        return json(['data' => $data, 'code' => $code, 'msg' => $msg]);
    }

    /**
     * 返回 json
     * @access protected
     * @param mixed $data 要返回的数据
     * @param int $code 返回的 code
     * @param mixed $msg 提示信息
     * @return void
     * @throws HttpResponseException
     */
    public function jsonResult($data, $code = 0, $msg = '')
    {
        $this->result($data, $code, $msg, 'json');
    }


    /**
     * 获取上一页的URL （有过滤）
     * @param bool $toUrl  url是否转完整地址
     * @return string
     */
    public function getPrePageUrl(){
        $pre_page = session('HTTP_REFERER_URL');
        if (!$pre_page || session('has404')){
            session('has404',null);
            return  $this->defaultPage;
        }

        return $pre_page;
    }

    protected function setPreUrl(){
        if (session('front_userid')) return false;
        $url = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : null;
        if (!empty($url) && !in_array($url,$this->noPreviousPage)) {
            session('HTTP_REFERER_URL' , $url);
            return true;
        }
        return false;
    }

    /**
     * 验证手机号是否正确
     *
     * <AUTHOR>
     * @param number $mobile
     */
    function isMobile($mobile) {
        if (!is_numeric($mobile)) {
            return false;
        }
        return preg_match('#^1[\d]{10}$#', $mobile) ? true : false;
    }


    /**
     * 正则表达式验证email格式
     *
     * @param string $str    所要验证的邮箱地址
     * @return boolean
     */
    function isEmail($str) {
        if (!$str) {
            return false;
        }
        return preg_match('/^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i', $str) ? true : false;
    }

    /**
     * 页面404输出
     */
    public function _abort404(){
        session('has404',true);
        abort(404);
    }

    /**
     * 更新用户信息缓存
     * @param $arr  ['字段名'=> 值]
     */
   /* public function _updateUserInfo($arr = []){
        if (empty($arr)) return false;
        $info = session('front_info');
        foreach ($arr as $filed=>$value){
            $info[$filed] = $value;
        }
        session('front_info',$info);
        return true;
    }*/
}
