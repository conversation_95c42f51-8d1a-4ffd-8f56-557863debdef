<?php

/**
 * 官网的公共控制器类
 *
 */

namespace app\mobile\controller;


use app\common\library\AirwallexPay;
use app\common\logic\PayCallback;
use app\common\model\GameServer;
use app\common\model\MemberCoinPay;
use app\common\model\MemberGameServer;
use app\common\model\Members;
use app\common\model\PayType;
use app\common\model\Subaccount;
use app\service\GamePayService;
use app\service\MemberCoinService;
use app\common\model\Game;
use app\common\logic\Pay as PayLogic;
use think\Cache;
use think\Env;
use think\Exception;

class CoinAirwallex extends Mobile
{
    //v1.game_pay_type
    /**
     * 获取支付方式
     */
    public function getPayType()
    {
        $denyPayType = [
            'airwallexh5',
        ];
        //wx-wap
        //xzzfbzf 新支付宝
        //old-zfb-wap 旧支付宝
        $payTypeModel = new PayType();
        $arrPayType = $payTypeModel->field('paytype,payname')
            ->where(['paytype' => ['in', $denyPayType]])
            ->select();


        $this->result($arrPayType, 1, '成功', 'json');
    }

    //v1.pay
    public function pay()
    {
        $data['type'] = input('type');                   //类型 1充值平台币 2 充值游戏
        $data['paytype'] = input('paytype');             //支付类型
        $data['amount'] = input('amount');                 //支付金额
        $data['accountType'] = input('accountType');                 //账号类型1当前账号2其他账号
        $data['username'] = input('username');
        $data['gameid'] = input('gameid');
        $data['serverid'] = input('serverid');
        $data['roleid'] = input('roleid');
        $data['ip'] = request()->ip();
        $checkResult = $this->validate($data,
            [
                ['type', 'require|integer|in:1', '类型不能为空|必须为整型'],
                ['paytype', 'require', '支付类型不能为空'],
                ['amount', 'require|integer|egt:0', '支付金额不能为空|支付金额必须为整型|支付金额大于等于0的整型'],
                ['accountType', 'require|in:1,2', '账号类型不能为空|账号类型错误'],
                ['username', 'requireIf:accountType,2', '账号必填'],
                ['gameid', 'requireIf:type,2|integer', '游戏必填|游戏必须为整型'],
                ['serverid', 'requireIf:type,2', '区服必填'],
                ['roleid', 'requireIf:type,2', '角色必填'],
                ['coin_amt', 'float|egt:0', '金额必须为数字|支付金额大于等于0'],
            ]);

        if (true !== $checkResult) {
            $this->result('', 0, $checkResult, 'json');
        }

        if ($data['accountType'] == 2) {
            if (!$user = (new Members())->getUseridByName($data)) {
                $this->result([], 0, '账号不存在');
            }
            $data['member_id'] = $user['id'];
        } else {
            $data['member_id'] = session('front_userid');
            if(!session('front_userid')){
                $this->result([], 0, '未登录请先登录');
            }
            if (!$user = (new Members())->getUseridById($data)) {
                $this->result([], 0, '账号不存在');
            }
        }
        if(!in_array($data['paytype'],['airwallexh5'])){
            $this->jsonResult('', 0, '支付类型错误');
        }
        if (in_array($data['paytype'], ['coinpay','coin-old-zfb-wap','coin-kdh5']) && $data['accountType'] != 1) {
            $this->jsonResult('', 0, '支付方式错误');
        }
        $data['place_user_id'] = session('front_userid');
        if ($data['type'] == 1) {//充值平台币
            $result = (new MemberCoinService())->payCoin($data);
        } else {
//            $data['gameid']     = $this->input('gameid');                   //游戏ID
//            $data['member_id']  = $this->input('userid');                   //用户ID
//            $data['serverid']   = $this->input('serverid');                 //服务器ID
//            $data['servername'] = $this->input('servername');             //区服名称
//            $data['amount']     = $this->input('amount');                   //充值金额
//            $data['roleid']     = $this->input('roleid');                   //角色ID
            $data['attach'] = makeOrderid('CP');                   //上一级的扩展参数，多数是订单编号[麻花网络自己聚合自己时，会加个aoyou###前缀]
            $data['ptb_amt'] = 0;                  //平台币使用金额
            $data['imeil'] = 'pc';              //手机imeil码
            $data['productname'] = '游戏充值';              //消费的商品名称
//            $data['paytype']    = $this->input('paytype');                  //支付类型
//            $data['rolename'] = $this->input('rolename', '');              //角色名
//            $data['rolelevel'] = $this->input('rolelevel', '');             //角色角色等级
//            $data['channel_id'] = $user['channel_id'];
//            $data['sub_username'] = $this->input('sub_username');



            if($data['accountType'] == 2 || $data['type'] == 1){
                $data['coin_amt'] = 0;
            }else{
                $data['coin_amt'] = input('coin_amt');                  //平台币使用金额
            }

            $result = (new GamePayService())->gamePay($data,2);
            if ($result['code'] == 1) {
                $this->jsonResult($result['data'], 1, '订单生成成功', false);
            } else {
                $this->jsonResult('', 0, $result['msg']);
            }

            //全部平台币支付，并且支付成功时
            if ($result['code'] == 1 && (isset($result['data']['pay_status'])&& $result['data']['pay_status'] == 1)) {

                // 通知CP，支付成功
                (new PayCallback())->callBackToCp($result['data']['orderid']);
                (new PayLogic())->payWarning($data['member_id'], $data['channel_id'], $data['gameid'], $data['amount']);
            }

        }
        if ($result['code'] == 1) {
            $this->jsonResult($result['data'], 1, (isset($result['data']['pay_status'])&&$result['data']['pay_status'] == 1) ? '付款成功' : '订单生成成功', false);
        } else {
            $this->jsonResult('', 0, $result['msg']);
        }

    }

    /**
     * 获取可支付游戏
     */
    public function getGameList()
    {
        $this->result((new Game())->getGameListPay(), 1, '成功');
    }

    public function getServerList()
    {
        $data['gameid'] = input('gameid');                 //游戏id

        $checkResult = $this->validate($data,
            [
                ['gameid', 'require|integer|gt:0', '游戏id不能为空|游戏id必须为整型|游戏id大于0的整型'],
            ]);

        if (true !== $checkResult) {
            $this->result('', 0, $checkResult, 'json');
        }

        $this->result((new GameServer())->getServerList($data), 1, '成功');
    }

    public function getRoleList()
    {
        $data['gameid'] = input('gameid');                 //游戏id
        $data['serverid'] = input('serverid');                 //游戏id
        $data['accountType'] = input('accountType');                 //账号类型1当前账号2其他账号
        $data['username'] = input('username');
        $checkResult = $this->validate($data,
            [
                ['gameid', 'require|integer|gt:0', '游戏id不能为空|游戏id必须为整型|游戏id大于0的整型'],
                ['serverid', 'require|integer|gt:0', '区服id不能为空|区服id必须为整型区服id大于0的整型'],
                ['accountType', 'require|in:1,2', '账号类型不能为空|账号类型错误'],
                ['username', 'requireIf:accountType,2', '账号必填']
            ]);

        if (true !== $checkResult) {
            $this->result('', 0, $checkResult, 'json');
        }
        if ($data['accountType'] == 2) {
            if (!$user = (new Members())->getUseridByName($data)) {
                $this->result([], 0, '账号不存在');
            }
            $data['userid'] = $user['id'];
        } else {
            $data['userid'] = session('front_userid');
        }

        $this->result((new MemberGameServer())->getRoleList($data), 1, '成功');
    }

    public function verifyUserName()
    {
        $data['username'] = input('username');                 //游戏id

        $checkResult = $this->validate($data,
            [
                ['username', 'require', '账号不能为空'],
            ]);

        if (true !== $checkResult) {
            $this->result('', 0, $checkResult, 'json');
        }
        $user = (new Members())->getUserName($data);
        if ($user) {
            $this->result($user, 1, '成功');
        } else {
            $this->result([], 0, '账号不存在');
        }
    }

    public function payHtml()
    {
        $data['data'] = input('data');

        $checkResult = $this->validate($data,
            [
                ['data', 'require', '参数不能为空'],
            ]);

        if (true !== $checkResult) {
            $this->result('', 0, $checkResult, 'json');
        }

        if (!$data = json_decode(opensslDecrypt($data['data']), true)) {
            $this->result('', 0, '订单不存在', 'json');
        }


        $this->assign('data', $data);
        return $this->fetch();
    }
    public function recharge()
    {

        $token = input('token');

        $token = str_ireplace(" ", "+", $token);

        if($token){
            //聚合渠道时,$sub_username对应的值是mg_username
            try {
                list($userid2, $username2, $gameid2,$sub_username,$token_random,$type) = explode('|', auth_code($token,"DECODE",Env::get('auth_key')));
//            var_dump($userid2, $username2, $gameid2,$sub_username,$token_random,$type);die();
                $redisTokenRandom = Cache::store('default')->get('token|'.$type.'|'.$userid2.'|'.$gameid2);
                $this->assign('username2', $username2);
            }catch (Exception $e){
                $username2 = '';
            }
        }else{
            $username2 = '';
        }
        $this->assign('username2',$username2 );

        return $this->fetch();
    }

    public function getCoin(){
        if (session('front_userid')){
            model('MemberCoinInfo')->releaseCoin(session('front_userid'));
            $userinfo = model('members')->field('id,amount')->where('id',session('front_userid'))->find();
            $this->jsonResult($userinfo, 1,'获取成功');
        }else{
            $this->jsonResult(['id'=>'','amount'=>'0'], 1,'获取成功');
        }
    }

    public function airwallex(){
        $pay = new AirwallexPay();
        $pay->getToken();
//        $money = input('money', 0, 'intval');

        $orderid = input('orderid', '');
        $goods = urldecode(input('goods', ''));
        $urltype = input('urltype',2);
        if($urltype == 2){
            $return_url = 'https://m.46yx.com/coin_airwallex/recharge.html';
        }elseif($urltype == 3){
            $return_url =  "https://" . HTTP_HOST_URL . "/v1.pay_return/pay_success_airwallex.html";
        }
        $money = cache('memberCoin:real_amount:'.$orderid);
        if($money<0){
            echo 'money err'; exit();
        }
        if(!$orderid){
            echo 'orderid err'; exit();
        }
        $result = $pay->createOrder($money, $orderid,$return_url);

        $result['goods'] = $goods;
        $this->assign('result', $result);
        $this->assign('orderid', $orderid);
        return $this->fetch();
    }
}
