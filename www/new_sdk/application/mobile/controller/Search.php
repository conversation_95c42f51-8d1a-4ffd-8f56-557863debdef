<?php

// 搜索页
namespace app\mobile\controller;

use think\Db;
use think\Config;
use app\common\model\GameHome as GameModel;
use app\common\model\News as NewsModel;

class Search extends Mobile{

	/**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 搜索
     */
    public function index(){

        // 热搜词
        $trendingSearch = Db::name('cy_keywords')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->where('type',1)->order('create_time desc')->value('title');

        // 热门搜索
        $data = [
            'a.type'               => 2 ,
            'b.is_show'            => 1 ,
            'b.cooperation_status' => ['in' , [1 , 2]]
        ];
    	$list = Db::name('cy_keywords')->alias('a')
            ->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
            ->join('cy_game b','a.title = b.id')
            ->field('b.nickname as title,b.id as gameid')
            ->where($data)->order('a.create_time desc')
            ->limit(10)
            ->select();

    	$this->assign('list',$list);
        $this->assign('trendingSearch',$trendingSearch);

        return $this->fetch();
    }

    /**
     * 搜索结果
     */
    public function search(){

        $keyword = input('keyword');

        $gameList = $this->_getGameList($keyword);
        $newsList = $this->_getNewsList($keyword);

        $this->assign('list',$gameList['list']);
        $this->assign('newsList',$newsList['list']);
        $this->assign('page',$gameList['page']);
        $this->assign('newsPage',$newsList['page']);
        $this->assign('keyword',$keyword);
  
        return $this->fetch();
    }


    // 游戏列表
    public function _getGameList($keyword) {
       
        $list = (new GameModel())->alias('game')->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
                ->join('cy_gameinfo info', 'game.id = info.game_id')
                ->join('cy_sdkgamelist sdk', 'game.id=sdk.gameid and sdk.package_type=0 and sdk.upload_status=1 and sdk.channel_id='.MEMBER_DEFAULT_CHANNEL_ID,'left')
                ->field('LENGTH(game.nickname) as tLen,LOCATE("'.$keyword.'",game.nickname) as tIndex,LOCATE("'.$keyword.'",game.pinyin) as cIndex,game.id,game.nickname,game.power,info.androidurl,game.pinyin,info.mobileicon,game.type,game.subject,sdk.filename,game.publicity,info.platform')
                ->where(['game.cooperation_status' => ['in','1,2'],'game.is_show' => 1])
                ->where('game.nickname|game.pinyin','like','%'. $keyword .'%')
               ->order('tIndex, cIndex,tLen,game.create_time desc')
                ->paginate(10, false, array('path'=>'javascript:AjaxGamePage([PAGE]);',))
				->each(function ($item, $key){
					$item['icon'] = getGameIcon($item['mobileicon']);
					$item['size'] = getSize($item);
                    if($item['platform']==1){
                        $item['download'] = '';
                        $item['ios_download'] = getIosDownload($item);
                    }
                    else{
                        $item['download'] = getDownload($item);
                        $item['ios_download'] = '';
                    }
					$item['subject'] = Db::connect(config('database_slave'))->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->table('cy_gamesubject')->where('id',$item['subject'])->value('name') ? : '未知';
					$item['type'] = Db::connect(config('database_slave'))->cache(Config::get('QUERY_RESULT_CACHE_TIME'))->table('cy_gametype')->where('id',$item['type'])->value('name') ? : '未知';
					$item['gameUrl'] = getGameUrl($item['id'],2);
					$item['libaonum'] = model('Gift')->where(['isdelete'=>0,'gameid'=>$item['id'],'endtime'=>['gt',NOW_TIMESTAMP]])->where('total > used')->count('id');
					return $item;
            });

			return ['list'=>$list,'page'=>$list->render()];

    }

    public function _getNewsList($keyword) {

        $newsList = (new NewsModel())->cache(Config::get('QUERY_RESULT_CACHE_TIME'))
				->alias('a')
                ->join('cy_content b', 'a.id = b.id','left')
                ->field('a.id,a.title,a.image,a.jlink,a.create_time,a.total,a.zhiding,a.type,a.gameid,b.content')
                ->where(['a.isdelete' => 0,'a.type' => ['in',[1,2,3,4,5]]])
                ->where('a.title','like','%'. $keyword .'%')
                ->order('a.zhiding desc,a.id desc')
                ->paginate(10, false, array('path'=>'javascript:AjaxPage([PAGE]);',))         
				->each(function ($item, $key){
					$item['image'] = getNewsImage($item['image']);
					$item['url'] = getMobileNewsUrl($item['id'],$item['jlink']);
					$item['createtime'] = date('Y-m-d H:i:s',$item['create_time']);
					$item['cutcontent'] = getCutcontent($item['content']);
					return $item;
            });

		return ['list'=>$newsList,'page'=>$newsList->render(),'keyword'=>$keyword];
    }
}