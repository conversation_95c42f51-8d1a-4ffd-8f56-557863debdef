<?php

include APP_PATH. 'home/common.php';

function getMobileGameUrl($id) {
	if(trim($id)){
		return MOBILE_SITE_DOMAIN.'/game'.$id.'.html';
	//	return url('game/detail',array('id'=>$id,'type'=>'index'));
	}
	else{
		return '';
	}
}

function getMobileNewsIndexUrl($type='') {
	if($type==1){
		return MOBILE_SITE_DOMAIN.'/news/xyzx/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==2){
		return MOBILE_SITE_DOMAIN.'/news/lyzx/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==3){
		return MOBILE_SITE_DOMAIN.'/news/xypc/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==4){
	    return MOBILE_SITE_DOMAIN.'/news/xygl/';
	//	return url('news/index',array('type'=>$type));
    }
	else if($type==5){
		return MOBILE_SITE_DOMAIN.'/news/jcsp/';
	//	return url('news/index',array('type'=>$type));
    }
	else {
		return MOBILE_SITE_DOMAIN.'/news/';
	//	return url('news/index');
    }
}
function getMobileNewsUrl($id,$redirecturl='') {
    if ( empty($redirecturl) ) {
	//	return url('news/detail',array('id'=>$id));
        return MOBILE_SITE_DOMAIN.'/news/'.$id.'.html';
    } else {
        return $redirecturl;
    }
}
function getMobileGameNewsUrl($id) {
	if(intval($id)){
		return MOBILE_SITE_DOMAIN.'/game'.$id.'/news/';
	//	return url('game/detail',array('id'=>$id,'type'=>'news'));
	}
	else{
		return '';
	}
}
function getMobileGameGiftUrl($id) {
	if(intval($id)){
		return MOBILE_SITE_DOMAIN.'/game'.$id.'/gift/';
	//	return url('game/detail',array('id'=>$id,'type'=>'gift'));
	}
	else{
		return '';
	}
}
function getMobileTypeGame($type=0) {
	return MOBILE_SITE_DOMAIN.'/game/type='.intval($type);
//	return url('game/index',array('type'=>intval($type)));
}
function getMobileSubjectGame($subject=0) {
	return MOBILE_SITE_DOMAIN.'/game/subject='.intval($subject);
//	return url('game/index',array('subject'=>intval($subject)));
}
/**
 * 获取礼包发号模块url
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getMobileGiftUrl($type,$id='',$redirecturl='') {
    if ( empty($redirecturl) ) {
        if (empty($id)){
            return MOBILE_SITE_DOMAIN.'/gift/'.$type.'/';
        }
        return MOBILE_SITE_DOMAIN.'/gift/'.$id.'.html';
//		return url('gift/detail',array('id'=>$id));
    } else {
        return $redirecturl;
    }
}
function getMobileGameLanmu($type='hot') {
	if($type=='hot'){
		return MOBILE_SITE_DOMAIN.'/tj/rmyx/';
	//	return url('game/gameList',array('type'=>$type));
    }
	else if($type=='new'){
		return MOBILE_SITE_DOMAIN.'/tj/zxyx/';
	//	return url('game/gameList',array('type'=>$type));
    }
	else if($type=='recommend'){
		return MOBILE_SITE_DOMAIN.'/tj/bztj/';
	//	return url('game/gameList',array('type'=>$type));
    }
	else {
		return MOBILE_SITE_DOMAIN.'/tj/rmyx/';
	//	return url('game/gameList',array('type'=>'hot'));
    }
}
// 开服开测
function getMobileServerUrl($type) {
    // 开服
    if ( $type == 'kf') {
        return MOBILE_SITE_DOMAIN.'/kf/';
	//	return url('Service/index');
    }
    // 开测
    if ( $type == 'kc') {
        return MOBILE_SITE_DOMAIN.'/kc/';
	//	return url('Service/kc');
    }

    return '';
}
function getGameTypeIcon($name='') {
	if($name=='角色'){
		$typeIcon = 'role.png';
	}
	else if($name=='动作'){
		$typeIcon = 'action.png';
	}
	else if($name=='休闲'){
		$typeIcon = 'casual.png';
	}
	else if($name=='体育'){
		$typeIcon = 'sports.png';
	}
	else if($name=='策略'){
		$typeIcon = 'strategy.png';
	}
	else if($name=='射击'){
		$typeIcon = 'shoot.png';
	}
	else if($name=='回合'){
		$typeIcon = 'round.png';
	}
	else if($name=='卡牌'){
		$typeIcon = 'cards.png';
	}
	else if($name=='放置'){
		$typeIcon = 'place.png';
	}
	else if($name=='竞技'){
		$typeIcon = 'athletics.png';
	}
	else{
		$typeIcon = 'role.png';
	}
	return $typeIcon;
}

// 其他地址
function getMobileOtherUrl($action){
    return MOBILE_SITE_DOMAIN.'/'.$action.'.html';
}


/**
 * 设置 【个人中心--账号安全】 模块 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getMobileAccountUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/changepwd/';
        }elseif ($type == 2){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/bind/selc/mobile';
		//	return url('member/toBindOne',array('type'=>'mobile'));
        }elseif ($type == 2.1){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/bind/validate/mobile/';
        }elseif ($type == 2.2){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/bindpage/';
        }elseif ($type == 3){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/bind/selc/email/';
        }elseif ($type == 3.1){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/bind/validate/email/';
        }elseif ($type == 3.2){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/bindpage/';
        }elseif ($type == 4){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/realname/';
        }
    } else {
        return $redirecturl;
    }
}
/**
 * 设置 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getMobileEncodeUrl($name,$suffix=1,$redirecturl='') {
    if ( empty($redirecturl) ) {
		/*if($name=='login'){
			return url('Mobile/member/login');
		}*/
        if ($suffix == 1){
            return MOBILE_SITE_DOMAIN.'/'.$name.'/';
        }elseif ($suffix == 2){
            return MOBILE_SITE_DOMAIN.'/'.$name.'.html';
        }

    } else {
        return $redirecturl;
    }
}

function getMobileSearchUrl() {
//	return MOBILE_SITE_DOMAIN.'/search/index';
	return url('Search/index');
}

// 客服路由
function getMobileCscUrl($type = ''){
	switch ($type) {
		case 'zhss':
			return MOBILE_SITE_DOMAIN.'/kefu/zhss.html';
		case 'sscx':
			return MOBILE_SITE_DOMAIN.'/kefu/sscx.html';
		case 'wxss':
			return MOBILE_SITE_DOMAIN.'/kefu/wxss.html';
        case 'feedback':
            return MOBILE_SITE_DOMAIN.'/feedback/';
		default:
			return MOBILE_SITE_DOMAIN.'/kefu.html';
	}

}

// 申诉路由
function getMobileAppealUrl($type=''){

	switch ($type) {
		case 'check':
			return MOBILE_SITE_DOMAIN.'/kefu/zhss/check.html';
		case 'user':
			return MOBILE_SITE_DOMAIN.'/kefu/zhss/user.html';
		case 'history':
			return MOBILE_SITE_DOMAIN.'/kefu/zhss/history.html';
		case 'result':
			return MOBILE_SITE_DOMAIN.'/kefu/zhss/result.html';
		default:
			return MOBILE_SITE_DOMAIN.'/kefu/zhss.html';
	}
}

/**
 * 设置 【忘记密码】 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getMobileForgetPwdUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return MOBILE_SITE_DOMAIN.'/forgotpwd.html';
        }elseif ($type == 2){
            return MOBILE_SITE_DOMAIN.'/forgotpwd/check.html';
        }elseif ($type == 3){
            return MOBILE_SITE_DOMAIN.'/forgotpwd/resetpwd.html';
        }elseif ($type == 4.1){
            return MOBILE_SITE_DOMAIN.'/forgotpwd/result1.html';
        }elseif ($type == 4.2){
            return MOBILE_SITE_DOMAIN.'/forgotpwd/result2.html';
        }elseif ($type == 2.1){
            return MOBILE_SITE_DOMAIN.'/forgotpwd/check/mobile.html';
        }elseif ($type == 2.2){
            return MOBILE_SITE_DOMAIN.'/forgotpwd/check/email.html';
        }
    } else {
        return $redirecturl;
    }
}
/**
 * 设置 【个人中心】 模块 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getMobileUserCenterUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return MOBILE_SITE_DOMAIN.'/user/userinfo/';
        }elseif ($type == 1.1){
            return MOBILE_SITE_DOMAIN.'/user/userinfo/qq.html';
        }elseif ($type == 1.2){
            return MOBILE_SITE_DOMAIN.'/user/userinfo/code.html';
        }elseif ($type == 1.3){
            return MOBILE_SITE_DOMAIN.'/user/userinfo/address.html';
        }elseif ($type == 2){
            return MOBILE_SITE_DOMAIN.'/user/rechargeinfo/';
        }elseif ($type == 2.1){
            return MOBILE_SITE_DOMAIN.'/user/rechargeinfo/detail/';
        }elseif ($type == 3){
            return MOBILE_SITE_DOMAIN.'/user/giftbox/';
        }elseif ($type == 3.1){
            return MOBILE_SITE_DOMAIN.'/user/giftbox/gq';
        }elseif ($type == 4){
            return MOBILE_SITE_DOMAIN.'/user/zhaq/';
        }elseif ($type == 'nickname'){
            return MOBILE_SITE_DOMAIN.'/user/userinfo/nickname.html';
        }
        elseif ($type == 'mygame'){
            return MOBILE_SITE_DOMAIN.'/user/mygame/';
        }
    } else {
        return $redirecturl;
    }
}
/**
 * 设置 【客服中心】 模块 路由
 * @param        $id
 * @param string $redirecturl
 * @return string
 */
function getMobileServiceUrl($type,$redirecturl='') {
    if ( empty($redirecturl) ) {
        if ($type == 1){
            return MOBILE_SITE_DOMAIN.'/kefu/cjwt/';
        }elseif ($type == 2){
            return MOBILE_SITE_DOMAIN.'/kefu/zhss/';
        }elseif ($type == 3){
            return MOBILE_SITE_DOMAIN.'/kefu/sscx/';
        }elseif ($type == 3.1){
            return MOBILE_SITE_DOMAIN.'/kefu/sscx/result/';
        }elseif ($type == 3.2){
            return MOBILE_SITE_DOMAIN.'/kefu/sscx/resetpwd/';
        }elseif ($type == 4){
            return MOBILE_SITE_DOMAIN.'/kefu/wxss/';
        }
    } else {
        return $redirecturl;
    }
}

/**
 * 获取iOS游戏下载地址
 * @return string
 */
function getIosDownload($game) {
    // 游戏的下载链接做特殊处理
	$gameInfo = model('Game')->field('id,name,nickname,game_kind,is_default,download_url')->where(['id'=>$game['id']])->find();
	if($gameInfo['is_default']){
		return $gameInfo['download_url'];
	}
    if ($game['filename']) {
        return APK_DOWN_DOMAIN.'/sygame/'.$game['pinyin'].'/'.str_replace(".ipa",".plist",$game['filename']);
    } else {
        return '';
    }
}