{extend name="layout/base" /}

{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<div>
			<a href="javascript:;" onclick="window.location.replace('{:getMobileServerUrl('kf')}')" {eq name='kfkc' value='kf'} class="active" {/eq}>
				<h2 {eq name='kfkc' value='kf'} class="active type" {/eq} class="type" >开服</h2>
			</a>
			<a href="javascript:;" onclick="window.location.replace('{:getMobileServerUrl('kc')}')" {eq name='kfkc' value='kc'} class="active" {/eq}>
				<h2 {eq name='kfkc' value='kc'} class="active" {/eq}>开测</h2>
			</a>
		</div>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->

	{empty name="list"}
	<!-- 无相关礼包 -->
	<div class="no_content" style="margin-top: 60%;">
		<div>
			<img src="/static/images/mobile/icon/no-gift.png">
			<p>暂无{if condition="$kfkc == 'kf'"}开服{else /}开测{/if}信息</p>
		</div>
	</div>
	<!-- 无相关礼包end-->

	{else/}
	
<div class="warp-service">

	<div class="game-list">
    {foreach name="list" item="vo" key="k" }
		{if condition="count($vo) > 0"}
		<div class="service-date">
			<img src="__STATIC__/images/mobile/icon/date.png">
            {switch name="$k"}
				{case value="td"}<p>今日{if condition="$kfkc == 'kf'"}开服{else /}开测{/if}<span>{$today}</span></p>{/case}
				{case value="tm"}<p>明日{if condition="$kfkc == 'kf'"}开服{else /}开测{/if}<span>{$tomorrow}</span></p>{/case}
				{case value="fn"}<p>长期有效</p>{/case}
				{default /}<p>{$k}</p>
            {/switch}
		</div>

		{foreach $vo as $item}
		<div class="game-info">
			<!-- have gift -->
			{if condition='$item.libaonum > 0'}<a href=""><img src="__STATIC__/images/mobile/icon/gift.png" class="gift"></a>{/if}
			<a href="{:getMobileGameUrl($item['gameid'])}">
				<img src="{$item.mobileicon}">
				<div class="content">
					<h4 class="title">{$item.nickname}</h4>
					<p>{$item.typename}<span>|</span>{$item.subjectname}<span>|</span>{$item.size}</p>
					<P class="highlight">
                    {switch name="$k"}
                    {case value="td"}今日{:date('H:i',$item.sertime)}{/case}
                    {case value="tm"}明日{:date('H:i',$item.sertime)}{/case}
                    {case value="fn"}长期有效{/case}
                    {default /}{:date('m-d H:i',$item.sertime)}
                    {/switch}
					 <span>|</span> {if condition="$kfkc == 'kf'"}{$item.sername}{else /}{$item.serstatusName}{/if}</P>
				</div>
			</a>
            {eq name="$item.subscribe" value="1"}
				{if condition="$kfkc == 'kf'"}
				<div class="order"><a href="javascript:kf_tip('{$item.id}','kf');">预约</a></div>
				{else /}
				<div class="order"><a href="javascript:kf_tip('{$item.id}','kc');">预约</a></div>
				{/if}
			{/eq}
		</div>
		{/foreach}
        {/if}
    {/foreach}
	</div>

</div>

{/empty}
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>

{include file="layout/footer" /}
{/block}
{block name="detail_js"}
<script>
	// 加载更多
	var currentPage = parseInt("{$listobj->currentPage()}");
	var lastPage = parseInt("{$listobj->lastPage()}");
	var total = parseInt("{$listobj->total()}");
	var url =  window.location.href;
	var defaultImg = "__STATIC__/images/icon/150-150.png";

	var currentTag = 'td';
	{foreach name="list" item="vo" key="k" }
    {switch name="$k"}
		{case value="td"} currentTag = 'td'; {/case}
		{case value="tm"} currentTag = 'tm'; {/case}
		{case value="fn"} currentTag = 'fn'; {/case}
		{default /} currentTag = '{$k}';
    {/switch}
	{/foreach}

	$(".no-more-data").hide();
	var loading = false; //状态标记
//	alert(url);
	$(document.body).infinite(90).on("infinite", function() {
	//	alert(total);
		if (loading) return;
		loading = true;
	//	alert(currentPage);
		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}
		$(".weui-loadmore").show();
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
				type: "POST",
				timeOut: 10000,
				url: url,
				data: {
					"page": currentPage
				},
				async: false,
				success: function(res) {
				//	console.log(res);
					var arr = res.data;
					console.log(arr);
					$.each(arr,function(key,item){ 
						html = '';
						if(item.length){
							if(!(currentTag && currentTag==key)){
								html += '<div class="service-date">'+
										'<img src="__STATIC__/images/mobile/icon/date.png">';
								if(key=='td'){
									currentTag = 'td';
									html += '<p>今日{if condition="$kfkc == 'kf'"}开服{else /}开测{/if}<span>{$today}</span></p>';
								}
								else if(key=='tm'){
									currentTag = 'tm';
									html += '<p>明日{if condition="$kfkc == 'kf'"}开服{else /}开测{/if}<span>{$tomorrow}</span></p>';
								}
								else if(key=='fn'){
									currentTag = 'fn';
									html += '<p>长期有效</p>';
								}
								else{
									currentTag = key;
									html += '<p>'+key+'</p>';
								}
								html += '</div>';
							}
							item.forEach(function(val, index) {
								var gameUrl = '{:getMobileGameUrl("'+val.gameid+'")}';
								html += '<div class="game-info">';
								if(val.libaonum > 0){
									html += '<a href=""><img src="__STATIC__/images/mobile/icon/gift.png" class="gift"></a>';
								}
								html += '<a href="'+gameUrl+'">'+
										'<img src="'+val.mobileicon+'">'+
										'<div class="content">'+
										'<h4 class="title">'+val.nickname+'</h4>'+
										'<p>'+val.typename+'<span>|</span>'+val.subjectname+'<span>|</span>'+val.size+'</p>'+
										'<P class="highlight">';
								if(key=='td'){
									html += '今日'+val.hsTime;
								}
								else if(key=='tm'){
									html += '明日'+val.hsTime;
								}
								else if(key=='fn'){
									html += '长期有效';
								}
								else{
									html += key;
								}
								html += '<span>|</span>'
								{if condition="$kfkc == 'kf'"}
									html += val.sername;
								{else /}
									html += val.serstatusName;
								{/if}
								html += '</P></div></a>';
								{if condition="$kfkc == 'kf'"}
									if(val.subscribe==1){
										html += "<div class='order'><a href=\"javascript:kf_tip("+val.id+",'kf');\">预约</a></div>";
									}
								{else /}
									if(val.subscribe==1){
										html += "<div class='order'><a href=\"javascript:kf_tip("+val.id+",'kc');\">预约</a></div>";
									}
								{/if}
								html += '</div>';
							});
							$(".game-list").append(html);
						}

					})
				},
				error: function() {
					$.alert("网络错误，请刷新页面重试");
				}
			});
			$.getScript("/static/js/mobile/reload.js");
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});


// 预约弹窗
function kf_tip(id,kfkc){
	$.ajax({
	   type: "POST",
	   timeOut: 10000,
	   url: '/service/subscribe',
	   data: {
		   "id": id,
		   "type": kfkc
	   },
	   async: false,
	   success: function (res) {
		   // console.log(res);
		   if (res.code == 1 || res.code == 4 || res.code == 5) {
			   $.modal({
				  title: "提示",
				  text: res.msg,
				  buttons: [{
						  text: "确定",
						  className: "pop-button",
						  onClick: function() {}
					  },
					  {
						  text: "关闭",
						  className: "default",
						  onClick: function() {}
					  },
				  ]
			   });
		   }
		   else if (res.code == 2){
			   window.location.href = res.data;
		   }
		   else if (res.code == 3){
				$.modal({
					title: "提示",
					text: res.msg,
					buttons: [{
							text: "去绑定",
							onClick: function() {
						   // 跳转链接
								window.location.href = res.data;
							}
						},
						{
							text: "取消",
							className: "default",
							onClick: function() {}
						},
					]
				});
		   }else {
			   alert(res.msg);
		   }
	   },
	   error: function () {
		   layer.msg('网络错误，请刷新页面重试');
	   }
	});
}
/*
	$(".order").on("click", function() {

		$.modal({
		    title: "提示",
		    text: "请先绑定手机号",
		    buttons: [{
		            text: "去绑定",
		            onClick: function() {
		           // 跳转链接
		            }
		        },
		        {
		            text: "取消",
		            className: "default",
		            onClick: function() {}
		        },
		    ]
		});

	// $.modal({
	// 	title: "提示",
	// 	text: "离开测时间不足1小时，无法预约",
	// 	buttons: [{
	// 			text: "确定",
	// 			className: "pop-button",
	// 			onClick: function() {}
	// 		},
	// 		{
	// 			text: "关闭",
	// 			className: "default",
	// 			onClick: function() {}
	// 		},
	// 	]
	// });

		// $.modal({
		// 	title: "提示",
		// 	text: "您已成功预约过该开测信息",
		// 	buttons: [{
		// 			text: "确定",
		// 			className: "pop-button",
		// 			onClick: function() {}
		// 		},
		// 		{
		// 			text: "关闭",
		// 			className: "default",
		// 			onClick: function() {}
		// 		},
		// 	]
		// });

		// $.modal({
		// 	title: "提示",
		// 	text: "您已成功预约该开测信息，到时会以短信形式通知，请留意",
		// 	buttons: [{
		// 			text: "确定",
		// 			className: "pop-button",
		// 			onClick: function() {}
		// 		},
		// 		{
		// 			text: "关闭",
		// 			className: "default",
		// 			onClick: function() {}
		// 		},
		// 	]
		// });

	})
	*/
</script>
{/block}
</body>
</html>
