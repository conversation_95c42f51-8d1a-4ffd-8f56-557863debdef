{extend name="layout/base" /}
{block name="title"}
<title>{$info.title}_{$info.title}领取_{$info.nickname}礼包_麻花网络礼包中心</title>
<meta name="keywords" content="{$info.title},{$info.title}领取,{$info.nickname}礼包"/>
<meta name="description" content="麻花网络礼包中心为大家提供{$info.title}领取，包含{$info.content}，更多精彩{$info.nickname}礼包，请关注麻花网络礼包中心！"/>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>礼包详细</h1>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->
<div class="top-half-1">
	<div class="game-info">
		<a href="javascript:;">
			<img lazy-src="{$info.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon"/>
			<div class="content" style="width: 1.6rem;margin-left: 0.1rem;">
				<h4 id="gift_title" title="{$info.title}">{$info.title}</h4>
				<div class="gift_progress">
					<span>剩余</span>
					<div class="warp_gift_surplus" style="width: 0.9rem;">
						<div class="gift_surplus" style="width:{$info.percent} ;"></div>
					</div>
					<span>{$info.percent}</span>
				</div>
			</div>
		</a>
		<div class="receive" data-id="{$info.id}">
			<a href="javascript:;">领取礼包</a>
		</div>
	</div>
</div>

<div class="game-gift-detail">
	<h4><span></span>有效时间</h4>
	<p>{:date('Y-m-d H:i',$info.starttime)} 至 {:date('Y-m-d H:i',$info.endtime)}</p>
</div>

<div class="game-gift-detail">
	<h4><span></span>礼包内容</h4>
	<p>{$info.content}</p>
</div>

<div class="game-gift-detail">
	<h4><span></span>使用说明</h4>
	<p>下载“<a href="{$info.game_url}">{$info.nickname}</a>”游戏后，在游戏内输入礼包码兑换</p>
</div>

<div class="game-gift-detail">
	<h4><span></span>相关礼包</h4>

	{empty name="libaoList"}
	<div class="no-info">
		  <img src="__STATIC__/images/mobile/icon/empty.png">
		  <p>相关礼包正在加紧补货中...</p>
	</div>
	{else/}
	<ul>
		{volist name="libaoList" id="vo"}
		<li><a href="{:getGiftUrl('',$vo.id)}" title="{$vo.title}">{$vo.title}</a></li>
		{/volist}
	</ul>
	{/empty}
</div>

<div class="relate-game">
		<a href="{$info.game_url}">
			<img lazy-src="{$info.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon"  />
			<div class="content">
				<h4>{$info.nickname}</h4>
				<p>{$info.typename}<span>|</span>{$info.subjectname}<span>|</span>{$info.size}</p>
			</div>
		</a>
		<div class="download">
		    <a href="javascript:;">下载</a>
		    <p class="android_down_url">{$info.download}</p>
		    <p class="ios_down_url">{$info.ios_download}</p>
		</div>
		<img src="__STATIC__/images/mobile/icon/close-white.png" class="close" />
</div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>
	
	if($(".relate-game").length > 0){
	    $(".footer").css("padding-bottom","0.75rem")
		$(".relate-game .close").click(function(){
			 $('.relate-game').fadeOut(500);
			setTimeout(function () {
					$(".footer").css("padding-bottom","0.25rem")
			    }, 500);
		})
	}
	
	$(".receive").on("click", function() {
		var id = $(".receive").data('id');
		var title = $("#gift_title").attr('title');
        $.ajax({
            type: "POST",
            timeOut: 10000,
            url: '/gift/receiveGift',
            data: {
                "id": id
            },
            async: false,
            success: function (res) {
                if (res.code == 1) {
                    $.modal({
                        title: "礼包码",
                        text: title+"<br>礼包码:<span id='gift_code'>"+res.data+"</span>",
                        buttons: [{
                            text: "复制",
                            className: "pop-button",
                            onClick: function() {
                                var code = document.getElementById("gift_code").innerText;
                                var oInput = document.createElement('input');
								oInput.readOnly=true;
                                oInput.value = code;
                                document.body.appendChild(oInput);
                                oInput.select(); // 选择对象
                                document.execCommand("Copy"); // 执行浏览器复制命令
                                oInput.className = 'oInput';
                                oInput.style.display = 'none';
                                $.toast("复制成功","text");
                            }
                        },
                            {
                                text: "关闭",
                                className: "default",
                                onClick: function() {}
                            },
                        ]
                    });
                } else if (res.code == 2) {
                    $.modal({
                        title: "礼包码",
                        text: "您已领取过该礼包，礼包码如下：<br>"+title+"<br>礼包码:<span id='gift_code'>"+res.data+"</span>",
                        buttons: [{
                            text: "复制",
                            className: "pop-button",
                            onClick: function () {
                                var code = document.getElementById("gift_code").innerText;
                                var oInput = document.createElement('input');
								oInput.readOnly=true;
                                oInput.value = code;
                                document.body.appendChild(oInput);
                                oInput.select(); // 选择对象
                                document.execCommand("Copy"); // 执行浏览器复制命令
                                oInput.className = 'oInput';
                                oInput.style.display = 'none';
                                $.toast("复制成功", "text");
                            }
                        },
                            {
                                text: "关闭",
                                className: "default",
                                onClick: function () {
                                }
                            },
                        ]
                    });
                } else if (res.code == -1 || res.code == -2) {
				   $.modal({
					   title: "提示",
					   text: res.msg,
					   buttons: [{
							   text: "确定",
							   className: "pop-button",
							   onClick: function() {}
						   },
						   {
							   text: "关闭",
							   className: "default",
							   onClick: function() {}
						   },
					   ]
				   });
                } else if (res.code == -3) {
                    window.location.href = res.data;
                } else {
                    alert(res.msg);
                }
            },
            error: function () {
                layer.msg('网络错误，请刷新页面重试');
            }
        });
	  
	  });
</script>
{/block}
