{extend name="layout/base" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<style>
	.weui-loadmore{
		background: #F9F9F9;
	}
	.weui-loadmore {
		display: none;
		width: 100%;
		margin: 0;
		padding: 1.5em 0 0;
	}
</style>
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>{$detail.nickname|default="游戏详情页"}</h1>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->

<div class="top-half game-content">
	<img  lazy-src="{$detail.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon">
	<h2>{$detail.nickname}</h2>
	<p>{$detail.typename}<span>|</span>{$detail.subjectname}<span>|</span>{$detail.size}</p>
	{if($detail['publicity'])}<p>{$detail.publicity}</p> {/if}
	<div class="download">
		<a href="javascript:;">下载</a>
		<p class="android_down_url">{$detail.download}</p>
		<p class="ios_down_url">{$detail.ios_download}</p>
	</div>
</div>

<ul class="game-info-title">
	<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameUrl($detail['id'])}')">介绍</a></li>
	<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameNewsUrl($detail['id'])}')">资讯</a></li>
	<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameGiftUrl($detail['id'])}')" class="active">礼包</a></li>
</ul>

<!-- 游戏资讯 -->
{notempty name="$list"}
<div class="info-gift">
	<p style="    margin-top: 0.16rem;">相关礼包<span>{$totalGiftCnt}</span>个</p>
	{volist name="list" id="vo"}
	<div class="gift-info">
		<div class="content">
			<a href="{:getMobileGiftUrl('',$vo['id'])}">
			<h4>{$vo.title}</h4>
			<div class="gift_progress">
				<span>剩余</span>
				<div class="warp_gift_surplus">
					<div class="gift_surplus" style="width:{$vo.percent};"></div>
				</div>
				<span>{$vo.percent}</span>
			</div>
			<P>{$vo.content}</P>
			</a>
		</div>
		<div class="receive">
			<a href="{:getMobileGiftUrl('',$vo['id'])}">领取</a>
		</div>
	</div>
	{/volist}
	
</div>
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍后</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>
{else/}
<div class="no-info">
	<img src="__STATIC__/images/mobile/icon/award.png">
	<p>暂无相关礼包</p>
</div>
{/notempty}

<!-- 相关游戏 -->
<div class="relate-game">
		<a href="{:getMobileGameUrl($detail['id'])}" style="float: left;">
			<img lazy-src="{$detail.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon"  />
			<div class="content">
				<h4>{$detail.nickname}</h4>
				<p>{$detail.typename}<span>|</span>{$detail.subjectname}<span>|</span>{$detail.size}</p>
			</div>
		</a>
		<div class="download" style="float: left;margin-top: 0.08rem;">
		    <a href="javascript:;">下载</a>
		    <p class="android_down_url">{$detail.download}</p>
		    <p class="ios_down_url">{$detail.ios_download}</p>
		</div>
		<img src="__STATIC__/images/mobile/icon/close-white.png" class="close" />
</div>
{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	//头部固定		
	var hT = $('.game-info-title').offset().top,
		hh = $('header').height();
	$(window).scroll(function() {
		var wS = $(this).scrollTop();
		if (wS >= hT - hh) {
			// $('.relate-game').css("display","flex");
			if($(".relate-game").hasClass("hide")){
				
			}else{
				$(".relate-game").fadeIn(500);
				$('.game-info-title').addClass("fixed-top");
			}
			
		} else {
			$('.game-info-title').removeClass("fixed-top");
			$(".relate-game").fadeOut(500);
		}
	});
	
	
	$(".close").click(function(){
		$(".relate-game").fadeOut(500);
		setTimeout(function(){
			$(".relate-game").addClass("hide");
		})
	
	})
	
	
	// 加载更多
	var currentPage = parseInt("{$list->currentPage()}");
	var lastPage = parseInt("{$list->lastPage()}");
	var total = parseInt("{$list->total()}");
	var url = "{:getMobileGameGiftUrl($detail['id'])}";
	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;

		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}
		$(".weui-loadmore").show();
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
				type: "POST",
				timeOut: 10000,
				url: url,
				data: {
					"page": currentPage
				},
				async: false,
				success: function(res) {
					console.log(res);
					var arr = res.data.data;
					arr.forEach(function(val, index) {
						var giftUrl = '{:getMobileGiftUrl("'+val.id+'")}';
						$(".info-gift").append(
							'<div class="gift-info">'+
								'<div class="content">'+
									'<h4>'+val.title+'</h4>'+
									'<div class="gift_progress">'+
										'<span>剩余</span>'+
										'<div class="warp_gift_surplus">'+
											'<div class="gift_surplus" style="width:'+val.percent+';"></div>'+
										'</div>'+
										'<span>'+val.percent+'</span>'+
									'</div>'+
									'<P>'+val.content+'</P>'+
								'</div>'+
								'<div class="receive">'+
									'<a href="'+giftUrl+'">领取</a>'+
								'</div>'+
							'</div>'
						);
					});
				},
				error: function() {
					$.alert("网络错误，请刷新页面重试");
				}
			});
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});
</script>


{/block}
