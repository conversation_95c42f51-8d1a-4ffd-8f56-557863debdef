{extend name="layout/base" /}
{block name="title"}
<title>{$seo.title}</title>
<meta name="keywords" content="{$seo.keywords}"/>
<meta name="description" content="{$seo.description}"/>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
<style>
	.game-info a {
		  display: flex;
		  width: 2.5rem;
		  height: 0.67rem;
	}
</style>
{/block}
{block name="content"}
		<!-- header start -->
		<header class="top">
			<div class="header">
				<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
				<h1>推荐游戏</h1>
				<div>
					<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
					<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
				</div>
			</div>
			<ul class="game_rec">
				<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameLanmu('hot')}')" {if(input('type')=='hot' || input('type')=='')} class="active"{/if}>热门游戏</a></li>
				<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameLanmu('new')}')" {if(input('type')=='new')} class="active"{/if}>最新游戏</a></li>
				<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameLanmu('recommend')}')"  {if(input('type')=='recommend')} class="active"{/if}>本周推荐</a></li>
			</ul>
		</header>
		<!-- header end -->
	
		<div class="game-list">
			{volist name="game_list" id="vo"}
			<div class="game-info">
				<div {if condition="$key lt 3"} class="number number-{$key+1}" {else/} class="number" {/if}>{$key+1}</div>
				<a href="{$vo.url}">
					<img lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />
					<div class="content">
						<h4>{$vo.nickname}</h4>
						<p>{$vo.typename}<span>|</span>{$vo.subjectname}<span>|</span>{$vo.size}</p>
						{if($vo['publicity'])}<P>{$vo.publicity}</P>{/if}
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url">{$vo.download}</p>
					<p class="ios_down_url">{$vo.ios_download}</p>
				</div>
			</div>
			{/volist}

		</div>
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>


{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>	
	// 加载更多
	var currentPage = parseInt("{$game_list->currentPage()}");
	var lastPage = parseInt("{$game_list->lastPage()}");
	var total = parseInt("{$game_list->total()}");
	var url = window.location.href;
	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;

		if (currentPage + 1 > lastPage || currentPage+1 > 5) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}
		$(".weui-loadmore").show();
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
				type: "POST",
				timeOut: 10000,
				url: url,
				data: {
					"page": currentPage
				},
				async: false,
				success: function(res) {
					console.log(res);
					var arr = res.data.data;
					arr.forEach(function(val, index) {
						var html='';
						var rankNum = parseInt((currentPage-1)*10+index+1);
						html += "<div class='game-info'>"+
							"<div class='number'>"+rankNum+"</div>"+
							"<a href='"+val.url+"'>"+
							"<img lazy-src='"+val.mobileicon+"'  src='__STATIC__/images/icon/150-150.png' class='smallIcon' />"+
							"<div class='content'><h4>"+val.nickname+"</h4><p>"+val.typename+"<span>|</span>"+val.subjectname+"<span>|</span>"+val.size+"</p>";
						if(val.publicity){
							html += "<P>"+val.publicity+"</P>";
						}
						html += "</div></a>"+
							"<div class='download'>"+
							"<a href='javascript:;'>下载</a>"+
							"<p class='android_down_url'>"+val.download+"</p>"+
							"<p class='ios_down_url'>"+val.ios_download+"</p></div></div>";
						$(".game-list").append(html);
					});
				},
				error: function() {
					$.alert("网络错误，请刷新页面重试");
				}
			});
			$.getScript("/static/js/mobile/reload.js");
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});
</script>

{/block}
