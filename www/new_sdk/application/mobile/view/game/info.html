{extend name="layout/base" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/swiper.min.css">
<style>
	.swiper-container{
	    visibility: hidden;
	}
</style>
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>{$detail.nickname|default="游戏详情页"}</h1>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->

<div class="top-half game-content">
	<img  lazy-src="{$detail.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon">
	<h2>{$detail.nickname}</h2>
	<p>{$detail.typename}<span>|</span>{$detail.subjectname}<span>|</span>{$detail.size}</p>
	{if($detail['publicity'])}<p>{$detail.publicity}</p> {/if}
	<div class="download">
		<a href="javascript:;">下载</a>
		<p class="android_down_url">{$detail.download}</p>
		<p class="ios_down_url">{$detail.ios_download}</p>
	</div>
</div>

<ul class="game-info-title">
	<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameUrl($detail['id'])}')" class="active">介绍</a></li>
	<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameNewsUrl($detail['id'])}')">资讯</a></li>
	<li><a href="javascript:;" onclick="window.location.replace('{:getMobileGameGiftUrl($detail['id'])}')">礼包</a></li>
</ul>
<!-- 游戏介绍 -->
<div class="warp-game-introduce">
{notempty name="$detail.description"}
<div class="game-introduce">
	<div>
		{$detail.description}
	</div>
</div>
<p class="open-close open"><a href="javascript:;">展开</a></p>
{/notempty}

{notempty name="$detail.screenShot"}
<!-- 小图 -->
<div class="warp-screenshots">
	<div>
<div class="screenshots">
	<?php $screenShot = $detail->screenShot; ?>
	{volist name="screenShot" id="bigimage"}
		<!-- <div class="{$key}"> -->
			<img lazy-src="{$bigimage}"  src="__STATIC__/images/icon/625-990.png" class="info-img" id="{$key}"/>
		<!-- </div> -->
	{/volist}
</div>
</div>
</div>

  <!-- Swiper 大图-->
  <div class="swiper-container">
    <div class="swiper-wrapper">
	{volist name="screenShot" id="bigimage"}
	  <div class="swiper-slide"><img src="{$bigimage}"/></div>
	{/volist}
    </div>
    <!-- Add Pagination -->
    <div class="swiper-pagination"></div>
  </div>
{/notempty}

{empty name="$detail.description" and empty name="$detail.screenShot"}
  <div class="no-info">
	  <img src="__STATIC__/images/mobile/icon/no-info.png">
	  <p>暂无相关内容</p>
  </div>
{/empty}
</div>

<!-- 开测 -->
	{notempty name="$kcServers"}
<div class="game-kc">
	<div class="title">
		<h4><span></span>游戏开测</h4>
		<p>共<span>{$kcServerCnt}</span>条</p>
	</div>

	<div class="kc-info">
		<ul class="tab-title">
		   <li>时间</li>
		   <li>新服名</li>
		   <li>预约</li>
		</ul>
		{volist name="kcServers" id="vo"}
		<ul class="kc-list">
		   <li>{$vo.serverDate} {$vo.serverTime}</li>
		   <li>{$vo.serverName}</li>
		   <li>{if condition='$vo.subscribe eq 1'}<a href="javascript:kf_tip('{$vo.id}','kc');">预约</a>{/if}</li>
		</ul>
		{/volist}
	</div>
	<a href="javascript:;" class="kc-more">查看更多</a>
	<div class="weui-loadmore">
		<i class="weui-loading"></i>
		<span class="weui-loadmore__tips">数据加载中，请稍后</span>
	</div>

	{/notempty}
</div>
<!-- 开服 -->
<div class="game-kf">
	<div class="title">
		<h4><span></span>游戏开服</h4>
		<p>共<span>{$kfServerCnt}</span>条</p>
	</div>
	{notempty name="$kfServers"}
	<div class="kf-info">
		<ul class="tab-title">
		   <li>时间</li>
		   <li>新服名</li>
		   <li>预约</li>
		</ul>
		{volist name="kfServers" id="vo"}
		<ul class="kf-list">
		   <li>{$vo.serverDate} {$vo.serverTime}</li>
		   <li>{$vo.serverName}</li>
		   <li>{if condition='$vo.subscribe eq 1'}<a href="javascript:kf_tip('{$vo.id}','kf');">预约</a>{/if}</li>
		</ul>
		{/volist}
	</div>
	<a href="javascript:;" class="kf-more">查看更多</a>
	<div class="weui-loadmore">
		<i class="weui-loading"></i>
		<span class="weui-loadmore__tips">数据加载中，请稍后</span>
	</div>
	{else/}
	<div class="no-info">
		  <img src="__STATIC__/images/mobile/icon/empty.png">
		  <p>暂无开服信息</p>
	</div>
	{/notempty}
	
</div>

<!-- 猜你喜欢 -->
{notempty name="$relGameList"}
 <div class="new-game">
        <div class="title">
            <h4><span></span>猜你喜欢</h4>
        </div>
        <div class="new-game-list">
			{volist name="relGameList" id="vo"}
            <div class="new-game-info">
                <a href="{$vo.url}">
                    <img lazy-src="{$vo.icon}"  src="__STATIC__/images/icon/150-150.png" class="smallIcon">
                    <h4>{$vo.nickname}</h4>
					<p>{$vo.size}</p>
                    <p>{$vo.typename}<span>|</span>{$vo.subjectname}</p>
                </a>
                <div class="download">
                    <a href="javascript:;">下载</a>
                    <p class="android_down_url">{$vo.download}</p>
                    <p class="ios_down_url">{$vo.ios_download}</p>
                </div>
            </div>
			{/volist}
        </div>
    </div>
{/notempty}

<!-- 相关信息 -->
<div class="rela-info">
    <div class="title">
		<h4><span></span>相关信息</h4>
	</div>
	<div class="content">
	<p>游戏版本：<span>{$detail.version}</span></p>
	<p>发布时间：<span>{$detail.updatetime}</span></p>
	<div class="manufacture">
		开发厂商：
	     <div>
			 <span>{$detail.developer}</span>
		 </div>
	</div>
	</div>
</div>
<!-- 相关游戏 -->
<div class="relate-game">
		<a href="{:getMobileGameUrl($detail['id'])}" style="float: left;">
			<img lazy-src="{$detail.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon"  />
			<div class="content">
				<h4>{$detail.nickname}</h4>
				<p>{$detail.typename}<span>|</span>{$detail.subjectname}<span>|</span>{$detail.size}</p>
			</div>
		</a>
		<div class="download" style="float: left;margin-top: 0.08rem;">
		    <a href="javascript:;">下载</a>
		    <p class="android_down_url">{$detail.download}</p>
		    <p class="ios_down_url">{$detail.ios_download}</p>
		</div>
		<img src="__STATIC__/images/mobile/icon/close-white.png" class="close" />
</div>
{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script src="__STATIC__/js/mobile/swiper.min.js"></script>
<script>
	setTimeout(function(){
		var w =$(".screenshots>img:last-child").attr('id');
		var maxh = w*1.21*****;
		console.log(w)
		console.log(maxh)
		$(".screenshots").width(maxh+'rem')	
	},500)
	
	
	// 游戏截图放大
	
	$(".info-img").error(function () {
	    $(this).attr("src", "/static/images/icon/625_990.png");
	})
    
	   var swiper = new Swiper('.swiper-container', {
	   	width: window.innerWidth,
	   	spaceBetween:20,
	      pagination: {
	       el: '.swiper-pagination',
	       type: 'fraction',
	   	clickable: true
	     }
	   });
		
		$(".swiper-container").hide();
		$(".swiper-pagination").hide();
		$(".screenshots>img").click(function(){
			var num = $(this).attr('id');
           swiper.slideTo(num, 1000, false);		
			$(".swiper-container").show();
			$(".swiper-pagination").show();
		   $("body").css("overflow-y","hidden");
			$(".swiper-container").css("visibility","inherit");
		})
		$(".swiper-container").click(function(){
			$(".swiper-container").hide();
			$(".swiper-pagination").hide();
			$("body").css("overflow-y","auto");
		})
	// 显示隐藏
	$(".game-introduce").each(function() {
		var rowNum = Math.round($(this).height() / parseFloat($(this).css('line-height')));
		if (rowNum > 3) {
			$(this).next().show();
			$(this).css("height", "0.6rem")
		}
	})

	$(".open-close").click(function() {
		var H = $(this).prev().find("div").height();
		if ($(this).hasClass("open")) {
			$(this).prev().animate({
				height: H
			}, 500)
			$(this).removeClass("open");
			$(this).html("隐藏");
		} else {
			$(this).prev().animate({
				height: '0.6rem'
			}, 500)
			$(this).addClass("open");
			$(this).html("展开");
		}
	})
	//头部固定		
	var hT = $('.game-info-title').offset().top,
		hh = $('header').height();
	$(window).scroll(function() {
		var wS = $(this).scrollTop();
		if (wS >= hT - hh) {
			// $('.relate-game').css("display","flex");
			if($(".relate-game").hasClass("hide")){
				
			}else{
				$(".relate-game").fadeIn(500);
				$('.game-info-title').addClass("fixed-top");
			}
			
		} else {
			$('.game-info-title').removeClass("fixed-top");
			$(".relate-game").fadeOut(500);
		}
	});
	
	
	$(".close").click(function(){
		$(".relate-game").fadeOut(500);
		setTimeout(function(){
			$(".relate-game").addClass("hide");
		})

	})
	// 点击开测的更多
	var currentPage = parseInt("{$kcServers->currentPage()}");
	var lastPage = parseInt("{$kcServers->lastPage()}");
	var total = parseInt("{$kcServers->total()}");
	if(total <= 5){
		$(".kc-more").hide();
	}
	var url = "{:getMobileGameUrl($detail['id'])}";
	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".kc-more").click(function(){
		$(".kc-more").hide();
		$(".game-kc .weui-loadmore").show();

		if (currentPage + 1 > lastPage) {
			$(".kc-more").hide();
			$(".game-kc .weui-loadmore").hide();
			return false;
		}
		$(".game-kc .weui-loadmore").show();

		currentPage += 1;
		$.ajax({
			type: "POST",
			timeOut: 10000,
			url: url,
			data: {
				"page": currentPage,
				"serverType": 1
			},
			async: false,
			success: function(res) {
				console.log(res);
				var arr = res.data.data;
				arr.forEach(function(val, index) {
					var html='';
					html +=	'<ul class="kc-list">'+
							  ' <li>'+val.serverDate+' '+val.serverTime+'</li>'+
							  ' <li>'+val.serverName+'</li>';
					if(val.subscribe == 1){
					//	html += '<li><a href="javascript:kf_tip('+val.id+',"kc");">预约</a></li>';
						html += "<li><a href=\"javascript:kf_tip("+val.id+",'kc');\">预约</a></li>";
					}
					html += '</ul>';
					$(".kc-info").append(html);
				});
			},
			error: function() {
				$.alert("网络错误，请刷新页面重试");
			}
		});
		$.getScript("/static/js/mobile/common.js");
		$(".game-kc .weui-loadmore").hide();

		if(currentPage  >= lastPage){
			$(".kc-more").hide();
		}
		else{
			$(".kc-more").show();
		}
		$(".game-kc .weui-loadmore").hide();	
	})

	// 点击开服更多
	var currentKfPage = parseInt("{$kfServers->currentPage()}");
	var lastKfPage = parseInt("{$kfServers->lastPage()}");
	var kfTotal = parseInt("{$kfServers->total()}");
	if(kfTotal <= 5){
		$(".kf-more").hide();
	}
	var kfUrl = "{:getMobileGameUrl($detail['id'])}";
//	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".kf-more").click(function(){
		$(".kf-more").hide();
		$(".game-kf .weui-loadmore").show();

		if (currentKfPage + 1 > lastKfPage) {
			$(".kf-more").hide();
			$(".game-kf .weui-loadmore").hide();
			return false;
		}
		$(".game-kf .weui-loadmore").show();

		currentKfPage += 1;
		$.ajax({
			type: "POST",
			timeOut: 10000,
			url: url,
			data: {
				"page": currentKfPage,
				"serverType": 0
			},
			async: false,
			success: function(res) {
				console.log(res);
				var arr = res.data.data;
				arr.forEach(function(val, index) {
					var html='';
					html +=	'<ul class="kf-list">'+
							  ' <li>'+val.serverDate+' '+val.serverTime+'</li>'+
							  ' <li>'+val.serverName+'</li>';
					if(val.subscribe == 1){
						html += "<li><a href=\"javascript:kf_tip("+val.id+",'kf');\">预约</a></li>";
					}
					html += '</ul>';
					$(".kf-info").append(html);
				});
			},
			error: function() {
				$.alert("网络错误，请刷新页面重试");
			}
		});
		$.getScript("/static/js/mobile/common.js");
		$(".game-kf .weui-loadmore").hide();

		if(currentKfPage  >= lastKfPage){
			$(".kf-more").hide();
		}
		else{
			$(".kf-more").show();
		}
		$(".game-kf .weui-loadmore").hide();	
	})

// 预约弹窗
function kf_tip(id,kfkc){
	$.ajax({
	   type: "POST",
	   timeOut: 10000,
	   url: '/service/subscribe',
	   data: {
		   "id": id,
		   "type": kfkc
	   },
	   async: false,
	   success: function (res) {
		   // console.log(res);
		   if (res.code == 1 || res.code == 4 || res.code == 5) {
			   $.modal({
				  title: "提示",
				  text: res.msg,
				  buttons: [{
						  text: "确定",
						  className: "pop-button",
						  onClick: function() {}
					  },
					  {
						  text: "关闭",
						  className: "default",
						  onClick: function() {}
					  },
				  ]
			   });
		   }
		   else if (res.code == 2){
			   window.location.href = res.data;
		   }
		   else if (res.code == 3){
				$.modal({
					title: "提示",
					text: res.msg,
					buttons: [{
							text: "去绑定",
							onClick: function() {
						   // 跳转链接
								window.location.href = res.data;
							}
						},
						{
							text: "取消",
							className: "default",
							onClick: function() {}
						},
					]
				});
		   }else {
			   alert(res.msg);
		   }
	   },
	   error: function () {
		   layer.msg('网络错误，请刷新页面重试');
	   }
	});
}


</script>


{/block}
