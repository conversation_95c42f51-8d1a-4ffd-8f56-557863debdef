{extend name="layout/base" /}
{block name="title"}<title>修改联系地址_个人资料_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<!-- header start -->
<header>
	<a href="{:getMobileUserCenterUrl(1)}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>联系地址</h1>
</header>

<!-- header end -->
<div class="login">
	<form class="form">
	     <textarea name="address" style="resize:none;" id="address">{$address}</textarea>
		<input type="button" value="保存" class="submit clickable" />
	</form>
</div>

{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
var address1 =$("#address").val();
  $('.submit').click(function(){
	var address =$("#address").val();
	if(address.length>40){
		 $.toast( "联系地址不能超过40个字", "text");
	}else if(address !=address1){
        $.ajax({
            type: 'POST',
            url: "/member/editInfo",
            dataType: 'json',
            data: {name:'address',value:address},
            success: function(res) {
                if (res.code){
                    $.toast("保存成功", "text");
                    setInterval(function() {
                        window.location.href = res.url
                    }, 1000);
                }else {
                    $.toast(res.msg, "text");
                }
            },
            error: function () {
                $.toast( "网络错误，请刷新页面重试", "text");
            }
        });
	  }
})


</script>
{/block}
