{extend name="layout/base" /}
{block name="title"}<title>修改密码_账号安全_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<header>
	<a href="{:getMobileUserCenterUrl('4')}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>修改密码</h1>
</header>
<form class="form reset-psw changepsw">
	<div class="warp-input">
		<p>旧密码</p>
		<input type="password" placeholder="请输入旧密码，6-15位字符" id="oldpassword" name="password" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	<div class="warp-input">
		<p>新密码</p>
		<input type="password" placeholder="请输入新密码，6-15位字符" id="newpassword"  name="password" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	<div class="warp-input">
		<p>确认密码</p>
		<input type="password" placeholder="请再次输入新密码" id="passwordone"  name="password" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	<input type="button" value="确认" class="submit clickable" />
</form>
{/block}


{block name="detail_js"}
<script>
	$(" #oldpassword,#newpassword,#passwordone").keyup(function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	
	})
	
	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})
	
	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("#oldpassword,#newpassword,#passwordone").on("postpaste", function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
			
	}).pasteEvents();
	
	
	$(".submit").click(function(){
		var oldpassword = $("#oldpassword").val();
		var newpassword = $("#newpassword").val();
		var passwordone = $("#passwordone").val();
		if(oldpassword==""){
			$.toast("请输入旧密码", "text");
		}else if(newpassword == ""){
			$.toast("请输入新密码,<br>6-15位字符", "text");
		}else if(newpassword.length<6 ||newpassword.length>15){
			$.toast("请输入正确的新密码,<br>6-15位字符", "text");
		}else if(passwordone==""){
			$.toast("请再次输入新密码", "text");
		}else if(newpassword != passwordone){
			$.toast("两次输入的密码不一致", "text");
		}else{
			// ajax 提交密码
            $.ajax({
                type: 'POST',
                url: "{:url('Mobile/member/personalChangePwd')}",
                dataType: 'json',
                data: {oldPwd:oldpassword,password1:newpassword,password2:passwordone},
                success: function(res) {
                    $.toast(res.msg, "text");
                    if(res.code == 1) {
                        setTimeout(function () {
                            window.location.href=res.url;
                        },3000);
                    }
                },
                error: function () {
                    $.toast("网络错误，请刷新页面重试", "text");
                }
            });
		}
	})
</script>
{/block}
