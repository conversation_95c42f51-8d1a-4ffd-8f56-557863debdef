{extend name="layout/base" /}
{block name="title"}<title>绑定{eq name="type" value="email"}邮箱{else /}手机{/eq}_账号安全_
麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>绑定{eq name="type" value="email"}邮箱{else /}手机{/eq}</h1>
</header>
<div class="verify-bind bind">
	<p>请输入要绑定的{eq name="type" value="email"}邮箱{else /}手机{/eq}，点击“发送验证码”，获取验证码进行验证</p>

	{switch name="type" }
	{case value="email" }
	<!-- 邮箱绑定 -->
	<div class="verify-mail">

		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/email.png">
			<input type="text" placeholder="请输入邮箱" id="email" class="bindnum"  name="email" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</div>

		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/code.png">
			<input type="number" placeholder="请输入短信验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送验证码">
		</div>
		<input type="button" value="确定" class="submit clickable" />
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
	</div>
	{/case}
	{case value="mobile"}

	<!-- 手机号绑定 -->
	<div class="verify-phone">
		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/phone.png">
			<input type="number" placeholder="请输入手机号" id="phonenum" class="bindnum"  name="phonenum" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</div>

		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/code.png">
			<input type="number" placeholder="请输入短信验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送验证码">
		</div>
		<input type="button" value="确定" class="submit clickable" />
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
	</div>
	{/case}
	{default /}
	{/switch}


</div>

{/block}


{block name="detail_js"}
<script>
	$(".sendcode").click(function() {

		if ($(".verify-mail").length > 0) {
			var email = $("#email").val();
			var preg = /^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i;
			if (email == "") {
				$.toast("请输入邮箱", "text");
			} else if (!preg.test(email)) {
				$.toast("请输入正确的邮箱", "text");
			} else {
				$("#TencentCaptcha").trigger("click");
			}
		} else {
			var phonenum = $("#phonenum").val();
			var preg = /^1[0123456789]{1}\d{9}$/;
			if (phonenum == "") {
				$.toast("请输入手机号", "text");
			} else if (!preg.test(phonenum)) {
				$.toast("请输入正确的手机号", "text");
			} else {
				$("#TencentCaptcha").trigger("click");
			}
		}
	});



	//拼图验证
	if ($(".verify-mail").length > 0) {
		$("header h1").html("绑定邮箱");
		var type = "email";
	} else {
		$("header h1").html("绑定手机");
		var type = "mobile";
	}
	var ticket = '';
	var randstr = '';
	window.callback = function(res) {
		if (res.ret === 0) {
			ticket = res.ticket;
			randstr = res.randstr;
		   var bindnum = $(".bindnum").val();
            $.ajax({
                type: 'POST',
                url: "/member/directbindsendcode",
                dataType: 'json',
                data: {ticket:ticket,randstr:randstr,contact:bindnum,type:type},
                success: function(res) {
                    $.toast(res.msg, "text");
                    if (res.code==1){
                        getcode();
                    }
                },
                error: function () {
                    $.toast("网络错误，请刷新页面重试", "text");
                }
            });
		}
	}

	// 倒计时方法
	function getcode() {
		var count = 60;
		$('.sendcode').addClass('hasSend');
		var index = setInterval(function() {
			if (count >= 0) {
				$('.sendcode').val(count + ' s');
				$('.sendcode').attr('disabled', true);

				count--;
			} else {
				$('.sendcode').removeClass('hasSend').val('发送验证码');
				$('.sendcode').attr('disabled', false);
				clearInterval(index);
			}
		}, 1000);
	}


	//邮箱绑定点击确认
	$(".verify-mail .submit").click(function() {
		var getcode = $(".verify-mail #getcode").val();
		if (getcode == "") {
			$.toast("请输入验证码", "text");
		} else {
			// ajax 判断
            var bindnum = $(".bindnum").val();
            bingContact(getcode,bindnum,type)
		}
	})


	// 手机号绑定点击确认
	$(".verify-phone .submit").click(function() {
		var getcode = $(".verify-phone #getcode").val();
		if (getcode == "") {
			$.toast("请输入验证码", "text");
		} else {
			// ajax 判断
            var bindnum = $(".bindnum").val();
            bingContact(getcode,bindnum,type)
		}
	})


	function bingContact(code,bindphone,type) {
        // 判断验证码是否正确
        $.ajax({
            type: 'POST',
            url: "/member/directbindcontact",
            dataType: 'json',
            data: {code:code,contact:bindphone,type:type},
            success: function(res) {
                // console.log(res)
                if (res.code){
                    $.toast("绑定成功", "text");
                    setTimeout(function () {
                        window.location.href=res.url;
                    },3000);
                }else {
                    $.toast(res.msg, "text");
                }
            },
            error: function () {
                $.toast("网络错误，请刷新页面重试", "text");
            }
        });
    }
</script>
{/block}
