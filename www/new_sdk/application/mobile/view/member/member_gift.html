{extend name="layout/base" /}
{block name="title"}
<title>存号箱未过期_麻花网络个人中心</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
<style>
	.warp_search_form{
      position: fixed;
	  height: 84.5%;
	  top: 15.5%;
	  background: rgb(0,0,0,0.7);
	  display: none;
	  overflow: hidden;
	}
	.search_form button {
	    width: 3.55rem;
	    height: 0.5rem;
	    background: #FFA42D;
	    border: none;
	    border-radius: 0.25rem;
	    color: #fff;
	    font-size: 0.18rem;
	}
	
	.search_form {
		position: relative;
		top: -1.7rem;
	    background: #fff;
	    width: 100%;
	    padding: 10px;
	}
	.search_form h4 {
	    line-height: 0.3rem;
	}
	.search_form input {
	    width: 3.35rem;
	    height: 0.24rem;
	    border: none;
	    background: #F9F9F9;
	    border-radius: 0.05rem;
	    padding: 0.1rem;
	    margin-bottom: 0.13rem;
	}
</style>
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header gift-box">
		<a href="{:getMobileEncodeUrl('user')}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>存号箱</h1>
		<div class="search_icon">
		<img src="__STATIC__/images/mobile/icon/search.png" style="width: 0.2rem;" id="gift_search">
		<img src="__STATIC__/images/mobile/icon/close.png" style="width: 0.2rem;display: none;" id="search_close">
		</div>
	</div>
	<div class="warp_search_form">
	<form class="search_form" action="{eq name='type' value='now'}{:getMobileUserCenterUrl(3)}{else /}{:getMobileUserCenterUrl(3.1)}{/eq}" method="post">
		<h4>礼包名</h4>
		<input id="keyword" name="keyword" placeholder="请输入礼包名" value="{$keyword}" autocomplete="off"/>
		<button>查询</button>
	</form>
	</div>
	
	<ul class="game_rec">
		<li><a href="javascript:;"  onclick="noexpired()"   class=" {eq name='type' value='now'} active {/eq}">未过期</a></li>
		<li><a href="javascript:;"  onclick="expired()" class="{eq name='type' value='overtime'} active {/eq}">已过期</a></li>
	</ul>
</header>
<!-- header end -->
{notempty name="$list"}
<div class="gift-box-list">
	<p>共<span>{$list->total()}</span>款礼包</p>

	{volist name="list" id="vo"}
	<div class="boxinfo">
		<div class="gift-info">
			<img lazy-src="{$vo['mobileicon']}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />
			<div class="content">
				<h4>{$vo.title}</h4>
				<p>有效期至：{$vo.endtime}</p>
				<P>{$vo.content}</P>
			</div>
		</div>
		<div class="giftcode">
			<div>
				<p>礼包码:</p>
				<div class="code">{$vo.code}</div>
			</div>
			{eq name='type' value='now'} <a class="copy">复制</a> {/eq}
		</div>
	</div>
	{/volist}

</div>

<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>
{/notempty}
{empty name="list"}
<!-- 无相关礼包 -->
<div class="no_content" style="margin-top: 140px !important;height: 4rem;">
	<div>
		<img src="/static/images/mobile/icon/no-gift.png">
		<p>
			暂无{eq name='type' value='now'}未{/eq}过期的礼包
		</p>
		<a href="/gift/index">立即领取礼包</a>
	</div>
</div>
<!-- 无相关礼包end-->
{/empty}

<!-- footer start -->
<footer class="footer">
    <div class="copyright">
        <p><a href="http://beian.miit.gov.cn">福建麻花网络科技有限公司版权所有</a></p>
        <p><a href="http://beian.miit.gov.cn">闽ICP备18008231号-2</a></p>
    </div>
</footer>
<!-- footer end -->
{/block}
{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>
	// 搜索弹窗
	$(".search_icon").click(function(){
		if ($(".warp_search_form").css("display") == "none") {
			$(".warp_search_form").show();
			$(".search_form").stop(true, false).animate({
				top: "0rem"
			}, 400)
			$("#search_close").show();
			$("#gift_search").hide();

		} else {
			setTimeout(function(){
				$(".warp_search_form").hide();
			},350);
			$(".search_form").stop(true, false).animate({
				top: "-2.45rem"
			}, 400);
			$("#search_close").hide();
			$("#gift_search").show();

		}
	})
	$(".warp_search_form").click(function(e) {
		var target = $(e.target);
		if (target.closest(".search_form").length != 0) return;
	$(".warp_search_form").hide();
	$("#search_close").hide();
	$("#gift_search").show();

	
	})
	// 分页
    var currentPage = parseInt("{$list->currentPage()}");
    var lastPage = parseInt("{$list->lastPage()}");
    var total = parseInt("{$list->total()}");
    var url = window.location.href;

	$(".copy").click(function() {
		var giftcode = $(this).parent(".giftcode").find(".code").html();
		var oInput = document.createElement('input');
		oInput.value = giftcode;
		document.body.appendChild(oInput);
		oInput.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
		oInput.className = 'oInput';
		oInput.style.display = 'none';
		$.toast("复制成功", "text");
	})

	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;
        if (currentPage + 1 > lastPage) {
            $(".weui-loadmore").hide();
            if (currentPage > 1) {
                $(".no-more-data").show();
            }
            loading = false;
            return false;
        }

		$(".weui-loadmore").show();
		setTimeout(function() {
            currentPage += 1;
            var keyword = $("#keyword").val();
            $.ajax({
                type: "POST",
                timeOut: 10000,
                url: url,
                data: {
                    "page": currentPage,
					"keyword": keyword
                },
                async: false,
                success: function(res) {
                    var arr = res.data.data;
                    arr.forEach(function(val, index) {
                        var str = // 加载的数据
                            '<div class="boxinfo">'+
                            '<div class="gift-info">'+
                            '<img lazy-src="'+val.mobileicon+'" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />'+
                            '<div class="content">'+
                            '<h4>'+val.title+'</h4>'+
                            '<p>有效期至：'+val.endtime+'</p>'+
                            '<P>'+val.content+'</P>'+
                            '</div>'+
                            '</div>'+
                            '<div class="giftcode">'+
                            '<div>'+
                            '<p>礼包码:</p>'+
                            '<div class="code">'+val.code+'</div>'+
                            '</div>';

                        if (res.msg == 'now'){
                            str += '<a class="copy">复制</a>';
                        }

                        str += '</div>'+
                            '</div>';
                        $(".gift-box-list").append(str)
                    });
                },
                error: function() {
                    $.toast("网络错误，请刷新页面重试", "text");
                }
            });

			$.getScript("/static/js/mobile/reload.js");
			$(".copy").click(function() {
				var giftcode = $(this).parent(".giftcode").find(".code").html();
				var oInput = document.createElement('input');
				oInput.value = giftcode;
				document.body.appendChild(oInput);
				oInput.select(); // 选择对象
				document.execCommand("Copy"); // 执行浏览器复制命令
				oInput.className = 'oInput';
				oInput.style.display = 'none';
				$.toast("复制成功", "text");
			})
			$(".weui-loadmore").hide();
			loading = false;
		}, 1500); //模拟延迟
	});
	
	if ($(".no_content").length > 0) {
		var head = $("header").height();
		var footer = $("footer").height();
		var H = window.screen.height;
		var p = $("footer").css("padding")
		p = p.replace("px", "")
		$("footer").css("background","#fff")
		$(".no_content").css("margin-top",head)
		$(".no_content").height(H - head - footer - p - p);
	}
	function noexpired(){
		var keyword =$("#keyword").val();
		window.location.href="/user/giftbox?keyword="+keyword;
	}
	function expired(){
		var keyword =$("#keyword").val();
		window.location.href="/user/giftbox/gq?keyword="+keyword;
	}
</script>
{/block}
