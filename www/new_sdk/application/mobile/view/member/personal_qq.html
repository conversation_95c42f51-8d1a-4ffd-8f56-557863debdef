{extend name="layout/base" /}
{block name="title"}<title>修改QQ_个人资料_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<!-- header start -->
<header>
	<a href="{:getMobileUserCenterUrl(1)}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>QQ</h1>
</header>

<!-- header end -->
<div class="login">
	<form class="form">
		<div class="warp-input">
			<input type="number" placeholder="请输入QQ" id="qqnum" name="qqnum" autocomplete="off" value="{$qq}">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
			<span></span>
		</div>
		<input type="button" value="保存" class="submit clickable" />
	</form>
</div>


{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	
		
	if($("#qqnum").val() != "" ){
		$(".cancel").show();
	}
	
	
	$("#qqnum").focus(function() {
		$(this).next().next().animate({
			width: "100%"
		}, 500)
	});
	
	$("#qqnum").blur(function() {
		$(this).next().next().animate({
			width: "0"
		}, 500)
	});
	
	$("#qqnum").keyup(function() {
		var qqnum = $("#qqnum").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})
	
	
	// 粘贴事件监控
	$.fn.pasteEvents = function( delay ) {
	    if (delay == undefined) delay = 10;
	    return $(this).each(function() {
	        var $el = $(this);
	        $el.on("paste", function() {
	            $el.trigger("prepaste");
	            setTimeout(function() { $el.trigger("postpaste"); }, delay);
	        });
	    });
	};
	// 使用
	$("#qqnum").on("postpaste", function() { 
	    var qqnum = $("#qqnum").val();
	    var text = $(this).val();
	    if (text != "") {
	    	$(this).next().show();
	    } else {
	    	$(this).next().hide();
	    }
	}).pasteEvents();
	
	var qqnum1 = $("#qqnum").val();
	
	$(".submit").click(function() {
		
     var qqnum = $("#qqnum").val();
	 var reg = /^[1-9][0-9]{4,10}$/;
	 
if(qqnum !="" && !reg.test(qqnum)){
	 $.toast("请输入正确的QQ号", "text");
}else{
	// ajax保存  和跳转链接
	$.ajax({
	    type: 'POST',
	    url: "/member/editInfo",
	    dataType: 'json',
	    data: {name:'qq',value:qqnum},
	    success: function(res) {
					 console.log(res);
	        if (res.code==1){
	            $.toast("保存成功", "text");
	            setInterval(function() {
	                window.location.href = res.url
	            }, 1000);
	        }else {
	            $.toast(res.msg, "text");
	        }
	    },
	    error: function () {
	        $.toast( "网络错误，请刷新页面重试", "text");
	    }
	});
}
 
	})
	

</script>
{/block}
