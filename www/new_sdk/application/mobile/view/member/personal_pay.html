{extend name="layout/base" /}
{block name="title"}<title>消费记录_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">

<style>
	body {
		background: #F9F9F9;
	}

	header {

		display: block;
		padding: inherit;
		height: auto;
	}

	.header h1 {
		margin-inline-end: 0.4rem !important;
	}

	.warp_search_form {
		position: fixed;
		height: 84.5%;
		top: 15.5%;
		background: rgb(0, 0, 0, 0.7);
		display: none;
		overflow: hidden;
	}

	.search_form button {
		width: 3.55rem;
		height: 0.5rem;
		background: #FFA42D;
		border: none;
		border-radius: 0.25rem;
		color: #fff;
		font-size: 0.18rem;
	}

	.search_form {
		position: relative;
		top: -2.45rem;
		background: #fff;
		width: 100%;
		padding: 0.1rem;
	}

	.search_form h4 {
		line-height: 0.3rem;
		font-size: 0.14rem;
	}

	.search_form input {
		width: 3.35rem;
		height: 0.24rem;
		border: none;
		background: #F9F9F9;
		border-radius: 0.05rem;
		padding: 0.1rem;
		margin-bottom: 0.13rem;
	}

	.ordertime {
		width: 2.15rem;
		float: left;
	}

	.ordertime input {
		width: 0.79rem;
	}

	.game-name {
		width: 1.4rem;
		float: left;
	}

	.game-name input {
		width: 1rem;
	}

	.orderid {
		font-size: 0.14rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 0.32rem;
		padding: 0 0.1rem;
		border-bottom: 0.01rem solid #EEEEEE;
	}

	.orderid img {
		width: 0.12rem;
		margin-top: 0.05rem;
	}

	.game-cont {
		display: flex;
		width: 2.5rem;
		height: 0.67rem;
	}

	.order-list {
		background: #fff;
		margin: 0 0.1rem 0.1rem;
		border-radius: 0.05rem;
	}

	.pay-price {
		color: #FF9A1C;
		font-size: 0.24rem;
	}

	.pay-price span {
		font-size: 0.15rem;
	}

	.total {
		color: #BCBCBC;
		font-size: 0.14rem;
		padding-left: 0.1rem;
		line-height: 0.3rem;
	}

	.total span {
		color: #FF9A1C;
	}
</style>
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">

		<a href="{:getMobileEncodeUrl('user')}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>充值记录</h1>
		<div class="search_icon">
			<img src="__STATIC__/images/mobile/icon/search.png" style="width: 0.2rem;" id="gift_search">
			<img src="__STATIC__/images/mobile/icon/close.png" style="width: 0.2rem;display: none;" id="search_close">
		</div>
	</div>

	<div class="warp_search_form">
		<form class="search_form" action="{:getMobileUserCenterUrl(2)}{$status}" method="post">
			<h4>订单号</h4>
			<input id="orderid" name="orderid" placeholder="请输入完整订单号" value="{$Think.request.orderid}" autocomplete="off"/>
			<div class="game-name">
				<h4>游戏名</h4>
				<input id="gamename" name="gamename" placeholder="请输入游戏名" value="{$Think.request.gamename}" autocomplete="off"/>
			</div>
			<div class="ordertime">
				<h4>下单时间</h4>
				<input id="startdate" name="start" placeholder="开始时间" value="{$Think.request.start}" autocomplete="off" readonly/>
				<span>-</span>
				<input id="enddate" name="end" placeholder="结束时间" value="{$Think.request.end}" autocomplete="off" readonly/>
			</div>
			<button>查询</button>
		</form>
	</div>

	<ul class="game_rec">
		<li><a href="javascript:;" onclick="allorder()" {eq name="$status" value=""} class="active" {/eq}>全部</a></li>
		<li><a href="javascript:;" onclick="success()" {eq name="$status" value="1"} class="active" {/eq}>支付成功</a></li>
		<li><a href="javascript:;" onclick="fail()" {eq name="$status" value="2"} class="active" {/eq}>支付失败</a></li>
		<li><a href="javascript:;" onclick="unpaid()" {eq name="$status" value="0"} class="active" {/eq}>未支付</a></li>
	</ul>
</header>

{empty name="list"}
<!-- 无相关充值记录 -->
 <div class="no_content" style="border-top: 0.1rem solid #f9f9f9;">
		<div style="    margin-top: 1.8rem;">
			<img src="/static/images/mobile/icon/no-gift.png">
			<p>暂无充值记录</p>
		</div>
	 </div>
<!-- 无相关充值记录end-->
{else/}

<div class="game-list">
	<p class="total">共<span>{$list->total()}</span>条记录</p>
{volist name="list" id="vo"}
	<div class="order-list" onclick='location.href=("{:getMobileUserCenterUrl(2.1)}{$vo.id}")'>
		<div class="orderid">
			<p>订单号：{$vo.orderid}</p>
			<div>
				<span>{$vo.pay_status}</span>
				<img src="__STATIC__/images/mobile/icon/right.png" />
			</div>
		</div>
		<div class="game-info">
			<div class="game-cont">
				<img lazy-src="{$vo.icon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />
				<div class="content">
					<h4>{$vo.nickname}</h4>
					<p>{$vo.productname}</p>
					<P>{$vo.create_time}</P>
				</div>
			</div>
			<div class="pay-price">
				<p><span>￥</span>{$vo.amount}</p>
			</div>
		</div>
	</div>
{/volist}
	
</div>

{/empty}


<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>


{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script src="__STATIC__/js/mobile/datePicker.js"></script>
<script>
	// 搜索弹窗
	$(".search_icon").click(function() {
		if ($(".warp_search_form").css("display") == "none") {
			$(".warp_search_form").show();
			$(".search_form").stop(true, false).animate({
				top: "0rem"
			}, 400)
			$("#search_close").show();
			$("#gift_search").hide();

		} else {
			setTimeout(function(){
				$(".warp_search_form").hide();
			},350);
			$(".search_form").stop(true, false).animate({
				top: "-2.45rem"
			}, 400);
			$("#search_close").hide();
			$("#gift_search").show();

		}
	})
	$(".warp_search_form").click(function(e) {
		var target = $(e.target);
		if (target.closest(".search_form").length != 0) return;
		$(".warp_search_form").hide();
		$("#search_close").hide();
		$("#gift_search").show();


	})

	$("#startdate").click(function() {
		setTimeout(function() {
			$(".date_ctrl>.date_btn").html("开始时间")
		})

	})
	$("#enddate").click(function() {
		setTimeout(function() {
			$(".date_ctrl>.date_btn").html("结束时间")
		})

	})
	
	var calendar = new datePicker();
	var myDate = new Date;
	var year = myDate.getFullYear();
	var month = myDate.getMonth() + 1;
	var date = myDate.getDate();
	calendar.init({
		'trigger': '#startdate',
		/*按钮选择器，用于触发弹出插件*/
		'type': 'date',
		/*模式：date日期；datetime日期时间；time时间；ym年月；*/
		'minDate': '1900-1-1',
		/*最小日期*/
		'maxDate': year + '-' + month + '-' + date,
		/*最大日期*/
		'onSubmit': function(theSelectData) {

			setTimeout(function() {
				var startTime = $("#stratdate").val();
				var start = new Date(startTime.replace("-", "/").replace("-", "/"));
				var endTime = $("#enddate").val();
				var end = new Date(endTime.replace("-", "/").replace("-", "/"));
				console.log(12332411)
				if (end < start) {
					$.toast("开始时间不能大于结束时间", "text");
					console.log(1111)

				}
			});

		},
		'onClose': function(theSelectData) {

		}
	});

	var calendar2 = new datePicker();
	calendar2.init({
		'trigger': '#enddate',
		/*按钮选择器，用于触发弹出插件*/
		'type': 'date',
		/*模式：date日期；datetime日期时间；time时间；ym年月；*/
		'minDate': '1900-1-1',
		/*最小日期*/
		'maxDate': year + '-' + month + '-' + date,
		/*最大日期*/
		'onSubmit': function(theSelectData) {

		},
		'onClose': function(theSelectData) {},
	});





	var currentPage = parseInt("{$list->currentPage()}");
	var lastPage = parseInt("{$list->lastPage()}");
	var total = parseInt("{$list->total()}");
	var url = $(".active").data('url');
	var detailUrl = "{:getMobileUserCenterUrl(2.1)}";

	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;
		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}

		$(".weui-loadmore").show();
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
				type: "POST",
				timeOut: 10000,
				url: url,
				data: {
					"page": currentPage
				},
				async: false,
				success: function(res) {
				    if (!res.code) return;

					var arr = res.data.data;
					arr.forEach(function(vo, index) {
						var durl = detailUrl+vo.id;

						$(".game-list").append(
							// 加载的数据
							"<div class='order-list' >"
							+'<a href='+durl+'>'
							+'<div class="orderid">'
							+'<p>订单号：'+vo.orderid+'</p>'
							+'<div>'
							+'<span>'+vo.pay_status+'</span>'
							+'<img src="__STATIC__/images/mobile/icon/right.png" />'
							+'</div>'
							+'</div>'
							+'<div class="game-info">'
							+'<div class="game-cont">'
							+'<img lazy-src="'+vo.icon+'" src="__STATIC__/images/icon/150-150.png" class="smallIcon" />'
							+'<div class="content">'
							+'<h4>'+vo.nickname+'</h4>'
							+'<p>'+vo.productname+'</p>'
							+'<P>'+vo.create_time+'</P>'
							+'</div>'
							+'</div>'
							+'<div class="pay-price">'
							+'<p><span>￥</span>'+vo.amount+'</p>'
							+'</div>'
							+'</div>'
							+'</a>'
							+'</div>'
						)
					});
				},
				error: function() {
					$.toast("网络错误，请刷新页面重试", "text");
				}
			});

			$.getScript("/static/js/mobile/reload.js");
			$(".copy").click(function(){
			   var payid =	$(this).parent(".payinfo").find(".payid").html();
			   var oInput = document.createElement('input');
			   oInput.readOnly=true;
			   oInput.value = payid;
			   document.body.appendChild(oInput);
			   oInput.select(); // 选择对象
			   document.execCommand("Copy"); // 执行浏览器复制命令
			   oInput.className = 'oInput';
			   oInput.style.display = 'none';
			   $.toast("订单号复制成功", "text");
			})
			
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});

	if ($(".no_content").length > 0) {
		var head = $("header").height();
		var footer = $("footer").height();
		var H = window.screen.height;
		var p = $("footer").css("padding")
		p = p.replace("px", "")
		$("footer").css("background", "#fff")
		$(".no_content").css("margin-top", head)
		$(".no_content").height(H - head - footer - p - p);
	}
	
	function allorder(){
	    var orderid = $("#orderid").val();
		var gamename = $("#gamename").val();
		var startdate =$("#startdate").val();
		var enddate =$("#enddate").val();
		window.location.href="/user/rechargeinfo?orderid="+orderid+"&gamename="+gamename+"&start="+startdate+"&end="+enddate; 
	}
	
	function success(){
	    var orderid = $("#orderid").val();
		var gamename = $("#gamename").val();
		var startdate =$("#startdate").val();
		var enddate =$("#enddate").val();
		window.location.href="/user/rechargeinfo/1?orderid="+orderid+"&gamename="+gamename+"&start="+startdate+"&end="+enddate; 
	}
	function fail(){
	    var orderid =$("#orderid").val();
		var gamename = $("#gamename").val();
		var startdate =$("#startdate").val();
		var enddate =$("#enddate").val();
		window.location.href="/user/rechargeinfo/2?orderid="+orderid+"&gamename="+gamename+"&start="+startdate+"&end="+enddate; 
	}
	function unpaid(){
	    var orderid =$("#orderid").val();
		var gamename = $("#gamename").val();
		var startdate =$("#startdate").val();
		var enddate =$("#enddate").val();
		window.location.href="/user/rechargeinfo/0?orderid="+orderid+"&gamename="+gamename+"&start="+startdate+"&end="+enddate; 
	}
	
	
</script>

{/block}
