{extend name="layout/base" /}
{block name="title"}<title>绑定{eq name="info.type" value="email"}邮箱{else /}手机{/eq}_账号安全_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<!--<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>-->
{/block}

{block name="content"}
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>安全验证</h1>
</header>
<div class="verify-bind">
    <p>点击“发送验证码”，获取验证码进行验证</p>

	{switch name="info.velidate_type" }
	{case value="email" }
	<!-- 邮箱验证 -->
	<div class="verify-mail">
		<div>
			<p>绑定邮箱：<span>{:mailObfuscation($info.info_data.email)} </span></p>
		</div>
		<div class="warp-input">
			<p>验证码：</p>
			<input type="number" placeholder="请输入短信验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送验证码">
			<span></span>
		</div>
		<input type="button" value="下一步" class="submit clickable" />
		<!--<input type="button" id="TencentCaptcha" data-appid="" data-cbfn="callback" style="display: none;" />-->
	</div>
	{/case}
	{case value="mobile"}
	<!-- 手机号验证 -->
	<div class="verify-phone">
		<div>
			<p>绑定手机：<span>{:mobileObfuscation($info.info_data.mobile)}</span></p>
		</div>
		<div class="warp-input">
			<p>验证码：</p>
			<input type="number" placeholder="请输入短信验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送验证码">
			<span></span>
		</div>
		<input type="button" value="下一步" class="submit clickable" />
		<!--<input type="button" id="TencentCaptcha" data-appid="" data-cbfn="callback" style="display: none;" />-->
	</div>
	{/case}
	{default /}
	{/switch}
	
</div>

{/block}


{block name="detail_js"}
<script>
	if ($(".verify-mail").length > 0) {
	    var type = "email";
	} else {
	    var type = "phone";
	}
	
	$(".sendcode").click(function(){
		
		var count = 60;
        $.ajax({
            type: 'POST',
            url: "{:url('toVelidateSendCode')}",
            dataType: 'json',
            data: { type: type },
            success: function (json) {
                if (json.code == 1) {
                    $.toast("验证码发送成功", "text");
                    $('.sendcode').addClass('hasSend');
                    var index = setInterval(function() {
                        if (count >= 0) {
                            $('.sendcode').val(count + ' s');
                            $('.sendcode').attr('disabled', true);

                            count--;
                        } else {
                            $('.sendcode').removeClass('hasSend').val('发送验证码');
                            $('.sendcode').attr('disabled', false);
                            clearInterval(index);
                        }
                    }, 1000);
                } else {
                    $.toast(json.msg, "text");
                }
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });
	});
	
	//邮箱验证点击下一步
	$(".verify-mail .submit").click(function(){
		var getcode = $(".verify-mail #getcode").val();
		if(getcode==""){
			$.toast("请输入验证码", "text");
		}else{
			// ajax 判断
            next(getcode)
		}
	})
	
	
	// 手机号验证点击下一步
	$(".verify-phone .submit").click(function(){
		var getcode = $(".verify-phone #getcode").val();
		if(getcode==""){
			$.toast("请输入验证码", "text");
		}else{
			// ajax 判断
            next(getcode)
		}
	})

    function next(code) {
        $.ajax({
            type: 'POST',
            url: "{:url('velidateCode')}",
            dataType: 'json',
            data: { type: type, code: code },
            success: function (json) {
                if (json.code == 1) {
                    window.location.href = json.url;
                } else {
                    $.toast(json.msg, "text");
                }
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });
    }
</script>
{/block}
