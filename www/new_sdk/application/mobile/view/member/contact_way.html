{extend name="layout/base" /}
{block name="title"}<title>忘记密码_麻花网络</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>安全验证</h1>
</header>
<div class="verify-way">
    <p>为了您的账号安全，请先验证身份</p>	
	<div>
		<a href="javascript:;">
		<img src="__STATIC__/images/mobile/icon/phone.png">
		<span>手机验证</span>
		</a>
		<a href="{empty name="$info.mobile"} javascript:; {else /} {:getMobileForgetPwdUrl('2.1')} {/empty}">

		<span>
		{empty name="$info.mobile"} 暂无绑定，无法使用手机找回 {else /} {$info.mobile} {/empty}
		</span>
		<img src="__STATIC__/images/mobile/icon/right-gray.png">
		</a>
	</div>
	
	<div>
		<a href="javascript:;">
		<img src="__STATIC__/images/mobile/icon/email.png">
		<span>邮箱验证</span>
		</a>
		<a href="{empty name="$info.email"} javascript:; {else /} {:getMobileForgetPwdUrl('2.2')} {/empty}">
		<span>
		{empty name="$info.email"} 暂无绑定，无法使用邮箱找回 {else /} {$info.email} {/empty}
		</span>
		<img src="__STATIC__/images/mobile/icon/right-gray.png">
		</a>
	</div>
	<a href="{:getMobileCscUrl('zhss')}">密保项不能使用？</a>
</div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
{/block}
