{extend name="layout/base" /}
{block name="title"}
<title>账号安全_麻花网络个人中心</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<!-- header start -->
<header style="background: #fff;">
	<a href="{:getMobileEncodeUrl('user')}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>账号安全</h1>
</header>
<ul class="personal-info">
	<li>
		<a href="{:getMobileAccountUrl(1)}">
			<p>登录密码</p>
			<p>
			<span>修改</span>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li>
		<a  href="{:getMobileAccountUrl(2)}">
			<p>绑定手机{notempty name="$info.mobile"}<span>{$info.mobile}</span>{/notempty}</p>
			<p>
				<span>{empty name="$info.mobile"}绑定{else /}换绑{/empty}</span>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li >
		<a  href="{:getMobileAccountUrl(3)}">
			<p>绑定邮箱{notempty name="$info.email"}<span>{$info.email}</span>{/notempty}</p>
			<p>
				<span>{empty name="$info.email"}绑定{else /}换绑{/empty}</span>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
	<li>
		<a  href="{:getMobileAccountUrl(4)}">
			<p>实名认证{eq name="$info.isVerified" value="1"}<span>已实名</span>{/eq}</p>
			<p>
				<span>{eq name="$info.isVerified" value="1"}查看{else /}去认证{/eq}</span>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</p>
		</a>
	</li>
</ul>



{/block}

{include file="layout/footer" /}
{/block}

{block name="detail_js"}

{/block}

