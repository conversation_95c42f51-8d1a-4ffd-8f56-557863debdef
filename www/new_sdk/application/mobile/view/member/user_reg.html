{extend name="layout/base" /}
{block name="title"}<title>用户注册_麻花网络</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<!-- header start -->
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>手机号注册</h1>
</header>

<!-- header end -->
<div style="width: 100%;overflow: hidden;">
<div class="reg-content">
	<!-- phone reg start -->
	<div class="phone-reg">
		<form class="form ">
			<div class="warp-input">
				<img src="__STATIC__/images/mobile/icon/phone.png">
				<input type="number" placeholder="请输入手机号" id="phonenum" name="phonenum" autocomplete="off">
				<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
				<span></span>
			</div>
			<div class="warp-input">
				<img src="__STATIC__/images/mobile/icon/password.png">
				<input type="password" placeholder="请输入密码，6-15位字符" id="password" name="password" autocomplete="off">
				<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
				<img src="__STATIC__/images/mobile/icon/hide.png" class="look">
				<span></span>
			</div>
			<div class="warp-input">
				<img src="__STATIC__/images/mobile/icon/code.png">
				<input type="number" placeholder="请输入短信验证码" id="getcode" name="getcode" autocomplete="off">
				<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
				<input class="sendcode" type="button" value="发送验证码">
				<span></span>
			</div>

			<p>注册即代表同意<a href="{:getMobileOtherUrl('syxy')}">《麻花网络络服务使用协议》</a></p>
			<input type="button" value="注册" class="submit" />
		</form>
		<div class="reg_bottom">
			<a href="javascript:;" class="user">用户名注册</a>
			<a href="{:getMobileEncodeUrl('login')}">返回登录</a>
		</div>
	</div>

	<!-- phone reg end-->
	<!-- user reg start -->
	<div class="user-reg">
		<form class="form ">
			<div class="warp-input">
				<img src="__STATIC__/images/mobile/icon/user.png">
				<input type="text" placeholder="请输入用户名，6-11位字母或数字" id="username" name="username" autocomplete="off">
				<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
				<span></span>
			</div>
			<div class="warp-input">
				<img src="__STATIC__/images/mobile/icon/password.png">
				<input type="password" placeholder="请输入密码，6-15位字符" id="password" name="password" autocomplete="off">
				<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
				<img src="__STATIC__/images/mobile/icon/hide.png" class="look">
				<span></span>
			</div>
			<p>注册即代表同意<a href="{:getMobileOtherUrl('syxy')}">《麻花网络络服务使用协议》</a></p>
			<input type="button" value="注册" class="submit" />
		</form>
		<div class="reg_bottom">
			<a href="javascript:;" class="phone">手机号注册</a>
			<a href="{:getMobileEncodeUrl('login')}">返回登录</a>
		</div>
	</div>
	<!-- user reg end -->
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
</div>
</div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>

	$("#username,#password,#phonenum,#getcode").focus(function() {
		$(this).nextAll("span").animate({
			width: "100%"
		}, 500)
	});

	$("#username,#password,#phonenum,#getcode").blur(function() {
		$(this).nextAll("span").animate({
			width: "0"
		}, 500)
	});

	$("#username,.user-reg #password,.phone-reg #password,#phonenum,#getcode").keyup(function() {
		var username = $("#username").val();
		var password = $(".user-reg #password").val();
		var phonenum = $("#phonenum").val();
		var password1 = $(".phone-reg #password").val();
		var getcode = $("#getcode").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
		if (username != "" && password != "") {
			$(".user-reg .submit").addClass("clickable");
		} else {
			$(".user-reg .submit").removeClass("clickable");
		}
		if (phonenum != "" && password1 != "" && getcode != "") {
			$(".phone-reg .submit").addClass("clickable");
		} else {
			$(".phone-reg .submit").removeClass("clickable");
		}

	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
		$(this).parent(".warp-input").nextAll(".submit").removeClass("clickable");
		// $(".submit").removeClass("clickable");
	})

	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("#username,.user-reg #password,.phone-reg #password,#phonenum,#getcode").on("postpaste", function() {
		var username = $("#username").val();
		var password = $(".user-reg #password").val();
		var phonenum = $("#phonenum").val();
		var password1 = $(".phone-reg #password").val();
		var getcode = $("#getcode").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
		if (username != "" && password != "") {
			$(".user-reg .submit").addClass("clickable");
		} else {
			$(".user-reg .submit").removeClass("clickable");
		}
		if (phonenum != "" && password1 != "" && getcode != "") {
			$(".phone-reg .submit").addClass("clickable");
		} else {
			$(".phone-reg .submit").removeClass("clickable");
		}
	}).pasteEvents();

	$(".look").click(function() {
		if ($(this).hasClass("key")) {
			$(this).removeClass("key");
			$(this).prevAll("#password").attr('type', 'password');
			$(this).attr("src", "/static/images/mobile/icon/hide.png");
		} else {
			$(this).prevAll("#password").attr('type', 'text');
			$(this).attr("src", "/static/images/mobile/icon/eye.png");
			$(this).addClass("key");
		}
	})

	// 用户名和注册名切换
	$('.reg_bottom .user').click(function() {
		
		$()
		$(".reg-content").stop(true, false).animate({
			'left': '-3.75rem'
		}, 500)
		$("header h1").html("用户名注册");
	})

	$(".reg_bottom .phone").click(function() {
		$(".reg-content").stop(true, false).animate({
			'left': '0rem'
		}, 500)
		$("header h1").html("手机号注册");
	})


	// 手机号注册  发送验证码

	$(".sendcode").click(function() {
		var phonenum = $("#phonenum").val();
		var preg = /^1[3456789]{1}\d{9}$/;
		if (phonenum == "" || !preg.test(phonenum)) {
			if (phonenum == "") {
				$.toast("请输入手机号", "text");
			} else if (!preg.test(phonenum)) {
				$.toast("请输入正确的手机号", "text");
			}
		} else {
			$("#TencentCaptcha").trigger("click");
		}
	})

	$(".phone-reg .submit").click(function() {
		if ($(this).hasClass("clickable")) {
			var phonenum = $("#phonenum").val();
			var password = $(".phone-reg #password").val();
			var getcode = $("#getcode").val();
			var preg = /^1[3456789]{1}\d{9}$/;
			if (phonenum == "" || !preg.test(phonenum) || password == "" || password.length > 15 || password < 6 || getcode ==
				"") {

				if (phonenum == "") {
					$.toast("请输入手机号", "text");
				} else if (!preg.test(phonenum)) {
					$.toast("请输入正确的手机号", "text");
				} else if (password == "") {
					$.toast("请输入密码", "text");
				} else if (password.length > 15 || password < 6) {
					$.toast("请输入正确的密码,<br>6-15位字符", "text");
				} else if (getcode == "") {
					$.toast("请输入短信验证码", "text");
				}
			} else {
				// ajax判断和提交手机注册
                $.ajax({
                    type: 'POST',
                    url: '/member/register',
                    dataType: 'json',
                    data: {
                        username: phonenum,
                        type: 'phone',
                        password: password,
                        passwordone: password,
                        smsCode: getcode
                    },
                    success: function (res) {
                        //console.log(res)
                        if (res.code == 1) {
                            $.toast(res.msg, "text");
                            setTimeout(function () {
                                window.location.href = res.url;
                            }, 3000);

                        } else {
                            $.toast(res.msg, "text");
                        }

                    },
                    error: function () {
                        alert('网络错误，请刷新页面重试');
                    }
                })
			}
		}

	});


	// 用户名注册按钮
	$(".user-reg  .submit").click(function() {
		if ($(this).hasClass("clickable")) {
			var username = $("#username").val();
			var password = $(".user-reg #password").val();
			var reg = /^[a-zA-Z0-9]{6,11}$/ ;
			if (!reg.test(username) || password.length < 6 || password.length > 15) {
				if (!reg.test(username)) {
					$.toast("请输入正确的用户名,<br>6-11位字母或数字", "text");
				} else if (password < 6 || password > 15) {
					$.toast("请输入正确的密码,<br>6-15位字符", "text");
				}
			} else {
				$("#TencentCaptcha").trigger("click");
			}
		}
	})



	//拼图验证回调
	var ticket = '';
	var randstr = '';
	window.callback = function(res) {
		if ($(".reg-content").css("left") == '0px') {
			// 手机注册验证码拼图验证
			if (res.ret === 0) {
				ticket = res.ticket;
				randstr = res.randstr;
				sendcode();
			}
		} else {
			//  回调用户名注册
			if (res.ret === 0) {
				ticket = res.ticket;
				randstr = res.randstr;
				var username = $("#username").val();
				var password = $(".user-reg #password").val();
                $.ajax({
                    type: 'POST',
                    url: '/member/register',
                    dataType: 'json',
                    data: {username:username,type:'username',password:password,passwordone:password,ticket:ticket,randstr:randstr},
                    success: function(res) {
                         console.log(res)
                        if(res.code == 1) {
                            $.toast(res.msg, "text");
                            setTimeout(function () {
                                window.location.href=res.url;
                            },3000);
                        }else{
                            $.toast(res.msg, "text");
                        }
                    },
                    error: function () {
                        alert('网络错误，请刷新页面重试');
                    }
                });

			}
		}

	}

	//获取短信验证码
	function sendcode() {

		var phonenum = $("#phonenum").val();

        $.ajax({
            type: 'POST',
            url: "/member/sendcode",
            data: {phone:phonenum,ticket:ticket,randstr:randstr,isExistUser:true},
            success: function(json) {
                if(json.code==1) {
                    var count = 60;
                    $('.sendcode').addClass('hasSend');
                    var index = setInterval(function () {
                        if (count >= 0) {
                            $('.sendcode').val(count + ' s');
                            $('.sendcode').attr('disabled', true);

                            count--;
                        } else {
                            $('.sendcode').removeClass('hasSend').val('发送验证码');
                            $('.sendcode').attr('disabled', false);
                            clearInterval(index);
                        }
                    }, 1000);
                }else {
                    $.toast(json.msg, "text");
				}
            },
            dataType: 'json'
        });
	};
</script>
{/block}
