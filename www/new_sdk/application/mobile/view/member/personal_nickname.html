{extend name="layout/base" /}
{block name="title"}<title>修改昵称_个人资料_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<!-- header start -->
<header>
	<a href="{:getMobileUserCenterUrl(1)}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>昵称</h1>
</header>

<!-- header end -->
<div class="login">
	<form class="form">
		<div class="warp-input">
			<input type="text" placeholder="请输入昵称" id="nickname" name="nickname" autocomplete="off" value="{$nickname}">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
			<span></span>
			<P style="width: 100%;text-align: left;color: #9c9c9c;">必填，不可超过14个字符，中文算2个字符</P>
		</div>
		<input type="button" value="保存" class="submit clickable" />
	</form>
</div>


{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	if($("#nickname").val() != "" ){
		$(".cancel").show();
	}
	
	$("#nickname").focus(function() {
		$(this).next().next().animate({
			width: "100%"
		}, 500)
	});
	
	$("#nickname").blur(function() {
		$(this).next().next().animate({
			width: "0"
		}, 500)
	});
	
	$("#nickname").keyup(function() {
		var nickname = $("#nickname").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})
	
	
	// 粘贴事件监控
	$.fn.pasteEvents = function( delay ) {
	    if (delay == undefined) delay = 10;
	    return $(this).each(function() {
	        var $el = $(this);
	        $el.on("paste", function() {
	            $el.trigger("prepaste");
	            setTimeout(function() { $el.trigger("postpaste"); }, delay);
	        });
	    });
	};
	// 使用
	$("#nickname").on("postpaste", function() { 
	    var nickname = $("#nickname").val();
	    var text = $(this).val();
	    if (text != "") {
	    	$(this).next().show();
	    } else {
	    	$(this).next().hide();
	    }
	}).pasteEvents();
	
	var nickname1 = $("#nickname").val();
	
	$(".submit").click(function() {
		
     var nickname = $("#nickname").val();
	 var reg = /^[\da-z\u2E80-\u9FFF]{2,14}$/i;
	 if(nickname==""){
		 $.toast( "请输入昵称", "text");
	 }else if(nickname == nickname1){
		 $.toast( "该昵称已存在，请重新输入", "text");
	 }else if(!reg.test(nickname)){
		  $.toast( "昵称不可超过14个字符", "text");
	 }else{
		 // ajax保存  和跳转链接
         $.ajax({
             type: 'POST',
             url: "{:url('Mobile/member/editnickname')}",
             data: {nickname:nickname},
             dataType: 'json',
             success: function (json) {
                 console.log(json);
                if (json.code == 1){
                    $.toast("保存成功", "text");
                    setInterval(function() {
                        window.location.href = json.url
                    }, 1000);
                }else {
                    $.toast(json.msg, "text");
                }
             },
             error: function () {
                 $.toast('网络错误，请刷新页面重试', "text");
             }
         });

	 }
	})
	

</script>
{/block}
