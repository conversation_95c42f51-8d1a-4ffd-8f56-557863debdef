{extend name="layout/base" /}
{block name="title"}<title>意见反馈_麻花网络</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<style>
	      header {
	          width: 94%;
	          padding: 3%;
			  height: auto;
	          display: flex;
	          position: fixed;
	          background: #ffffff;
	          border-bottom: 0.01rem solid #EEEEEE;
	          top: 0;
	          z-index: 2;
	      }
		   .feedback_form>div {
		       overflow: hidden;
		   }
		 .feedback_form p{
			 color: #262228;
			 line-height: 0.5rem;
			 font-weight: bold;
		 }
		   .feedback_form  p b {
		       color: #FC5351;
		   }
	
		  
		   .feedback_form textarea {
		       width: 3.33rem;
		       height: 1.06rem;
		       padding: 0.1rem;
		       border: 0.01rem solid #EEEEEE;
		       border-radius: 0.05rem;
		   }
		 
		   .tip_cont {
		       float: right;
		           line-height: normal !important;
		           color: #BCBCBC !important;
		           font-weight: normal !important;
		   }
		   .tip_cont span{
			   color: #51a7ff;
		   }
		   .type input {
			       width:0.25rem;
			       height:0.25rem;
			       appearance: none;
			       -webkit-appearance: none;
				   border: none;
				   background: none;
				   outline: none;
			       position: relative;
			       top: -0.01rem;
		   }
		   .type div{
			   width: 50%;
			   float: left;
		   }

		   .radio_type:before {
		       content: '';
		       width: 0.13rem;
		       height: 0.13rem;
		       border: 1px solid #626262;
		       display: flex;
		       border-radius: 50%;
		       vertical-align: middle;
		   }
		   .radio_type:checked:before {
		       content: '';
		       width: 0.13rem;
		       height: 0.13rem;
		       border: 1px solid #51a7ff;
		       display: flex;
		       border-radius: 50%;
		       vertical-align: middle;
		   }
		   .radio_type:checked:after {
		       content: '';
		       width: 0.13rem;
		       height: 0.13rem;
		       text-align: center;
		       background: #51a7ff;
		       border-radius: 50%;
		       display: block;
		       position: absolute;
		       top: 0.01rem;
		       left: 0.01rem;
		   }
		   
		   .type label {
		       line-height: 20px;
		       display: inline-block;
		       margin-left: 5px;
		       margin-right: 15px;
		       color: #777;
		   }
		   .submit{
			   background: #FFA42D;
			   color: #fff;
			   font-size: 0.18rem;
			   height: 0.5rem;
			   border: none;
			   border-radius: 0.25rem;
		   }
		
		
		
		   .tip_uoload {
		      font-size: 0.13rem;
		          color: #BCBCBC !important;
		          line-height: 0.2rem !important;
		          font-weight: normal !important;
		          margin-top: 0.2rem;
		   }
		   .contact input {
		       width:3.43rem;
			   height: 0.45rem;
			   line-height: 0.45rem;
		       border: 0.01rem solid #EEEEEE;
		       border-radius: 0.05rem;
		       padding-left: 0.1rem;
		   }
		   
		   #upBox{
		   	width: 100%;
		   	position: relative;
			    margin: 0.2rem 0;
			float: left;
		   }
		  #inputBox {
		      width: 1.08rem;
		      height: 1.08rem;
		      border: 1px solid #EEEEEE;
		      color: #B2B2B2;
		      position: relative;
		      text-align: center;
		      line-height: 1.08rem;
		      overflow: hidden;
		      font-size: 0.5rem;
		      bottom: 0px;
		      float: left;
			  cursor: pointer;
			  border-radius: 5px;
		  }
		   #inputBox input{
		       width: 1.08rem;
		       height: 1.08rem;
		       opacity: 0;
		       cursor: pointer;
		       position: absolute;
		       top: 0;
		       left: 0;
		   }
		
		   .imgContainer {
		       display: inline-block;
		       width: 1.08rem;
		       height: 1.08rem;
		       margin-right: 0.1rem;
		       position: relative;
		       box-sizing: border-box;
		       float: left;
		   }
		   .imgContainer img{
		   	width: 100%;
		   	height: 100%;
		   	cursor: pointer;
		   }
		   .imgContainer p{
		   	    position: absolute;
		   	    top: -8px;
		   	    right: -6px;
		   	    width: 0.2rem;
		   	    height: 0.2rem;
		   	    background: #fff;
		   	    text-align: center;
		   	    line-height: 0.2rem;
		   	    color: #494949;
		   	    font-size: 0.19rem;
		   	    font-weight: bold;
		   	    cursor: pointer;
		   	    border-radius: 50%;
		   	    border: 0.01rem solid #D2D2D2;
		   }
		   .imgContainer:hover p{
		   	display: block;
		   }

</style>
{/block}

{block name="content"}
<!-- header start -->
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>意见反馈</h1>
</header>

<!-- header end -->
<div class="feedback">
	<p class="tip">如出现无法充值、充值不到账、游戏无法更新、无法注册登录等，可以先联系麻花网络客服QQ：<span>{$kefu}</span></p>

	<form class="feedback_form">
		<div class="type">
			<p>反馈类型<b>*</b></p>
			<div>
			<input type="radio" name="type" value="1" id="radio1" class="radio_type" />Bug反馈
			<label for="radio1"></label>
			</div>
			<div>
			<input type="radio" name="type" value="2" id="radio2" class="radio_type" />游戏相关问题
			<label for="radio2"></label>
			</div>
			<div>
			<input type="radio" name="type" value="3" id="radio3" class="radio_type" />优化建议
			<label for="radio3"></label>
			</div>
			<div>
			<input type="radio" name="type" value="4" id="radio4" class="radio_type" />其他
			<label for="radio4"></label>
			</div>
		</div>


		<div class="content">
			<p>反馈内容 <b>*</b></p>
			<textarea id="content" maxlength="500" name="content" placeholder="请具体描述您的意见，我们将不断改进"></textarea>
			<div>
				<p class="tip_cont">最少5个字，最多还可以输入<span id="contentwordage">500</span>个字</p>
			</div>
		</div>


		<div class="contact">
			<p>联系QQ</p>
			<input placeholder="选填，方便客服人员与您取得联系" class="qq"  name="qq"/>

		</div>

		<div class="upload_img">
			<p>上传图片</p>
			<!-- <div id="cupload"></div> -->
			<div id="upBox">
				<div id="imgBox">

				</div>
				<div id="inputBox"><input type="file" title="请选择图片" id="file"  name="file1" accept="image/png,image/jpg,image/JPEG" />+</div>
			</div>

			<p class="tip_uoload">选填，仅可上传jpg、png、的图片，最多可上传3张，每张不超过3M.</p>
		</div>

		<input type="button" value="提交" class="submit" id="btnSubmit" />
	</form>
</div>

{include file="layout/footer" /}
{/block}
{block name="detail_js"}
<script>

	imgUpload({
		inputId: 'file', //input框id
		imgBox: 'imgBox', //图片容器id
		buttonId: 'btnSubmit', //提交按钮id
		upUrl: 'Member/member/feedback', //提交地址
		data: 'file1', //参数名
		num: "3" //上传个数
	})
		var limitNum = 500;
		$('#content').keyup(function() {
			var remain = $(this).val().length;
		   var result = limitNum - remain;
			$('#contentwordage').html(result);
		});
		

var imgSrc = []; //图片路径
var imgFile = []; //文件流
var imgName = []; //图片名字
//选择图片
function imgUpload(obj) {
	var oInput = '#' + obj.inputId;
	var imgBox = '#' + obj.imgBox;
	var btn = '#' + obj.buttonId;
	$(oInput).on("change", function() {
		var fileImg = $(oInput)[0];
		var fileList = fileImg.files;
		var fileSize = fileImg.files[0].size;
		var size = fileSize / 1024; 
		var name = fileImg.files[0].name;
		var fileName = name.substring(name.lastIndexOf(".")+1).toLowerCase();
		 if(size > 3000 || (fileName !="jpg" && fileName !="png")){  
			   $.alert('请上传不超过3M的.jpg或.png的图片文件。')
		       return
		     }
		for(var i = 0; i < fileList.length; i++) {
			var imgSrcI = getObjectURL(fileList[i]);
			imgName.push(fileList[i].name);
			imgSrc.push(imgSrcI);
			imgFile.push(fileList[i]);
		}
		addNewContent(imgBox);
		if( $(".imgContainer").length > 2){
			$("#inputBox").hide();
			}
	})
	
	
	$(btn).on('click', function() {
		
		var type = $('input:radio[name="type"]:checked').val();
		var content =$("#content").val().length;
		var qq = $(".qq").val();
		var  regexp = /^[1-9]\d{4,9}$/;
		
		
		if (type == null || content<5 ||(qq != "" && regexp.test(qq) == false)) {
			if (type == null ){
				 $.alert('请选择反馈类型');
				 return
			}
			if(content<5){
				$.alert('反馈内容不少于5个字');
				return
			}
			if(qq != "" && regexp.test(qq) == false){
				$.alert('请输入正确的QQ号');
				return
			}
		}else{
			//用formDate对象上传
			var fd = new FormData($('form')[0]);
			for(var i=0;i<imgFile.length;i++){
				fd.append(obj.data+"[]",imgFile[i]);
			}
			submitPicture(obj.upUrl, fd);
			
		}
		

	})
}
//图片展示
function addNewContent(obj) {
	$(imgBox).html("");
	for(var a = 0; a < imgSrc.length; a++) {
		var oldBox = $(obj).html();
		$(obj).html(oldBox + '<div class="imgContainer"><img title=' + imgName[a] + ' alt=' + imgName[a] + ' src=' + imgSrc[a] + ' onclick="imgDisplay(this)"><p onclick="removeImg(this,' + a + ')" class="imgDelete">x</p></div>');
	}
}
//删除
function removeImg(obj, index) {
	imgSrc.splice(index, 1);
	imgFile.splice(index, 1);
	imgName.splice(index, 1);
	var boxId = "#" + $(obj).parent('.imgContainer').parent().attr("id");
	addNewContent(boxId);
	if( $(".imgContainer").length < 3){
		$("#inputBox").show();
		}
	 $("#file").val('');
}
//限制图片个数
function limitNum(num){
	if(!num){
		return true;
	}else if(imgFile.length>num){
		return false;
	}else{
		return true;
	}
}

//上传(将文件流数组传到后台)
function submitPicture(url,data) {
    for (var p of data) {
	  	console.log(p);
	}
		$.ajax({
			type: "post",
			url: url,
			async: true,
			data: data,
			processData: false,
			contentType: false,
			success: function(dat) {
				 console.log(dat);
				if (dat.code==1){
					
					$.modal({
					   title: "提示",
					   text: dat.msg,
					   buttons: [{
					           text: "确定",
					           onClick: function() {
								    window.location.href =dat.url
							   }
					       }
					   ]
					});
				}else {
				  $.alert(dat.msg);
				}
			},
			error: function () {
				alert("网络错误，请刷新页面重试");
			}
		});

}
//图片灯箱
function imgDisplay(obj) {
	var src = $(obj).attr("src");
	var imgHtml = '<div style="width: 100%;height: 100vh;overflow: auto;background: rgba(0,0,0,0.5);text-align: center;position: fixed;top: 0;left: 0;z-index: 1000;"><img src=' + src + ' style="margin-top: 100px;width: 70%;margin-bottom: 100px;"/><p style="font-size: 50px;position: fixed;top: 30px;right: 30px;color: white;cursor: pointer;" onclick="closePicture(this)">×</p></div>'
	$('body').append(imgHtml);
}
//关闭
function closePicture(obj) {
	$(obj).parent("div").remove();
}

//图片预览路径
function getObjectURL(file) {
	var url = null;
	if(window.createObjectURL != undefined) { // basic
		url = window.createObjectURL(file);
	} else if(window.URL != undefined) { // mozilla(firefox)
		url = window.URL.createObjectURL(file);
	} else if(window.webkitURL != undefined) { // webkit or chrome
		url = window.webkitURL.createObjectURL(file);
	}
	return url;
}	
		
</script>
{/block}
