{extend name="layout/base" /}
{block name="title"}<title>实名认证_账号安全_麻花网络个人中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<header>
	<a href="{:getMobileUserCenterUrl(4)}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>实名认证</h1>
</header>
<form class="form identity">
	<p>根据《网络游戏管理暂行办法》，麻花网络游戏用户需要使用有效身份证进行实名认证。</p>
	<p> 实名后无法修改，请务必填写真实信息，我们将全力保证您的账户资料安全。</p>
	<div class="warp-input">
		<p>姓名：</p>
		<input type="text" placeholder="请输入姓名" id="fullname" name="fullname" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	<div class="warp-input">
		<p>身份证号：</p>
		<input type="number" placeholder="请输入身份证号" id="idnumber"  name="idnumber" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
	</div>
	<input type="button" value="确认" class="submit clickable" />
</form>
{/block}

{include file="layout/footer" /}
{/block}
{block name="detail_js"}
<script>
	$(" #idnumber,#fullname").keyup(function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	
	})
	
	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})
	
	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("#idnumber,#fullname").on("postpaste", function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
			
	}).pasteEvents();
	
	
	$(".submit").click(function(){
		var fullname = $("#fullname").val();
		var idnumber = $("#idnumber").val();
		var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
		var nameReg = /^[\u4e00-\u9fa5]{2,10}$/;
		 var myDate = new Date();
		 var month = myDate.getMonth() + 1;
		 var day = myDate.getDate();
		 var age = myDate.getFullYear() - idnumber.substring(6, 10) - 1;
		 if(reg.test(idnumber)){
		  if (idnumber.substring(10, 12) < month || idnumber.substring(10, 12) == month && idnumber.substring(12, 14) <= day) {
		   age++;
		  }
		 }
		 
		if(fullname==""){
			$.toast("请输入姓名", "text");
		}else if(!nameReg.test(fullname)){
			$.toast("姓名只能是2-10位的汉字", "text");
		}else if(idnumber == ""){
			$.toast("请输入身份证号", "text");
		}else if(!reg.test(idnumber)){
			$.toast("身份证号格式不正确", "text");
		}else if(age<6||age>120){
			$.toast("您未满足年龄要求（9-120岁），无法通过认证", "text");
		}else{
			// ajax 提交
            $.ajax({
                type: 'POST',
                url: "/member/personalidentity",
                dataType: 'json',
                data: {realname:fullname,idcard:idnumber},
                success: function(res) {
                    // console.log(res)
                    // layer.msg(res.msg);
                    if(res.code == 1) {
                        $.toast("实名认证成功","text");
                        $(".clickable").attr("disabled", "disabled");
                        setTimeout(function () {
                            window.location.href=res.url;
                        },3000);
                    }else{
                        $.toast(res.msg,"text");
                    }

                },
                error: function () {
                    $.toast("网络错误，请刷新页面重试", "text");
                }
            });
		}
	})
</script>
{/block}
