{extend name="layout/base" /}
{block name="title"}<title>用户登录_麻花网络</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<!-- header start -->
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>用户登录</h1>
</header>

<!-- header end -->
<div class="login">
	<img src="__STATIC__/images/mobile/logo.png" class="logo">
	<form class="form">
		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/user.png">
			<input type="text" placeholder="请输入用户名" id="username" name="username" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
			<span></span>
		</div>
		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/password.png">
			<input type="password" placeholder="请输入密码" id="password" name="password" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel pw">
			<span></span>
		</div>
		<input type="button" value="登录" class="submit" />
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
	</form>
	<div class="login_bottom">
		<a href="{:getMobileForgetPwdUrl(1)}">忘记密码？</a>
		<a href="{:getMobileEncodeUrl('register')}">立即注册></a>
	</div>
</div>


{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>
	
	$("#username,#password").focus(function() {
		$(this).next().next().animate({
			width: "100%"
		}, 500)
	});
	
	$("#username,#password").blur(function() {
		$(this).next().next().animate({
			width: "0"
		}, 500)
	});
	
	$("#username,#password").keyup(function() {
		var username = $("#username").val();
		var password = $("#password").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
		if (username != "" && password != "") {
			$(".submit").addClass("clickable");
		} else {
			$(".submit").removeClass("clickable");
		}
	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
		$(".submit").removeClass("clickable");
	})
	
	
	// 粘贴事件监控
	$.fn.pasteEvents = function( delay ) {
	    if (delay == undefined) delay = 10;
	    return $(this).each(function() {
	        var $el = $(this);
	        $el.on("paste", function() {
	            $el.trigger("prepaste");
	            setTimeout(function() { $el.trigger("postpaste"); }, delay);
	        });
	    });
	};
	// 使用
	$("#username,#password").on("postpaste", function() { 
	    var username = $("#username").val();
	    var password = $("#password").val();
	    var text = $(this).val();
	    if (text != "") {
	    	$(this).next().show();
	    } else {
	    	$(this).next().hide();
	    }
	    if (username != "" && password != "") {
	    	$(".submit").addClass("clickable");
	    } else {
	    	$(".submit").removeClass("clickable");
	    }
	}).pasteEvents();
	
	
	$(".submit").click(function() {
		if($(this).hasClass("clickable")){
		$("#TencentCaptcha").trigger("click");
		}
	})
	
	//拼图验证
	var ticket = '';
	var randstr = '';
	window.callback = function(res){
	    if(res.ret === 0){
	        var username = $("#username").val();
	        var password = $("#password").val();
	        var ticket = res.ticket;
	        var randstr = res.randstr;
	
	        $.ajax({
	            type: 'POST',
	            url: '{:url("Mobile/member/login")}',
	            data: {username:username,password:password,ticket:ticket,randstr:randstr},
	            success: function(json) {
//	                 console.log(json);
	                if(json.code == 1) {
	                    window.location.href = json.url;
	                }else{
	                   $.toast( json.msg, "text");
					   $("#password").val("");
					   $(".pw").hide();
					   $(".submit").removeClass("clickable");
	                }
	            },
	            dataType: 'json',
	            error: function () {
					$.alert("网络错误，请刷新页面重试");
	            }
	        });
	    }
	}
</script>
{/block}
