{extend name="layout/base" /}
{block name="title"}<title>忘记密码_麻花网络</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<!-- header start -->
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>确认账号</h1>
</header>

<!-- header end -->
<div class="login">
	<form class="form">
		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/user.png">
			<input type="text" placeholder="请输入用户名" id="username" name="username" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
			<span></span>
		</div>
		
		<input type="button" value="确定" class="submit" />
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
	</form>
</div>



{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	$("#username").focus(function() {
		$(this).next().next().animate({
			width: "100%"
		}, 500)
	});
	
	$("#username").blur(function() {
		$(this).next().next().animate({
			width: "0"
		}, 500)
	});
	
	$("#username").keyup(function() {
		var username = $("#username").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
			$(".submit").addClass("clickable");
		} else {
			$(this).next().hide();
			$(".submit").removeClass("clickable");
		}
	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
		$(".submit").removeClass("clickable");
	})
	
	
	// 粘贴事件监控
	$.fn.pasteEvents = function( delay ) {
	    if (delay == undefined) delay = 10;
	    return $(this).each(function() {
	        var $el = $(this);
	        $el.on("paste", function() {
	            $el.trigger("prepaste");
	            setTimeout(function() { $el.trigger("postpaste"); }, delay);
	        });
	    });
	};
	// 使用
	$("#username").on("postpaste", function() { 
	    var username = $("#username").val();
	    var text = $(this).val();
	    if (text != "") {
	    	$(this).next().show();
	    } else {
	    	$(this).next().hide();
	    }
	}).pasteEvents();
	
	
	$(".submit").click(function() {
	var username = $("#username").val();
	if(username==""){
		 $.toast( "请输入用户名", "text");
	}else{
		$("#TencentCaptcha").trigger("click");
	}	
	})
	
	//拼图验证
	var ticket = '';
	var randstr = '';
	window.callback = function(res){
	    if(res.ret === 0){
            var username = $('input[name=username]').val();
            ticket = res.ticket;
            randstr = res.randstr;

            $.ajax({
                type: 'POST',
                url: "{:url('Mobile/member/contactAccount')}",
                dataType: 'json',
                data: {username:username,ticket:ticket,randstr:randstr},
                success: function(json) {
//                     console.log(json);
                    if(json.code == 1) {
                        window.location.href = json.url;
                    }else {
                        $.toast( json.msg, "text");
                    }
                },
                error: function () {
                    alert('网络错误，请刷新页面重试');
                }
            });
	       
	    }
	}
</script>
{/block}
