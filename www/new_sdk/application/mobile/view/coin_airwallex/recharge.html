{extend name="layout/base" /}
{block name="title"}<title>麻花网络</title>{/block}
{block name="header"}
<!-- 引入样式 -->
<!-- import CSS -->
<!--引入 element-ui 的样式，-->
<!--<link rel="stylesheet" href="https://unpkg.com/vant@2.12/lib/index.css"/>-->
<!--<link rel="stylesheet" href="http://at.alicdn.com/t/font_2742661_uo77yoc5uc.css">-->
<!--&lt;!&ndash; 必须先引入vue，  后使用element-ui &ndash;&gt;-->
<!--<script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>-->
<!--&lt;!&ndash; 引入element 的组件库&ndash;&gt;-->
<!--<script src="https://unpkg.com/vue@2.6/dist/vue.min.js"></script>-->
<!--<script src="https://unpkg.com/vant@2.12/lib/vant.min.js"></script>-->

<link rel="stylesheet" href="__STATIC__/vue/index.css"/>
<link rel="stylesheet" href="__STATIC__/vue/font_2742661_uo77yoc5uc.css">
<!-- 必须先引入vue，  后使用element-ui -->
<script src="__STATIC__/vue/vue.js"></script>
<!-- 引入element 的组件库-->
<script src="__STATIC__/vue/vue.min.js"></script>
<script src="__STATIC__/vue/vant.min.js"></script>
<link rel="stylesheet" href="__STATIC__/css/mobile/swiper.min.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/index.css">

<script src="__STATIC__/js/jquery v1.11.3.min.js"></script>
<link rel="stylesheet" href="__STATIC__/css/coin/mobile.css">
<!--<link rel="stylesheet" href="__STATIC__/css/coin/reset.css">-->
<style>

    .van-tabs--line .van-tabs__wrap {

        border-bottom: 0.04rem solid #ededed;
    }

    [data-v-225d8f26] .van-tabs__line {
        position: absolute;
        bottom: 15px;
        left: 0;
        z-index: 1;

        background: #2db7ff;
        border-radius: var(--van-tabs-bottom-bar-height);
        width: 1rem;
    }

    .van-tab__text--ellipsis {
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        font-size: 0.32rem;
        font-family: Source Han Sans SC;
        font-weight: bold;
        line-height: 0.48rem;
        color: #000000;
    }

    .van-tabs__line {
        position: absolute;
        bottom: 15px;
        left: 0;
        z-index: 1;
        background: #2db7ff;
        border-radius: var(--van-tabs-bottom-bar-height);
        width: 1rem;
    }
</style>
{/block}


{block name="content"}
<header class="top">
    <div class="header">
        <div class="logo"><a href="" title="麻花网络游戏"><img src="__STATIC__/images/mobile/logo.png"></a></div>
        <div class="search">
            <img src="__STATIC__/images/mobile/icon/search.png">
            <input type="text" placeholder="" onclick='location.href=("{:getMobileSearchUrl()}")' readonly="readonly">
        </div>
        <div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
    </div>
</header>
<div id="app">

    <div class="box" style="padding: 0.55rem 0 0.1rem 0;">

        <div class="center">
            <van-tabs v-model="active" @change="handleTabs">
                <van-tab v-for="(item, index) in payList">
                    <div slot="title" class="tabs" @click="tab = item.tab">
                        <img :src=item.image alt=""/>
                        <span>{{item.name}}</span>
                    </div>
                    <div class="tab_center">
                        <div class="center_a">
                            <div class="center_box" @click="showPicker = true" :value="value">
                                <img src="__STATIC__/images/coin/路径 <EMAIL>" alt="" class="center_box_imga"/>
                                <span>{{ value }}</span>
                                <img src="__STATIC__/images/coin/路径 514.png" alt="" class="center_box_imgb"/>
                            </div>
                            <van-popup v-model="showPicker" round position="bottom">
                                <van-picker show-toolbar :columns="columns" @cancel="showPicker = false"
                                            @confirm="onConfirm"/>
                            </van-popup>
                            <div class="center_boxtwo" v-if="value == '当前账号'" @change="handleValue">
                                <span style="margin-left: 0.2rem">{$userinfo['username'] ?? ''}</span>
                            </div>
                            <div class="center_boxtwo" v-if="value == '其他账号'" @change="handleValue">
                                <input type="text" v-model="username" placeholder="请输入账号"
                                       @change="handleChangeUserName"/>
                                <!--                                <input type="text"  v-model="passusername" placeholder="确定账号"  @change="handleChangeUserName"/>-->
                            </div>
                        </div>
                        <div class="center_b">请选择充值对象:</div>
                        <div class="xz">
                            <van-radio-group v-model="checkType" direction="horizontal">
<!--                                <van-radio name="2">游戏</van-radio>-->
                                <van-radio name="1" style="margin-left: 0.4rem">平台币</van-radio>
                            </van-radio-group>
                        </div>
                        <div class="center_b" v-show="checkType == 2">
                            请选择需要充值的游戏角色:
                        </div>
                        <div v-show="checkType == 2">
                            <van-field readonly clickable label="游戏名称" :value="gameName" placeholder="选择游戏名称"
                                       @click="showPickereGame = true">
                            </van-field>

                            <van-popup v-model="showPickereGame" round position="bottom">
                                <van-picker show-toolbar :columns="gameList" @cancel="showPickereGame = false"
                                            @confirm="onConfirmeGame">
                            </van-popup>

                            <van-field readonly clickable label="区服名称" :value="serverName" placeholder="选择区服名称"
                                       @click="showPickereServer = true">
                            </van-field>
                            <van-popup v-model="showPickereServer" round position="bottom">
                                <van-picker show-toolbar :columns="serverList" @cancel="showPickereServer = false"
                                            @confirm="onConfirmeServer"/>
                            </van-popup>

                            <van-field readonly clickable label="角色名称" :value="roleName" placeholder="选择角色名称"
                                       @click="showPickereRole = true">
                            </van-field>
                            <van-popup v-model="showPickereRole" round position="bottom">
                                <van-picker show-toolbar :columns="roleList" @cancel="showPickereRole = false"
                                            @confirm="onConfirmeRole"/>
                            </van-popup>
                        </div>
                        <div class="center_b">
                            <img src="__STATIC__/images/coin/复件 (1) 路径 <EMAIL>" alt=""/><span>请选择充值金额:<span
                                v-show="accountType == 1">（当前平台币余额：<span v-html="coin_amount"> </span>）</span> </span>
                        </div>
<!--                        <div class="jo_item">-->
<!--                            <van-radio-group v-model="radio" @change="amountChange" direction="horizontal">-->
<!--                                <van-radio :name="'' + 10">10元 &#8197;</van-radio>-->
<!--                                <van-radio :name="'' + 20">20元 &#8197;</van-radio>-->
<!--                                <van-radio :name="'' + 50">50元 &#8197;&#8197;&#8197;</van-radio>-->
<!--                                <van-radio :name="'' + 100">100元 &#8197;</van-radio>-->
<!--                            </van-radio-group>-->
<!--                        </div>-->
                        <div class="jo_item">
                            <van-radio-group v-model="radio" @change="amountChange" direction="horizontal">
                                <van-radio :name="'' + 200">200 USD</van-radio>
                                <van-radio :name="'' + 500">500 USD</van-radio>
                                <van-radio :name="'' + 1000">1000 USD</van-radio>

                            </van-radio-group>
                        </div>
                        <div class="jo_item">
                            <van-radio-group v-model="radio" @change="amountChange" direction="horizontal">
                                <van-radio :name="'' + 2000">2000 USD</van-radio>
                                <van-radio :name="'' + 5000">5000 USD</van-radio>
                                <van-radio :name="'' + 10000">10000 USD</van-radio>
                            </van-radio-group>
                        </div>
                        <div class="jo_item">
                            <van-radio-group v-model="radio" @change="amountChange" direction="horizontal">
                                <van-radio name="-1">其他金额</van-radio>
                            </van-radio-group>
                            <div class="ji_index">￥<input type="text" v-model="amount" name="amount" id="amount"
                                                          @change="handleAmount" @focus="handleAmount"/></div>
                        </div>
                        <div class="butt" @click="dialogPay">立即充值</div>
                        <div class="aaa" v-show="pageLoading">
                            <van-loading class="loading-bg" type="spinner" color="#303030" size="24px" />加载中..
                        </div>
                    </div>
                </van-tab>




            </van-tabs>
        </div>
    </div>
</div>

{/block}

{block name="detail_js"}
<script>
    username2 = "{$username2}"
    username3 = "{$userinfo['username'] ?? ''}"
    console.log(username3)
    verifyUserNameStatusStr = 0
    if (username2) {
        accountTypeStr = "其他账号"
        verifyUserNameStatusStr = 1
    } else {
        if (username3) {
            accountTypeStr = "当前账号"
            verifyUserNameStatusStr = 1
        } else {
            accountTypeStr = "其他账号"
        }

    }
    new Vue({
        el: '#app',
        data: function () {
            return {
                value: accountTypeStr,
                showPicker: false,
                columns: ["当前账号", "其他账号"],
                columns2: ["当前账号"],
                // columnse: ['杭州', '宁波', '温州', '绍兴', '湖州', '嘉兴', '金华', '衢州'],
                // valuee: '',
                showPickere: false,
                show: false,
                fieldValue: "",
                cascaderValue: "",
                radioe: "1",
                tab: "airwallexh5",
                active: "",
                radio: "200",
                amount: 200,
                accountType: 2,
                checkType: "1",
                username: username2,
                passusername: username2,
                username3: username3,
                payList: [
                    {
                        tab: 'airwallexh5',
                        name: "空中云汇支付",
                        image: "__STATIC__/images/coin/m4.png"
                    },


                    // {
                    //     tab: 'coinpay',
                    //     name: "平台币支付",
                    //     image: "__STATIC__/images/coin/m3.png"
                    // },

                ],
                gameList: [],
                serverList: [],
                roleList: [],
                gameid: "",
                gameName: "",
                serverid: "",
                serverName: "",
                roleid: "",
                roleName: "",
                verifyUserNameStatus: verifyUserNameStatusStr,
                showPickereGame: false,
                showPickereServer: false,
                showPickereRole: false,
                mixValue: 0,
                payCoinList: [
                    {
                        tab: 'old-zfb-wap',
                        name: "支付宝支付",//现在
                        image: "__STATIC__/images/coin/zfb.png"
                    },
                    {
                        tab: 'kdh5',
                        name: "微信支付",//酷点扫码
                        image: "__STATIC__/images/coin/wx.png"
                    },

                ],
                payRadio: 'airwallexh5',
                coin_amount: 0,
                pay_amount: 0, //还需支付金额
                coin_amt: 0,   //平台币支付金额
                pageLoading:false
            };
        },
        components: {},
        mounted() {
            // this.getPaytype()
            this.getGameList()
            this.handleValue()
            this.getCoin()
        },
        methods: {
            getCoin() {
                $.ajax({
                    url: '/coin_airwallex/getCoin',
                    type: 'post',
                    data: {},
                    success: function (data) {
                        if (data.code == 1) {
                            that.coin_amount = data.data.amount
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            pay_amount_fun() { // 还需支付金额
                money = parseFloat(this.amount);
                if (!money) {
                    money = 0;
                }
                if (money <= parseFloat(this.coin_amount) && this.accountType == 1) {
                    this.pay_amount = 0
                    this.coin_amt = this.amount
                    this.mixValue = 0
                } else {
                    if (this.accountType == 2) {
                        counp_money = 0;
                    } else {
                        counp_money = parseFloat(this.coin_amount);
                    }
                    real_money = money - counp_money;
                    real_money = real_money.toFixed(2);
                    this.pay_amount = real_money;
                    this.coin_amt = this.coin_amount
                    this.mixValue = 1
                }
            },
            handleAmount() {

                if (this.amount && parseFloat(this.amount) > 0) {
                    this.amount = parseFloat(this.amount).toFixed(0);
                }

                console.log(this.amount)
                this.radio = "-1"
                this.pay_amount_fun()
            },
            handleValue() {
                if (this.value == '其他账号') {
                    this.accountType = 2
                } else {
                    console.log(username3)
                    if (!username3) {
                        alert('当前未登录请先登陆')
                        this.value = '其他账号'
                        this.accountType = 2
                    } else {
                        this.accountType = 1
                    }
                    this.username = ''
                }

                this.gameid = ''
                this.serverid = ''
                this.roleid = ''
                this.gameName = ""
                this.serverName = ""
                this.roleName = ""
                this.serverList = []
                this.roleList = []
            },
            onConfirm(value) {
                this.value = value;
                this.handleValue()
                this.showPicker = false;
            },

            onConfirme(value) {
                this.valuee = value;
                console.log(value);
                this.showPickere = false;
            },
            onConfirmeGame(value) {
                this.gameid = value.id;
                this.gameName = value.text
                this.showPickereGame = false;
                this.handleChangeGame()
            },
            onConfirmeServer(value) {
                this.serverid = value.id;
                this.serverName = value.text
                this.showPickereServer = false;
                this.handleChangeServer()
            },
            onConfirmeRole(value) {
                this.roleid = value.id;
                this.roleName = value.text
                this.showPickereRole = false;
            },
            amountChange(val) {
                console.log(val)
                if (parseFloat(val) > 0) {
                    this.amount = parseFloat(val).toFixed(0);
                    this.pay_amount_fun()
                } else {
                    this.amount = null
                }
            },
            handleTabs(val) {
                console.log(val)
                console.log(this.tab)

                if (this.tab == 'coinpay') {
                    if (this.accountType != 1) {
                        that = this
                        that.gameid = ''
                        that.serverid = ''
                        that.roleid = ''
                        that.gameName = ''
                        that.serverName = ""
                        that.roleName = ""
                    }
                    if (!username3) {
                        alert('当前未登录请先登陆')
                    }else{
                        this.value = '当前账号'
                        this.accountType = 1
                    }

                    this.pay_amount_fun()
                }
            },
            handleChangeUserName() {
                that = this
                that.verifyUserNameStatus = 0

                that.gameid = ''
                that.serverid = ''
                that.roleid = ''
                that.gameName = ""
                that.serverName = ""
                that.roleName = ""

                $.ajax({
                    url: '/coin_airwallex/verifyUserName',
                    type: 'post',
                    data: {username: that.username},
                    success: function (data) {
                        if (data.code == 1) {
                            that.verifyUserNameStatus = 1
                            that.pay_amount_fun()
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            handleChangeGame(val) {
                that = this
                that.serverid = ''
                that.roleid = ''
                that.serverName = ""
                that.roleName = ""
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                $.ajax({
                    url: '/coin_airwallex/getServerList',
                    type: 'post',
                    data: {gameid: that.gameid},
                    success: function (data) {
                        if (data.code == 1) {
                            that.serverList = data.data
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            handleChangeServer() {
                that = this
                that.roleid = ''
                that.roleName = ""
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                if(this.accountType == 1 && !username3){
                    alert('未登录，请先登录')
                    return
                }
                $.ajax({
                    url: '/coin_airwallex/getRoleList',
                    type: 'post',
                    data: {
                        gameid: that.gameid,
                        serverid: that.serverid,
                        accountType: that.accountType,
                        username: that.username
                    },
                    success: function (data) {
                        if (data.code == 1) {
                            that.roleList = data.data
                            that.pay_amount_fun()
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            getGameList() {
                that = this
                $.ajax({
                    url: '/coin_airwallex/getGameList',
                    type: 'post',
                    success: function (data) {
                        if (data.code == 1) {
                            that.gameList = data.data
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            },
            dialogPay() {
                that = this
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                if (that.tab == 'coinpay') {
                    if (!username3) {
                        alert('未登录，请先登录')
                        return
                    }
                    if (that.pay_amount > 0) {
                        if (that.payRadio == '') {
                            alert('请先输入选择支付方式')
                            return
                        }
                    }
                }
                if (this.accountType == 2){
                    username = that.username
                }else{
                    username = username3
                }
                if(!that.amount){
                    alert('请先输入金额')
                    return
                }
                this.$dialog.confirm({
                    title: "确认充值",
                    message: " 您要充值的账号：<span style='color: #00a0e9'><b>"+username+"</b></span> 金额：<span style='color: #00a0e9'><b>"+that.amount+"</b></span> USD",
                    confirmButtonText: "确定",
                    cancelButtonText: "取消"
                }).then(() => {
                    that.pageLoading = true
                    that.pay()
                }).catch(() => {
                    console.log("点击了取消按钮噢")
                    that.pageLoading = false
                })
            },
            pay() {

                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    that.pageLoading = false
                    return
                }
                that = this
                var tab = that.tab
                if (that.tab == 'coinpay') {
                    if (!username3) {
                        alert('未登录，请先登录')
                        that.pageLoading = false
                        return
                    }
                    if (that.pay_amount > 0) {
                        if (that.payRadio == '') {
                            alert('请先输入选择支付方式')
                            that.pageLoading = false
                            return
                        }
                        tab = 'coin-' + that.payRadio
                    }
                    that.checkType = 2
                } else {
                    that.coin_amt = 0;
                }
                // if(that.checkType == 1 || that.accountType == 2){
                //     that.coin_amt = 0;
                // }
                $.ajax({
                    url: '/coin_airwallex/pay',
                    type: 'post',
                    data: {
                        type: that.checkType,
                        paytype: tab,
                        amount: that.amount,
                        coin_amt: that.coin_amt,
                        accountType: that.accountType,
                        username: that.username,
                        gameid: that.gameid,
                        serverid: that.serverid,
                        roleid: that.roleid
                    },
                    success: function (data) {
                        that.pageLoading = false
                        console.log(data)
                        paytype = tab
                        if (data.code == 1) {
                            if (paytype == 'airwallexh5') { //支付宝扫码(支付宝官方扫码)
                                window.location.href = data.data.airwallexh5_param.mweb_url
                            } else if (paytype == 'coinpay') {
                                alert(data.msg)
                                window.location.href = window.location.href
                            }
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            }
        },
    })
</script>
{/block}
