<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <!--  s-->
    <title></title>
    <style type="text/css">

        .pay_b {
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            line-height: 28px;
            color: #000000;
            margin-right: 10px;
        }
    </style>
</head>
<body>
<div class="pay">
    <div class="paytwo">
        <div class="pay_b">
            <span>商品:</span>
            <span style="color:#FB5E5E ;">{$result['goods']}</span>
        </div>
    </div>
    <div id="dropIn"></div>
</div>
</body>

<script src="https://checkout.airwallex.com/assets/elements.bundle.min.js"></script>
<script>
    var id = "{$result['id']}"
    var client_secret = "{$result['client_secret']}"
    var currency = "{$result['currency']}"
    var orderid = "{$orderid}"
    Airwallex.init({
        env: 'prod', // Setup which Airwallex env('demo' | 'prod') to integrate with
        origin: window.location.origin, // Set up your event target to receive the browser events message
    });

    const element = Airwallex.createElement('dropIn', {
        intent_id: id,
        client_secret: client_secret,
        currency:currency,
    });

    element.mount('dropIn'); // Injects iframe into the Drop-in Element container

    window.addEventListener('onReady', (event) => {
        /*
        ... Handle event
         */
        // window.alert(event.detail);
        console.log(event)
    });


    window.addEventListener('onSuccess', (event) => {
        // Handle event on success
        console.log(event)
        setTimeout(function () {
            window.location.href='https://sdkapi.46yx.com/v1.pay_return/pay_success_airwallex.html?orderid='+orderid;
        },2000)

    });

    window.addEventListener('onError', (event) => {
        // Handle event on error
        console.log(event)
    });
</script>
</html>
