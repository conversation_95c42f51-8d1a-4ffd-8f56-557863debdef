{extend name="layout/base" /}

{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/swiper.min.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/index.css">

{/block}

{block name="content"}

    <!-- page top start -->
    <header class="top">
        <div class="header">
            <div class="logo"><a href="" title="麻花网络游戏"><img src="__STATIC__/images/mobile/logo.png"></a></div>
            <div class="search">
                <img src="__STATIC__/images/mobile/icon/search.png">
                <input type="text" placeholder="大家都在搜“{$trendingSearch}”" onclick='location.href=("{:getMobileSearchUrl()}")' readonly="readonly">
            </div>
            <div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
        </div>
        <ul class="nav">
            <li><a href="{:getMobileNewsIndexUrl()}">资讯</a></li>
            <li><a href="{:getMobileGiftUrl('hot')}">礼包</a></li>
            <li><a href="{:getMobileTypeGame()}">找游戏</a></li>
            <li><a href="{:getMobileServerUrl('kf')}">开服</a></li>
			<!--
            <li><a href="{:getMobileEncodeUrl('fastgame')}">加速</a></li>
			-->
        </ul>
    </header>
    <!-- page top end -->
    
    <!-- slide start -->
    <div class="warp-content">
        <!-- Swiper -->
        <div class="swiper-container">
            <div class="swiper-wrapper">
                {volist name="topBannerList" id="vo"}
                <div class="swiper-slide"><a href="{$vo.url}"><img lazy-src="{$Think.STATIC_DOMAIN}{$vo.image}" src="__STATIC__/images/icon/990-625.png" class="new-img"></a></div>
                {/volist}
            </div>
            <!-- Add Pagination -->
            <div class="swiper-pagination"></div>
        </div>
    </div>
    <!-- slide end -->
    <!-- classified nav start -->
    <div class="classified-nav">
        <a href="{:getMobileNewsIndexUrl()}">
            <img src="__STATIC__/images/mobile/icon/info-1.png">
            资讯
        </a>
        <a href="{:getMobileGiftUrl('hot')}">
            <img src="__STATIC__/images/mobile/icon/gift-2.png">
            礼包
        </a>
        <a href="{:getMobileTypeGame()}">
            <img src="__STATIC__/images/mobile/icon/classify-1.png">
            找游戏
        </a>
        <a href="{:getMobileServerUrl('kf')}">
            <img src="__STATIC__/images/mobile/icon/mark-2.png">
            开服
        </a>
		<!--
        <a href="{:getMobileEncodeUrl('fastgame')}">
            <img src="__STATIC__/images/mobile/icon/speed-1.png">
            加速
        </a>
		-->
    </div>
    <!-- classified nav end -->
    <!-- hot game start -->
    <div class="hot-game">
        <div class="title">
            <img src="__STATIC__/images/mobile/icon/hot.png" />
            <h4>热门游戏</h4>
        </div>
        <div class="hot-game-list">
            {volist name="hotGameList" id="vo"}
            <div class="hot-game-info">
                <a href="{$vo.url}">
                    <img lazy-src="{$vo.mobileicon}"  src="__STATIC__/images/icon/150-150.png" class="smallIcon">
                    <h4>{$vo.nickname}</h4>
                    <p>{$vo.typename}<span>|</span>{$vo.subjectname}</p>
                </a>
                <div class="download">
                    <a href="javascript:;">下载</a>
                    <p class="android_down_url">{$vo.download}</p>
                    <p class="ios_down_url">{$vo.ios_download}</p>
                </div>
            </div>
            {/volist}
        </div>
        <a href="{:getMobileGameLanmu('hot')}" class="more-hot-game">查看更多<img src="__STATIC__/images/mobile/icon/right-1.png"></a>
    </div>
    <!-- hot game end-->
    <!-- new game start -->
    <div class="new-game">
        <div class="title">
            <img src="__STATIC__/images/mobile/icon/new.png" />
            <h4>最新游戏</h4>
            <a href="{:getMobileGameLanmu('new')}">更多></a>
        </div>
        <div class="new-game-list">
            {volist name="newGameList" id="vo"}
            <div class="new-game-info">
                <a href="{$vo.url}">
                    <img lazy-src="{$vo.mobileicon}"  src="__STATIC__/images/icon/150-150.png" class="smallIcon">
                    <h4>{$vo.nickname}</h4>
                    <p>{$vo.typename}<span>|</span>{$vo.subjectname}</p>
                </a>
                <div class="download">
                    <a href="javascript:;">下载</a>
                    <p class="android_down_url">{$vo.download}</p>
                    <p class="ios_down_url">{$vo.ios_download}</p>
                </div>
            </div>
            {/volist}
        </div>
    </div>
    <!-- new game end -->
    <!-- week recommend start -->
    <div class="new-game">
        <div class="title">
            <img src="__STATIC__/images/mobile/icon/mark-1.png" />
            <h4>本周推荐</h4>
            <a href="{:getMobileGameLanmu('recommend')}">更多></a>
        </div>
        <div class="new-game-list">
            {volist name="recommendGameList" id="vo"}
            <div class="new-game-info">
                <a href="{$vo.url}">
                    <img lazy-src="{$vo.mobileicon}"  src="__STATIC__/images/icon/150-150.png" class="smallIcon">
                    <h4>{$vo.nickname}</h4>
                    <p>{$vo.typename}<span>|</span>{$vo.subjectname}</p>
                </a>
                <div class="download">
                    <a href="javascript:;">下载</a>
                    <p class="android_down_url">{$vo.download}</p>
                    <p class="ios_down_url">{$vo.ios_download}</p>
                </div>
            </div>
            {/volist}
        </div>
    </div>
    <!-- week recommend end -->
    <!-- rank list start -->
    <div class="rank">
        <div class="top-three">
            <h4><i></i>排行榜</h4>
            <div class="top-three-list">
				{if(isset($rankGameList[1]))}
                <div class="top-two-info">
                    <a href="{$rankGameList[1]['url']}">
                        <img src="__STATIC__/images/mobile/icon/top-two.png" class="rank-icon" />
                        <img lazy-src="{$rankGameList[1]['mobileicon']}" src="__STATIC__/images/icon/150-150.png" class="smallIcon game-icon">
                        <h4>{$rankGameList[1]['nickname']}</h4>
                        <p>{$rankGameList[1]['typename']}<span>|</span>{$rankGameList[1]['size']}</p>
                    </a>
                    <div class="download">
                        <a href="javascript:;">下载</a>
                        <p class="android_down_url">{$rankGameList[1]['download']}</p>
                        <p class="ios_down_url">{$rankGameList[1]['ios_download']}</p>
                    </div>
                </div>
				{/if}
				{if(isset($rankGameList[0]))}
                <div class="top-one-info">
                    <a href="{$rankGameList[0]['url']}">
                        <img src="__STATIC__/images/mobile/icon/top-one.png" class="rank-icon" />
                        <img lazy-src="{$rankGameList[0]['mobileicon']}" src="__STATIC__/images/icon/150-150.png" class="smallIcon game-icon">
                        <h4>{$rankGameList[0]['nickname']}</h4>
                        <p>{$rankGameList[0]['typename']}<span>|</span>{$rankGameList[0]['size']}</p>
                    </a>
                    <div class="download">
                        <a href="javascript:;">下载</a>
                        <p class="android_down_url">{$rankGameList[0]['download']}</p>
                        <p class="ios_down_url">{$rankGameList[0]['ios_download']}</p>
                    </div>
                </div>
				{/if}
				{if(isset($rankGameList[2]))}
                <div class="top-three-info">
                    <a href="{$rankGameList[2]['url']}">
                        <img src="__STATIC__/images/mobile/icon/two-three.png" class="rank-icon" />
                        <img lazy-src="{$rankGameList[2]['mobileicon']}" src="__STATIC__/images/icon/150-150.png" class="smallIcon game-icon">
                        <h4>{$rankGameList[2]['nickname']}</h4>
                        <p>{$rankGameList[2]['typename']}<span>|</span>{$rankGameList[2]['size']}</p>
                    </a>
                    <div class="download">
                        <a href="javascript:;">下载</a>
                        <p class="android_down_url">{$rankGameList[2]['download']}</p>
                        <p class="ios_down_url">{$rankGameList[2]['ios_download']}</p>
                    </div>
                </div>
				{/if}
            </div>
        </div>

        <div class="game-list">
            {volist name="rankGameList" id="vo" offset="3" length="7"}
            <div class="game-info">
                <a href="{$vo.url}">
                    <div class="rankid">{$key+1}</div>
                    <img lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon">
                    <div class="content">
                        <h4 class="title">{$vo.nickname}</h4>
                        <p>{$vo.typename}<span>|</span>{$vo.subjectname}<span>|</span>{$vo.size}</p>
                        {if($vo['publicity'])}<P>{$vo.publicity}</P>{/if}
                    </div>
                </a>
                <div class="download">
                    <a href="javascript:;">下载</a>
                    <p class="android_down_url">{$vo.download}</p>
                    <p class="ios_down_url">{$vo.ios_download}</p>
                </div>
            </div>
            {/volist}
        </div>
    </div>
    <!-- rank list end -->
    <!-- server  table start -->
    <div class="open-service">
        <div class="title">
            <h4><span></span>开服表</h4>
            {if condition='count($kfList)>5')} <a href="{:getMobileServerUrl('kf')}">更多></a>{/if}
        </div>
        <div class="game-list">
            {volist name="kfList" id="vo" offset="0" length="5"}
            <div class="game-info">
                <!-- have gift -->
                {if condition='$vo.libaonum'}<a href="{$vo.url}"><img src="__STATIC__/images/mobile/icon/gift.png" class="gift"></a>{/if}
                <a href="{$vo.url}">
                    <img lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon">
                    <div class="content">
                        <h4 class="title">{$vo.nickname}</h4>
                        <p>{$vo.typename}<span>|</span>{$vo.subjectname}<span>|</span>{$vo.size}</p>
                        <P {if condition='$vo.highlight'} class="highlight"{/if}>{$vo.showSertime} <span>|</span> {$vo.sername}</P>
                    </div>
                </a>
                <div class="order">{if condition='$vo.subscribe'}<a href="javascript:kf_tip('{$vo.id}','kf');">预约</a>{/if}</div>
            </div>
            {/volist}
            {empty name="kfList"}
       <div class="no-info">
          <img src="__STATIC__/images/mobile/icon/empty.png">
          <p>暂无开服信息</p>
       </div>
            {/empty}
        </div>
    </div>
    <!-- server  table end -->
    <!-- open test  table start -->
    <div class="open-service">
        <div class="title">
            <h4><span></span>开测表</h4>
            {if condition='count($kcList)>5')} <a href="{:getMobileServerUrl('kc')}">更多></a>{/if}
        </div>
        <div class="game-list">
            {volist name="kcList" id="vo" offset="0" length="5"}
            <div class="game-info">
                <!-- have gift -->
                {if condition='$vo.libaonum'}<a href="{$vo.url}"><img src="__STATIC__/images/mobile/icon/gift.png" class="gift"></a>{/if}
                <a href="{$vo.url}">
                    <img lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon">
                    <div class="content">
                        <h4 class="title">{$vo.nickname}</h4>
                        <p>{$vo.typename}<span>|</span>{$vo.subjectname}<span>|</span>{$vo.size}</p>
                        <P {if condition='$vo.highlight'} class="highlight"{/if}>{$vo.showSertime} <span>|</span>{$vo.serstatusName}</P>
                    </div>
                </a>
                <div class="order">{if condition='$vo.subscribe'}<a href="javascript:kf_tip('{$vo.id}','kc');">预约</a>{/if}</div>
            </div>
            {/volist}
            {empty name="kcList"}
         <div class="no-info">
              <img src="__STATIC__/images/mobile/icon/empty.png">
              <p>暂无开测信息</p>
         </div>
            {/empty}
        </div>
    </div>
    <!-- open test table end -->
    <!-- Game Category start -->
    <div class="game-category">
        {volist name="gameTypeList" id="vo"}
        <div class="game-category-list" onclick="window.open('{:getMobileTypeGame($vo['id'])}','_self')">
            <img src="__STATIC__/images/mobile/game-sort/{$vo.typeicon}">
            <div class="content">
                <h4>{$vo.name}</h4>
                <p>共{$vo.cnt}款</p>
            </div>
        </div>
        {/volist}
    </div>
    <!-- Game Category end -->

    <div class="bottom-link">
        <p>
            <a href="" class="pc">电脑版</a>
            <span class="pc">|</span>
            <a href="{:getMobileTypeGame()}">找游戏</a>
            <span>|</span>
            <a href="{:getMobileCscUrl()}">客服中心</a>
            <span>|</span>
            <a href="about/aboutus.html">关于我们</a>
        </p>
    </div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script src="__STATIC__/js/mobile/swiper.min.js"></script>
<script src="__STATIC__/js/mobile/index.js"></script>
<script>
/*
    $(".order").on("click", function() {

        $.modal({
            title: "提示",
            text: "请先绑定手机号",
            buttons: [{
                    text: "去绑定",
                    onClick: function() {
                        console.log(1)
                    }
                },
                {
                    text: "取消",
                    className: "default",
                    onClick: function() {}
                },
            ]
        });

        // $.confirm({
        //   title: '提示',
        //   text: '离开服时间不足1小时，无法预约',
        //   onOK: function () {
        //     //点击确认
        //   },
        //   onCancel: function () {
        //   }
        // });


    })
*/

// 预约弹窗
function kf_tip(id,kfkc){
    $.ajax({
       type: "POST",
       timeOut: 10000,
       url: '/service/subscribe',
       data: {
           "id": id,
           "type": kfkc
       },
       async: false,
       success: function (res) {
           // console.log(res);
           if (res.code == 1 || res.code == 4 || res.code == 5) {
               $.modal({
                  title: "提示",
                  text: res.msg,
                  buttons: [{
                          text: "确定",
                          className: "pop-button",
                          onClick: function() {}
                      },
                      {
                          text: "关闭",
                          className: "default",
                          onClick: function() {}
                      },
                  ]
               });
           }
           else if (res.code == 2){
               window.location.href = res.data;
           }
           else if (res.code == 3){
                $.modal({
                    title: "提示",
                    text: res.msg,
                    buttons: [{
                            text: "去绑定",
                            onClick: function() {
                           // 跳转链接
                                window.location.href = res.data;
                            }
                        },
                        {
                            text: "取消",
                            className: "default",
                            onClick: function() {}
                        },
                    ]
                });
           }else {
               alert(res.msg);
           }
       },
       error: function () {
           layer.msg('网络错误，请刷新页面重试');
       }
    });
}

</script>
{/block}