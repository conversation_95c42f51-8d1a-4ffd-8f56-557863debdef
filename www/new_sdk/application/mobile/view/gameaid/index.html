{extend name="layout/base" /}
{block name="title"}
<title>手机游戏加速_手机游戏辅助_麻花网络</title>
<meta name="keywords" content="加速游戏,手机游戏加速,手机游戏辅助"/>
<meta name="description" content="麻花网络提供加速版手机游戏,手机游戏自动加速，让您更加畅爽地享受游戏的乐趣。"/>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>加速游戏</h1>
		<div>
			<div class="search-icon"><a href="{:getMobileSearchUrl()}"><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->
<div class="gameaid-list">
	{volist name="list" id="vo"}
	<div class="warp-gameaid-info">
		<div class="game-info">
			<a href="">
				<img title="{$vo.nickname}"  lazy-src="{$vo.mobileicon}" src="__STATIC__/images/icon/150-150.png" class="smallIcon" >
				<div class="content">
					<h4>{$vo.nickname}</h4>
					<p>{$vo.typeName}</p>
				</div>
			</a>
			<div class="download">
				<a href="javascript:;">下载</a>
				<p class="android_down_url">{$vo.android_down_url}</p>
				<p class="ios_down_url">{$vo.ios_down_url}</p>
			</div>
		</div>
		<div class="gameaid-info">
			<div>
				{$vo.content}
			</div>
		</div>
		<p class="open-close open">展开<img src="__STATIC__/images/mobile/icon/bottom-gray.png"></p>
	</div>
	{/volist}
</div>

<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>
    var currentPage = parseInt("{$list->currentPage()}");
    var lastPage = parseInt("{$list->lastPage()}");
    var total = parseInt("{$list->total()}");
    var url = window.location.href;
    var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;
        if (currentPage + 1 > lastPage) {
            $(".weui-loadmore").hide();
            if (currentPage > 1) {
                $(".no-more-data").show();
            }
            loading = false;
            return false;
        }
		$(".weui-loadmore").show();
		setTimeout(function() {
            currentPage += 1;

            $.ajax({
                type: "POST",
                timeOut: 10000,
                url: url,
                data: {
                    "page": currentPage
                },
                async: false,
                success: function(res) {
                    console.log(res);
                    var arr = res.data.data;
                    arr.forEach(function(val, index) {
                        $(".gameaid-list").append(

                            '<div class="warp-gameaid-info">'+
                            '<div class="game-info">'+
                            '<a href="">'+
                            '<img title="'+val.nickname+'"  lazy-src="'+val.mobileicon+'" src="'+defaultImg+'" class="smallIcon" >'+
                            '<div class="content">'+
                            '<h4>'+val.nickname+'</h4>'+
                            '<p>'+val.typeName+'</p>'+
                            '</div>'+
                            '</a>'+
                            '<div class="download">'+
                            '<a href="javascript:;">下载</a>'+
                            '<p class="android_down_url">'+val.android_down_url+'</p>'+
                            '<p class="ios_down_url">'+val.ios_down_url+'</p>'+
                            '</div>'+
                            '</div>'+
                            '<div class="gameaid-info">'+
                            '<div>'+ val.content+
                        '</div>'+
                        '</div>'+
                        '<p class="open-close open">展开<img src="__STATIC__/images/mobile/icon/bottom-gray.png"></p>'+
                        '</div>'
                        );
                    });

                },
                error: function() {
                    $.alert("网络错误，请刷新页面重试");
                }
            });

			$.getScript("/static/js/mobile/reload.js");
				$(".weui-loadmore").hide();
				loading = false;
			}, 500); //模拟延迟
	});
	
	
	
	// 显示隐藏
	$(".gameaid-info").each(function(){
		var rowNum=Math.round($(this).height()/parseFloat($(this).css('line-height')));
		if(rowNum > 6){
			$(this).next().show();
			$(this).css("height","1.2rem")
		}
	 })
	 
	 $(".open-close").click(function(){
		 var H = $(this).prev().find("div").height();
		 if($(this).hasClass("open")){
			 $(this).prev().animate({ height: H }, 500)
			  $(this).html("<a href='javascript:;'>隐藏<img src='/static/images/mobile/icon/top-gray.png'></a>");
			  $(this).removeClass("open");
		 }else{
			 $(this).prev().animate({ height: '1.2rem' }, 500)
			  $(this).addClass("open");
			   $(this).html("<a href='javascript:;'>展开<img src='/static/images/mobile/icon/bottom-gray.png'></a>");
		 }
	 })
</script>
{/block}
