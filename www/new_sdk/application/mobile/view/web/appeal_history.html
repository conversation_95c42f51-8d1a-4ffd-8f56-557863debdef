{extend name="layout/base" /}

{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>账号申诉</h1>
</header>

<ul class="text-center top-half">
	<li class="step1 pass">
		<div class="box-outside" id="outside">
			<div class="box-num">
				1
			</div>
		</div>
		账号信息
	</li>
	<li class="step2 current">
		<div class="box-outside" id="outside">
			<div class="box-num">
				2
			</div>
		</div>
		历史信息
	</li>
	<li class=" step3">
		<div class="box-outside" id="outside">
			<div class="box-num">
				3
			</div>
		</div>
		提交成功
	</li>

	<div class="clear">

	</div>
</ul>

<div class="account-info">

	<ul class="personal-info">
		<li class="warp-input password1">
			<p>历史密码1<sup>*</sup></p>
			<input type="password" placeholder="请输入历史密码" name="oldpwd[]"  />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input password2">
			<p>历史密码2&nbsp;</p>
			<input type="password" placeholder="请输入历史密码" name="oldpwd[]" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input password3">
			<p>历史密码3&nbsp; </p>
			<input type="password" placeholder="请输入历史密码" name="oldpwd[]"/>
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input password4 hide">
			<p>历史密码4&nbsp; </p>
			<input type="password" placeholder="请输入历史密码" name="oldpwd[]" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input password5 hide">
			<p>历史密码5&nbsp; </p>
			<input type="password" placeholder="请输入历史密码" name="oldpwd[]"/>
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input more_password">
			<a href="javascript:;" >填写更多</a>
		</li>
		
		<li class="warp-input mail1">
			<p>历史绑定邮箱&nbsp;</p>
			<input type="text" placeholder="请输入邮箱" name="oldmail[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input mail2 hide">
			<p>历史绑定邮箱2</p>
			<input type="text" placeholder="请输入邮箱" name="oldmail[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input mail3 hide">
			<p>历史绑定邮箱3 </p>
			<input type="text" placeholder="请输入邮箱" name="oldmail[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input mail4 hide">
			<p>历史绑定邮箱4 </p>
			<input type="text" placeholder="请输入邮箱" name="oldmail[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input mail5 hide">
			<p>历史绑定邮箱5 </p>
			<input type="text" placeholder="请输入邮箱" name="oldmail[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input more_mail">
			<a href="javascript:;">填写更多</a>
		</li>
		
		<li class="warp-input phone1">
			<p>历史绑定手机&nbsp; </p>
			<input type="number" placeholder="请输入手机号" name="oldmobile[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input phone2 hide">
			<p>历史绑定手机2 </p>
			<input type="number" placeholder="请输入手机号" name="oldmobile[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input phone3 hide">
			<p>历史绑定手机3 </p>
			<input type="number" placeholder="请输入手机号" name="oldmobile[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input phone4 hide">
			<p>历史绑定手机4 </p>
			<input type="number" placeholder="请输入手机号" name="oldmobile[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="warp-input phone5 hide">
			<p>历史绑定手机5 </p>
			<input type="number" placeholder="请输入手机号" name="oldmobile[]" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		
		<li class="warp-input more_phone">
			<a href="javascript:;">填写更多</a>
		</li>
		
		<li class="warp-input">
			<p>注册渠道名称</p>
			<input type="text" placeholder="请输入注册渠道名称" id="regway" name="regway" class="regchannelname" autocomplete="off" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="tip">
			<p>充值记录核实：提供三次充值大致时间和金额，时间精确到<span>1 小时之内</span></p>
		</li>
		
		<li>
		    <p>充值时间1</p>
		    <input type="text" placeholder="请选择充值时间" name="rechargetime[]" autocomplete="off" id="time1" class="time" />
		</li>
		
		<li class="warp-input money">
			<p>充值金额&nbsp;&nbsp;</p>
			<input type="text" placeholder="请输入相应的充值金额" name="rechargeamount[]"  autocomplete="off" id="money1" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li>
		    <p>充值时间2</p>
		    <input type="text" placeholder="请选择充值时间" name="rechargetime[]" autocomplete="off" id="time2" class="time" />
		</li>
		
		<li class="warp-input money">
			<p>充值金额&nbsp;&nbsp;</p>
			<input type="text" placeholder="请输入相应的充值金额" name="rechargeamount[]"  autocomplete="off" id="money1" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		
		<li>
		    <p>充值时间3</p>
		    <input type="text" placeholder="请选择充值时间" name="rechargetime[]" autocomplete="off" id="time3" class="time" />
		</li>
		
		<li class="warp-input money">
			<p>充值金额&nbsp;&nbsp;</p>
			<input type="text" placeholder="请输入相应的充值金额" name="rechargeamount[]"  autocomplete="off" id="money1"  />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		
		
		<li  class="logined1">
		    <p>登录过的游戏<sup>*</sup></p>
		    <input type="text" placeholder="请选择登录过的游戏" name="boundgameid[]" class="logined_game"  autocomplete="off"  />
		</li>
		<li  class="hide logined2">
		    <p>登录过的游戏2</p>
		    <input type="text" placeholder="请选择登录过的游戏" name="boundgameid[]" class="logined_game"  autocomplete="off"  />
		</li>
		<li  class="hide logined3">
		    <p>登录过的游戏3</p>
		    <input type="text" placeholder="请选择登录过的游戏" name="boundgameid[]" class="logined_game"  autocomplete="off"  />
		</li>
		<li  class="hide logined4">
		    <p>登录过的游戏4</p>
		    <input type="text" placeholder="请选择登录过的游戏" name="boundgameid[]" class="logined_game"  autocomplete="off"  />
		</li>
		<li  class="hide logined5">
		    <p>登录过的游戏5</p>
		    <input type="text" placeholder="请选择登录过的游戏" name="boundgameid[]" class="logined_game"  autocomplete="off"  />
		</li>
		<li class="warp-input more_logined_game">
			<a href="javascript:;">填写更多</a>
		</li>
		<li class="tip">
			<input type="button" value="下一步" class="next">
		</li>
	</ul>
</div>

<!-- search game -->
<!-- 注册游戏搜索弹窗 -->
<div class="warp-serchgame">
	<div class="account-info search-header">
       <div class="close-poup"><img src="__STATIC__/images/mobile/icon/close.png"></div>
        <div class="warp-input">
			<p>搜索游戏：</p>
			<input type="text" placeholder="请输入游戏名" id="gamename" name="gamename" autocomplete="off">
			<img src="/static/images/mobile/icon/close-01.png" class="cancel code-cancel">
         </div>
     </div>
	 
	<ul class="search-list">
		<li>
            <a href="javascript:;">
				<p>三国演义dsafsdf</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义21321</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
		    <a href="javascript:;">
				<p>三国演义</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
	</ul>
	 
	 <div class="weui-loadmore">
	 	<i class="weui-loading"></i>
	 	<span class="weui-loadmore__tips">数据加载中，请稍后</span>
	 </div>
	 <div class="no-more-data"><span></span>我也是有底线的<span></span></div>
</div>

{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script src="__STATIC__/js/mobile/datePicker.js"></script>
<script>
	
	// 取消相应表单的填写数据
	$(" input").keyup(function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})

	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("input").on("postpaste", function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}

	}).pasteEvents();
	
	// 点击填写更多
	$(".more_password").click(function(){
		if($(".password4").css("display")=='none'){
			$(".password4").removeClass('hide')
		}else{
			$(".password5").removeClass('hide')
		}
	})
	
	$(".more_mail").click(function(){
		if($(".mail2").css("display")=='none'){
			$(".mail2").removeClass('hide')
		}else if($(".mail3").css("display")=='none'){
			$(".mail3").removeClass('hide')
		}else if($(".mail4").css("display")=='none'){
			$(".mail4").removeClass('hide')
		}else{
			$(".mail5").removeClass('hide')
		}
	})
	$(".more_phone").click(function(){
		if($(".phone2").css("display")=='none'){
			$(".phone2").removeClass('hide')
		}else if($(".phone3").css("display")=='none'){
			$(".phone3").removeClass('hide')
		}else if($(".phone4").css("display")=='none'){
			$(".phone4").removeClass('hide')
		}else{
			$(".phone5").removeClass('hide')
		}
	})
	
	$(".more_logined_game").click(function(){
		if($(".logined2").css("display")=='none'){
			$(".logined2").removeClass('hide')
		}else if($(".logined3").css("display")=='none'){
			$(".logined3").removeClass('hide')
		}else if($(".logined4").css("display")=='none'){
			$(".logined4").removeClass('hide')
		}else{
			$(".logined5").removeClass('hide')
		}
	})
	
	
	// 弹出充值时间
	  var calendar = new datePicker();
	  var myDate = new Date;
	  var year=myDate.getFullYear();
	  var month=myDate.getMonth()+1; 
	  var date=myDate.getDate();
	calendar.init({
	    'trigger': '#time1', /*按钮选择器，用于触发弹出插件*/
	    'type': 'datetime',/*模式：date日期；datetime日期时间；time时间；ym年月；*/
	    'minDate':'1900-1-1',/*最小日期*/
	    'maxDate':year+'-'+month+'-'+date,/*最大日期*/
	    'onSubmit':function(theSelectData){ 
	    },
	    'onClose':function(theSelectData){
	    },
	});
	var calendar2 = new datePicker();
	calendar2.init({
	    'trigger': '#time2', /*按钮选择器，用于触发弹出插件*/
	    'type': 'datetime',/*模式：date日期；datetime日期时间；time时间；ym年月；*/
	    'minDate':'1900-1-1',/*最小日期*/
	    'maxDate':year+'-'+month+'-'+date,/*最大日期*/
	    'onSubmit':function(theSelectData){ 
	    },
	    'onClose':function(theSelectData){
	    },
	});
	var calendar3 = new datePicker();
	calendar3.init({
	    'trigger': '#time3', /*按钮选择器，用于触发弹出插件*/
	    'type': 'datetime',/*模式：date日期；datetime日期时间；time时间；ym年月；*/
	    'minDate':'1900-1-1',/*最小日期*/
	    'maxDate':year+'-'+month+'-'+date,/*最大日期*/
	    'onSubmit':function(theSelectData){ 
	    },
	    'onClose':function(theSelectData){
	    },
	});


     // 弹出游戏选择
	 
	 $(".logined_game").click(function(){
	 	$('.warp-serchgame').stop(true, false).animate({
			'top': '0%'
		}, 300)
		var a = $(this).parent("li").attr('class');
		$('.warp-serchgame').addClass(a)
	 })

	 $(".close-poup img").click(function(){
	 	$(this).parents(".warp-serchgame").stop(true, false).animate({
	 		'top': '100%'
	 	}, 300)
	 })
	 
	 $(".search-list a").click(function(){
		 var gamename =$(this).find("p").html();
		var s= $(this).parents(".warp-serchgame").attr('class')
		s = s.split(' ');
		 $('.warp-serchgame').removeClass(s[1])
        $('.'+s[1]).find('input').val(gamename)
		
		 $('.warp-serchgame').stop(true, false).animate({
		 	'top': '100%'
		 }, 300);
	 })
	 
	 
	 // 游戏选择弹窗
	 $(".warp-serchgame  #gamename").keyup(function() {
	 	var text = $(this).val();
		var texta = text.replace(/\s*/g, "");
	 	if (text != "") {
	 		$(this).next().show();
	 	} else {
	 		$(this).next().hide();
	 	}
	    // ajax 改变搜索游戏列表数据
	 })
	 
	 // 加载更多
	 $(".warp-serchgame  .no-more-data").hide();
	 var loading = false; //状态标记
	 $('.reg-search').infinite(90).on("infinite", function() {
	 	if (loading) return;
	 	loading = true;
	 	$(".reg-search .weui-loadmore").show();
	 	setTimeout(function() {
	 		$(".reg-search  .search-list").append(
	 			'<li>'+
	 			    '<a href="javascript:;">'+
	 					'<p>三国演义124134</p>'+
	 					'<img src="__STATIC__/images/mobile/icon/right-gray.png">'+
	 				'</a>'+
	 			'</li>'
	 		);
	 				$(".reg-search .weui-loadmore").hide();
	 				loading = false;
	 			}, 500); //模拟延迟
	 		});

	 
	
	
// 点击下一步

/* 提交申诉
     */
    $('.next').on('click', function () {
        var oldpwd = new Array;
        var oldmail = new Array;
        var oldmobile = new Array;
        var boundgameid = new Array;
        var rechargetime = new Array;
        var rechargeamount = new Array;

        var regchannelname = $('.regchannelname').val();
        $("input[name='oldpwd[]']").each(function (i) {
             oldpwd[i] = $(this).val();
        });
        $("input[name='oldmail[]']").each(function (i) {
             oldmail[i] = $(this).val();
        });
        $("input[name='oldmobile[]']").each(function (i) {
             oldmobile[i] = $(this).val();
        });
        $("input[name='boundgameid[]']").each(function (i) {
             boundgameid[i] = $(this).val();
        });
        $("input[name='rechargetime[]']").each(function (i) {
             rechargetime[i] = $(this).val();
        });
        $("input[name='rechargeamount[]']").each(function (i) {
             rechargeamount[i] = $(this).val();
        });

        if (validate(oldpwd, oldmail, oldmobile, boundgameid ,rechargeamount) == false) {
            return;
        }

      
    })


    /**
     * 验证
     * @param oldpwd
     * @param oldmail
     * @param oldmobile
     * @param boundgameid
     * @param province
     * @param city
     * @param area
     * @param logintime
     */
    function validate(oldpwd, oldmail, oldmobile,boundgameid, rechargeamount) {
        var runnable = true;
        for (var i = 0; i < oldpwd.length; i++) {
            if (i == 0 && oldpwd[i] == '') {
                $.toast("请输入历史密码", "text");
                runnable = false;
				return
            }
        }
		
        if (boundgameid[0] == '') {
             $.toast("请选择登录过的游戏", "text");
             runnable = false;
			 return
        }

		
		for (var i = 0; i < oldpwd.length; i++) {
		    if ( oldpwd[i] != '' && (oldpwd[i].length<6 || oldpwd[i].length>15)) {
		        $.toast("密码长度为6-15位字符！", "text");
		        runnable = false;
				return
		    }
		}
         
		 for (var i = 0; i < oldmail.length; i++) {
			 var preg = /^[a-z0-9]+([._-][a-z0-9]+)*@([0-9a-z]+\.[a-z]{2,14}(\.[a-z]{2})?)$/i;
		     if ( oldmail[i] != '' && !preg.test(oldmail[i])) {
		         $.toast("请输入正确的邮箱", "text");
		         runnable = false;
		 		return
		     }
		 }
		
		for (var i = 0; i < oldmobile.length; i++) {
			var hreg = /^1[3456789]{1}\d{9}$/;
		    if ( oldmobile[i] != '' && !hreg.test(oldmobile[i])) {
		        $.toast("请输入正确的手机号", "text");
		        runnable = false;
				return
		    }
		}
		
		for (var i = 0; i < rechargeamount.length; i++) {
			var reg = /(^[1-9]{1}[0-9]*$)/;
		    if ( rechargeamount[i] != '' && !reg.test(rechargeamount[i])) {
		        $.toast("请输入正确的充值金额", "text");
		        runnable = false;
				return
		    }
		}

        return runnable;
    }

</script>
{/block}
