={extend name="layout/base" /}

{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>账号申诉</h1>
</header>

<ul class="text-center top-half">
	<li class="step1 pass">
		<div class="box-outside" id="outside">
			<div class="box-num">
				1
			</div>
		</div>
		账号信息
	</li>
	<li class="step2 pass">
		<div class="box-outside" id="outside">
			<div class="box-num">
				2
			</div>
		</div>
		历史信息
	</li>
	<li class=" step3 current">
		<div class="box-outside" id="outside">
			<div class="box-num">
				3
			</div>
		</div>
		提交成功
	</li>

	<div class="clear">

	</div>
</ul>
<!-- 申诉成功 -->
<div class="appeal-end">
	<img src="__STATIC__/images/mobile/icon/success.png">
	<p>您的账号<span>gghhjj</span>申诉已受理</p>
	<p>申诉编号<span class="appeal-code"> [201908291656439]</span> ，请妥善保管次申诉编号，已备后续使用</p>
	<p>点击<a href="">【申诉查询】</a>了解您账号当前申诉处理进度</p>

	<a href="javascript:;" class="copy">复制申诉编号</a>
</div>
<!-- 申诉成功end -->
<!-- 申诉失败 -->
<div class="appeal-end">
	<img src="__STATIC__/images/mobile/icon/default.png">
	<p>抱歉，您的账号<span>gghhjj</span>申诉已受理</p>
	<p>申诉编号为<span class="appeal-code"> [201908291656439]</span> ，但因资料不正确或不完善而未通过审核</p>
	<p>您可稍后再申诉或联系麻花网络客服咨询</p>

	<a href="javascript:;" class="copy">复制申诉编号</a>
</div>
<!-- 申诉失败end -->


<div class="back-csc">
	<a href="">返回客服中心</a>
</div>
{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	$(".copy").click(function() {
		var appealcode = $('.appeal-code').html();
		appealcode = appealcode.slice(1, appealcode.length);
		appealcode = appealcode.substring(1,appealcode.length - 1);
		var oInput = document.createElement('input');
		oInput.value = appealcode;
		document.body.appendChild(oInput);
		oInput.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
		oInput.className = 'oInput';
		oInput.style.display = 'none';
		$.toast("复制成功", "text");
	})
</script>
{/block}
