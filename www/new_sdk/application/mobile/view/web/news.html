{extend name="layout/base" /}
{block name="title"}
<title>游戏昵称下载_游戏昵称开服开测_麻花网络</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>资讯</h1>
		<div>
			<div class="search-icon"><a href=""><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
	<ul class="game_rec">
		<li><a href="" class=" active">资讯</a></li>
		<li><a href="">麻花网络</a></li>
		<li><a href="">评测</a></li>
		<li><a href="">攻略</a></li>
		<li><a href="">视频</a></li>
	</ul>
</header>
<!-- header end -->

<!-- 游戏资讯 -->
<div class="info-new" style="margin-top: 1rem;">
	<p>相关资讯<span>25</span>个</p>
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>《拳无双》火爆不删档测试震撼开启《拳无双》火爆不删档测试震撼开启《拳无双》火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>《拳无双》火爆不删档测试震撼开启火爆不删档测试震撼开启火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href=""> 
			<h4>《拳无双》火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
</div>
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>


<!-- 
	<div class="no_content">
		<div>
			<img src="/static/images/mobile/icon/information.png">
			<p>暂无相关资讯</p>
		</div>
	</div> -->
	
{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>

	// 加载更多
	
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;
	
		// if (currentPage + 1 > lastPage) {
		// 	$(".weui-loadmore").hide();
		// 	if (currentPage > 1) {
		// 		$(".no-more-data").show();
		// 	}
		// 	loading = false;
		// 	return false;
		// }
		$(".weui-loadmore").show();
		setTimeout(function() {
						$(".info-new").append(
					'<div class="new-info">'+
						'<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>'+
						'<div>'+
						'<a href="">'+
							'<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>'+
							'<p>2019-9-09 10:24:30</p></a>'+
						'</div>'+
					'</div>'
						);
						
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});
</script>


{/block}
