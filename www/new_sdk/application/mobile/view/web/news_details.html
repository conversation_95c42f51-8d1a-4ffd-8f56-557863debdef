{extend name="layout/base" /}
{block name="title"}
<title>游戏昵称下载_游戏昵称开服开测_麻花网络</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<style>
	.weui-loadmore{
		background: #F9F9F9;
	}
	.weui-loadmore {
		display: none;
		width: 100%;
		margin: 0;
		padding: 1.5em 0 0;
	}
</style>
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>资讯详情</h1>
		<div>
			<div class="search-icon"><a href=""><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end --> 
<div class="news-details top-half">
	
	<h1> 《梦幻西游》燃烧暑假带你一统天下《梦幻西游》    燃烧暑假带你一统天下</h1>
	<div>
		<p>来源：麻花网络整理</p>
		<p>发布时间：2019-08-09 15:08:30</p>
	</div>
	
	<p>身悬三尺剑，铁马金戈来。《萌萌守卫塔防》8月10日超燃来袭。那个时代，诞生了无数的悍将与能臣，也创造了一个个精彩的故事，那个时代，有激烈冲杀的战争、有杀气四起的朝堂，也有无数的遗憾。《萌萌守卫塔防》带你梦回千古，与各路英豪一起逐鹿天下。</p>
	
	<img src="__STATIC__/images/mobile/slider.jpg" />
	<p>身悬三尺剑，铁马金戈来。《萌萌守卫塔防》8月10日超燃来袭。那个时代，诞生了无数的悍将与能臣，也创造了一个个精彩的故事，那个时代，有激烈冲杀的战争、有杀气四起的朝堂，也有无数的遗憾。《萌萌守卫塔防》带你梦回千古，与各路英豪一起逐鹿天下。</p>
	
	<img src="__STATIC__/images/mobile/slider.jpg" />
	<p>身悬三尺剑，铁马金戈来。《萌萌守卫塔防》8月10日超燃来袭。那个时代，诞生了无数的悍将与能臣，也创造了一个个精彩的故事，那个时代，有激烈冲杀的战争、有杀气四起的朝堂，也有无数的遗憾。《萌萌守卫塔防》带你梦回千古，与各路英豪一起逐鹿天下。</p>
	
	<img src="__STATIC__/images/mobile/slider.jpg" />
	
</div>
<div class="rela-news">
	<div class="title">
			<h4><span></span>游戏开测</h4>
	</div>
	
	<ul>
		<li>
			<a href="">
			<p>【拳无双】火爆不删档测试震撼开启火爆不删档测试震撼开启</p>
			<span>2019-09-22</span>
			</a>
		</li>
		<li>
			<a href="">
			<p>【拳无双】火爆不删档测试震撼开启</p>
			<span>2019-09-22</span>
			</a>
		</li>
		<li>
			<a href="">
			<p>【拳无双】火爆不删档测试震撼开启</p>
			<span>2019-09-22</span>
			</a>
		</li>
		<li>
			<a href="">
			<p>【拳无双】火爆不删档测试震撼开启</p>
			<span>2019-09-22</span>
			</a>
		</li>
		<li>
			<a href="">
			<p>【拳无双】火爆不删档测试震撼开启</p>
			<span>2019-09-22</span>
			</a>
		</li>
	</ul>
	
	
<!-- 	<div class="no-info">
		  <img src="__STATIC__/images/mobile/icon/information.png">
		  <p style="max-width: 100%;">暂无相关资讯</p>
	</div>	 -->	
</div>





<!-- 相关游戏 -->
<div class="relate-game">
	<a href="">
		<img src="__STATIC__/images/mobile/game.png" />
		<div class="content">
			<h4>阴阳师新手礼包阴阳师新手礼包阴阳师新手礼包</h4>
			<p>类型<span>|</span>题材<span>|</span>999.MB</p>
		</div>
	</a>
	<div class="download">
		<a href="javascript:;">下载</a>
		<p class="android_down_url"></p>
		<p class="ios_down_url"></p>
	</div>
	<img src="__STATIC__/images/mobile/icon/close-white.png" class="close" />
</div>
{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	//头部固定		
	var hT = $('.game-info-title').offset().top,
		hh = $('header').height();
	$(window).scroll(function() {
		var wS = $(this).scrollTop();
		if (wS >= hT - hh) {
			$('.game-info-title').addClass("fixed-top");
		} else {
			$('.game-info-title').removeClass("fixed-top");
		}
	});
	
	// 加载更多
	
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;
	
		// if (currentPage + 1 > lastPage) {
		// 	$(".weui-loadmore").hide();
		// 	if (currentPage > 1) {
		// 		$(".no-more-data").show();
		// 	}
		// 	loading = false;
		// 	return false;
		// }
		$(".weui-loadmore").show();
		setTimeout(function() {
						$(".info-new").append(
					'<div class="new-info">'+
						'<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>'+
						'<div>'+
						'<a href="">'+
							'<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>'+
							'<p>2019-9-09 10:24:30</p></a>'+
						'</div>'+
					'</div>'
						);
						
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});
</script>


{/block}
