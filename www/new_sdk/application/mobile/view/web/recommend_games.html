{extend name="layout/base" /}
{block name="title"}
<title>热门游戏_热门手机游戏排行_麻花网络</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}
{block name="content"}
		<!-- header start -->
		<header class="top">
			<div class="header">
				<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
				<h1>推荐游戏</h1>
				<div>
					<div class="search-icon"><a href=""><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
					<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
				</div>
			</div>
			<ul class="game_rec">
				<li><a href="" class="active">热门游戏</a></li>
				<li><a href="">最新游戏</a></li>
				<li><a href="">本周推荐</a></li>
			</ul>
		</header>
		<!-- header end -->
	
		<div class="game-list">
			<div class="game-info">
				<div class="number number-1">1</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number number-2">2</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙消零世界消零世界消零世界消零世界</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					</div>
				</a>
				<div lazy-src="" class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number number-3">3</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number">4</div>
				<a href="">
					<img  lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number">5</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number">6</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number">7</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number">8</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number">9</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<div class="number">10</div>
				<a href="">
					<img lazy-src="" src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

		</div>
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>


{include file="layout/footer" /}
{/block}

{block name="detail_js"}
	<script>
			$(".no-more-data").hide();
			var loading = false; //状态标记
			$(document.body).infinite(90).on("infinite", function() {
				if (loading) return;
				loading = true;
				
				$(".weui-loadmore").show();
				setTimeout(function() {
					$(".warp-gamelist.active .game-list").append(
					"<div class='game-info'>"+
				"<div class='number'>10</div>"+
				"<a href=''>"+
					"<img src='__STATIC__/images/icon/150-150.png' />"+
					"<div class='content'><h4>饥饿龙</h4><p>卡牌<span>|</span>回合<span>|</span>100.55MB</p><P>体验电影联动任务，赢取精美</P></div></a>"+
					"<div class='download'>"+
					"<a href='javascript:;'>下载</a>"+
					"<p class='android_down_url'></p>"+
					"<p class='ios_down_url'></p></div></div>"
				);
					$(".weui-loadmore").hide();
					loading = false;
				}, 500); //模拟延迟
			});
		</script>
{/block}
