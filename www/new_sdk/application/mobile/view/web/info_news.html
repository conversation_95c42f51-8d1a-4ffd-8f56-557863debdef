{extend name="layout/base" /}
{block name="title"}
<title>游戏昵称下载_游戏昵称开服开测_麻花网络</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<style>
	.weui-loadmore{
		background: #F9F9F9;
	}
	.weui-loadmore {
		display: none;
		width: 100%;
		margin: 0;
		padding: 1.5em 0 0;
	}
</style>
{/block}
{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>大话西游大话西游大话西游大话西游大话西游</h1>
		<div>
			<div class="search-icon"><a href=""><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->

<div class="top-half game-content">
	<img src="__STATIC__/images/icon/150-150.png">
	<h2>大话西游大话西游大话西游大话西游大话西游</h2>
	<p>类型<span>|</span>题材<span>|</span>1.55GB</p>
	<p>文能挂机喷队友，武能越塔送人头文能挂机喷队友，武能越塔送人头</p>
	<div class="download">
		<a href="javascript:;">下载</a>
		<p class="android_down_url"></p>
		<p class="ios_down_url"></p>
	</div>
</div>

<ul class="game-info-title">
	<li><a href="">介绍</a></li>
	<li><a href="" class="active">资讯</a></li>
	<li><a href="">礼包</a></li>
</ul>

<!-- 游戏资讯 -->
<div class="info-new">
	<p>相关资讯<span>25</span>个</p>
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>《拳无双》火爆不删档测试震撼开启《拳无双》火爆不删档测试震撼开启《拳无双》火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>《拳无双》火爆不删档测试震撼开启火爆不删档测试震撼开启火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href=""> 
			<h4>《拳无双》火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
	<div class="new-info">
		<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>
		<div>
			<a href="">
			<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>
			<p>2019-9-09 10:24:30</p>
			</a>
		</div>
	</div>
	
</div>
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>



	<!-- <div class="no-info">
		  <img src="__STATIC__/images/mobile/icon/information.png">
		  <p>暂无相关资讯</p>
	</div> -->


<!-- 相关游戏 -->
<div class="relate-game">
	<a href="">
		<img src="__STATIC__/images/mobile/game.png" />
		<div class="content">
			<h4>阴阳师新手礼包阴阳师新手礼包阴阳师新手礼包</h4>
			<p>类型<span>|</span>题材<span>|</span>999.MB</p>
		</div>
	</a>
	<div class="download">
		<a href="javascript:;">下载</a>
		<p class="android_down_url"></p>
		<p class="ios_down_url"></p>
	</div>
	<img src="__STATIC__/images/mobile/icon/close-white.png" class="close" />
</div>
{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	//头部固定		
	var hT = $('.game-info-title').offset().top,
		hh = $('header').height();
	$(window).scroll(function() {
		var wS = $(this).scrollTop();
		if (wS >= hT - hh) {
			$('.game-info-title').addClass("fixed-top");
		} else {
			$('.game-info-title').removeClass("fixed-top");
		}
	});
	
	// 加载更多
	
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;
	
		// if (currentPage + 1 > lastPage) {
		// 	$(".weui-loadmore").hide();
		// 	if (currentPage > 1) {
		// 		$(".no-more-data").show();
		// 	}
		// 	loading = false;
		// 	return false;
		// }
		$(".weui-loadmore").show();
		setTimeout(function() {
						$(".info-new").append(
					'<div class="new-info">'+
						'<a href=""> <img src="__STATIC__/images/mobile/slider.jpg"></a>'+
						'<div>'+
						'<a href="">'+
							'<h4>火爆不删档测试震撼开启火爆不删档测试震撼开启</h4>'+
							'<p>2019-9-09 10:24:30</p></a>'+
						'</div>'+
					'</div>'
						);
						
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});
</script>


{/block}
