{extend name="layout/base" /}
{block name="title"}
<title>类型手机游戏_麻花网络</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}
{block name="content"}
		<!-- header start -->
		<header class="top">
			<div class="header">
				<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
				<div>
					<a href=""><h2 class="active type">类型</h2></a>
					<a href=""><h2>题材</h2></a>
				</div>
				<div>
					<div class="search-icon"><a href=""><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
					<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
				</div>
			</div>
		</header>
		<!-- header end -->


		<ul class="game-category-title">
			<li class="active"><a href="">全部</a></li>
			<li><a href="">角色</a></li>
			<li><a href="">动作</a></li>
			<li><a href="">休闲</a></li>
			<li><a href="">体育</a></li>
			<li><a href="">策略</a></li>
			<li><a href="">射击</a></li>
			<li><a href="">回合</a></li>
			<li><a href="">卡牌</a></li>
			<li><a href="">放置</a></li>
			<li><a href="">竞技</a></li>
		</ul>
<div>
	<div class="warp-gamelist">
		<div class="game-list" >
			<p>共<span>211</span>款游戏</p>
			
			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙消零世界消零世界消零世界消零世界</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>

			<div class="game-info">
				<a href="">
					<img src="__STATIC__/images/icon/150-150.png" />
					<div class="content">
						<h4>饥饿龙</h4>
						<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
						<P>体验电影联动任务，赢取精美</P>
					</div>
				</a>
				<div class="download">
					<a href="javascript:;">下载</a>
					<p class="android_down_url"></p>
					<p class="ios_down_url"></p>
				</div>
			</div>
			
			
		</div>	
		
		<div class="weui-loadmore">
			<i class="weui-loading"></i>
			<span class="weui-loadmore__tips">数据加载中，请稍等</span>
		</div>
		<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>
		
		{include file="layout/footer" /}
		
		
		
		<!-- 无相关游戏 -->
	<!-- 	<div class="no_content">
			<div>
				<img src="/static/images/mobile/icon/no-gift.png">
				<p>暂无相关游戏</p>
			</div>
		</div> -->
		<!-- 无相关游戏end-->
			 
		
		</div>	
	
	</div>

{/block}

{block name="detail_js"}
<script>
		
			$(".no-more-data").hide();
			var loading = false; //状态标记
			$(document.body).infinite(90).on("infinite", function() {
				if (loading) return;
				loading = true;
				
				// if (currentPage + 1 > lastPage) {
				// 	$(".weui-loadmore").hide();
				// 	if (currentPage > 1) {
				// 		$(".no-more-data").show();
				// 	}
				// 	loading = false;
				// 	return false;
				// }
				$(".weui-loadmore").show();
				setTimeout(function() {
					$(".game-list.active").append(
					"<div class='game-info'>"+
				"<a href=''>"+
					"<img src='__STATIC__/images/icon/150-150.png' />"+
					"<div class='content'><h4>饥饿龙</h4><p>卡牌<span>|</span>回合<span>|</span>100.55MB</p><P>体验电影联动任务，赢取精美</P></div></a>"+
					"<div class='download'>"+
					"<a href='javascript:;'>下载</a>"+
					"<p class='android_down_url'></p>"+
					"<p class='ios_down_url'></p></div></div>"
				);
					$(".weui-loadmore").hide();
					loading = false;
				}, 500); //模拟延迟
			});
		</script>
{/block}
		

