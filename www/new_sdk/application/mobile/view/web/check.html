{extend name="layout/base" /}
{block name="title"}<title>验证手机_账号申诉_麻花网络客服中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>账号申诉</h1>
</header>
<div class="verify-bind">

	<!-- 邮箱验证 -->
	<div class="verify-mail">
		<div  class="tip">
			<p>绑定邮箱：114****@qq.com </p>
            <p>请点击“发送验证码”，获取短信验证码后，进行手机验证</p>
                            
		</div>
		<div class="warp-input">
			<p>验证码：</p>
			<input type="number" placeholder="请输入验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送短信">
			<span></span>
		</div>
		<input type="button" value="确定" class="submit clickable" />
		<input type="button" id="TencentCaptcha" data-appid="" data-cbfn="callback" style="display: none;" />
	</div>


	<!-- 手机号验证 -->
	<div class="verify-phone">
		<div class="tip">
			<p>绑定邮箱：36****6664 </p>
			<p>请点击“发送验证码”，获取短信验证码后，进行手机验证</p>
		</div>
		<div class="warp-input">
			<p>验证码：</p>
			<input type="number" placeholder="请输入验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送短信">
			<span></span>
		</div>
		<input type="button" value="确定" class="submit clickable" />
		<input type="button" id="TencentCaptcha" data-appid="" data-cbfn="callback" style="display: none;" />
	</div>


	
</div>

{/block}

{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	var iHigh = document.body.clientHeight;
	var aHigh = window.screen.height;
	if (aHigh > iHigh) {
		$(".footer").addClass("fixed");
	}
	
	if ($(".verify-mail").length > 0) {
	    var type = "email";
	} else {
	    var type = "phone";
	}
	
	// 点击发送验证码
	$(".sendcode").click(function(){
		$("#TencentCaptcha").trigger("click");
	});
	
	
	//拼图验证
	if ($(".verify-mail").length > 0) {
		$("header h1").html("绑定邮箱");
		var type = "email";
	} else {
		$("header h1").html("绑定手机");
		var type = "phone";
	}
	var ticket = '';
	var randstr = '';
	window.callback = function(res) {
		if (res.ret === 0) {
			ticket = res.ticket;
			randstr = res.randstr;
		   var bindnum = $(".bindnum").val();
			
		}
	}
	
	// 倒计时方法
	function getcode() {
		var count = 60;
		$('.sendcode').addClass('hasSend');
		var index = setInterval(function() {
			if (count >= 0) {
				$('.sendcode').val(count + ' s');
				$('.sendcode').attr('disabled', true);
	
				count--;
			} else {
				$('.sendcode').removeClass('hasSend').val('发送验证码');
				$('.sendcode').attr('disabled', false);
				clearInterval(index);
			}
		}, 1000);
	}
	
	
	
	
	//邮箱验证点击下一步
	$(".verify-mail .submit").click(function(){
		var getcode = $(".verify-mail #getcode").val();
		if(getcode==""){
			$.toast("请输入验证码", "text");
		}else{
			// ajax 判断
            next(getcode)
		}
	})
	
	
	// 手机号验证点击下一步
	$(".verify-phone .submit").click(function(){
		var getcode = $(".verify-phone #getcode").val();
		if(getcode==""){
			$.toast("请输入验证码", "text");
		}else{
			// ajax 判断
            next(getcode)
		}
	})

    function next(code) {
        $.ajax({
            type: 'POST',
            url: "/member/resetpwdcheckcode",
            dataType: 'json',
            data: { type: type, code: code },
            success: function (json) {
                if (json.code == 1) {
                    window.location.href = json.url;
                } else {
                    $.toast(json.msg, "text");
                }
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });
    }
</script>
{/block}
