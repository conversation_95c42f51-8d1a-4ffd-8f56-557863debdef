{extend name="layout/base" /}

{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<div>
			<a href="">
				<h2 class="active type">开服</h2>
			</a>
			<a href="">
				<h2>开测</h2>
			</a>
		</div>
		<div>
			<div class="search-icon"><a href=""><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->
<div class="warp-service">

	<div class="game-list">

		<div class="service-date">
			<img src="__STATIC__/images/mobile/icon/date.png">
			<p>今日开服<span>09月09日</span></p>
		</div>

		<div class="game-info">
			<!-- have gift -->
			<a href=""><img src="__STATIC__/images/mobile/icon/gift.png" class="gift"></a>
			<a href="">
				<img src="__STATIC__/images/mobile/game.png">
				<div class="content">
					<h4 class="title">饥饿龙饥饿龙饥饿龙饥饿龙饥饿龙饥饿龙饥饿龙</h4>
					<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					<P class="highlight">今日10:00 <span>|</span> S19君临天下</P>
				</div>
			</a>
			<div class="order"><a href="javascript:;">预约</a></div>
		</div>

		<div class="game-info">
			<a href="">
				<img src="__STATIC__/images/mobile/game.png">
				<div class="content">
					<h4 class="title">饥饿龙</h4>
					<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					<P class="highlight">今日10:00 <span>|</span> S19君临天下S19君临天下S19君临天下</P>
				</div>
			</a>
			<div class="order"><a href="javascript:;">预约</a></div>
		</div>

		<div class="service-date">
			<img src="__STATIC__/images/mobile/icon/date.png">
			<p>明日开服<span>09月09日</span></p>
		</div>

		<div class="game-info">
			<a href="">
				<img src="__STATIC__/images/mobile/game.png">
				<div class="content">
					<h4 class="title">饥饿龙</h4>
					<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					<P class="highlight">明日10:00 <span>|</span> 新服名</P>
				</div>
			</a>
			<div class="order"><a href="javascript:;">预约</a></div>
		</div>

		<div class="game-info">
			<!-- have gift -->
			<a href=""><img src="__STATIC__/images/mobile/icon/gift.png" class="gift"></a>
			<a href="">
				<img src="__STATIC__/images/mobile/game.png">
				<div class="content">
					<h4 class="title">饥饿龙</h4>
					<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					<P class="highlight">明日10:00 <span>|</span> 新服名</P>
				</div>
			</a>
			<div class="order"><a href="javascript:;">预约</a></div>
		</div>

		<div class="service-date">
			<img src="__STATIC__/images/mobile/icon/date.png">
			<p>10月1日开服</p>
		</div>

		<div class="game-info">
			<a href="">
				<img src="__STATIC__/images/mobile/game.png">
				<div class="content">
					<h4 class="title">饥饿龙</h4>
					<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					<P class="highlight">10月1日10:00<span>|</span> 新服名</P>
				</div>
			</a>
			<div class="order"><a href="javascript:;">预约</a></div>
		</div>

		<div class="service-date">
			<img src="__STATIC__/images/mobile/icon/date.png">
			<p>长期有效</p>
		</div>

		<div class="game-info">
			<a href="">
				<img src="__STATIC__/images/mobile/game.png">
				<div class="content">
					<h4 class="title">饥饿龙</h4>
					<p>卡牌<span>|</span>回合<span>|</span>100.55MB</p>
					<P class="highlight">长期有效 <span>|</span> 新服名</P>
				</div>
			</a>
			<div class="order"><a href="javascript:;">预约</a></div>
		</div>



	</div>

</div>
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>
{/block}
{include file="layout/footer" /}
{/block}
{block name="detail_js"}
<script>
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;

		$(".weui-loadmore").show();
		setTimeout(function() {

			$(".game-list").append(
				// 加载的数据
				"加载的数据"
			)

			$.getScript("/static/js/mobile/common.js");
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
	});

	// 预约
	$(".order").on("click", function() {

		$.modal({
		    title: "提示",
		    text: "请先绑定手机号",
		    buttons: [{
		            text: "去绑定",
		            onClick: function() {
		           // 跳转链接
		            }
		        },
		        {
		            text: "取消",
		            className: "default",
		            onClick: function() {}
		        },
		    ]
		});

	// $.modal({
	// 	title: "提示",
	// 	text: "离开测时间不足1小时，无法预约",
	// 	buttons: [{
	// 			text: "确定",
	// 			className: "pop-button",
	// 			onClick: function() {}
	// 		},
	// 		{
	// 			text: "关闭",
	// 			className: "default",
	// 			onClick: function() {}
	// 		},
	// 	]
	// });

		// $.modal({
		// 	title: "提示",
		// 	text: "您已成功预约过该开测信息",
		// 	buttons: [{
		// 			text: "确定",
		// 			className: "pop-button",
		// 			onClick: function() {}
		// 		},
		// 		{
		// 			text: "关闭",
		// 			className: "default",
		// 			onClick: function() {}
		// 		},
		// 	]
		// });

		// $.modal({
		// 	title: "提示",
		// 	text: "您已成功预约该开测信息，到时会以短信形式通知，请留意",
		// 	buttons: [{
		// 			text: "确定",
		// 			className: "pop-button",
		// 			onClick: function() {}
		// 		},
		// 		{
		// 			text: "关闭",
		// 			className: "default",
		// 			onClick: function() {}
		// 		},
		// 	]
		// });

	})
</script>
{/block}
</body>
</html>
