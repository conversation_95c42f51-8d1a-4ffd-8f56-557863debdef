{extend name="layout/base" /}
{block name="title"}<title>麻花网络</title>{/block}
{block name="header"}
<!-- 引入样式 -->
<!-- import CSS -->
<!--引入 element-ui 的样式，-->
<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
<!-- 必须先引入vue，  后使用element-ui -->
<script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>
<!-- 引入element 的组件库-->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>
<script src="__STATIC__/js/jquery v1.11.3.min.js"></script>
<!--<link rel="stylesheet" href="__STATIC__/css/coin/topup.css">-->
{/block}


{block name="content"}
<div id="app" class="coin">
    <div class="classifyincreased_b">
        <div class="box">
            <!--            <div class="title" @click="topHide">-->
            <!--                <img src="__STATIC__/images/coin/组 <EMAIL>" alt=""/>-->
            <!--            </div>-->
            <div class="outer-div">
                <header class="header">
                    <ul class="tab-tilte" v-for="(item, index) in payList">
                        <li :class="{ active: tab == item.tab }" @click="tab = item.tab">
                            <div class="li_a">
                                <img :src=item.image alt=""/><span>{{item.name}}</span>
                            </div>
                        </li>
                    </ul>
                </header>

                <div class="tab-content">
                    <div v-show="tab == 'old-zfb-wap' || tab == 'kdh5' ">
                        <!-- 账号: -->
                        <div class="info">

                            <div>
                                <el-select v-model="accountType" class="info_select" placeholder="请选择账号"
                                         >
                                    <el-option v-for="item in account" :key="item.value" :label="item.label"
                                               :value="item.value">
                                    </el-option>
                                </el-select>
                                <span class="b" v-if="accountType==1">{$userinfo['username'] ?? ''}</span>
                                <span v-if="accountType==2">
                                    <input type="text" placeholder="请输入账号" v-model="username" style="height: 34px;">
                                    确定账号: <input type="text" placeholder="确定账号" v-model="passusername"
                                                 @change="handleChangeUserName" style="height: 34px;">
                                </span>
                            </div>

                            <div v-if="accountType==1">
                                <span>平台币余额:</span><span span class="b">{$userinfo['amount']??''}</span>
                            </div>
                            <div class="c">
                                <!--                                充值流水-->
                            </div>
                        </div>
                        <div class="but"></div>
                        <!-- 请选择充值对象: -->
                        <div class="info_item">请选择充值对象:</div>
                        <div class="xz">
<!--                            <el-radio v-model="checkType" label="1">平台币</el-radio>-->
                            <el-radio v-model="checkType" label="2">游戏</el-radio>
                        </div>
                        <div class="ptb" v-show="checkType == 2">
                            <div class="ptb_item">请选择需要充值的游戏:</div>
                            <div class="ptb_index">
                                <el-select v-model="gameid" placeholder="请选择" @change="handleChangeGame">
                                    <el-option v-for="item in gameList" :key="item.id" :label="item.name"
                                               :value="item.id">
                                    </el-option>
                                </el-select>

                                <el-select v-model="serverid" placeholder="请选择区服" @change="handleChangeServer">
                                    <el-option v-for="item in serverList" :key="item.serverid" :label="item.servername"
                                               :value="item.serverid">
                                    </el-option>
                                </el-select>

                                <el-select v-model="roleid" placeholder="请选择角色">
                                    <el-option v-for="item in roleList" :key="item.roleid" :label="item.rolename"
                                               :value="item.roleid">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>

                        <!-- 请选择充值金额: -->
                        <div class="jo">
                            <div class="jo_a">
                                <img src="__STATIC__/images/coin/路径 <EMAIL>" alt=""/>
                                <div class="foot_item">
                                    <div class="foot">兑换比例: 1元=1平台币</div>
                                </div>
                            </div>
                            <span class="jo_b">请选择充值金额:
                                    <div class="foot_index">
                                        <div class="foot">金额将直接充值到所选游戏中</div>
                                    </div>
                                </span>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="10">10元</el-radio>
                                <el-radio :label="20" style="margin-left: 7px">20元</el-radio>
                                <el-radio :label="50" style="margin-left: 8px">50元</el-radio>
                                <el-radio :label="100" style="margin-left: 8px">100元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="200">200元</el-radio>
                                <el-radio :label="400">400元</el-radio>
                                <el-radio :label="800">800元</el-radio>
                                <el-radio :label="1200">1200元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="-1">其他金额:</el-radio>
                            </el-radio-group>
                            <div class="ji_index">￥<input type="text" v-model="amount" name="amount" id="amount"
                                                          @change="handleAmount" @focus="handleAmount"/></div>
                        </div>
                        <div class="butt" @click="pay">
                            <div class="butt_item">立即充值</div>
                        </div>
                    </div>

                    <div v-show="tab == 'coinpay'">
                        <!-- 账号: -->
                        <div class="info">
                            <div>
                                <span class="a">账号:</span><span class="b">{$userinfo['username']??''}</span>
                            </div>
                            <div>
                                <span>平台币余额:</span><span span class="b">{$userinfo['amount']??''}</span>
                            </div>
                            <div class="c">
                                <!--                                充值流水-->
                            </div>
                        </div>
                        <div class="but"></div>
                        <!-- 请选择充值对象: -->

                        <div class="ptb">
                            <div class="ptb_item">请选择需要充值的游戏:</div>
                            <div class="ptb_index">
                                <el-select v-model="gameid" placeholder="请选择" @change="handleChangeGame">
                                    <el-option v-for="item in gameList" :key="item.id" :label="item.name"
                                               :value="item.id">
                                    </el-option>
                                </el-select>

                                <el-select v-model="serverid" placeholder="请选择区服" @change="handleChangeServer">
                                    <el-option v-for="item in serverList" :key="item.serverid" :label="item.servername"
                                               :value="item.serverid">
                                    </el-option>
                                </el-select>

                                <el-select v-model="roleid" placeholder="请选择角色">
                                    <el-option v-for="item in roleList" :key="item.roleid" :label="item.rolename"
                                               :value="item.roleid">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <!-- 请选择充值金额: -->
                        <div class="jo">
                            <div class="jo_a">
                                <img src="__STATIC__/images/coin/路径 <EMAIL>" alt=""/>
                                <div class="foot_item">
                                    <div class="foot">兑换比例: 1元=1平台币</div>
                                </div>
                            </div>
                            <span class="jo_b">请选择充值金额:
                                    <div class="foot_index">
                                        <div class="foot">金额将直接充值到所选游戏中</div>
                                    </div>
                                </span>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="10">10元</el-radio>
                                <el-radio :label="20" style="margin-left: 7px">20元</el-radio>
                                <el-radio :label="50" style="margin-left: 8px">50元</el-radio>
                                <el-radio :label="100" style="margin-left: 8px">100元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="200">200元</el-radio>
                                <el-radio :label="400">400元</el-radio>
                                <el-radio :label="800">800元</el-radio>
                                <el-radio :label="1200">1200元</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="jo_item">
                            <el-radio-group v-model="radio" @change="handleChange">
                                <el-radio :label="-1">其他金额:</el-radio>
                            </el-radio-group>
                            <div class="ji_index">￥<input type="text" v-model="amount" name="amount" id="amount"
                                                          @change="handleAmount" @focus="handleAmount"/></div>
                        </div>
                        <div class="butt" @click="pay">
                            <div class="butt_item">立即充值</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="detail_js"}
<script>
    username2 = "{$username2}"
    username3 = "{$userinfo['username'] ?? ''}"

    if(username2){
        accountTypeStr = "2"
        verifyUserNameStatusStr = 1
    }else{
        if(username3){
            accountTypeStr = "1"
        }else{
            accountTypeStr = "2"
        }

    }
    new Vue({
        el: '#app',
        data: function () {
            return {
                istoday: true,
                tab: "old-zfb-wap",
                radio: 10,
                amount: 10,
                accountType: accountTypeStr,
                isxsyc: false,
                checkType: "2",
                username: username2,
                passusername: username2,
                account: [
                    {
                        value: '1',
                        label: "当前账号:"
                    },
                    {
                        value: '2',
                        label: "其他账号:"
                    },
                ],
                payList: [
                    {
                        tab: 'old-zfb-wap',
                        name: "支付宝支付",
                        image: "__STATIC__/images/coin/m1.png"
                    },
                    // {
                    //     tab: 'xzzfbzf',
                    //     name: "支付宝H5支付(现在)",
                    //     image: "__STATIC__/images/coin/m1.png"
                    // },

                    // {
                    //     tab: 'wxsmzf',
                    //     name: "微信支付",//（汇付宝）扫码
                    //     image: "__STATIC__/images/coin/m2.png"
                    // },
                    {
                        tab: 'kdh5',
                        name: "微信支付",//酷点扫码
                        image: "__STATIC__/images/coin/m2.png"
                    },
                    // {
                    //
                    //     tab: 'wx-wap',
                    //     name: "微信支付",//（汇付宝）H5
                    //     image: "__STATIC__/images/coin/m2.png"
                    // },
                    // {
                    //     tab: 'kdh5',
                    //     name: "微信支付", //酷点H5
                    //     image: "__STATIC__/images/coin/m2.png"
                    // },
                    // {
                    //     tab: 'coinpay',
                    //     name: "平台币支付",
                    //     image: "__STATIC__/images/coin/m3.png"
                    // },
                ],
                gameList: [],
                serverList: [],
                roleList: [],
                options: [],
                gameid: "",
                serverid: "",
                roleid: "",
                verifyUserNameStatus: verifyUserNameStatusStr,
            };
        },
        components: {},
        mounted() {
            // this.getPaytype()
            this.getGameList()
        },
        methods: {
            handleAmount() {
                this.radio = -1
            },

            handleChange(val) {
                if (val > 0) {
                    this.amount = val
                } else {
                    this.amount = null
                }
            },
            handleChangeGame(val) {
                that = this
                that.serverid = ''
                that.roleid = ''
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                $.ajax({
                    url: '/coin/getServerList',
                    type: 'post',
                    data: {gameid: that.gameid},
                    success: function (data) {
                        if (data.code == 1) {
                            that.serverList = data.data
                        }
                    }
                })
            },
            handleChangeServer() {
                that = this
                that.roleid = ''
                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                $.ajax({
                    url: '/coin/getRoleList',
                    type: 'post',
                    data: {
                        gameid: that.gameid,
                        serverid: that.serverid,
                        accountType: that.accountType,
                        username: that.username
                    },
                    success: function (data) {
                        if (data.code == 1) {
                            that.roleList = data.data
                        }
                    }
                })
            },
            handleChangeUserName() {
                that = this
                that.verifyUserNameStatus = 0
                if (that.username != that.passusername) {
                    alert("两次输入的账号不一致")
                    exit
                } else {
                    that = this
                    that.gameid = ''
                    that.serverid = ''
                    that.roleid = ''
                    $.ajax({
                        url: '/coin/verifyUserName',
                        type: 'post',
                        data: {username: that.username},
                        success: function (data) {
                            if (data.code == 1) {
                                that.verifyUserNameStatus = 1
                            } else {
                                alert(data.msg)
                            }
                        }
                    })
                }
            },
            getGameList() {
                that = this
                $.ajax({
                    url: '/coin/getGameList',
                    type: 'post',
                    success: function (data) {
                        if (data.code == 1) {
                            that.gameList = data.data
                        }
                    }
                })
            },

            // getPaytype() {
            //     $.ajax({
            //         url: '/coin/getPayType',
            //         type: 'post',
            //         success: function (data) {
            //             console.log(data)
            //         }
            //     })
            // },
            // 隐藏与显示
            changeIxsyc() {
                this.isxsyc = !this.isxsyc;
            },
            topHide() {
                $('.coin').hide();
            },

            pay() {

                if (this.accountType == 2 && this.verifyUserNameStatus != 1) {
                    alert('请先输入账号验证')
                    return
                }
                that = this
                $.ajax({
                    url: '/coin/pay',
                    type: 'post',
                    data: {
                        type: that.checkType,
                        paytype: that.tab,
                        amount: that.amount,
                        accountType: that.accountType,
                        username: that.username,
                        gameid: that.gameid,
                        serverid: that.serverid,
                        roleid: that.roleid
                    },
                    success: function (data) {
                        console.log(data)
                        paytype = that.tab
                        if (data.code == 1) {
                            if (paytype == 'old-zfb-wap') { //支付宝扫码(支付宝官方扫码)
                                const div = document.createElement('div')
                                div.innerHTML = data.data.alipay_param.mweb_url

                                document.body.appendChild(div)
                                document.forms[0].submit()
                            } else if (paytype == 'kdh5') { //微信扫码（ku_param)
                                window.location.href = data.data.kdh5_param.mweb_url
                            }
                        } else {
                            alert(data.msg)
                        }
                    }
                })
            }
        },
    })
</script>
{/block}
