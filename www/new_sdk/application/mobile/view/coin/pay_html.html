{extend name="layout/base" /}
{block name="title"}<title>麻花网络</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/coin/pay1.css">
{/block}


{block name="content"}


<div id="app">
  <div class="pay">
    <div class="center">
      <div class="center_item"><span>收银台</span>
        <div class="bou"></div>
      </div>
      <div class="paytwo">
        <div class="paytwo_item">
          <div class="pay_a" style="margin-left: 10px;">请您尽快付款，以便订单及时处理！</div>
          <div class="pay_b"><span>应付金额:</span><span style="color:#FB5E5E ;">{$data['real_amount']}</span><span>元</span></div>
        </div>
        <div class="paytwo_item">
          <div class="pay_a" style="font-size: 12px;color: #808080;margin-left: 10px;">请您在提交订单后<span style="color:#FB5E5E">5分钟</span><span>内完成支付，否则订单会自动取消。</span></div>
        </div>
        <div class="paytwo_index">
          <div class="index"><span class="dd" style="margin-left: 10px;">订单号:</span><span class="hm">{$data['orderid']}</span></div>
        </div>
        <div class="paytwo_index">
        <div class="index" style="margin-left: 10px;"><span class="dd">订单时间:</span><span class="hm">{$data['create_time']}</span></div>
        <div class="index" style="margin-left: 30px;"><span class="dd">商品:</span><span class="hm">{$data['goods']}</span></div>
        </div>
        <div class="rwr">
          <div class="rwr_item" id="qrcode"></div>
<!--          <div class="rwr_wzi">客服热线:18115179677</div>-->
        </div>
        <div class="butt">请使用微信扫一扫，扫描二维码支付</div>
<!--        <div class="butt_item">-->
<!--          <div class="butt_left">联系客服</div>-->
<!--          <div class="butt_left">查询支付状态</div>-->
<!--        </div>-->
      </div>
    </div>
  </div>
</div>
{/block}

{block name="detail_js"}
<script src="__STATIC__/js/qrcode.min.js"></script>
<script>
  // url生成二维码
  var qrcode_list = new QRCode(document.getElementById("qrcode"), {
    width: 266,
    height: 266
  });
  qrcode_list.makeCode("{$data['kdsm_param']['mweb_url']}");

</script>
{/block}
