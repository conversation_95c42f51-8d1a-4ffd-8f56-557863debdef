{extend name="layout/base" /}
{block name="title"}
<title>麻花网络手机游戏_资讯_开服开测_礼包搜索_麻花网络</title>
<meta name="keywords" content="手机游戏搜索,手机游戏资讯搜索,手机游戏开服开测搜索,手机游戏礼包搜索"/>
<meta name="description" content="
麻花网络搜索功能，帮您找到想要的手机游戏以及全面的新闻资讯、游戏礼包等信息，输入关键字即可快速搜索。"/>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
<style>
	.warp-search-cont{
		top: 0.556rem;
		z-index: 666;
		}
		.no_content{
			margin-top: 30%;
		}
</style>
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
 <div class="header search-page">
 	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png"></a>
 	<div class="search">
 		<input type="text" value="{$keyword}"  autocomplete="off" class="search-content" id="keywords"   />
 		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
 	</div>
 	<a href="javascript:;"><img src="__STATIC__/images/mobile/icon/search.png" class="input-search" id="search"></a>
 </div>
	<ul class="game_rec">
		<li><a href="javascript:;" class="active search_game">游戏 ({$list->total()})</a></li>
		<li><a href="javascript:;" class="search_new">资讯 ({$newsList->total()})</a></li>
	</ul>
</header>
<div class="warp-search-cont">
   <ul class="search_cont" id="search_ul">

    </ul>
</div>
<!-- header end -->

<div class="game-list search-game">
	{volist name="list" id="vo"}
	<div class="game-info">
		<a href="{$vo.gameUrl}">
		{if($vo.libaonum)}
			<img src="__STATIC__/images/mobile/icon/gift.png" class="gift">
		{/if}
		
			<img lazy-src="{$vo.icon}"  src="__STATIC__/images/icon/150-150.png" class="smallIcon" >
			<div class="content">
				<h4 class="title">{$vo.nickname}</h4>
				<p>{$vo.subject}<span>|</span>{$vo.type}<span>|</span>{$vo.size}</p>
				{if($vo['publicity'])}<P>{$vo.publicity}</P>{/if}
			</div>
		</a>
		<div class="download">
			<a href="javascript:;">下载</a>
			<p class="android_down_url">{$vo.download}</p>
			<p class="ios_down_url">{$vo.ios_download}</p>
		</div>
	</div>
	{/volist}
	{empty name="$list"}
	<!-- 无相关游戏 -->
		<div class="no_content" >
			<div>
				<img src="/static/images/mobile/icon/game.png">
				<p>抱歉暂未搜索到相关游戏，请换个关键词试一试吧！</p>
			</div>
		</div>
	<!-- 无相关游戏end-->
	{/empty}
</div>


<div class="info-new search-new hide" style="margin-top: 1rem;">
	
    {volist name="newsList" id="vo"}
	<div class="new-info">
		<a href="{$vo.url}"> <img lazy-src="{$vo.image}" src="__STATIC__/images/icon/990-625.png" class="new-img"></a>
		<div>
			<a href="{$vo.url}">
			<h4>{$vo.title}</h4>
			<p>{$vo.createtime}</p>
			</a>
		</div>
	</div>
	{/volist}
	
	
	{empty name="$newsList"}
	
	<!-- 无相关资讯 -->
		<div class="no_content">
			<div>
				<img src="/static/images/mobile/icon/information.png" style="width: 2rem;">
				<p>抱歉暂未搜索到相关资讯，请换个关键词试一试吧！</p>
			</div>
		</div>
	<!-- 无相关资讯end-->
		{/empty}
</div>





<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>已经到底啦(>_<)<span></span></div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>
	$(".search_new").click(function(){
		$(this).addClass("active");
		$(".search_game").removeClass("active");
		$(".game-list").addClass("hide");
		$(".search-new").removeClass("hide");
		$(".no-more-data").hide();
		
		// 底部位置
			var iHigh = document.body.clientHeight;
			var aHigh = $(window).height();
			var head = $("header").height();
			var h = $("footer").height();
			if (aHigh > (iHigh + head + h)) {
				$(".footer").addClass("fixed");
				
			}else{
			  $(".footer").removeClass("fixed");
			}
	})
	
	$(".search_game").click(function(){
		$(this).addClass("active");
		$(".search_new").removeClass("active");
		$(".game-list").removeClass("hide");
		$(".search-new").addClass("hide");
		$(".no-more-data").hide();
		var iHigh = document.body.clientHeight;
		var aHigh = window.screen.height;
		var head = $("header").height();
		// var h = $("footer").height();
		if (aHigh > (iHigh + head)) {
			$(".footer").addClass("fixed");
		}else{
			 $(".footer").removeClass("fixed");
		}
	})

	$('.search').on('click', function() {
	   $('#keywords').focus();
	  });
	 $('.search').trigger('click');
		
		$(".search-content").keyup(function() {
		var text = $(this).val();
		text = text.replace(/\s*/g, "");
		if (text != "") {
			$.ajax({
		        type: 'POST',
		        url: '/game/ajaxGameList',
		        dataType: 'json',
		        data: { keyword: text },
		        success: function (json) {

		            if ($.isEmptyObject(json)) {
		                $("#search_ul").empty();
		                $(".warp-search-cont").hide();

		            } else {
		                var html = '',
		                    turl = '/game101';

		                $.each(json, function (i, row) {
		                    toUrl = turl.replace('101', row.id)
		                    html += '<li><a href="' + row.url + '" target="_blank">' + row.nickname + '</a></li>';
		                });

		                arrow = '<div class="border-up-empty"><span></span></div>'
		                $("#search_ul").html(html + arrow);

		            }
					if($(".search_cont li").length > 0){
						$(".warp-search-cont").show();
					}else{
						$(".warp-search-cont").hide();
					}

		        }

		    });
			$(this).next().show();
			// $(".warp-search-cont").show();
		} else {
			$(this).next().hide();
			  $("#search_ul").empty();
			$(".warp-search-cont").hide();
		}
		
	})
	$(".search-content").click(function(){
		if($(".search_cont li").length > 0){
			$(".warp-search-cont").show();
		}
		var text = $(this).val();
		text = text.replace(/\s*/g, "");
		if (text != "") {
			$.ajax({
		        type: 'POST',
		        url: '/game/ajaxGameList',
		        dataType: 'json',
		        data: { keyword: text },
		        success: function (json) {
		
		            if ($.isEmptyObject(json)) {
		                $("#search_ul").empty();
		                $(".warp-search-cont").hide();
		
		            } else {
		                var html = '',
		                    turl = '/game101';
		
		                $.each(json, function (i, row) {
		                    toUrl = turl.replace('101', row.id)
		                    html += '<li><a href="' + toUrl + '">' + row.nickname + '</a></li>';
		                });
		
		                arrow = '<div class="border-up-empty"><span></span></div>'
		                $("#search_ul").html(html + arrow);
		
		            }
					if($(".search_cont li").length > 0){
						$(".warp-search-cont").show();
					}else{
						$(".warp-search-cont").hide();
					}
		
		        }
		
		    });
			$(this).next().show();
			// $(".warp-search-cont").show();
		} else {
			$(this).next().hide();
			  $("#search_ul").empty();
			$(".warp-search-cont").hide();
		}
	})
	
	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
		$(".warp-search-cont").hide();
		$(".search_cont").html("");
	})
	
	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$(".search-content").on("postpaste", function() {
		var text = $(this).val();
		text = text.replace(/\s*/g, "");
		if (text != "") {
			$(this).next().show();
			$(".warp-search-cont").show();
		} else {
			$(this).next().hide();
			$(".warp-search-cont").hide();
		}
			
	}).pasteEvents();



$(".warp-search-cont").click(function(e) {
	var target = $(e.target);
	if (target.closest(".search-content").length != 0) return;	
	$(".warp-search-cont").hide();
})

	
	
	// 加载更多
	var currentPage = parseInt("{$list->currentPage()}");
	var lastPage = parseInt("{$list->lastPage()}");
	var total = parseInt("{$list->total()}");
	var new_currentPage = parseInt("{$newsList->currentPage()}");
	var new_lastPage = parseInt("{$newsList->lastPage()}");
	var new_total = parseInt("{$newsList->total()}");	
	var defaultImg = "__STATIC__/images/icon/150-150.png";
	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		if (loading) return;
		loading = true;
	
		if($('.search_game').hasClass("active")){
		// 游戏加载更多
		
		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			 }
			loading = false;
			return false;
		}else{
			$(".weui-loadmore").show();
		}
		

		var keyword = encodeURI('{$keyword}');
		setTimeout(function() {
			currentPage += 1;
			$.ajax({
		        url: '/search/_getGameList' + '?keyword=' + keyword ,
		        type: 'post',
				timeOut: 10000,
		        dataType: 'json',
		        data: {
		        	"page": currentPage
		        },
				async: false,
		        success: function (res) {
		        console.log(res);
		        	var arr = res.list.data;
		        	arr.forEach(function(vo, index) {
		        		var html='';
		        		html += "<div class='game-info'>"+
		        			"<a href='"+vo.gameUrl+"'>"+
		        			"<img lazy-src='"+vo.icon+"' src='/static/images/icon/150-150.png' class='smallIcon' />"+
		        			"<div class='content'><h4>"+vo.nickname+"</h4><p>"+vo.subject+"<span>|</span>"+vo.type+"<span>|</span>"+vo.size+"</p>";
		        		if(vo.publicity){
		        			html += "<P>"+vo.publicity+"</P>";
		        		}
		        		html += "</div></a>"+
		        			"<div class='download'>"+
		        			"<a href='javascript:;'>下载</a>"+
		        			"<p class='android_down_url'>"+vo.download+"</p>"+
		        			"<p class='ios_down_url'>"+vo.ios_download+"</p></div></div>";
		        		$(".game-list").append(html);
		        	});
		        },
		        error: function() {
		        	$.alert("网络错误，请刷新页面重试");
		        }
		    });
			$.getScript("/static/js/mobile/reload.js");
			$(".weui-loadmore").hide();
			loading = false;
		}, 500); //模拟延迟
		
		
		}else{
			
		// 资讯加载更多	
		
		if (new_currentPage + 1 > new_lastPage) {
			$(".weui-loadmore").hide();
			if (new_currentPage > 1) {
				$(".no-more-data").show();
			 }
			loading = false;
			return false;
		}else{
			$(".weui-loadmore").show();
		}
		
		
        $(".no-more-data").hide();
		$(".weui-loadmore").show();
		setTimeout(function() {
			var keyword = encodeURI('{$keyword}');
			new_currentPage += 1;
			$.ajax({
		        url: '/search/_getNewsList' + '?keyword=' + keyword ,
		        type: 'post',
				timeOut: 10000,
		        dataType: 'json',
		        data: {
		        	"page": new_currentPage
		        },
				async: false,
		        success: function (res) {
		        console.log(res);
		        	var arr = res.list.data;
		        	arr.forEach(function(vo, index) {
		        		var html='';
		        		html += '<div class="new-info">'+
							'<a href="'+vo.url+'"> <img lazy-src="'+vo.image+'"  src="__STATIC__/images/icon/990-625.png" class="new-img"></a>'+
							'<div>'+
							'<a href="'+vo.url+'">'+
								'<h4>'+vo.title+'</h4>'+
								'<p>'+vo.createtime+'</p></a>'+
							'</div>'+
						'</div>';
		        		$(".info-new").append(html);
		        	});
		        },
		        error: function() {
		        	$.alert("网络错误，请刷新页面重试");
		        }
		    });
			$.getScript("/static/js/mobile/reload.js");
			$(".weui-loadmore").hide();
			loading = false;

		}, 500); //模拟延迟
		
			
			
		}
	});
	
	
	

// 搜索历史

if($(".history-search ul li").length ==0){
	$(".history-search").hide();
}else{
	$(".history-search").show();
}
   $(function(){ 
        update_history(); 
        // 绑定回车事件 
        $(document).keydown(function(event){  
            if(event.keyCode==13){  
                $("#search").click();  
            }  
        });  
 
        // 搜索点击事件 
        $("#search").click(function(){ 
            var keywords = $("#keywords").val(); 
			keywords= keywords.replace(/\s*/g, "");
            historya(keywords); //添加到缓存 
            update_history(); //更新搜索历史 
			if (keywords == ""){
				keywords ="空之轨迹";
			}
			location.href = "/search/keyword/" + keywords;
        }) 

    }) 
 
 
    /** 
     * [update_history 更新搜索历史] 
     * @return {[type]} [description] 
     */ 
    function update_history(){ 
        console.log(mystorage.get("keywords")); 
        var history = mystorage.get("keywords"); 
        if (history) { 
            var html = ""; 
            $.each(history,function(i,v){ 
                html += "<li><a href=''>"+v+"</a><a href='javascript:;'  class='delete'><img src='/static/images/mobile/icon/close.png'></a></li>" 
            }) 
            $(".history-search ul").html(html); 
        }; 
		if($(".history-search ul li").length ==0){
			$(".history-search").hide();
		}else{
			$(".history-search").show();
		}
		// 点击删除历史
		$(".delete").click(function(){
		   var tqtest =  $(this).prev().html();
		   var history = mystorage.get("keywords");
		   history.splice($.inArray(tqtest,history),1);
		   mystorage.set("keywords",history); 
		   $(this).parents("li").remove();
			update_history(); 
		}) 
    } 

			 
 
    /** 
     * [history //搜索历史函数存储] 
     * @param  {[type]} value [搜索词] 
     * @return {[type]}       [description] 
     */ 
  function historya(value){
      var data = mystorage.get("keywords"); 
  
      if (!data) { 
          var data = []; //定义一个空数组 
      }else if(data.length === 10){ //搜索历史数量 
          data.pop();  //删除数组最后一个元素
      }else{ 
      }; 
    
      if (value) {  //判断搜索词是否为空 
          if (data.indexOf(value)<0) {  //判断搜索词是否存在数组中 
              // data.push(value);    //搜索词添加到数组中 
        data.splice(0, 0,value); //搜索词添加到数组第一个位置 
              mystorage.set("keywords",data);  //存储到本地化存储中 
        
          }else{
				data.splice($.inArray(value,data),1);
				data.splice(0, 0,value); //搜索词添加到数组第一个位置 
				mystorage.set("keywords",data);  //存储到本地化存储中 
				
			};
      }; 
  }
 
    /** 
     * [mystorage 存储localstorage时候最好是封装一个自己的键值，在这个值里存储自己的内容对象，封装一个方法针对自己对象进行操作。避免冲突也会在开发中更方便。] 
     * @param  {String} ){                 var ms [description] 
     * @return {[type]}     [description] 
            console.log(mystorage.set('tqtest','tqtestcontent'));//存储 
            console.log(mystorage.set('aa','123'));//存储 
            console.log(mystorage.set('tqtest1','tqtestcontent1'));//存储 
            console.log(mystorage.set('tqtest1','newtqtestcontent1'));//修改 
            console.log(mystorage.get('tqtest'));//读取 
            console.log(mystorage.remove('tqtest'));//删除 
            mystorage.clear();//整体清除 
     */ 
    var mystorage = (function mystorage(){ 
        var ms = "mystorage"; 
        var storage=window.localStorage; 
        if(!window.localStorage){ 
            alert("浏览器不支持localstorage"); 
            return false; 
        } 
        var set = function(key,value){ 
            //存储 
            var mydata = storage.getItem(ms); 
            if(!mydata){ 
                this.init(); 
                mydata = storage.getItem(ms); 
            } 
            mydata = JSON.parse(mydata); 
            mydata.data[key] = value; 
            storage.setItem(ms,JSON.stringify(mydata)); 
            return mydata.data; 
        }; 
        var get = function(key){ 
            //读取 
            var mydata = storage.getItem(ms); 
            if(!mydata){ 
                return false; 
            } 
            mydata = JSON.parse(mydata); 
            return mydata.data[key]; 
        }; 
        var remove = function(key){ 
            //读取 
            var mydata = storage.getItem(ms); 
            if(!mydata){ 
                return false; 
            } 
            mydata = JSON.parse(mydata); 
            delete mydata.data[key]; 
            storage.setItem(ms,JSON.stringify(mydata)); 
            return mydata.data; 
        }; 
        var clear = function(){ 
            //清除对象 
            storage.removeItem(ms); 
        }; 
        var init = function(){ 
          storage.setItem(ms,'{"data":{}}'); 
        }; 
        return { 
            set : set, 
            get : get, 
            remove : remove, 
            init : init, 
            clear : clear 
        }; 
    })(); 
</script>
{/block}
