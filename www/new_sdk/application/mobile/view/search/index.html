{extend name="layout/base" /}

{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
<link rel="stylesheet" href="__STATIC__/css/mobile/game-info.css">
{/block}

{block name="content"}

<!-- page top start -->
<header class="top">
	<div class="header search-page">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png"></a>
		<div class="search">
			<input type="text" placeholder="大家都在搜“{$trendingSearch}”" autocomplete="off" class="search-content" id="keywords"  autofocus="autofocus" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</div>
		<a href="javascript:;"><img src="__STATIC__/images/mobile/icon/search.png" class="input-search" id="search"></a>
	</div>
</header>
<div class="warp-search-cont">
   <ul class="search_cont" id="search_ul">

    </ul>
</div>

<!-- page top end -->
<div class="hot-search top-half">
	<div class="title" style="display: none;">
		<img src="__STATIC__/images/mobile/icon/hot-search.png">
		<h4>热门搜索</h4>
	</div>
	<div>
		{volist name="list" id="vo"}
		<a href="{:getMobileGameUrl($vo.gameid)}">{$vo.title}</a>
		{/volist}
	</div>
</div>
<div class="history-search">
	<div class="title">
		<div>
			<img src="__STATIC__/images/mobile/icon/history-search.png">
			<h4>搜索历史</h4>
		</div>
		<a href="javascript:;" class="clear-history">
			<img src="__STATIC__/images/mobile/icon/delete.png">
			<h4>清空历史</h4>
		</a>

	</div>
	<ul>
	<!-- 	<li>
			<a href="">阴阳师</a>
			<a href="javascript:;" class="delete"><img src="__STATIC__/images/mobile/icon/close.png"></a>
		</li>
		<li>
			<a href="">随便搜搜</a>
			<a href="javascript:;" class="delete"><img src="__STATIC__/images/mobile/icon/close.png"></a>
		</li>
		<li>
			<a href="">亮剑上</a>
			<a href="javascript:;" class="delete"><img src="__STATIC__/images/mobile/icon/close.png"></a>
		</li> -->
	</ul>
</div>

{/block}


{block name="detail_js"}
<script>
	if($(".hot-search div a").length ==0){
		$(".hot-search .title").hide();
	}else{
		$(".hot-search .title").show();
	}

 $('.search').on('click', function() {
    $('#keywords').focus();
   });
  $('.search').trigger('click');
	// $("#keywords").trigger("click").focus();
	// $("input").trigger("click").focus();

	 
		$(".search-content").keyup(function() {
		var text = $(this).val();
		text = text.replace(/\s*/g, "");
		if (text != "") {
			$.ajax({
		        type: 'POST',
		        url: '/game/ajaxGameList',
		        dataType: 'json',
		        data: { keyword: text },
		        success: function (json) {

		            if ($.isEmptyObject(json)) {
		                $("#search_ul").empty();
		                $(".warp-search-cont").hide();

		            } else {
		                var html = '';

		                $.each(json, function (i, row) {
		                    html += '<li><a href="' + row.url + '">' + row.nickname + '</a></li>';
		                });

		                arrow = ' <div class="border-up-empty"> <span></span></div>'
		                $("#search_ul").html(html + arrow);

		            }
					if($(".search_cont li").length > 0){
						$(".warp-search-cont").show();
					}else{
						$(".warp-search-cont").hide();
					}

		        }

		    });
			$(this).next().show();
			// $(".warp-search-cont").show();
		} else {
			$(this).next().hide();
			  $("#search_ul").empty();
			$(".warp-search-cont").hide();
		}
		
		
	
	})
	$(".search-content").click(function(){
		if($(".search_cont li").length > 0){
			$(".warp-search-cont").show();
		}
	})
	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
		$(".warp-search-cont").hide();
		$(".search_cont").html("");
	})
	
	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$(".search-content").on("postpaste", function() {
		var text = $(this).val();
		text = text.replace(/\s*/g, "");
		if (text != "") {
			$(this).next().show();
			$(".warp-search-cont").show();
		} else {
			$(this).next().hide();
			$(".warp-search-cont").hide();
		}
			
	}).pasteEvents();



$(".warp-search-cont").click(function(e) {
	var target = $(e.target);
	if (target.closest(".search-content").length != 0) return;	
	$(".warp-search-cont").hide();
})

// 搜索历史

if($(".history-search ul li").length ==0){
	$(".history-search").hide();
}else{
	$(".history-search").show();
}
  
  
   $(function(){ 
        update_history(); 
        // 绑定回车事件 
        $(document).keydown(function(event){  
            if(event.keyCode==13){  
                $("#search").click();  
            }  
        });  
 
        // 搜索点击事件 
        $("#search").click(function(){ 
            var keywords = $("#keywords").val(); 
			keywords= keywords.replace(/\s*/g, "");
            historya(keywords); //添加到缓存 
            update_history(); //更新搜索历史 
			if (keywords == ""){
				keywords = encodeURI('{$trendingSearch}');
			}
			location.href = "/search/keyword/" + keywords;
        }) 
 
        // 清空搜索历史 
        $(".clear-history").click(function(){ 
            mystorage.remove('keywords'); 
            $(".history-search ul").html(" "); 
			$(".history-search").hide();
        }) 
    }) 
 
 
    /** 
     * [update_history 更新搜索历史] 
     * @return {[type]} [description] 
     */ 
    function update_history(){ 
        console.log(mystorage.get("keywords")); 
        var history = mystorage.get("keywords"); 
        if (history) { 
            var html = ""; 
            $.each(history,function(i,v){ 
			var	url = "/search/keyword/"+ v;
                html += '<li><a href="'+url+'">'+v+'</a><a href="javascript:;"  class="delete"><img src="/static/images/mobile/icon/close-gray.png"></a></li>'
            }) 
            $(".history-search ul").html(html); 
        }; 
		if($(".history-search ul li").length ==0){
			$(".history-search").hide();
		}else{
			$(".history-search").show();
		}
		// 点击删除历史
		$(".delete").click(function(){
		   var tqtest =  $(this).prev().html();
		  var history = mystorage.get("keywords");
		  	history.splice($.inArray(tqtest,history),1);
		  	mystorage.set("keywords",history); 
		  // $(this).parents("li").remove();
		  	update_history(); 
		}) 
    } 

			 
 
    /** 
     * [history //搜索历史函数存储] 
     * @param  {[type]} value [搜索词] 
     * @return {[type]}       [description] 
     */ 
    function historya(value){
        var data = mystorage.get("keywords"); 
    
        if (!data) { 
            var data = []; //定义一个空数组 
        }else if(data.length === 10){ //搜索历史数量 
            data.pop();  //删除数组最后一个元素
        }else{ 
        }; 
      
        if (value) {  //判断搜索词是否为空 
            if (data.indexOf(value)<0) {  //判断搜索词是否存在数组中 
                // data.push(value);    //搜索词添加到数组中 
          data.splice(0, 0,value); //搜索词添加到数组第一个位置 
                mystorage.set("keywords",data);  //存储到本地化存储中 
          
            }else{
				data.splice($.inArray(value,data),1);
				data.splice(0, 0,value); //搜索词添加到数组第一个位置 
				mystorage.set("keywords",data);  //存储到本地化存储中 
				
			}; 
        }; 
    }
 
    /** 
     * [mystorage 存储localstorage时候最好是封装一个自己的键值，在这个值里存储自己的内容对象，封装一个方法针对自己对象进行操作。避免冲突也会在开发中更方便。] 
     * @param  {String} ){                 var ms [description] 
     * @return {[type]}     [description] 
            console.log(mystorage.set('tqtest','tqtestcontent'));//存储 
            console.log(mystorage.set('aa','123'));//存储 
            console.log(mystorage.set('tqtest1','tqtestcontent1'));//存储 
            console.log(mystorage.set('tqtest1','newtqtestcontent1'));//修改 
            console.log(mystorage.get('tqtest'));//读取 
            console.log(mystorage.remove('tqtest'));//删除 
            mystorage.clear();//整体清除 
     */ 
    var mystorage = (function mystorage(){ 
        var ms = "mystorage"; 
        var storage=window.localStorage; 
        if(!window.localStorage){ 
            alert("浏览器不支持localstorage"); 
            return false; 
        } 
        var set = function(key,value){ 
            //存储 
            var mydata = storage.getItem(ms); 
            if(!mydata){ 
                this.init(); 
                mydata = storage.getItem(ms); 
            } 
            mydata = JSON.parse(mydata); 
            mydata.data[key] = value; 
            storage.setItem(ms,JSON.stringify(mydata)); 
            return mydata.data; 
        }; 
        var get = function(key){ 
            //读取 
            var mydata = storage.getItem(ms); 
            if(!mydata){ 
                return false; 
            } 
            mydata = JSON.parse(mydata); 
            return mydata.data[key]; 
        }; 
        var remove = function(key){ 
            //读取 
            var mydata = storage.getItem(ms); 
            if(!mydata){ 
                return false; 
            } 
            mydata = JSON.parse(mydata); 
            delete mydata.data[key]; 
            storage.setItem(ms,JSON.stringify(mydata)); 
            return mydata.data; 
        }; 
        var clear = function(){ 
            //清除对象 
            storage.removeItem(ms); 
        }; 
        var init = function(){ 
          storage.setItem(ms,'{"data":{}}'); 
        }; 
        return { 
            set : set, 
            get : get, 
            remove : remove, 
            init : init, 
            clear : clear 
        }; 
    })(); 
</script>
{/block}
