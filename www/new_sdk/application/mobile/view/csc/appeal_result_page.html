{extend name="layout/base" /}
{block name="title"}<title>申诉结果_申诉查询_麻花网络客服中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<!-- header start -->

{switch name="$info.status" }
{case value="0" }
<header class="top">
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>申诉处理中</h1>
</header>
<!-- 申诉处理中 -->
<div class="appeal-result top-half">
	<img src="__STATIC__/images/mobile/icon/manage.png">
	<p>检测到账号<span>{$info.username}</span>的申诉编号<span>{$info.code}</span>，正在处理中。</p>
	<p>我们将在1~3个工作日内处理完毕，届时将通过<span>{empty name="$info.mail"}手机号：{else /}邮箱：{/empty}{empty name="$info.mail"}{$info.mobile}{else /}{$info.mail}{/empty}</span>通知您，敬请关注！</p>
	<p>为保障处理进度，请勿重复提交申诉。</p>

	<a href="{:getMobileCscUrl('sscx')}" class="define">确定</a>
</div>
<!-- 申诉处理中end -->
{/case}
{case value="1"}
<header class="top">
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>申诉成功</h1>
</header>
<!-- 申诉成功 -->
<div class="appeal-result top-half">
	<img src="__STATIC__/images/mobile/icon/succeed.png">
	<p>恭喜您！账号<span>{$info.username}</span>的申诉编号<span>{$info.code}</span>，已申诉通过。</p>
	<p>我们已将成功的凭证发送到{empty name="$info.mail"}手机号：{else /}邮箱：{/empty}<span>{empty name="$info.mail"}{$info.mobile}{else /}{$info.mail}{/empty}。</span> </p>
	<p>请输入您的成功凭证，通过后，可重置密码。</p>
	<div class="verify-bind bind">
     <div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/inquiry.png" style="top: 0.14rem;width: 0.2rem;">
			<input type="number" placeholder="请输入成功凭证" id="appealcode"  name="appealcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel" style="    width: 0.2rem;">
		</div>
		</div>
	<a href="javascript:;" class="submit define" >确定</a>
</div>
<!-- 申诉成功end -->
{/case}
{case value="3"}
<header class="top">
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>申诉完成</h1>
</header>
<!-- 申诉完成 -->
<div class="appeal-result top-half">
	<p>恭喜您！账号<span>{$info.username}</span>的申诉编号<span>{$info.code}</span>，已申诉通过并完成。</p>
	<p>如有其它疑问，请咨询麻花网络客服，客服<span>QQ:800802927</span></p>
	<a href="{:getMobileCscUrl('sscx')}" class="define" >确定</a>
</div>
<!-- 申诉完成end -->
{/case}
{case value="2"}
<header class="top">
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>申诉失败</h1>
</header>
<!-- 申诉失败 -->
<div class="appeal-result top-half">
	<p>很遗憾，因您提供的资料不正确，账号<span>{$info.username}</span>的申诉编号<span>{$info.code}</span>，申诉未通过。</p>
	<p>建议您查看是否申诉错帐号。若帐号无误，请您在申诉时提供正确的帐号注册及使用信息，包括注册手机号或邮箱、注册及登录设备信息、历史密码以及充值记录等信息.</p>
	<p>如有疑问，请联系麻花网络客服。若您想再次申诉，请点击下方“<a><span>再次申诉</span></a>”按钮</p>
	<a href="{:getMobileCscUrl('zhss')}" class="define " >再次申诉</a>
	<a href="{:getMobileCscUrl()}" class="backcsc define" >返回客服中心</a>
</div>
<!-- 申诉失败end -->
{/case}
{default /}
{/switch}

{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
// 取消相应表单的填写数据
	$(" input").keyup(function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})

	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("input").on("postpaste", function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}

	}).pasteEvents();
	
	$('.submit').click(function(){
		var appealcode = $("#appealcode").val();
		if(appealcode==""){
			$.toast("请输入申诉编号", "text");
		}else{
			//ajax判断
            toVoucher();
		}
	})


function toVoucher() {
    var code = $("#appealcode").val();
    $.ajax({
        type: 'POST',
        url: '{:url("Mobile/csc/appealResultPage")}',
        dataType: 'json',
        data: {certificate:code},
        success: function(res) {
            //console.log(res);
            if (!res.code){
                $.toast(res.msg, "text");
                // layer.alert(res.msg);
                return;
            }
            window.location.href = res.url;
        },
        error: function () {
            $.toast("网络错误，请刷新页面重试", "text");
        }
    });
}
	

</script>
{/block}
