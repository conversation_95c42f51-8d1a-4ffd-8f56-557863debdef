{extend name="layout/base" /}
{block name="title"}
<title>关注公众号操作_麻花网络</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
<style>
	html {
		height: 100%;
	}
	.header .back{
		padding-right: 0.1rem;
	}
	.header div {
	    display: flex;
	    align-items: center;
	}
	.subscription {
		width: 3rem;
		height: calc(100% - 1rem);
		padding: 1rem 0.4rem 0;
		text-align: center;
		background: url("/static/images/mobile/bg/bg-build.png") no-repeat;
		background-size: 100%;
		background-position: bottom;
	}
	.subscription>img {
		width: 100%;
		margin-bottom: 0.2rem;
	}
	.subscription div {
		display: flex;
		align-items: center;
		margin: 0.2rem 0;
	}
	.subscription div img {
		width: 0.46rem;
		margin-right: 0.14rem;
	}
	.wecode {
		width: 1.7rem;
		width: 1.7rem !important;
		margin-bottom: 0 !important;
	}
	.header {
		border-bottom: none;
	}
</style>
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header">
		<div>
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>关注公众号操作</h1>
		</div>
		<div>
			<div class="search-icon"><a href=""><img src="__STATIC__/images/mobile/icon/search.png"></a></div>
			<div class="menu"><img src="__STATIC__/images/mobile/icon/menu-bar.png"></div>
		</div>
	</div>
</header>
<!-- header end -->
<div class="subscription">
	<img src="__STATIC__/images/mobile/font.png">
	<div><img src="__STATIC__/images/mobile/font-one.png">
		<p>保存以下二维码或截屏当前页面</p>
	</div>
	<img src="__STATIC__/images/code.jpg" class="wecode">
	<div><img src="__STATIC__/images/mobile/font-two.png">
		<p>打开微信“扫一扫”页面，选择【相册】</p>
	</div>
	<div><img src="__STATIC__/images/mobile/font-three.png">
		<p>选择保存的二维码，关注麻花网络公众号</p>
	</div>
</div>

{/block}


{block name="detail_js"}

{/block}
