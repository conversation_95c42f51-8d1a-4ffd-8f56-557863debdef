{extend name="layout/base" /}
{block name="title"}<title>客服中心首页_麻花网络客服中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<style>
	body {
		background: #f9f9f9;
	}
</style>
{/block}

{block name="content"}
<!-- header start -->
<div class="service-center">
	<div class="header">
		<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left-white.png" class="back"></a>
		<h1>客服中心</h1>
		<div>
			<div class="menu-1"><img src="__STATIC__/images/mobile/icon/menu-bar-1.png"></div>
		</div>
	</div>
	<!-- 跳转常见问题 -->
	<a href="{:getMobileServiceUrl(1)}"></a>
</div>

<ul class="service-list">
	<li>
		<a href="{:getMobileCscUrl('zhss')}">
			<p>
				<img src="__STATIC__/images/mobile/icon/account.png">
				<span>账号申诉</span>
			</p>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
		</a>
	</li>
	<li>
		<a href="{:getMobileCscUrl('sscx')}">
			<p>
				<img src="__STATIC__/images/mobile/icon/query.png">
				<span>申诉查询</span>
			</p>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
		</a>
	</li>
	<!-- <li>
		<a href="{:getMobileCscUrl('wxss')}">
			<p>
				<img src="__STATIC__/images/mobile/icon/wexin.png">
				<span>微信申诉</span>
			</p>
			<img src="__STATIC__/images/mobile/icon/right-gray.png">
		</a>
	</li> -->
</ul>
<div class="customer-service">
	<p>
		<img src="__STATIC__/images/mobile/icon/clock.png">
		<span>工作时间：{$kf_time}</span>
	</p>

	{volist name="kefuList" id="vo"}
	<p class="qq">
		<img src="__STATIC__/images/mobile/icon/qq.png">
		<span>{$vo.nickname}：</span>
		<span class="qqnum">{$vo.qq}</span>
		<a class="copy">复制</a>
	</p>
	{/volist}
</div>

<footer class="footer" style="background: #F9F9F9 !important;">
    <div class="copyright">
        <p><a href="http://beian.miit.gov.cn">南京麻花网络科技有限公司版权所有</a></p>
        <p><a href="http://beian.miit.gov.cn">苏ICP备2021029006号-1</a></p>
    </div>
</footer>
{/block}

{block name="detail_js"}
<script>
	$(".copy").click(function() {
		var qqnum = $(this).parent(".qq").find(".qqnum").html();
		var oInput = document.createElement('input');
		oInput.value = qqnum;
		document.body.appendChild(oInput);
		oInput.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
		oInput.className = 'oInput';
		oInput.style.display = 'none';
		$.toast("QQ号复制成功", "text");
	})
</script>
{/block}
