{extend name="layout/base" /}
{block name="title"}
<title>常见问题_麻花网络客服中心</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/style.css">
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<div class="header question">
		<a href="{:getMobileCscUrl(4)}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
		<h1>常见问题</h1>

	</div>
	<ul class="game_rec">
		{foreach name="typeList" item="vo" key="k" }
		<li><a href="javascript:;" onclick="window.location.replace('{:getMobileServiceUrl(1)}{$k}')" class=" {if condition='$type == $k'} active {/if} ">{$vo}</a></li>
		{/foreach}
	</ul>
</header>
<!-- header end -->

{empty name="list"}
<div class="no_content" style="margin-top: 1.8rem;">
	<div>
		<img src="/static/images/mobile/icon/no-gift.png">
		<p>暂无相关常见问题</p>
	</div>
</div>
{else/}
<ul class="question-list">
	{volist name="list" id="item" id='item' key="k"}
	<li>
		<h4>{$current_num + $k}、{$item.title}</h4>
		<p>{$item.answer}</p>
	</li>
	{/volist}
</ul>
{/empty}
<div class="weui-loadmore">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">数据加载中，请稍等</span>
</div>
<div class="no-more-data"><span></span>我也是有底线的<span></span></div>

{include file="layout/footer" /}
{/block}


{block name="detail_js"}
<script>
	
if($(".game_rec li").length <1){
		$(".game_rec").hide()
	}
	
    var currentPage = parseInt("{$list->currentPage()}");
    var lastPage = parseInt("{$list->lastPage()}");
    var total = parseInt("{$list->total()}");
    var url = window.location.href;

	$(".no-more-data").hide();
	var loading = false; //状态标记
	$(document.body).infinite(90).on("infinite", function() {
		
		if (loading) return;
		loading = true;
		
		if (currentPage + 1 > lastPage) {
			$(".weui-loadmore").hide();
			if (currentPage > 1) {
				$(".no-more-data").show();
			}
			loading = false;
			return false;
		}
	

		loading = true;
		$(".weui-loadmore").show();
		setTimeout(function() {
            currentPage += 1;
            $.ajax({
                type: "POST",
                timeOut: 10000,
                url: url,
                data: {
                    "page": currentPage
                },
                async: false,
                success: function(res) {
                    var num = (res.data.current_page -1) * 10;
                    var arr = res.data.data;
                    arr.forEach(function(val, index) {
                        var nownum = num+index+1;
                        $(".question-list").append(
                            "<li>"+
                            	"<h4>"+nownum+"、"+val.title+"</h4>"+
                        		"<p>"+val.answer+"</p>"+
                        	"</li>"
                        );
                    });

                },
                error: function() {
                    $.alert("网络错误，请刷新页面重试");
                }
            });
			$(".weui-loadmore").hide();
			loading = false;
		}, 600); //模拟延迟
	});
	
</script>
{/block}
