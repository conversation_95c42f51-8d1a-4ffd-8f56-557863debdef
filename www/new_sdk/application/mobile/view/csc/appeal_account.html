{extend name="layout/base" /}
{block name="title"}
<title>填写账号信息_账号申诉_麻花网络客服中心</title>
{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<style>
	html{
		width: 100%;
		overflow-x: hidden;
	}
</style>
{/block}

{block name="content"}
<!-- header start -->
<header class="top">
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>账号申诉</h1>
</header>

<ul class="text-center top-half">
	<li class="step1 current">
		<div class="box-outside" id="outside">
			<div class="box-num">
				1
			</div>
		</div>
		账号信息
	</li>
	<li class="step2 ">
		<div class="box-outside" id="outside">
			<div class="box-num">
				2
			</div>
		</div>
		历史信息
	</li>
	<li class=" step3">
		<div class="box-outside" id="outside">
			<div class="box-num">
				3
			</div>
		</div>
		提交成功
	</li>

	<div class="clear">

	</div>
</ul>

<div class="account-info">
	<h4>请仔细填写下方的账号信息</h4>

	<ul class="personal-info">
		<li>
			<a href="javascript:;" id="regdate" >
				<p>注册时间<sup>*</sup><span>请选择注册时间</span></p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li class="warp-input">
			<p>注册时IMEI码</p>
			<input type="text" placeholder="请输入IMEI码" id="imeicode" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="tip">
			IMEI码查看方法：手机拨号键输入*#06#；若为官网注册的
			用户，则请勿填写该项
		</li>
		<li>
			<a href="javascript:;" id="regaddress">
				<p>注册地址<sup>*</sup><span>请选择注册地址</span></p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
				<p class="province_id" style="display: none;"></p>
				<p class="city_id" style="display: none;"></p>
			</a>
		</li>
		<li>
			<a href="javascript:;" id="reggame" >
				<p>注册游戏<sup>*</sup><span>请选择注册时的游戏</span></p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		<li>
			<a href="javascript:;" id="paygame">
				<p>首充游戏<sup>*</sup><span>请选择首次充值的游戏</span></p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">

			</a>
		</li>
		<li class="warp-input">
			<p>注册手机<sup>*</sup></p>
			<input type="text" placeholder="请输入手机号" id="phonenum" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="tip">
			若不是手机号注册，请填写”无“
		</li>
		<li class="warp-input">
			<p>累充金额<sup>*</sup></p>
			<input type="number" placeholder="请输入累积充值的金额" id="totel" />
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</li>
		<li class="tip">
			只能是大于等于0的整数
		</li>
		<li class="tip">
			<input type="button" value="下一步" class="next">
		</li>
	</ul>
</div>

<!-- search game -->
<!-- 注册游戏搜索弹窗 -->
<div class="warp-serchgame  reg-search">
	<div class="account-info search-header">
       <div class="close-poup"><img src="__STATIC__/images/mobile/icon/close.png"></div>
        <div class="warp-input">
			<p>搜索游戏：</p>
			<input type="text" placeholder="请输入游戏名" id="gamename" name="gamename" autocomplete="off">
			<img src="/static/images/mobile/icon/close-01.png" class="cancel code-cancel">
         </div>
     </div>
	 
	<ul class="search-list" id="reg-gamesearch">
		<li>
			<a href="javascript:;" class="0">
				<p>麻花网络注册</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		{volist name="gamelist" id="vo"}
		<li>
            <a href="javascript:;" class="{$vo.id}">
				<p>{$vo.nickname}</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		{/volist}
	</ul>
	 
	 <!-- <div class="weui-loadmore">
	 	<i class="weui-loading"></i>
	 	<span class="weui-loadmore__tips">数据加载中，请稍后</span>
	 </div> -->
	 <div class="no-more-data"><span></span>我也是有底线的<span></span></div>
</div>

<!-- 充值游戏搜索弹窗 -->
<div class="warp-serchgame pay-search">
	<div class="account-info search-header">
       <div class="close-poup"><img src="__STATIC__/images/mobile/icon/close.png"></div>
        <div class="warp-input">
			<p>搜索游戏：</p>
			<input type="text" placeholder="请输入游戏名" id="gamename" name="gamename" autocomplete="off">
			<img src="/static/images/mobile/icon/close-01.png" class="cancel code-cancel">
         </div>
     </div>
	 
	<ul class="search-list" id="pay-gamesearch">
		<li>
			<a href="javascript:;" class="0">
				<p>无</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		{volist name="gamelist" id="vo"}
		<li>
            <a href="javascript:;" class="{$vo.id}">
				<p>{$vo.nickname}</p>
				<img src="__STATIC__/images/mobile/icon/right-gray.png">
			</a>
		</li>
		{/volist}
	</ul>
	 
	 <div class="weui-loadmore">
	 	<i class="weui-loading"></i>
	 	<span class="weui-loadmore__tips">数据加载中，请稍后</span>
	 </div>
	 <div class="no-more-data"><span></span>我也是有底线的<span></span></div>
</div>

{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script src="__STATIC__/js/mobile/datePicker.js"></script>
<script src="__STATIC__/js/mobile/picker.min.js"></script>
<script>
	$(" #imeicode,#phonenum,#totel,#gamename").keyup(function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	})


	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("#imeicode,#phonenum,#totel,#gamename").on("postpaste", function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}

	}).pasteEvents();
	
	
	// 弹出注册时间
	  var calendar = new datePicker();
	  var myDate = new Date;
	  var year=myDate.getFullYear();
	  var month=myDate.getMonth()+1; 
	  var date=myDate.getDate();
	calendar.init({
	    'trigger': '#regdate', /*按钮选择器，用于触发弹出插件*/
	    'type': 'date',/*模式：date日期；datetime日期时间；time时间；ym年月；*/
	    'minDate':'1900-1-1',/*最小日期*/
	    'maxDate':year+'-'+month+'-'+date,/*最大日期*/
	    'onSubmit':function(theSelectData){
			var theSelectData = calendar.value;
			var datevalue = $("#date span").html();
			if(theSelectData != datevalue){
			$("#regdate span").html(theSelectData);
			$("#regdate span").css("color","#262626")
			 }
	         
	    },
	    'onClose':function(theSelectData){
	    },
	});
	$('#regdate').click(function(){
	$('h1.date_btn').html("注册时间")
	})
	
	// 弹出注册地址
	
	// 二级联动地址
	var city = {$province};
	
     var nameEl = document.getElementById('regaddress');
     
     var first = []; /* 省，直辖市 */
     var second = []; /* 市 */
     
     var selectedIndex = [0, 0]; /* 默认选中的地区 */
     
     var checked = [0, 0]; /* 已选选项 */
     
     function creatList(obj, list){
       obj.forEach(function(item, index){
       var temp = new Object();
       temp.text = item.name;
	   temp.id = item.id;
       temp.value = index;
       list.push(temp);
       })
     }
     
     creatList(city, first);
     
     if (city[selectedIndex[0]].hasOwnProperty('sub')) {
       creatList(city[selectedIndex[0]].sub, second);
     } else {
       second = [{text: '', value: 0}];
     }
     
     
     var picker = new Picker({
     	data: [first, second],
       selectedIndex: selectedIndex,
     	title: '地址选择'
     });
     
     picker.on('picker.select', function (selectedVal, selectedIndex) {
       var text1 = first[selectedIndex[0]].text;
       var text2 = second[selectedIndex[1]].text;
	   var id1 = first[selectedIndex[0]].id;
	   var id2 = second[selectedIndex[1]].id;
	   $(".province_id").html(id1);
	   $(".city_id").html(id2);
       $("#regaddress span").html(text1 + text2)
	   $("#regaddress span").css("color","#262626")
     	// nameEl.innerText = text1 + ' ' + text2 ;
     });
     
     picker.on('picker.change', function (index, selectedIndex) {
       if (index === 0){
         firstChange();
       }
       function firstChange() {
         second = [];
         third = [];
         checked[0] = selectedIndex;
         var firstCity = city[selectedIndex];
         if (firstCity.hasOwnProperty('sub')) {
           creatList(firstCity.sub, second);
     
           var secondCity = city[selectedIndex].sub[0]
           if (secondCity.hasOwnProperty('sub')) {
             creatList(secondCity.sub);
           } else {
             third = [{text: '', value: 0}];
             checked[2] = 0;
           }
         } else {
           second = [{text: '', value: 0}];
           third = [{text: '', value: 0}];
           checked[1] = 0;
           checked[2] = 0;
         }
     
         picker.refillColumn(1, second);
         picker.scrollColumn(1, 0)
       }
     
     
     });
     

     nameEl.addEventListener('click', function () {
     	picker.show();
     });
     
    $(".picker").click(function(e) {
    	var target = $(e.target);
    	if (target.closest(".picker .picker-panel.show").length != 0) return;
    	$(".picker").hide()
    })

     // 弹出游戏选择
	 
	 $("#reggame").click(function(){
	 	$('.reg-search').stop(true, false).animate({
			'top': '0%'
		}, 600)
	 })
	 $("#paygame").click(function(){
	 	$('.pay-search').stop(true, false).animate({
	 			'top': '0%'
	 		}, 600)
	 })
	 $(".close-poup img").click(function(){
	 	$(this).parents(".warp-serchgame").stop(true, false).animate({
	 		'top': '200%'
	 	}, 600)
	 })
	 
	 $(".reg-search .search-list a").click(function(){
		 var gamename = $(this).find("p").html();
		 $('.reg-search').stop(true, false).animate({
		 	'top': '200%'
		 }, 600);
		 $("#reggame span").html(gamename);
		  $("#reggame span").css("color","#262626")
		  var gameid =$(this).attr("class");
		  $("#reggame span").attr('class',gameid)
	 })
	 
	 $(".pay-search .search-list a").click(function(){
	 		 var gamename = $(this).find("p").html();
	 		 $('.pay-search ').stop(true, false).animate({
	 		 	'top': '200%'
	 		 }, 600);
	 		 $("#paygame span").html(gamename);
	 		  $("#paygame span").css("color","#262626")
			  var gameid =$(this).attr("class");
			  $("#paygame span").attr('class',gameid)
	 })
	 
	 // 注册游戏
	 $(".reg-search  .code-cancel").click(function(){
		 $(this).prev().val("");
		 $(this).hide();
		// ajax 改变搜索游戏列表数据
		$.ajax({
		    type: 'GET',
		    url: "/Csc/ajaxGameList"+ '?keyword=',
		    dataType: 'json',
		    success: function (data) {
		    	var html = '';
		         $.each(data.list, function (i, row) {
		            html += '<li>'+
		 			    '<a href="javascript:void(0);" class="'+row.id+'" onclick="reggame_method('+row.id+',`'+row.nickname+'`)">'+
		 					'<p>' + row.nickname+'</p>'+
		 					'<img src="__STATIC__/images/mobile/icon/right-gray.png">'+
		 				'</a>'+
		 			'</li>';
		        
		    	});
		        
				if(html != ""){
					$(".reg-search .0").hide();
				}else{
					$(".reg-search .0").show();
				}
				
		    	$("#reg-gamesearch").html(html);
		    },
		    error: function () {
		        $.toast('网络错误，请刷新页面重试', "text");
		    }
		}); 
		 
	 })
	 
	 $(".reg-search  #gamename").keyup(function() {
	 	var text = $(this).val();
		var texta = text.replace(/\s*/g, "");
	 	if (text != "") {
	 		$(this).next().show();	
	 	} else {
	 		$(this).next().hide();
	 	}
    	// ajax 改变搜索游戏列表数据
    	$.ajax({
            type: 'GET',
            url: "/Csc/ajaxGameList"+ '?keyword=' + texta,
            dataType: 'json',
            success: function (data) {
            	var html = '';
                 $.each(data.list, function (i, row) {
	                html += '<li>'+
		 			    '<a href="javascript:void(0);" class="'+row.id+'" onclick="reggame_method('+row.id+',`'+row.nickname+'`)">'+
		 					'<p>' + row.nickname+'</p>'+
		 					'<img src="__STATIC__/images/mobile/icon/right-gray.png">'+
		 				'</a>'+
		 			'</li>';
                
            	});
				if(html != ""){
					$(".reg-search .0").hide();
				}else{
					$(".reg-search .0").show();
				}

            	$("#reg-gamesearch").html(html);
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });
	 })

	 function reggame_method(gameid,gamename){
	 	console.log(gamename);
		 $('.reg-search').stop(true, false).animate({
		 	'top': '200%'
		 }, 600);
		 $("#reggame span").html(gamename);
		  $("#reggame span").css("color","#262626")
		  $("#reggame span").attr('class',gameid)
	 }

	 
	 
	 // // 加载更多
	 // if($(".reg-search").css("display")=="block"){
	 
	 // 	$(".reg-search .no-more-data").hide();
	 // 	var loading = false; //状态标记
	 // 	$('.reg-search .search-list').infinite(90).on("infinite", function() {
	 // 		if (loading) return;
	 // 		loading = true;
	 // 		$(".reg-search .weui-loadmore").show();
	 // 		setTimeout(function() {
	 // 			$(".reg-search  .search-list").append(
	 // 				'<li>'+
		//  			    '<a href="javascript:;">'+
		//  					'<p>三国演义124134</p>'+
		//  					'<img src="__STATIC__/images/mobile/icon/right-gray.png">'+
		//  				'</a>'+
		//  			'</li>'
		//  		);
	 // 			$(".reg-search .weui-loadmore").hide();
	 // 			loading = false;
	 // 		}, 500); //模拟延迟
	 // 	});
	 // }
	 
	 
	// 充值游戏
	
	$(".pay-search  .code-cancel").click(function(){
			 $(this).prev().val("");
			 $(this).hide();
			// ajax 改变搜索游戏列表数据
			$.ajax({
			    type: 'GET',
			    url: "/Csc/ajaxGameList"+ '?keyword='+'&type=pay',
			    dataType: 'json',
			    success: function (data) {
			    	var html = '';
			         $.each(data.list, function (i, row) {
			            html += '<li>'+
			 			    '<a href="javascript:void(0);" class="'+row.id+'" onclick="paygame_method('+row.id+',`'+row.nickname+'`)">'+
			 					'<p>' + row.nickname+'</p>'+
			 					'<img src="__STATIC__/images/mobile/icon/right-gray.png">'+
			 				'</a>'+
			 			'</li>';
			        
			    	});
			
			if(html != ""){
				$(".pay-search .0").hide();
			}else{
				$(".pay-search .0").show();
			}
			
			    $("#pay-gamesearch").html(html);
			    },
			    error: function () {
			        $.toast('网络错误，请刷新页面重试', "text");
			    }
			});
			 
	})
	$(".pay-search  #gamename").keyup(function(){
		var text = $(this).val();
		var texta = text.replace(/\s*/g, "");
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	   	// ajax 改变搜索游戏列表数据
	   	$.ajax({
            type: 'GET',
            url: "/Csc/ajaxGameList"+ '?keyword=' + texta +'&type=pay',
            dataType: 'json',
            success: function (data) {
            	var html = '';
                 $.each(data.list, function (i, row) {
	                html += '<li>'+
		 			    '<a href="javascript:void(0);" class="'+row.id+'" onclick="paygame_method('+row.id+',`'+row.nickname+'`)">'+
		 					'<p>' + row.nickname+'</p>'+
		 					'<img src="__STATIC__/images/mobile/icon/right-gray.png">'+
		 				'</a>'+
		 			'</li>';
                
            	});
            	$("#pay-gamesearch").html(html);
				
				if(html != ""){
					$(".pay-search .0").hide();
				}else{
					$(".pay-search .0").show();
				}
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });
	})


	function paygame_method(gameid,gamename){
		$('.pay-search ').stop(true, false).animate({
			'top': '200%'
		}, 600);
		$("#paygame span").html(gamename);
		$("#paygame span").css("color","#262626")
		$("#paygame span").attr('class',gameid)
	 }

	
	// // 加载更多
	// if($(".pay-search").css("display")=="block"){
	
	// $(".pay-search .no-more-data").hide();
	// var loading = false; //状态标记
	// $('.pay-search .search-list').infinite(90).on("infinite", function() {
	// 	if (loading) return;
	// 	loading = true;
	// 	$(".pay-search .weui-loadmore").show();
	// 	setTimeout(function() {
	// 		$(".pay-search .search-list").append(
	// 			'<li>'+
	// 			    '<a href="javascript:;">'+
	// 					'<p>三国演义124134</p>'+
	// 					'<img src="__STATIC__/images/mobile/icon/right-gray.png">'+
	// 				'</a>'+
	// 			'</li>'
	// 		);
	// 				$(".pay-search .weui-loadmore").hide();
	// 				loading = false;
	// 			}, 500); //模拟延迟
	// 		});
	// }



// 点击下一步

$(".next").click(function(){
	var regtime = $("#regdate span").html();
	var imeicode =$('#imeicode').val();
	var regaddress =$("#regaddress span").html();
	var provinceid = $(".province_id").html();
	var cityid = $(".city_id").html();
	var reggame = $("#reggame span").html();
	var paygame = $("#paygame span").html();
	var reggameid = $("#reggame span").attr("class");
	var firstrechargegameid = $("#paygame span").attr("class");
	var phonenum = $("#phonenum").val();
	var totel = $("#totel").val();
	var preg = /^1[3456789]{1}\d{9}$/;
	var reg = /(^[0-9]{1}[0-9]*$)/;

	if(regtime =="请选择注册时间"){
		$.toast("请选择注册时间", "text");
	}else if(regaddress == "请选择注册地址"){
		$.toast("请选择注册地址", "text");
	}else if(reggame =="请选择注册时的游戏"){
		$.toast("请选择注册游戏", "text");
	}else if(paygame=="请选择首次充值的游戏"){
		$.toast("请选择首充游戏", "text");
	}else if(phonenum==""){
		$.toast("请输入注册手机", "text");
	}else if(phonenum!= "无"&& !preg.test(phonenum)){
		$.toast("请输入正确的手机号", "text");
	}else if(totel==""){
		$.toast("请输入累充金额", "text");
	}else if(!reg.test(totel)){
		$.toast("请输入正确的累充金额", "text");
	}else{
        regaddress = '中国' + regaddress;
        
		// ajax 提交数据
		$.ajax({
            type: 'POST',
            url: "/Csc/appealAccountTij",
            dataType: 'json',
            data: { regtime: regtime,imei: imeicode,addr: regaddress,reggameid: reggameid,regmobile: phonenum,totalrecharge: totel,firstrechargegameid: firstrechargegameid},
            success: function (json) {
                if ( json.code) {
                	window.location.href = json.url;
                }else{
                	$.toast(json.msg, "text");
                }
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });

	}
	
})



	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})
</script>
{/block}
