{extend name="layout/base" /}
{block name="title"}<title>验证手机_账号申诉_麻花网络客服中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>账号申诉</h1>
</header>
<div class="verify-bind">
	
	{if condition=" isset($info['mobile']) "}
	<!-- 手机号邮箱验证 -->
	<div class="verify-phone">
		<div  class="tip">
			<p>验证手机：{:stringObfuscation($info['mobile'] , 3)} </p>
            <p>请点击“发送验证码”，获取短信验证码后，进行手机验证</p>
                            
		</div>
		<div class="warp-input">
			<p>验证码：</p>
			<input type="number" placeholder="请输入验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送验证码">
			<span></span>
		</div>
		<input type="button" value="确定" class="submit clickable" />
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
	</div>
	{/if}

    {if condition=" isset($info['mail']) "}
	<!-- 邮箱验证 -->
	<div class="verify-mail">
		<div class="tip">
			<p>验证邮箱：{:mailObfuscation($info['mail'])} </p>
			<p>请点击“发送验证码”，获取邮件验证码后，进行邮箱验证</p>
		</div>
		<div class="warp-input">
			<p>验证码：</p>
			<input type="number" placeholder="请输入验证码" id="getcode" name="getcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel code-cancel">
			<input class="sendcode" type="button" value="发送验证码">
			<span></span>
		</div>
		<input type="button" value="确定" class="submit clickable" />
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
	</div>
	{/if}

	
</div>

{/block}

{include file="layout/footer" /}
{/block}

{block name="detail_js"}
<script>
	var iHigh = document.body.clientHeight;
	var aHigh = window.screen.height;
	if (aHigh > iHigh) {
		$(".footer").addClass("fixed");
	}
	
	if ($(".verify-mail").length > 0) {
	    var type = "email";
	} else {
	    var type = "mobile";
	}
	
	// 点击发送验证码
	$(".sendcode").click(function(){
		$("#TencentCaptcha").trigger("click");
	});
	
	
	//拼图验证
	if ($(".verify-mail").length > 0) {
		$("header h1").html("账号申诉");
		var type = "email";
	} else {
		$("header h1").html("账号申诉");
		var type = "mobile";
	}
	var ticket = '';
	var randstr = '';
	window.callback = function(res) {
		if (res.ret === 0) {
			ticket = res.ticket;
			randstr = res.randstr;
		    var bindnum = $(".bindnum").val();
		   	$.ajax({
	            type: 'POST',
	            url: "/Csc/sendCode",
	            dataType: 'json',
	            data: { type: type,ticket:ticket,randstr:randstr},
	            success: function (json) {
	                $.toast(json.msg, "text");
	                if ( json.code) {
	                	getcode();
	                }
	            },
	            error: function () {
	                $.toast('网络错误，请刷新页面重试', "text");
	            }
	        });
			
		}
	}
	
	// 倒计时方法
	function getcode() {
		var count = 60;
		$('.sendcode').addClass('hasSend');
		var index = setInterval(function() {
			if (count >= 0) {
				$('.sendcode').val(count + ' s');
				$('.sendcode').attr('disabled', true);
	
				count--;
			} else {
				$('.sendcode').removeClass('hasSend').val('发送验证码');
				$('.sendcode').attr('disabled', false);
				clearInterval(index);
			}
		}, 1000);
	}
	
	
	
	
	//邮箱验证点击下一步
	$(".verify-mail .submit").click(function(){
		var getcode = $(".verify-mail #getcode").val();
		if(getcode==""){
			$.toast("请输入验证码", "text");
		}else{
			// ajax 判断
            next(getcode)
		}
	})
	
	
	// 手机号验证点击下一步
	$(".verify-phone .submit").click(function(){
		var getcode = $(".verify-phone #getcode").val();
		if(getcode==""){
			$.toast("请输入验证码", "text");
		}else{
			// ajax 判断
            next(getcode)
		}
	})

    function next(code) {
        $.ajax({
            type: 'POST',
            url: "/Csc/appealCheckCode",
            dataType: 'json',
            data: { type: type, code: code },
            success: function (json) {
                if (json.code == 1) {
                    window.location.href = json.url;
                } else {
                    $.toast(json.msg, "text");
                }
            },
            error: function () {
                $.toast('网络错误，请刷新页面重试', "text");
            }
        });
    }
</script>
{/block}
