{extend name="layout/base" /}
{block name="title"}<title>申诉查询_麻花网络客服中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
{/block}

{block name="content"}
<header>
	<a href="{:url('Mobile/csc/index')}"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>申诉查询</h1>
</header>
<div class="verify-bind bind">
	<p> 输入申诉编号，可查询当前编号的申诉进度。通过的申诉可在查询后，重置密码。</p>

		<div class="warp-input">
			<img src="__STATIC__/images/mobile/icon/inquiry.png" style="top: 0.14rem;">
			<input type="number" placeholder="请输入申诉编号" id="appealcode"  name="appealcode" autocomplete="off">
			<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		</div>

		<input type="button" value="查询" class="submit clickable" />
		<input type="button" id="TencentCaptcha" data-appid="{$appid}" data-cbfn="callback" style="display: none;" />
	</div>


{/block}


{block name="detail_js"}
<script>
	// 取消相应表单的填写数据
	$(" input").keyup(function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	})

	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})

	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("input").on("postpaste", function() {
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}

	}).pasteEvents();
	
	$('.submit').click(function(){
		var appealcode = $("#appealcode").val();
		if(appealcode==""){
			$.toast("请输入申诉编号", "text");
		}else{
			$("#TencentCaptcha").trigger("click");
		}
	})
	
	
	var ticket = '';
		var randstr = '';
		window.callback = function(res){
		    if(res.ret === 0){
	            var appealcode = $("#appealcode").val();
	            ticket = res.ticket;
	            randstr = res.randstr;
                toverify(ticket,randstr,appealcode)
		    }
		}


    function toverify(ticket,randstr,code) {
        $.ajax({
            type: 'POST',
            url: "{:url('Mobile/csc/appealResult')}",
            dataType: 'json',
            data: {ticket:ticket,randstr:randstr,code:code},
            success: function(res) {
                //console.log(res);
                if (!res.code){
                    $.toast(res.msg, "text");
                    return;
                }
                window.location.href = res.url;
            },
            error: function () {
                $.toast("网络错误，请刷新页面重试", "text");
            }
        });
    }
</script>
{/block}
