{extend name="layout/base" /}
{block name="title"}<title>重置密码_申诉查询_麻花网络客服中心</title>{/block}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/mobile/form.css">
{/block}

{block name="content"}
<header>
	<a href="javascript:history.back(-1)"><img src="__STATIC__/images/mobile/icon/left.png" class="back"></a>
	<h1>重置密码</h1>
</header>
<form class="form reset-psw changepsw">
	<div class="warp-input">
		<p>密码</p>
		<input type="password" placeholder="请输入密码，6-15位字符" id="password" class="password" name="password" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		<!-- <img src="__STATIC__/images/mobile/icon/hide.png" class="look"> -->
	</div>
	<div class="warp-input">
		<p>确认密码</p>
		<input type="password" placeholder="请再次输入密码" id="passwordone" class="password" name="password" autocomplete="off">
		<img src="__STATIC__/images/mobile/icon/close-01.png" class="cancel">
		<!-- <img src="__STATIC__/images/mobile/icon/hide.png" class="look"> -->
	</div>
	<input type="button" value="确认" class="submit clickable" />
</form>
{/block}


{block name="detail_js"}
<script>
	var iHigh = document.body.clientHeight;
	var aHigh = window.screen.height;
	if (aHigh > iHigh) {
		$(".footer").addClass("fixed");
	}
	
	
	$(" #password,#passwordone").keyup(function() {
		var password = $("#password").val();
		var passwordone = $("#passwordone").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
	
	})
	
	$(".cancel").click(function() {
		$(this).prev().val("");
		$(this).hide();
	})
	
	// 粘贴事件监控
	$.fn.pasteEvents = function(delay) {
		if (delay == undefined) delay = 10;
		return $(this).each(function() {
			var $el = $(this);
			$el.on("paste", function() {
				$el.trigger("prepaste");
				setTimeout(function() {
					$el.trigger("postpaste");
				}, delay);
			});
		});
	};
	// 使用
	$("#password,#passwordone").on("postpaste", function() {
		var password = $("#password").val();
		var passwordone = $("#passwordone").val();
		var text = $(this).val();
		if (text != "") {
			$(this).next().show();
		} else {
			$(this).next().hide();
		}
			
	}).pasteEvents();
	
	// $(".look").click(function() {
	// 	if ($(this).hasClass("hide")) {
	// 		$(this).removeClass("hide");
	// 		$(this).prevAll(".password").attr('type', 'password');
	// 		$(this).attr("src", "/static/images/mobile/icon/hide.png");
	// 	} else {
	// 		$(this).prevAll(".password").attr('type', 'text');
	// 		$(this).attr("src", "/static/images/mobile/icon/eye.png");
	// 		$(this).addClass("hide");
	// 	}
	// })
	
	$(".submit").click(function(){
		var password = $("#password").val();
		var passwordone = $("#passwordone").val();
		if(password==""){
			$.toast("请输入密码", "text");
		}else if(password.length<6 ||password.length>15){
			$.toast("请输入正确的密码,<br>6-15位字符", "text");
		}else if(passwordone==""){
			$.toast("请再次输入密码", "text");
		}else if(password != passwordone){
			$.toast("两次输入的密码不一致", "text");
		}else{
			// ajax 提交密码
            $.ajax({
                type: 'POST',
                url: "/Csc/resetPwd",
                dataType: 'json',
                data: {password1:password,password2:passwordone},
                success: function(json) {
                    $.toast(json.msg, "text");
                    if(json.code == 1){
                        var index = setInterval(function() {
                            window.location.href = json.url;
							clearInterval(index);
                        }, 3000);
                    }
                },
                error: function () {
                    $.toast('网络错误，请刷新页面重试', "text");
                }
            });
		}
	})
</script>
{/block}
