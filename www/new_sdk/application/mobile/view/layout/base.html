<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    {block name="title"}
    {notempty name="$seo.title"}<title>{$seo.title}</title> {/notempty}
    {notempty name="$seo.keywords"}
    <meta name="keywords" content="{$seo.keywords}" /> {/notempty}
    {notempty name="$seo.description"}
    <meta name="description" content="{$seo.description}" /> {/notempty}
    {/block}
    <script src="__STATIC__/js/mobile/rem.js"></script>
    <link rel="stylesheet" href="__STATIC__/css/mobile/weui.min.css">
    <link rel="stylesheet" href="__STATIC__/css/mobile/jquery-weui.min.css">
    <link rel="stylesheet" href="__STATIC__/css/mobile/common.css">
    {block name="header"} {/block}
</head>

<body>

    {block name="content"}{/block}

<!-- sidebar menu start -->
<div class="warp-sidebar">
    <div class="sidebar" id="sidebar">
        <div class="user-heade">
            {empty name="Think.session.front_userid"}
            <!-- 未登录 -->
            <div><a href="{:getMobileEncodeUrl('login')}"><img src="__STATIC__/images/wl_user.png"></a></div>
            <p><a href="{:getMobileEncodeUrl('login')}">点击登录</a><p>
            {else /}
            <!-- 登录状态下 -->
            <div><a href="{:getMobileEncodeUrl('user')}"><img src="{empty name='$userinfo.avatar'}/static/images/wl_user.png{else /}{$Think.STATIC_DOMAIN}{$userinfo.avatar}{/empty}"></a></div>
            <p><a href="{:getMobileEncodeUrl('user')}">{$userinfo.nickname}</a><p>
            {/empty}

        </div>
        <ul class="sidebar-nav">
            <li><a href="/"><img src="__STATIC__/images/mobile/icon/home.png">首页</a></li>
            <li><a href="/coin/recharge.html"><img src="__STATIC__/images/mobile/icon/speed.png">充值</a></li>
            <li><a href="{:getMobileTypeGame()}" ><img src="__STATIC__/images/mobile/icon/classify.png" style="margin-left: 0.12rem;">找游戏</a></li>
            <li><a href="{:getMobileNewsIndexUrl()}"><img src="__STATIC__/images/mobile/icon/info.png">资讯</a></li>
            <li><a href="{:getMobileGiftUrl('hot')}"><img src="__STATIC__/images/mobile/icon/gift-1.png">礼包</a></li>
            <li><a href="{:getMobileServerUrl('kf')}"><img src="__STATIC__/images/mobile/icon/mark.png">开服</a></li>
            <!-- <li><a href="{:getMobileEncodeUrl('fastgame')}"><img src="__STATIC__/images/mobile/icon/speed.png">加速</a></li> -->
            <li><a href="{:getMobileCscUrl()}"><img src="__STATIC__/images/mobile/icon/kefu.png">客服</a></li>
			<li><a href="{:getMobileCscUrl('feedback')}"><img src="__STATIC__/images/mobile/icon/feedback-black.png">反馈</a></li>
        </ul>
       <!-- <p >关注麻花网络公众号发现更多精彩内容</p>
        <a href="{:getMobileEncodeUrl('gzgzh')}">点击关注</a>-->
    </div>
</div>
<!-- sidebar menu end -->
  <!-- Return to the top -->
  <img src="__STATIC__/images/mobile/icon/top.png" class="return-top">
 <!-- 下载的判断弹窗 start -->
    <!-- Wechat mask layer start -->
    <div id="Mask-weChat" class="modal-open">
        <div class="weChatTip-container">
            <img src="__STATIC__/images/mobile/prompt.png">
        </div>
    </div>
    <!-- Wechat mask layer end -->
    <!-- ios下载步骤的提示 -->
    <div id="Mask-IOS" class="modal-open">
        <div class="iosTip-container">
            <div class="setProcess-wrap" id="setProcess-wrap">
                <img src="__STATIC__/images/mobile/popup.jpg">
            </div>
            <div class="iosdlBtn-wrap">
                <a href="javascript:;" id="iosdlBtn">知道了，立即下载</a>
            </div>
        </div>
    </div>

    <!-- 下载的判断弹窗 end -->
<!-- jquery CDN -->
<script src="__STATIC__/js/mobile/jquery.min.js"></script>
<script src="__STATIC__/js/mobile/jquery-weui.min.js"></script>
<script src="__STATIC__/js/mobile/common.js"></script>
  <!-- 网站统计控件 statr -->
    <div style="display: none">
        <script
            type="text/javascript">var cnzz_protocol = (("https:" == document.location.protocol) ? "https://" : "http://"); document.write();</script>
    </div>
    <!-- / 网站统计控件 end -->
{block name="detail_js"}{/block}
</body>
</html>
