<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/18
 * Time: 11:13
 */
use think\Env;

return [
    'app_debug' =>false,
	'default_filter'			 => 'trim,htmlspecialchars,addslashes,strip_tags',
	'DATA_CACHE_PREFIX' => 'clearable_weilong_mobile',
	'QUERY_RESULT_CACHE_TIME' => 120,

    // 相关站点网址
    'WEILONG_WEBSITE' => array(
        'HOME'     => WEBSITE_DOMAIN,
        'MOBILE'   => MOBILE_SITE_DOMAIN,
        'STATIC'   => STATIC_DOMAIN,
        'DOWNLOAD' => APK_DOWN_DOMAIN,
    ),

    'http_exception_template'    =>  [
        // 定义404错误的重定向页面地址
        404 =>  APP_PATH.'mobile/view/error_html/404.html',
        500 =>   APP_PATH.'mobile/view/error_html/500.html',
        // 还可以定义其它的HTTP status
        401 =>   APP_PATH.'mobile/view/error_html/404.html',
    ],
    'exception_tmpl'=> APP_PATH.'mobile/view/error_html/500.html',

    // +----------------------------------------------------------------------
    // | 会话设置
    // +----------------------------------------------------------------------
    'session'               => [
        'id'             => '',
        // SESSION_ID的提交变量,解决flash上传跨域
        'var_session_id' => '',
        // SESSION 前缀
        'prefix'         => 'think',
        // 驱动方式 支持redis memcache memcached
        'type'           => 'redis',
        // 是否自动开启 SESSION
        'auto_start'     => true,
        // 服务器地址
        'host'   => Env::get('redis.host', '127.0.0.1'),
        //端口号
        'port'   => Env::get('redis.port', 6379),
        //密码
        'password' => Env::get('redis.password', ''),
        //缓存redis库
        'select' => Env::get('redis.select', 0),
        // 是否长连接
        'persistent'     => false,
        //session过期时间
        'expire'         => 60 * 60 * 24 * 7,

        'cache_expire'   => 60 * 60 * 24 * 7,
    ],
];