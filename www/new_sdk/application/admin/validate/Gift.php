<?php
/**
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/1/22
 * Time: 16:07
 */

namespace app\admin\validate;


use app\common\library\ValidateExtend;

class Gift extends ValidateExtend
{
    protected $rule = [
        'gameid|游戏名称 '        => 'require|isEmpty',
        'title|礼包标题'          => 'require|isEmpty|max:100',
        'content|礼包内容'        => 'require|isEmpty',
        'code|礼包序列号'          => 'require|isEmpty',
        'starttime|有效期开始时间 ' => 'require|isEmpty',
        'endtime|有效期结束时间 '   => 'require|isEmpty|gt:starttime',
        'consume|领取条件 '       => 'require|number|egt:0',
        'is_top|是否推荐 '       => 'in:0,1',
        'description|使用方法'           => 'max:400',

    ];

    protected $message = [
        'gameid.isEmpty'    => '游戏名称不能为空',
        'title.isEmpty'     => '礼包标题不能为空',
        'content.isEmpty'   => '礼包内容不能为空',
        'code.isEmpty'      => '礼包序列号',
        'starttime.isEmpty' => '有效期开始时间不能为空',
        'endtime.isEmpty'   => '有效期结束时间不能为空',
        'endtime.gt'        => '有效期结束时间需大于开始时间',
        'consume.egt'       => '领取条件要大于等于0',
    ];

    protected $scene = [
        'edit'        => ['gameid', 'title', 'content','description' ,'starttime', 'endtime','consume'],
        'setnew'      => ['isnew'],
        'lottery_add' => ['gameid', 'name' => 'require|isEmpty', 'content', 'code', 'starttime', 'endtime'],
    ];


}