<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\admin\validate;

use app\common\library\ValidateExtend;

class ChannelFrozen extends ValidateExtend
{
    protected $rule = [
        'game_id' => 'require',
        'channel_id'  => 'require',
        'grant_money' => 'require',
        'consume' => 'require',
        'member_login' => 'require',
        'register' => 'require'
    ];
    protected $message = [
        'game_id.require' => '游戏不能为空',
        'channel_id.require'  => '渠道不能为空',
        'grant_money.require'  => '发币选项不能为空',
        'consume.require'  => '消费选项不能为空',
        'member_login.require'  => '登录选项不能为空',
        'register.require'  => '注册选项不能为空'
    ];

    protected $scene = [
        'add'  => ['game_id', 'channel_id', 'grant_money', 'consume', 'member_login', 'register'],
        'edit' => ['game_id', 'channel_id', 'grant_money', 'consume', 'member_login', 'register'],
        'sdkgamelist' => ['game_id', 'grant_money', 'consume', 'member_login', 'register']
    ];
}