<?php
/**
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/1/22
 * Time: 16:07
 */

namespace app\admin\validate;


use app\common\library\ValidateExtend;

class Appkey extends ValidateExtend
{
    protected $rule = [
        'gameid|游戏名称 '   => 'require|isEmpty|unique:cy_app',
        'appid|Appid标识符' => 'require|isEmpty',
        //        'cp_appid|appid'   => 'require|isEmpty',
        //        'callback_url|回调地址 '  => 'require|isEmpty',

    ];

    protected $message = [
        'gameid.isEmpty' => '游戏名称不能为空',
        //        'cp_appid.isEmpty' => 'cp_appid不能为空',

        //        'callback_url.isEmpty' => '回调地址不能为空',
    ];
}