<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\admin\validate;

use app\common\library\ValidateExtend;

class User extends ValidateExtend
{
    protected $rule = [
        'username' => 'require',
        'password'  => 'require|length:6,30|passwordStrength:2',
        'mobile' => 'require|is:mobile'
    ];
    protected $message = [
        'username.require' => '用户不能为空',
        'password.require'  => '密码不能为空',
        'password.length' => '新密码长度为 6 - 30',
        'password.passwordStrength' => '新密码强度不足,至少要包含字母、数字、符号中的两种',
        'mobile.require' => '手机号不能为空',
        'mobile.is' => '请输入正确的手机号'
    ];

    protected $scene = [
        'add'  => ['username', 'password', 'mobile'],
        'edit' => ['mobile'],
    ];
}