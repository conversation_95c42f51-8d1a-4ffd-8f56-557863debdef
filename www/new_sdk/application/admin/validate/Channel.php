<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\admin\validate;

use app\common\library\ValidateExtend;

class Channel extends ValidateExtend
{
    protected $rule = [
        'username' => 'require|max:30',
        'password'  => 'require|length:6,30|passwordRule',
        'repassword' => 'require|length:6,30',
        'pay_password' => 'require|length:6|number',
        'pay_repassword' => 'require|length:6',
        'login_check' => 'require',
        'status' => 'require',
        'mobile' => 'is:mobile',
        'beizhu' => 'max:20'

    ];

    protected $message = [
        'username.require' => '渠道名称不能为空',
        'username.max' => '渠道名称不能超过30个字符',
        'password.require'  => '密码不能为空',
        'password.length' => '密码长度为 6 - 30',
        'password.passwordRule' => '密码为数字和字母的组合',
        'repassword.require'  => '密码不能为空',
        'repassword.length' => '新密码长度为 6 - 30',
        'pay_password.require' => '支付密码不能为空',
        'pay_password.length' => '支付密码长度为 6',
        'pay_password.number' => '支付密码为数字',
        'pay_repassword.require'  => '支付密码不能为空',
        'pay_repassword.length' => '支付密码长度为 6',
        'login_check.require'  => '登录校验不能为空',
        'status.require' => '状态不能为空',
        'mobile.is' => '请输入正确的手机号',
        'beizhu.max' => '备注不能超过20个字符'
    ];

    protected $scene = [
        'add'  => ['username', 'password', 'repassword', 'login_check', 'status', 'mobile','beizhu'],
        'edit' => ['login_check', 'status', 'mobile','beizhu'],
        'password' => ['password', 'repassword'],
        'pay_password' => ['pay_repassword', 'pay_password']
    ];


    /**
     * 密码为数字和字母的组合
     *
     * @param $value
     * @return bool
     */
    public function passwordRule($value)
    {
        if (empty($value)) {
            return false;
        }

        return preg_match('/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9a-zA-Z]+$/', $value) == 1;
    }
}