<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/11/7
 * Time: 16:07
 */

namespace app\admin\validate;


use app\common\library\ValidateExtend;

class Lottery extends ValidateExtend
{
    protected $rule = [
        'name|奖项 '              => 'require',
        'info_id|活动名'          => 'require|number',
        'prize_id|奖品'           => 'number',
        'content|表现形式'         => 'regex:/^\d([, ]\d)+$/',
        'first|概率'              => 'regex:/^0\.?\d+$/',
        'next|下一次概率'          => 'regex:/^0\.\d+$/',
        'daily_limit|每日限制个数' => 'regex:/^\d+$/',
        'daily_remained|当天剩余个数' => 'regex:/^\d+$/',
        'next|下一次概率'             => 'regex:/^0\.\d+$/',
        'sort|排序'                   => 'number',
    ];
    
    protected $message = [
        'content.regex'          => '内容格式不正确！',
        'first.regex'            => '概率格式不正确！',
        'next.regex'             => '下一次概率格式不正确！',
        'daily_limit.regex'      => '每日限制个数格式不正确！',
        'daily_remained.regex'   => '当天剩余个数格式不正确！',
        'next.regex'             => '下一次概率格式不正确！',
    ];
    
    protected $scene = [
        'add'         => ['info_id', 'name', 'explain','content' ,'prize_type', 'prize_id','first', 'next', 'daily_limit','daily_remained', 'personal_limit','sort'],
        'lottery_add' => ['gameid', 'name' => 'require|isEmpty', 'content', 'code', 'starttime', 'endtime'],
    ];
    
    
}