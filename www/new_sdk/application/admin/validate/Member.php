<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\admin\validate;

use app\common\library\ValidateExtend;
use think\Db;

class Member extends ValidateExtend
{
    protected $rule = [
        'username'   => 'require|length:6,11|regex:[0-9a-zA-Z]+|checkName',
        'password'   => 'require|length:6,15',
        'channel_id' => 'require|integer|min:1',
        'gameid'     => 'require|integer|min:1'
    ];
    protected $message = [
        'username.require'   => '请输入用户名',
        'username.length'    => '用户名长度6-11位',
        'username.regex'     => '用户名只能是字母或数字',
        'username.checkName' => '用户已经存在',
        'password.require'   => '请输入密码',
        'password.length'    => '密码长度6-15位',
        'channel_id.require' => '请选择绑定渠道',
        'channel_id.integer' => '渠道ID为整数',
        'channel_id.min'     => '请选择绑定渠道',
        'gameid.require'     => '请选择绑定游戏',
        'gameid.integer'     => '游戏ID为整数',
        'gameid.min'         => '请选择绑定游戏',
    ];

    protected $scene = [
        'add'  => ['username', 'password', 'channel_id', 'gameid'],
    ];

    /**
     * 检测用户名唯一
     * @param $value
     * @return bool
     */
    public function checkName($value)
    {
        $exist = Db::table('cy_members')->where(['username' => $value])->count();

        return ! $exist;
    }
}