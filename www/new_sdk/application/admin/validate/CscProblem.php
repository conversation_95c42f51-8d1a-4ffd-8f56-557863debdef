<?php
/**
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/1/22
 * Time: 16:07
 */

namespace app\admin\validate;


use app\common\library\ValidateExtend;

class CscProblem extends ValidateExtend
{
    protected $rule
        = [
            'title|问题 '   => 'require|isEmpty|max:25' ,
            'type|类型'     => 'require|gt:0' ,
            'order|序列号'   => 'require|integer|gt:0' ,
            'answer|礼包内容' => 'require|isEmpty' ,
        ];

    protected $message
        = [
            'title.max'      => '问题字数不超过25个字' ,
            'title.require'  => '请输入问题' ,
            'title.isEmpty'  => '请输入问题' ,
            'type.require'   => '请选择问题类型' ,
            'type.gt'        => '请选择问题类型' ,
            'order.require'  => '请输入序列号' ,
            'order.integer'  => '序列号只能是大于0的整数' ,
            'order.gt'       => '序列号只能是大于0的整数' ,
            'answer.isEmpty' => '请输入回答内容' ,
            'answer.require' => '请输入回答内容' ,

        ];

    protected $scene = [
        'add'        => [ 'title', 'type','order' ,'answer'],
        'edit'        => [ 'title', 'type','order' ,'answer'],
    ];


}