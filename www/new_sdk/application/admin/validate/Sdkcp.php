<?php
/**
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/1/22
 * Time: 16:07
 */

namespace app\admin\validate;


use app\common\library\ValidateExtend;

class Sdkcp extends ValidateExtend
{
    protected $rule = [
        'appid|游戏名称 ' => 'require|isEmpty|unique:cy_cpurl',
        //        'cp_appid|appid'   => 'require|isEmpty',
        'url|回调地址 '   => 'require|isEmpty',

    ];

    protected $message = [
        'appid.isEmpty' => '游戏名称不能为空',
        //        'cp_appid.isEmpty' => 'cp_appid不能为空',zs
        'url.isEmpty'   => '回调地址不能为空',
    ];
}