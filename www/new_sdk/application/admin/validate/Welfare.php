<?php
// +----------------------------------------------------------------------
// | ThinkCMF [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | Copyright (c) 2013-2017 http://www.thinkcmf.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 小夏 < <EMAIL>>
// +----------------------------------------------------------------------
namespace app\admin\validate;

use app\common\library\ValidateExtend;

class Welfare extends ValidateExtend
{
    protected $rule = [
        'game_id' => 'require|integer|gt:0',
        'grant_type_id' => 'require|integer|in:1,2,3,4,5,6,7,8,9',
        'page' => 'require|number|gt:0',
        'pageSize' => 'require|number|gt:0',
        'cycle_id' => 'integer|in:1,2',
        'login_start_time' => 'dateFormat:Y-m-d',
        'login_end_time' => 'dateFormat:Y-m-d',
        'ids' => 'require',
        'server_id' => 'require',
        'welfare_id' => 'require|integer|gt:0',
        'login_time' => 'require|dateFormat:Y-m-d',


    ];
    protected $message = [

        'game_id.require' => '游戏不能为空',
        'game_id.integer' => '游戏id必须为正整型',
        'game_id.gt' => '游戏id必须大于0',
        'grant_type_id.require' => '福利类型不能为空',
        'grant_type_id.number' => '福利类型id必须为数值类型',
        'grant_type_id.gt' => '福利类型id必须大于0',
        'page.require' => '页码不能为空',
        'page.number' => '页码必须为数值类型',
        'page.gt' => '页码必须大于0',
        'pageSize.require' => '分页数不能为空',
        'pageSize.number' => '分页数必须为数值类型',
        'pageSize.gt' => '分页数必须大于0',
        'cycle_id.integer' => '周期数值类型',
        'cycle_id.in' => '周期数值类型',
        'login_start_time.dateFormat' => '开始时间格式错误',
        'login_end_time.dateFormat' => '结束时间格式错误',
        'ids.require' => '发放记录必填',
        'server_id.require' => '区服必填',
        'welfare_id.require' => '福利必填',
        'welfare_id.integer' => '福利必须数值类型',
        'welfare_id.gt' => '福利必须大于0',
        'login_time.require' => '充值时间必填',
        'login_time.dateFormat' => '充值时间格式错误',

    ];

    protected $scene = [
        'enterGameWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time'],
        'everydayWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time'],
        'singleWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time', 'welfare_id'],
        'grandWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time', 'welfare_id', 'cycle_id'],
        'singleGrandWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'login_time', 'welfare_id'],
        'sevenDayWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'welfare_id', 'server_id'],
        'initialWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time', 'welfare_id'],
        'weekWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'welfare_id', 'cycle_id'],
        'monthWelfare' => ['game_id', 'grant_type_id', 'page', 'pageSize', 'welfare_id', 'cycle_id'],

        'applyEnterGameWelfare' => ['ids', 'game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time'],
        'applyEverydayWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time'],
        'applySingleWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time', 'welfare_id'],
        'applyGrandWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time', 'welfare_id', 'cycle_id'],
        'applySingleGrandWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'login_time', 'welfare_id'],
        'applySevenDayWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'welfare_id', 'server_id'],
        'applyInitialWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'login_start_time', 'login_end_time', 'welfare_id'],
        'applyWeekWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'welfare_id', 'cycle_id'],
        'applyMonthWelfare' => ['ids','game_id', 'grant_type_id', 'page', 'pageSize', 'welfare_id', 'cycle_id'],

    ];
}
