{extend name="layout/content" /}

{block name="header"}
<title>处理异常记录</title>
<link rel="stylesheet" href="__STATIC__/lib/layui-v2.9.16/css/layui.css?v={$Think.STATIC_VERSION}">
{/block}

{block name="content"}
<div class="layui-form" lay-filter="form-handle" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label">对象类型</label>
        <div class="layui-input-block">
            <input type="text" name="object_type_text" readonly class="layui-input" style="background-color: #f2f2f2;">
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">对象值</label>
        <div class="layui-input-block">
            <input type="text" name="object_value" readonly class="layui-input" style="background-color: #f2f2f2;">
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">异常类型</label>
        <div class="layui-input-block">
            <input type="text" name="exception_type_text" readonly class="layui-input" style="background-color: #f2f2f2;">
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">异常值</label>
        <div class="layui-input-block">
            <input type="text" name="exception_value" readonly class="layui-input" style="background-color: #f2f2f2;">
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">处理方式</label>
        <div class="layui-input-block">
            <select name="handle_type" required lay-verify="required">
                <option value="">请选择处理方式</option>
                <option value="add_blacklist">加入黑名单</option>
                <option value="add_whitelist">加入白名单</option>
                <option value="ignore">忽略</option>
            </select>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">处理备注</label>
        <div class="layui-input-block">
            <textarea name="handle_remark" placeholder="请输入处理备注" class="layui-textarea"></textarea>
        </div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="form-submit">立即处理</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;
    
    // 加载数据
    var id = '{$Think.get.id}';
    $.ajax({
        url: '{:url("handle")}',
        type: 'get',
        data: {id: id},
        dataType: 'json',
        success: function(res){
            if(res.code === 200){
                var data = res.data;
                // 设置对象类型文本
                var objectTypes = {
                    'ip': 'IP地址',
                    'imei': 'IMEI',
                    'account': '账号',
                    'id_card': '身份证号',
                    'phone': '手机号'
                };
                data.object_type_text = objectTypes[data.object_type] || data.object_type;
                
                // 设置异常类型文本
                var exceptionTypes = {
                    'frequency': '频率异常',
                    'statistics': '统计异常'
                };
                data.exception_type_text = exceptionTypes[data.exception_type] || data.exception_type;
                
                form.val('form-handle', data);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }
    });
    
    // 监听提交
    form.on('submit(form-submit)', function(data){
        data.field.record_id = id;
        $.ajax({
            url: '{:url("handle")}',
            type: 'post',
            data: data.field,
            dataType: 'json',
            success: function(res){
                if(res.code === 200){
                    layer.msg(res.msg, {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(){
                layer.msg('网络错误，请稍后重试', {icon: 2});
            }
        });
        return false;
    });
});
</script>
{/block} 