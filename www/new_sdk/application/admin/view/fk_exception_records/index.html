{extend name="layout/content" /}

{block name="header"}
<title>异常记录管理</title>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            异常记录列表
        </div>
        <div class="layui-card-body">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">对象类型</label>
                        <div class="layui-input-block">
                            <select name="exception_type">
                                <option value="">全部</option>
                                <option value="ip">IP地址</option>
                                <option value="imei">IMEI</option>
                                <option value="username">账号</option>
                                <option value="id_card">身份证号</option>
                                <option value="phone">手机号</option>
                                <option value="duration">耗时</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">对象值</label>
                        <div class="layui-input-block">
                            <input type="text" name="exception_value" placeholder="请输入对象值" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="LAY-app-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                        </button>
                    </div>
                </div>
            </div>

            <table id="LAY-app-list" lay-filter="LAY-app-list"></table>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script type="text/html" id="toolbar">
<!--    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="handle">处理</a>-->
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
</script>

<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 获取URL参数
    function getUrlParams() {
        var params = {};
        var search = window.location.search.substring(1);
        if (search) {
            var pairs = search.split('&');
            for (var i = 0; i < pairs.length; i++) {
                var pair = pairs[i].split('=');
                params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
            }
        }
        return params;
    }

    // 获取URL参数并设置到表单
    var urlParams = getUrlParams();
    var whereParams = {};

    // 如果URL中有筛选参数，设置到表单和查询条件中
    if (urlParams.exception_type) {
        $('select[name="exception_type"]').val(urlParams.exception_type);
        whereParams.exception_type = urlParams.exception_type;
    }
    if (urlParams.exception_value) {
        $('input[name="exception_value"]').val(urlParams.exception_value);
        whereParams.exception_value = urlParams.exception_value;
    }

    // 重新渲染表单
    form.render();

    // 表格渲染
    table.render({
        elem: '#LAY-app-list',
        url: '{:url("index")}',
        where: whereParams, // 添加初始筛选条件
        parseData: function(res) {
            // 自定义数据解析，适配您的接口返回格式
            return {
                "code": res.code === 200 ? 0 : res.code, // 成功时返回0，失败时返回原状态码
                "msg": res.msg,
                "count": res.count || 0,
                "data": res.data || []
            };
        },
        cols: [[
            {field: 'record_id', width: 80, title: 'ID', sort: true},
            {field: 'exception_type', width: 120, title: '对象类型', templet: function(d){
                var types = {
                    'ip': 'IP地址',
                    'imei': 'IMEI',
                    'username': '账号',
                    'id_card': '身份证号',
                    'phone': '手机号'
                };
                return types[d.exception_type] || d.exception_type;
            }},
            {field: 'exception_value', minWidth: 130, title: '对象值'},
            {field: 'exception_content', width: 320, title: '异常内容'},
            {field: 'exception_degree', width: 120, title: '异常程度(%)', sort: true},
            {field: 'status', width: 100, title: '处理状态', templet: function(d){
                if(d.status == 1){
                    return '<span class="layui-badge layui-bg-green">监控中</span>';
                }else if (d.status == 2){
                    return '<span class="layui-badge layui-bg-orange">已处理</span>';
                }else if (d.status == 3) {
                    return '<span class="layui-badge layui-bg-gray">已过期</span>';
                }
            }},
            {field: 'validity_expire_time', width: 180, title: '异常移除(过期时间)', templet: function(d){
                // TODO: 判断时间是否大于当前时间，是则显示过期时间，否则显示“无”
                var validity_expire_time = d.validity_expire_time;
                if (d.validity_expire_time && new Date() > new Date(d.validity_expire_time)) {
                    validity_expire_time = "<span class='layui-bg-red'>"+validity_expire_time+"</span>";
                }

                return validity_expire_time;
            }},
            {field: 'created_at', width: 180, title: '创建时间'},
            {field: 'updated_at', width: 180, title: '更新时间'},
            {title: '操作', align: 'center', fixed: 'right', toolbar: '#toolbar'}
        ]],
        page: true,
        limit: 20,
        text: {
            none: '暂无相关数据'
        }
    });

    // 监听搜索
    form.on('submit(LAY-app-search)', function(data){
        var field = data.field;

        // 执行重载
        table.reload('LAY-app-list', {
            where: field
        });
    });

    // 监听工具条
    table.on('tool(LAY-app-list)', function(obj){
        var data = obj.data;
        if(obj.event === 'handle'){
            layer.open({
                type: 2,
                title: '处理异常记录',
                content: '{:url("handle")}?id=' + data.record_id,
                area: ['600px', '400px'],
                maxmin: true,
                end: function(){
                    table.reload('LAY-app-list');
                }
            });
        } else if(obj.event === 'delete'){
            layer.confirm('确定删除此异常记录？', function(index){
                $.ajax({
                    url: '{:url("delete")}',
                    type: 'post',
                    data: {id: data.record_id},
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 200){
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        }
    });
});
</script>
{/block} 