{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>用户充值列表</title>
<style>
    label {
        padding-top: 10px;
        margin-left: 10px;
    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }
    .showPassword{
        cursor: pointer;
        text-decoration: underline;
        color: blue;
    }
</style>
{/block}

{block name="content"}
<div class="x-body">
    
        <form class="layui-form">

            <div>

            <div class="layui-inline">
                <label for="start">开始时间：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="开始日" value="{$start}" name="start" id="start" autocomplete="off">
                </div>
            </div>


            <div class="layui-inline">
                <label for="end">结束时间：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="截止日" value="{$end}" name="end" id="end">
                </div>
            </div>


            <div class="layui-inline">
                <label for="playerAcount">玩家账号：</label>
                <div class="layui-input-inline">
                    <input class="layui-input" placeholder="" name="username" value="{$Request.get.username}" id="playerAcount">
                </div>
            </div>
           
            <div class="layui-inline">
                <label>游戏：</label>
<!--                <div class="layui-input-inline FuzzySearch_Container">-->
<!--                    <div>-->
<!--                        <input type="hidden" id='J_gameid' name="gameid" value="{$Request.get.gameid}" />-->
<!--                    </div>-->
<!--                </div>-->

                <div class="layui-input-inline" style="width: 200px">
                    <div id="game"></div>
<!--                    <input type="hidden" class="game_id" name='gameid' value="{$Request.get.gameid}"/>-->
                </div>
            </div>

            <div class="layui-inline" style="margin-left: 10px;">
                <button class="layui-btn" type="submit" id="J_search_submit">查询</button>
                <button type="button" onClick="window.location.href = '{url(\'pay/user\')}'" class="layui-btn layui-btn-primary">重置</button>
                <a class="layui-btn" href="javascript:void(0);" onclick="reportDownload();">下载报表</a>
            </div>
        </div>



        <table class="layui-table" lay-filter="listData">
                <thead>
                    <tr>
                        <th lay-data="{field:'userid'}">账户ID</th>
                        <th lay-data="{field:'username'}">充值账号</th>
                        <th lay-data="{field:'amount_total', sort:true}">累计充值金额</th>
                        <th lay-data="{field:'pay_total'}">累计充值次数</th>
                        <th lay-data="{field:'recent_pay'}">最近充值金额</th>
                        <th lay-data="{field:'gameid'}">游戏名</th>
                        <th lay-data="{field:'rolename'}">角色名</th>
                        <th lay-data="{field:'roleid'}">角色ID</th>
                    </tr>
                </thead>
                <tbody>
                    {empty name="list"}
                    <tr>
                        <td colspan="20" style="text-align: center;">暂无数据</td>
                    </tr>
                    {else/}
                    {volist name="list" id="vo"}
                    <tr>
<!--                        <td id="J_td_{$i}"><a class="showPassword" href="javascript:void(0);" onclick="showUsername({$vo.userid},{$i})">{:stringObfuscation($vo.username)}</a></td>-->
                        <td>{$vo.userid}</td>
                        <td id="J_td_{$i}">{$vo.username}</td>
                        <td>{$vo.amount_total}</td>
                        <td>{$vo.pay_total}</td>
                        <td>{$vo.recent_pay}</td>
                        <td>{$game_list_by_ids[$vo.gameid]['name']}</td>
                        <td>{$vo.rolename}</td>
                        <td>{$vo.roleid}</td>
                    </tr>
                    {/volist}
                    {/empty}
                </tbody>
            </table>
            
            <div class="pager-container" style="margin-top:10px;">
                <span>
                    {$list->total()}条记录
                </span>
                {$page}
                <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                    <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                </div>
            </div>
        </form>
</div>
{/block}

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/xm-select.js"></script>
<script async>
    /**
     * 获取当前 URL 并替换或添加参数
     * @param {string} paramName - 参数名
     * @param {string} paramValue - 参数值
     * @returns {string} - 更新后的完整 URL
     */
    function updateUrlParam(paramName, paramValue) {
        // 获取当前 URL 的路径和查询参数
        const url = new URL(window.location.href);

        // 使用 URLSearchParams 处理查询参数
        const searchParams = new URLSearchParams(url.search);

        // 替换或添加参数
        if (paramName && paramValue !== undefined) {
            searchParams.set(paramName, paramValue);
        }

        // 更新 URL 的查询参数
        url.search = searchParams.toString();

        // 返回完整的 URL
        return url.toString();
    }

   //  $("#J_gameid").FuzzySearch({
	//
	//  inputID    : 'J_gameid',
	//  title   	: '请输入游戏名称',
	//  data       :{:json_encode($game_list)},
	//  searchBtn	:'J_search_submit',
   // });


    layui.use(['laydate', 'table'], function () {
        var laydate = layui.laydate;
        var starttime = laydate.render({
            elem: '#start',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1,//关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

        var table = layui.table;
        table.init('listData', {limit: 20, autoSort: false});
        //触发排序事件
        table.on('sort(listData)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            console.log(obj.field); //当前排序的字段名
            console.log(obj.type); //当前排序类型：desc（降序）、asc（升序）、null（空对象，默认排序）
            window.location.href = updateUrlParam(obj.field, obj.type);
        });
    });
    
  	//显示全部姓名
	function showUsername(userid,id)
	{
		$.ajax({
            type: "post",
            data: {'userid':userid},
            url: "{:url('showUsername')}",
            dataType: "json",
            timeOut: 10,
            success: function (result) {
                if(result.code) {
                	
                	$('#J_td_'+id).html(result.data);
               
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试！', {icon: 2});
            }
        });
	}

    var tagData1 = {:json_encode($game_list)}; // 获取到后台给出的数据-PHP写法
    var LeftData = ''
    var tagIns1 = xmSelect.render({
        el: '#game', // div的id值
        toolbar: { // 工具条【‘全选’，‘清空’】
            show: true, // 开启工具条
            showIcon: false, // 隐藏工具条的图标
        },
        autoRow: true,
        // tips: '选择校区', // 让默认值不是“请选择”，而是“选择校区”
        filterable: true, // 开启搜索模式，默认按照name进行搜索
        paging: true, // 启用分页
        pageSize: 100, // 每页的数据个数
        data: tagData1,
        name: 'gameid',
        prop: {
            name: 'name',
            value: 'id'
        },
        initValue: [{$Request.get.gameid}],
        // on: function (data) {
        //
        //     var arr = data.arr;
        //     if (!arr) {
        //         return;
        //     }
        //     gameArr = []
        //     for (i = 0; i < arr.length; i++) {
        //         gameArr[i] = arr[i].id;
        //     }
        //     $('.game_id').val(gameArr.toString())
        //
        //
        // },
    })

    /**
     * 下载
     */
    function reportDownload()
    {
        console.log($('.layui-form').serialize());

        $.ajax({
            type: "post",
            url: "{:url('user')}",
            async: false,
            data: $('.layui-form').serialize()+"&down=true",
            dataType: 'json',
            timeout: 5000,
            success: function (data) {
                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    }
</script>
{/block}
