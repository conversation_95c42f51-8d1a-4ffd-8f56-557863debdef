{extend name="layout/content" /} {block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>渠道账户明细</title>
<style>
    label {

        padding-top: 10px;
        margin-left: 10px;

    }

    .x-so {
        text-align: unset;
        margin-bottom: 20px;
    }

    section {
        margin-top: 10px;
        display: flex;
    }
    .showPassword{
        cursor: pointer;
        text-decoration: underline;
        color: blue;
    }

    .layui-inline{
        margin-top: 10px;
    }
</style>
{/block} {block name="content"}

<div class="x-body">
     
            <form class="layui-form">
                    <div>
                        <div class="layui-inline">
                            <label for="playerAcount">渠道ID：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="" name="channel_id" value="{$Request.get.channel_id}">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label for="playerAcount">渠道账号：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="" name="channel_name" value="{$Request.get.channel_name}">
                            </div>
                        </div>

						<div class="layui-inline">
							<label class="layui-form-label" style="font-weight: bold;width: 95px;">账户类型：</label>
							<div class="layui-input-inline">
								<select name="account_type" >
									<option value="">- 请选择账户类型 -</option>
									<option value="1" {if condition="$Request.get.account_type eq '1'" }selected="selected" {/if}>平台币</option>
									<option value="2" {if condition="$Request.get.account_type eq '2'" }selected="selected" {/if}>结算币</option>
								</select>
							</div>
						</div>

						<div class="layui-inline">
							<label class="layui-form-label" style="font-weight: bold;width: 95px;">交易类型：</label>
							<div class="layui-input-inline">
								<select name="type" >
									<option value="">- 请选择交易类型 -</option>
									<option value="1" {if condition="$Request.get.type eq '1'" }selected="selected" {/if}>转账支出</option>
									<option value="2" {if condition="$Request.get.type eq '2'" }selected="selected" {/if}>转账收入</option>
									<option value="3" {if condition="$Request.get.type eq '3'" }selected="selected" {/if}>直充</option>
									<option value="4" {if condition="$Request.get.type eq '4'" }selected="selected" {/if}>充值收入</option>
									<option value="5" {if condition="$Request.get.type eq '5'" }selected="selected" {/if}>充值支出</option>
									<!--
									<option value="6" {if condition="$Request.get.type eq '6'" }selected="selected" {/if}>币追回</option>
									-->
									<option value="7" {if condition="$Request.get.type eq '7'" }selected="selected" {/if}>结算充值</option>
									<option value="8" {if condition="$Request.get.type eq '8'" }selected="selected" {/if}>结算收入</option>
									<option value="9" {if condition="$Request.get.type eq '9'" }selected="selected" {/if}>提现</option>
									<option value="10" {if condition="$Request.get.type eq '10'" }selected="selected" {/if}>提现打回</option>
								</select>
							</div>
						</div>

                        <div class="layui-inline">
                            <label>开始时间：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="开始日" value="{$start_time}" name="start_time" id="start_time">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label>结束时间：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="截止日" value="{$end_time}" name="end_time" id="end_time">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label for="playerAcount">关联订单号：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" placeholder="" name="out_orderid" value="{$Request.get.out_orderid}">
                            </div>
                        </div>

                            <div class="layui-inline">
                                <button class="layui-btn mg_L" id="J_search_submit" lay-submit lay-filter="sreach">查询</button>
                                <a href="{:url()}" class="layui-btn layui-btn-primary">重置</a>
                            </div>

                        </div>

                        <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>渠道ID</th>
                                        <th>渠道名称</th>
                                        <th>账户类型</th>
										<th>变动金额</th>
                                        <th>交易类型</th>
										<th>关联订单号</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                    
                                    {notempty name="list"} {volist name="list" id="vo"}
                                    <tr>
                                        <td>{:escape($vo.channel_id)}</td>
                                        <td>{:escape($vo.channel_name)}</td>
										<td>{if condition="$vo['account_type'] eq 1"} 平台币 {elseif condition="$vo['account_type'] eq 2"} 结算币 {else} {/if}</td>
										<td>{$vo.change_amount}</td>
										<td>{if condition="$vo['type'] eq 1"} 转账支出 {elseif condition="$vo['type'] eq 2"} 转账收入 {elseif condition="$vo['type'] eq 3"} 直充 {elseif condition="$vo['type'] eq 4"} 充值收入 {elseif condition="$vo['type'] eq 5"} 充值支出 {elseif condition="$vo['type'] eq 6"} 币追回 {elseif condition="$vo['type'] eq 7"} 结算充值 {elseif condition="$vo['type'] eq 8"} 结算收入 {elseif condition="$vo['type'] eq 9"} 提现 {elseif condition="$vo['type'] eq 10"} 提现打回 {else} {/if}</td>
                                        <td>{$vo.out_orderid}</td>
                                        <td>{$vo.create_time}</td>
                    
                                    </tr>
                                    {/volist} {/notempty}
                                </tbody>
                    
                            </table>

                                <div class="pager-container margin-top-10">
                                    <span>
                                        {$total}条记录
                                    </span>
                                    {$page}
                                    <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                                        <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                                    </div>
                                </div>
                        

            </form>


         
        



        
</div>
{/block} {block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        var starttime = laydate.render({
            elem: '#start_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                endtime.config.min = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                };
            }
        });
        var endtime = laydate.render({
            elem: '#end_time',
            type: 'date',
            format: 'yyyy-MM-dd',
            done: function (value, dates) {
                starttime.config.max = {
                    year: dates.year,
                    month: dates.month - 1, //关键
                    date: dates.date,
                    hours: 0,
                    minutes: 0,
                    seconds: 0
                }
            }
        });

    });

    // 报表下载
    $('.download').on('click', function (event) {
        var url = $(this).data('url');
        $.ajax({
            type: "post",
            url: url,
            async: false,
            data: $('.layui-form').serialize(),
            dataType: 'json',
            timeout: 5000,
            success: function (data) {
                if (data['code']) {
                    layer.alert(data['msg']);
                } else {
                    layer.alert(data['msg']);
                }
            },
            error: function () {
                layer.alert('网络错误，请重试');
            },
        });
    });

</script>
{/block}