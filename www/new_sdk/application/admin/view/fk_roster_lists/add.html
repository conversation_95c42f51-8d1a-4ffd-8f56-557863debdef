{extend name="layout/content" /}

{block name="header"}
<title>添加风控名单</title>
<link rel="stylesheet" href="__STATIC__/lib/layui-v2.9.16/css/layui.css?v={$Think.STATIC_VERSION}">
{/block}

{block name="content"}
<div class="layui-form" lay-filter="form-add-lists" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label">名单类型</label>
        <div class="layui-input-block">
            <select name="list_type" required lay-verify="required">
                <option value="">请选择名单类型</option>
                <option value="black">黑名单</option>
                <option value="white">白名单</option>
            </select>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">对象类型</label>
        <div class="layui-input-block">
            <select name="object_type" required lay-verify="required">
                <option value="">请选择对象类型</option>
                <option value="ip">IP地址</option>
                <option value="imei">IMEI</option>
                <option value="username">账号</option>
                <option value="id_card">身份证号</option>
                <option value="phone">手机号</option>
            </select>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">对象值</label>
        <div class="layui-input-block">
            <input type="text" name="object_value" required lay-verify="required" placeholder="请输入对象值" autocomplete="off" class="layui-input">
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">原因</label>
        <div class="layui-input-block">
            <textarea name="remark" placeholder="请输入加入名单的原因" class="layui-textarea"></textarea>
        </div>
    </div>
    
<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label">过期时间</label>-->
<!--        <div class="layui-input-block">-->
<!--            <input type="text" name="expire_time" id="expire_time" placeholder="选填，不填则永不过期" autocomplete="off" class="layui-input">-->
<!--        </div>-->
<!--    </div>-->

    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="form-submit-lists">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script>
layui.use(['form', 'layer', 'laydate'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;
    var $ = layui.$;
    
    // 日期时间选择器
    laydate.render({
        elem: '#expire_time',
        type: 'datetime'
    });
    
    // 监听提交
    form.on('submit(form-submit-lists)', function(data){
        $.ajax({
            url: '{:url("add")}',
            type: 'post',
            data: data.field,
            dataType: 'json',
            success: function(res){
                if(res.code == 200){
                    layer.msg(res.msg, {icon: 1}, function(){
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(){
                layer.msg('网络错误，请稍后重试', {icon: 2});
            }
        });

        return false;
    });
});
</script>
{/block} 