{extend name="layout/content" /}

{block name="header"}
<title>风控名单管理</title>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            风控名单列表
        </div>
        <div class="layui-card-body">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">名单类型</label>
                        <div class="layui-input-block">
                            <select name="list_type">
                                <option value="">全部</option>
                                <option value="black">黑名单</option>
                                <option value="white">白名单</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">对象类型</label>
                        <div class="layui-input-block">
                            <select name="object_type">
                                <option value="">全部</option>
                                <option value="ip">IP地址</option>
                                <option value="imei">IMEI</option>
                                <option value="username">账号</option>
                                <option value="id_card">身份证号</option>
                                <option value="phone">手机号</option>
                                <option value="duration">耗时</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">对象值</label>
                        <div class="layui-input-block">
                            <input type="text" name="object_value" placeholder="请输入对象值" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="LAY-app-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-primary" id="resetSearch">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                        
                        <button type="button" class="layui-btn layui-btn-normal" id="addItem">
                            <i class="layui-icon layui-icon-add-1"></i>添加
                        </button>
                        <button type="button" class="layui-btn layui-btn-normal" id="batchImport">
                            <i class="layui-icon layui-icon-upload"></i>批量导入
                        </button>
                    </div>
                </div>
            </div>
            
            <table id="LAY-app-list" lay-filter="LAY-app-list"></table>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script type="text/html" id="toolbar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    {{# if(d.list_type == 'black'){ }}
        <a class="layui-btn layui-btn-green layui-btn-xs" lay-event="checkWhite">改为白名单</a>
    {{# } }}
    {{# if(d.status == 4){ }}
        <a class="layui-btn layui-btn-cyan layui-btn-xs" lay-event="removeBan">移除封禁</a>
    {{# } }}
</script>

<script>
$(function() {
    layui.use(['table', 'form', 'layer'], function(){
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;

        // 表格渲染
        table.render({
            elem: '#LAY-app-list',
            url: '{:url("index")}',
            parseData: function(res) {
                // 自定义数据解析，适配您的接口返回格式
                return {
                    "code": res.code === 200 ? 0 : res.code, // 成功时返回0，失败时返回原状态码
                    "msg": res.msg,
                    "count": res.count || 0,
                    "data": res.data || []
                };
            },
            cols: [[
                {field: 'roster_lists_id', width: 80, title: 'ID', sort: true},
                {field: 'list_type', width: 100, title: '名单类型', templet: function(d){
                    return d.list_type == 'black' ? '<span class="layui-badge layui-bg-black">黑名单</span>' : '<span class="layui-badge layui-bg-gray">白名单</span>';
                }},
                {field: 'object_type', width: 120, title: '对象类型', templet: function(d){
                    var types = {
                        'ip': 'IP地址',
                        'imei': 'IMEI',
                        'username': '账号',
                        'id_card': '身份证号',
                        'phone': '手机号',
                        'duration': '耗时',
                    };
                    return types[d.object_type] || d.object_type;
                }},
                {field: 'object_value', title: '对象值'},
                {field: 'remark', title: '原因'},
                {field: 'created_at', width: 180, title: '创建时间', sort: true},
                {title: '操作', width: 150, align: 'center', fixed: 'right', toolbar: '#toolbar'}
            ]],
            page: true,
            limit: 20,
            text: {
                none: '暂无相关数据'
            }
        });

        // 监听搜索
        form.on('submit(LAY-app-search)', function(data){
            var field = data.field;

            // 执行重载
            table.reload('LAY-app-list', {
                where: field
            });
        });

        // 监听工具条
        table.on('tool(LAY-app-list)', function(obj){
            var data = obj.data;
            if(obj.event == 'delete'){
                layer.confirm('确定 删除此风控名单？', function(index){
                    $.ajax({
                        url: '{:url("delete")}',
                        type: 'post',
                        data: {id: data.roster_lists_id},
                        dataType: 'json',
                        success: function(res){
                            if(res.code === 200){
                                layer.msg(res.msg, {icon: 1});
                                obj.del();
                                table.reload('LAY-app-list');
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    layer.close(index);
                });
            } else if(obj.event == 'checkWhite'){
                layer.confirm('确定改为 改为白名单？', function(index){
                    $.ajax({
                        url: '{:url("checkWhite")}',
                        type: 'post',
                        data: {id: data.roster_lists_id},
                        dataType: 'json',
                        success: function(res){
                            if(res.code === 200){
                                layer.msg(res.msg, {icon: 1});
                                obj.del();
                                table.reload('LAY-app-list');
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });

        // 添加
        $('#addItem').on('click', function(){
            layer.open({
                type: 2,
                title: '添加风控名单',
                content: '{:url("add")}',
                area: ['600px', '550px'],
                maxmin: true,
                end: function(){
                    table.reload('LAY-app-list');
                }
            });
        });

        // 重置
        $('#resetSearch').on('click', function(){
            // 重置表单
            $('select[name="list_type"]').val('');
            $('select[name="object_type"]').val('');
            $('input[name="object_value"]').val('');
            form.render('select');
            // 重新加载表格数据
            table.reload('LAY-app-list', {
                where: {
                    list_type: '',
                    object_type: '',
                    object_value: ''
                }
            });
        });

        // 批量导入
        $('#batchImport').on('click', function(){
            var importHtml = '<div style="padding: 20px;">' +
                '<form class="layui-form" lay-filter="importForm">' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">名单类型</label>' +
                '<div class="layui-input-block">' +
                '<select name="list_type" lay-verify="required">' +
                '<option value="">请选择名单类型</option>' +
                '<option value="black">黑名单</option>' +
                '<option value="white">白名单</option>' +
                '</select>' +
                '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">对象类型</label>' +
                '<div class="layui-input-block">' +
                '<select name="object_type" lay-verify="required">' +
                '<option value="">请选择对象类型</option>' +
                '<option value="ip">IP地址</option>' +
                '<option value="imei">IMEI</option>' +
                '<option value="username">账号</option>' +
                '<option value="id_card">身份证号</option>' +
                '<option value="phone">手机号</option>' +
                '</select>' +
                '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">原因说明</label>' +
                '<div class="layui-input-block">' +
                '<input type="text" name="reason" placeholder="请输入原因说明" autocomplete="off" class="layui-input">' +
                '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">对象值列表</label>' +
                '<div class="layui-input-block">' +
                '<textarea name="object_values" placeholder="请输入对象值，一行一个" class="layui-textarea" style="height: 200px;" lay-verify="required"></textarea>' +
                '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<div class="layui-input-block">' +
                '<button class="layui-btn" lay-submit lay-filter="importSubmit">立即导入</button>' +
                '<button type="reset" class="layui-btn layui-btn-primary">重置</button>' +
                '</div>' +
                '</div>' +
                '</form>' +
                '</div>';

            layer.open({
                type: 1,
                title: '批量导入名单',
                content: importHtml,
                area: ['600px', '550px'],
                success: function(layero, index){
                    form.render();

                    // 监听提交
                    form.on('submit(importSubmit)', function(data){
                        var field = data.field;
                        // 将多行文本分割成数组
                        var values = field.object_values.split('\n').filter(function(v){
                            return v.trim() !== '';
                        });

                        if(values.length == 0){
                            layer.msg('请输入至少一个对象值', {icon: 2});
                            return false;
                        }

                        // 提交数据
                        $.ajax({
                            url: '{:url("batchImport")}',
                            type: 'post',
                            data: {
                                list_type: field.list_type,
                                object_type: field.object_type,
                                reason: field.reason,
                                object_values: values
                            },
                            dataType: 'json',
                            success: function(res){
                                if(res.code === 200){
                                    layer.msg(res.msg, {icon: 1}, function(){
                                        layer.close(index);
                                        table.reload('LAY-app-list');
                                    });
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            error: function(){
                                layer.msg('网络错误，请稍后重试', {icon: 2});
                            }
                        });

                        return false;
                    });
                }
            });
        });
    });
});
</script>
{/block} 