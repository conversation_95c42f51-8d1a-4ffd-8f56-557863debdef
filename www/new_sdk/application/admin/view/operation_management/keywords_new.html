{extend name="layout/content" /}
{block name="header"}
<title>手游排行编辑管理</title>
<!--<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">-->
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<style>
    .layui-form-label {
        width: 84px;
    }

    .layui-textarea {
        width: 40%;
    }

    .layui-form-selected dl {
        z-index: 100000;
    }

    .layui-form-checkbox i {
        border-left: 1px solid #d2d2d2;
    }

    #showPos-container div span{
        margin-right: 10px;
    }

    .layui-form-item .layui-form-checkbox[lay-skin=primary] {
        margin-top: 0;
    }

    .hide{
        display: none;
    }

    .show{
        display: block;
    }

    .layui-form-label {
        width: 110px;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">
    <form class="layui-form">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">热搜词类型：</label>
            <div class="layui-input-inline">
                <select name="type" lay-verify="required" id="selectid" lay-search lay-filter="keyType">
                    <option value="">请选择类型</option>
                    <option value="1">搜索框热搜词</option>
                    <option value="2">热门热搜</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">热搜词名称：</label>
            <div class="layui-input-inline hotword_input">
                <input type="text" name="title" value="" class="layui-input" >
            </div>

            <div class="layui-input-inline FuzzySearch_Container hotword_select" style="display: none">
                <div>
                    <input type="hidden" id='J_gameid' name="gameid" value="" />
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">操作：</label>
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-radius submit" lay-submit lay-filter="">确定</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn  layui-btn-radius layui-btn-primary">返回
                </button>
            </div>
        </div>

    </form>
</div>
{/block}
{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
    $("#J_gameid").FuzzySearch({
        inputID     : 'J_gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_search_submit',
    });

    layui.use('form', function(){
        var form = layui.form;

        //各种基于事件的操作，下面会有进一步介绍
        form.on('select(keyType)', function(data){
            var type = data.value;

            if (type == 1){
                $(".hotword_input").show();
                $(".hotword_select").hide();
            }else if (type == 2){
                $(".hotword_input").hide();
                $(".hotword_select").show();
            }

        });
    });

    $(".submit").click(function () {
        var data = $("form").serialize();
        $.ajax({
            type: "POST",
            dataType: "json",
            url: "{:url('keywordsCreate')}",
            data: data,
            success: function (res) {
                /*console.log(res);*/
                if (res.code == 0) {
                    layer.msg(res.msg)
                } else {
                    window.location.href = res.url;
                }
            },
            error: function () {
                alert("异常！");
            }
        });
        return false;
    })
</script>
{/block}