{{extend name="layout/content" /} {block name="header"}
<title>开服开测订阅管理</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">

<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }
    
</style>
{/block} 

{block name="content"}
    <div class="x-body">
          	<form class="layui-form"  method='get'>
                <div style="float: right;">
                
                	 <div class="layui-inline">
                        <label>用户名</label>
                        <div class="layui-input-inline set_width">
                            <input class="layui-input" type="text" name='username' value="{$Request.get.username}" />
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label>游戏名称：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <div>
                                <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label>通知类型：</label>
                        <div class="layui-input-inline">
                            <select name="type" lay-search>
                                <option value="">请选择通知类型</option>
                                <option value="1" {if condition="$Request.get.type eq 1" }selected="selected" {/if}>开服</option>
                                <option value="2" {if condition="$Request.get.type eq 2" }selected="selected" {/if}>开测</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-inline">
                        <label>短信状态：</label>
                        <div class="layui-input-inline">
                            <select name="is_del" lay-search>
                                <option value="">请选择发送状态</option>
                                <option value="0" {if condition="$Request.get.is_del eq '0'" }selected="selected" {/if}>正常</option>
                                <option value="1" {if condition="$Request.get.is_del eq 1" }selected="selected" {/if}>作废</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label>发送状态：</label>
                        <div class="layui-input-inline">
                            <select name="is_send" lay-search>
                                <option value="">请选择发送状态</option>
                                <option value="1" {if condition="$Request.get.is_send eq 1" }selected="selected" {/if}>已发送</option>
                                <option value="0" {if condition="$Request.get.is_send eq '0'" }selected="selected" {/if}>未发送</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-inline">
	                    <label class="layui-form-label" style="width:114px;">创建时间：</label>
	                    <input class="layui-input" placeholder="开始日" name="start_time" id="start" value="{:input('request.start_time')}" autocomplete="off" style="width:130px;display:inline-block">
	                    <span>-</span>
	                    <input class="layui-input" placeholder="截止日" name="end_time" id="end" value="{:input('request.end_time')}" autocomplete="off" style="width:130px;display:inline-block">
	        		</div>

                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                    </div>

                </div>

                <div style="clear:both"></div>

                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>手机号</th>
                                <th>游戏名称</th>
                                <th>通知类型</th>
                                <th>开服/开测状态</th>
                                <th>开服/开测时间</th>
                                <th>短信状态</th>
                                <th>发送状态</th>
                                <th>发送时间</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
            
                        <tbody id="viewer_wrap">
                            {notempty name="list"} {volist name="list" id="vo"}
                            <tr>
                                <td><a href="javascript:void(0);" onclick="showUsername(this,{$vo.id},{$vo.gameid})">{:stringObfuscation($vo.username)}</a></td>
                                <td><a href="javascript:void(0);" onclick="showMobile(this,{$vo.id},{$vo.gameid})">{:mobileObfuscation($vo.mobile)}</a></td>
                                <td>{:escape($vo.name)}</td>
                                <td>
                                    {switch name=$vo.type} 
                                    {case value="1"}开服{/case} {case value="2"}开测{/case}
                                    {default /}
                                    {/switch}
                                </td>
                                <td>
                                	{if($vo.type==1)}
                                		{$vo.sername}
                                	{else}
	                                	{switch name=$vo.serstatus} 
	                                    {case value="3"}删档内测{/case} {case value="4"}不删档内测{/case}{case value="5"}公测{/case}
	                                    {default /}
	                                    {/switch}
                                    {/if}
                                </td>
                                <td>{:date('Y-m-d H:i:s',$vo.sertime)}</td>
                                <td>
                                	{if($vo.is_del==1)}
                                		作废
                                	{else}
	                                	正常
                                    {/if}
                                </td>
                                <td>
                                	{if($vo.is_send==1)}
                                		已发送
                                	{else}
	                                	未发送
                                    {/if}
                                </td>
                                <td>{if($vo.send_time)}{:date('Y-m-d H:i:s',$vo.send_time)}{/if}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                            </tr>
                            {/volist} {/notempty}
                        </tbody>
                    </table>

                    <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>
    
            </form>
    </div>
    
{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

    $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_search_submit',
    });
    
    layui.use('laydate', function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            elem: '#start', //指定元素
           	type: 'date'
        });

        //执行一个laydate实例
        laydate.render({
            elem: '#end', //指定元素
           	type: 'date'
        });
    });
    
  	//显示全部姓名
	function showUsername(obj,id,gameid)
	{
		$.ajax({
            type: "post",
            data: {'id':id,'gameid':gameid},
            url: "{:url('showSubscribeName')}",
            dataType: "json",
            timeOut: 10,
            success: function (result) {
                if(result.code) {
                	$(obj).parent('td').html(result.data);
               
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试！', {icon: 2});
            }
        });
	}
  	
	//显示手机
	function showMobile(obj,id,gameid)
	{
		$.ajax({
            type: "post",
            data: {'id':id,'gameid':gameid},
            url: "{:url('showSubscribeMobile')}",
            dataType: "json",
            timeOut: 10,
            success: function (result) {
                if(result.code) {
                	
                	$(obj).parent('td').html(result.data);
               
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            },
            error: function () {
                layer.alert('网络错误，请刷新页面重试！', {icon: 2});
            }
        });
	}
</script>
{/block}