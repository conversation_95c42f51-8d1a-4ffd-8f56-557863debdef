{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>光环助手游戏编辑</title>
<style>
    .layui-form-label {
        width: 96px;
    }

    .layui-textarea {
        width: 40%;
    }

    .layui-form-select dl {
        z-index: 10000;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">
    <form class="layui-form" action="{:url('gameAidEdit',['id'=>$info.id])}" method="POST">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">游戏名称：<span class="x-red">*</span></label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <input type="hidden" id='gameid' name="gameid" value="{$info.gameid}" />
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">游戏类型：<span class="x-red">*</span></label>
            <div class="layui-input-inline">
            	<select name="type" lay-verify="required" lay-search>
                    <option value="">请选择类型</option>
					<option value="1" {if condition="$info.type eq 1" }selected="selected"{/if}>加速版</option>
					<option value="2" {if condition="$info.type eq 2" }selected="selected"{/if}>跳过版</option>             
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label for="" class="layui-form-label">游戏链接：</label>
            <div class="layui-input-inline" style="width:500px;">
                <input type="text" name="url" value="{$info.url}" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label for="" class="layui-form-label">内容：</label>
            <div class="layui-input-inline">
            	<textarea name="content" id="J_content" rows="30" cols="100" lay-verify="required">{$info.content}</textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">操作：</label>
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-radius" lay-submit lay-filter="" id="J_submit_btn">确定</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn  layui-btn-radius layui-btn-primary">返回
                </button>
            </div>
        </div>

    </form>
</div>
{/block}
{block name="footer"}
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.config.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

    $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_submit_btn',
    });

    layui.use(['form', 'layer', 'laydate', 'layedit'], function () {
        var ue = UE.getEditor('J_content');
    });
</script>
{/block}