{extend name="layout/content" /}
{block name="header"}
<title>手游排行编辑管理</title>
<style>
    .layui-form-label {
        width: 84px;
    }

    .layui-textarea {
        width: 40%;
    }
</style>
{/block}
{block name="content"}

<div class="x-body">
    <form class="layui-form" action="{:url('gameRankEdit',['id'=>$info.id])}" method="POST">

        <div class="layui-form-item">
            <label for="" class="layui-form-label">游戏名称：</label>
            <div class="layui-input-inline">
                <input type="text" readonly value="{$info.name}" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">统计热度：</label>
            <div class="layui-input-inline">
                <input type="text" readonly class="layui-input" value="{$info.power_stat}">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">修改热度：</label>
            <div class="layui-input-inline">
                <input type="text" name="power_plus" required class="layui-input" value="{$info.power_plus}">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">操作：</label>
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-radius" lay-submit lay-filter="">确定</button>
                <button type="button" onClick="javascript:history.back(-1);" class="layui-btn  layui-btn-radius layui-btn-primary">返回
                </button>
            </div>
        </div>

    </form>
</div>
{/block}
{block name="footer"}

{/block}