{{extend name="layout/content" /} {block name="header"}
<title>手游排行</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .edit {
        padding: 0 8.5px;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
    }
</style>
{/block} 

{block name="content"}
    <div class="x-body">

            

        	
        
          	<form class="layui-form"  method='get'>

                <div style="float: left;">
                    <a class="layui-btn layui-btn-radius" href="{:url('keywordsCreate')}">
                        <i class="layui-icon">&#xe654;</i>新增
                    </a>
                </div>


                <div style="float: right;">

                    <div class="layui-inline">
                        <label>热搜词</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" name='title' value="{$Request.get.title}" />
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                    </div>

                </div>


                <div style="clear:both"></div>



                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>热搜词名称</th>
                                <th>热搜词类型</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
            
                        <tbody>
                            {notempty name="list"} {volist name="list" id="vo"}
                            <tr>
                                <td>{:escape($vo.title)}</td>
                                <td>
                                    {if($vo.type==2)}
                                        热门搜索
                                    {elseif($vo.type==1)}
                                        搜索框热搜词
                                    {/if}
                                </td>
                                <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                                <td class="td-manage">
                                    <a title="编辑" href="{:url('keywordsEdit',['id'=>$vo.id])}" class="layui-btn layui-btn-normal edit">
                                        <i class="layui-icon">&#xe63c;</i>编辑
                                    </a>
                                    
                                    <a href="javascript:;" onclick="del_info(this,'{:url('keywordsDel',['id'=>$vo.id])}')" class="layui-btn  layui-btn-danger"><i class="layui-icon"></i>删除</a>
                                </td>
                            </tr>
                            {/volist} {/notempty}
                        </tbody>
                    </table>

                    <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>

            </form>
      

    


    </div>
    
{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
{/block}