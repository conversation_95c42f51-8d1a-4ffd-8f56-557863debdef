{{extend name="layout/content" /} {block name="header"}
<title>手游排行</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }

    .edit {
        padding: 0 8.5px;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
    }
</style>
{/block} 

{block name="content"}
    <div class="x-body">
       
            <form class="layui-form" method='get'>

                <div style="float: right;">

                    <div class="layui-inline">
                        <label>游戏名称</label>
                        <div class="layui-input-inline set_width">
                            <input class="layui-input" type="text" name='name' value="{$Request.get.name}" />
                        </div>
                    </div>


                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                    </div>

                </div>


                <div style="clear:both"></div>

                <table class="layui-table">
                        <thead>
                            <tr>
                                 <th>排行榜名次</th>
                                <th>游戏ID</th>
                                <th>游戏名称</th>
                                <th>热度</th>
                                <th>统计热度</th>
                                <th>修改热度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
            
                        <tbody>
                            {notempty name="list"} {volist name="list" id="vo"}
                            <tr>
                                <td>{$Think.get.page ? $i + ($Think.get.page -1) * 10 : $i }</td>
                                <td>{$vo.id}</td>
                                <td>{:escape($vo.name)}</td>
                                <td class="td-status">{$vo.power}</td>
                                <td>{$vo.power_stat}</td>
                                <td class="td-status">{$vo.power_plus}</td>
                                <td class="td-manage">
                                    <a title="编辑" href="{:url('gameRankEdit',['id'=>$vo.id])}" class="layui-btn layui-btn-normal edit">
                                        <i class="layui-icon">&#xe63c;</i>编辑
                                    </a>
                                </td>
                            </tr>
                            {/volist} {/notempty}
                        </tbody>
                    </table>
                    
                   

                    <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>


            </form>
    </div>
    
{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
{/block}