{{extend name="layout/content" /} {block name="header"}
<title>光环助手游戏列表</title>

<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">

<style>
    .layui-table th,
    .layui-table td {
        text-align: center;
    }

    .layui-icon {
        vertical-align: bottom;
    }

    .td-manage button {
        border: 0;
        padding: 5px;
        border-radius: 5px;
        color: #FFFFFF;
    }

    .layui-form-label {
        float: left;
        display: block;
        padding: 9px 15px;
        width: 48px;
        font-weight: 400;
        text-align: right;
    }

    .layui-form-item .layui-input-inline {
        float: none;
        margin-left: 10px;
    }
    
    .check_photo+img {
        display: none;
    }
    
</style>
{/block} 

{block name="content"}
    <div class="x-body">

       
        	
        
          	<form class="layui-form"  method='get'>

                <div style="float: left;">
                    <a class="layui-btn layui-btn-radius" href="{:url('gameAidCreate')}">
                        <i class="layui-icon">&#xe654;</i>新增
                    </a>
                </div>


                <div style="float: right;">

                    <div class="layui-inline">
                        <label>游戏名称：</label>
                        <div class="layui-input-inline FuzzySearch_Container">
                            <div>
                                <input type="hidden" id='gameid' name="gameid" value="{$Request.get.gameid}" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label>游戏类型：</label>
                        <div class="layui-input-inline">
                            <select name="type" lay-search>
                                <option value="">请选择类型</option>
                                <option value="1" {if condition="$Request.get.type eq 1" }selected="selected" {/if}>加速版</option>
                                <option value="2" {if condition="$Request.get.type eq 2" }selected="selected" {/if}>跳过版</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">查询</button>
                    </div>

                </div>

                <div style="clear:both"></div>


                <table class="layui-table">
                        <thead>
                            <tr>
                                <th>游戏名称</th>
                                <th>游戏类型</th>
                                <th>游戏链接</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
            
                        <tbody id="viewer_wrap">
                            {notempty name="list"} {volist name="list" id="vo"}
                            <tr>
                                <td>{:escape($vo.name)}</td>
                                <td>
                                    {switch name=$vo.type} 
                                    {case value="1"}加速版{/case} {case value="2"}跳过版{/case}
                                    {default /}
                                    {/switch}
                                </td>
                                <td>{$vo.url}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                                <td class="td-manage">
                                    <a href="{:url('gameAidEdit',['id'=>$vo.id])}" class="layui-btn btn-info"><i class="layui-icon">&#xe63c;</i>编辑</a>
                                    <a href="javascript:;" onclick="del_info(this,'{:url('gameAidDel',['id'=>$vo.id])}')" class="layui-btn layui-btn-danger"><i class="layui-icon"></i>删除</a>
                                </td>
                            </tr>
                            {/volist} {/notempty}
                        </tbody>
                    </table>
                    
                 

                    <div class="pager-container">
                        <span>
                            {$list->total()}条记录
                        </span>
                        {$page}
                        <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                            <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                        </div>
                    </div>
    



            </form>
      

     
    </div>
    
{/block}{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

    $("#gameid").FuzzySearch({
        inputID     : 'gameid',
        title   	: '请输入游戏名称',
        data        :{:json_encode($game_list)},
        searchBtn	:'J_search_submit',
    });

    var viewer = new Viewer(document.getElementById('viewer_wrap'), {
        url: 'data-original'
    });
    
    $(document).ready(function () {

        $(".check_photo").on("click", function () {
            $(this).siblings().click()
        })
    });
</script>
{/block}