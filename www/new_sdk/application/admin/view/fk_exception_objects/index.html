{extend name="layout/content" /}

{block name="header"}
<title>异常对象管理</title>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            异常对象列表
        </div>
        <div class="layui-card-body">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">对象类型</label>
                        <div class="layui-input-block">
                            <select name="exception_type">
                                <option value="">全部</option>
                                <option value="ip">IP地址</option>
                                <option value="imei">IMEI</option>
                                <option value="username">账号</option>
                                <option value="id_card">身份证号</option>
                                <option value="phone">手机号</option>
                                <option value="duration">耗时</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">对象值</label>
                        <div class="layui-input-block">
                            <input type="text" name="exception_value" placeholder="请输入对象值" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="1">正常</option>
                                <option value="2">异常</option>
                                <option value="3">自动封禁</option>
                                <option value="4">手动封禁</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="LAY-app-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                        </button>
<!--                        <button type="button" class="layui-btn layui-btn-normal" id="exception-objects-addItem">-->
<!--                            <i class="layui-icon layui-icon-add-1"></i>添加-->
<!--                        </button>-->
                    </div>
                </div>
            </div>
            
            <table id="LAY-app-list" lay-filter="LAY-app-list"></table>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script type="text/html" id="toolbar">
    {{# if(d.status == 2){ }}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="block">直接封禁(加黑)</a>
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="whitelist">移除异常(加白)</a>
    {{# }else{ }}
    无
    {{# } }}
</script>

<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 表格渲染
    table.render({
        elem: '#LAY-app-list',
        url: '{:url("index")}',
        parseData: function(res) {
            // 自定义数据解析，适配您的接口返回格式
            return {
                "code": res.code === 200 ? 0 : res.code, // 成功时返回0，失败时返回原状态码
                "msg": res.msg,
                "count": res.count || 0,
                "data": res.data || []
            };
        },
        cols: [[
            {field: 'object_id', width: 80, title: 'ID', sort: true},
            {field: 'exception_type', width: 120, title: '对象类型', templet: function(d){
                var types = {
                    'ip': 'IP地址',
                    'imei': 'IMEI',
                    'username': '账号',
                    'id_card': '身份证号',
                    'phone': '手机号',
                    'duration': '耗时'
                };
                return types[d.exception_type] || d.exception_type;
            }},
            {field: 'exception_value', minWidth: 130, title: '对象值'},
            {field: 'exception_degree_total', width: 80, title: '异常程度', templet: function(d){
                var exception_degree_total = d.exception_degree_total?d.exception_degree_total+'%':0;
                if(d.exception_degree_total > 100){
                    return '<span class="layui-badge layui-bg-red">'+exception_degree_total+'</span>';
                }else{
                    return '<span class="">'+exception_degree_total+'</span>';
                }
            }},
            {field: 'exception_log_by_name', title: '异常记录', width: 360, templet: function(d){
                var shortText = d.exception_log_by_name.length > 30 ? d.exception_log_by_name.substring(0, 30) + '...' : d.exception_log_by_name;
                return '<span>' + shortText + '</span>';
            }},
            {title: '异常详情', width: 100, templet: function(d){
                    if(d.exception_log_by_name && d.exception_log_by_name.length > 0){
                        return '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="showExceptionRecords(\'' + d.exception_type + '\', \'' + d.exception_value + '\')">查看详情</button>';
                    } else {
                        return '<span class="layui-text-muted">暂无记录</span>';
                    }
                }},
            {field: 'status', width: 100, title: '状态', templet: function(d){
                switch (d.status) {
                    case 1:
                        return '<span class="layui-badge layui-bg-green">正常</span>';
                    case 2:
                        return '<span class="layui-badge layui-bg-red">异常</span>';
                    case 3:
                        return '<span class="layui-badge layui-bg-orange">自动封禁</span>';
                    case 4:
                        return '<span class="layui-badge layui-bg-orange">手动封禁</span>';
                }
            }},
            {field: 'created_at', width: 180, title: '创建时间', sort: true},
            {field: 'updated_at', width: 180, title: '更新时间', sort: true},
            {title: '操作', align: 'center', fixed: 'right', toolbar: '#toolbar'}
        ]],
        page: true,
        limit: 20,
        text: {
            none: '暂无相关数据'
        }
    });

    // 监听搜索
    form.on('submit(LAY-app-search)', function(data){
        var field = data.field;

        // 执行重载
        table.reload('LAY-app-list', {
            where: field
        });
    });

    // 添加
    $('#exception-objects-addItem').on('click', function(){
        layer.open({
            type: 2,
            title: '添加异常对象',
            content: '{:url("add")}',
            area: ['600px', '400px'],
            maxmin: true,
            end: function(){
                table.reload('LAY-app-list');
            }
        });
    });

    // 监听工具条
    table.on('tool(LAY-app-list)', function(obj){
        var data = obj.data;
        if(obj.event === 'block'){
            // 直接封禁
            layer.confirm('确定要直接封禁此异常对象吗？<br>将会：添加到黑名单', function(index){
                $.ajax({
                    url: '{:url("blockObject")}',
                    type: 'post',
                    data: {
                        object_id: data.object_id,
                        exception_type: data.exception_type,
                        exception_value: data.exception_value
                    },
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 200){
                            layer.msg(res.msg, {icon: 1});
                            table.reload('LAY-app-list');
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.msg('网络错误，请稍后重试', {icon: 2});
                    }
                });
                layer.close(index);
            });
        } else if(obj.event === 'whitelist'){
            // 移除异常（加入白名单）
            layer.confirm('确定要移除此异常对象吗？<br>将会：添加到白名单', function(index){
                $.ajax({
                    url: '{:url("whitelistObject")}',
                    type: 'post',
                    data: {
                        object_id: data.object_id,
                        exception_type: data.exception_type,
                        exception_value: data.exception_value
                    },
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 200){
                            layer.msg(res.msg, {icon: 1});
                            table.reload('LAY-app-list');
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.msg('网络错误，请稍后重试', {icon: 2});
                    }
                });
                layer.close(index);
            });
        } else if(obj.event === 'delete'){
            layer.confirm('确定删除此异常对象？', function(index){
                $.ajax({
                    url: '{:url("delete")}',
                    type: 'post',
                    data: {id: data.object_id},
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 200){
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                layer.close(index);
            });
        }
    });
});

// 显示异常记录详情弹窗
function showExceptionRecords(exceptionType, exceptionValue) {
    layui.use(['layer'], function(){
        var layer = layui.layer;

        // 构建异常记录页面URL，带上筛选参数
        var url = '{:url("fk_exception_records/index")}?exception_type=' + encodeURIComponent(exceptionType) + '&exception_value=' + encodeURIComponent(exceptionValue);

        layer.open({
            type: 2,
            title: '异常记录详情 - ' + exceptionType + ': ' + exceptionValue,
            content: url,
            area: ['90%', '80%'],
            maxmin: true,
            success: function(layero, index){
                // 弹窗成功后的回调
            }
        });
    });
}
</script>
{/block}