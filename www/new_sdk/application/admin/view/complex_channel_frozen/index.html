{extend name="layout/content" /} {block name="header"}
<title>冻结渠道管理</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block}
{block name="content"}
<div class="x-body">
   
        <div class="layui-inline">
            <a href="{:url('add')}" class="layui-btn layui-btn-radius"><i class="layui-icon">&#xe654;</i>添加冻结渠道</a>
        </div>


        <form class="layui-form" method="get" action="{:url('index')}">


            <div style="float:right">

                <div class="layui-inline">
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_gameid' name="game_id" value="{$game_id}" />
                        </div>
                    </div>
                </div>
                
                <div class="layui-inline">
                    <div class="layui-input-inline FuzzySearch_Container">
                        <div>
                            <input type="hidden" id='J_channel_id' name="channel_id" value="{$channel_id}" />
                        </div>
                    </div>
                </div>


                <div class="layui-inline">
                    <button class="layui-btn layui-btn-radius" lay-submit id="J_search_submit">搜索</button>
                    <a class="layui-btn layui-btn-radius" href="{:url('index')}">清空</a>
                </div>

            </div>



            <div style="clear:both;"></div>



            <table class="layui-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>创建时间</th>
                            <th>游戏名称</th>
                            <th>渠道名称</th>
                            <th>消费</th>
                            <th>登录</th>
                            <th>新增</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="channle_frozen" id="vo"}
                        <tr>
                            <td>{$vo.id}</td>
                            <td>{$vo.create_time|date="Y-m-d H:i:s",###}</td>
                            <td>
                                {php} echo get_game_nickname($vo['game_id']) {/php}
                            </td>
                            <td>
                                {php} echo get_complex_channel_name($vo['channel_id']) {/php}
                            </td>
                            <td>
                                {if condition="$vo['consume'] eq 0"}
                                <span class="layui-btn layui-btn-normal layui-btn-mini">
                                    未禁止</span> |
                                <a href="{:url('complex_channel_frozen/ban', array('id'=>$vo['id'], 'action' => 'consume'))}" style="color: red">禁止</a>
                                {else/}
                                <span class="layui-btn layui-btn-danger layui-btn-mini">
                                    已禁止</span> |
                                <a href="{:url('complex_channel_frozen/cancelBan', array('id'=>$vo['id'], 'action' => 'consume'))}">开启</a>
                                {/if}
                            </td>
                            <td>
                                {if condition="$vo['member_login'] eq 0"}
                                <span class="layui-btn layui-btn-normal layui-btn-mini">
                                    未禁止</span> |
                                <a href="{:url('complex_channel_frozen/ban', array('id'=>$vo['id'], 'action' => 'member_login'))}" style="color: red">禁止</a>
                                {else/}
                                <span class="layui-btn layui-btn-danger layui-btn-mini">
                                    已禁止</span> |
                                <a href="{:url('complex_channel_frozen/cancelBan', array('id'=>$vo['id'], 'action' => 'member_login'))}">开启</a>
                                {/if}
                            </td>
                            <td>
                                {if condition="$vo['register'] eq 0"}
                                <span class="layui-btn layui-btn-normal layui-btn-mini">
                                    未禁止</span> |
                                <a href="{:url('complex_channel_frozen/ban', array('id'=>$vo['id'], 'action' => 'register'))}" style="color: red">禁止</a>
                                {else/}
                                <span class="layui-btn layui-btn-danger layui-btn-mini">
                                    已禁止</span> |
                                <a href="{:url('complex_channel_frozen/cancelBan', array('id'=>$vo['id'], 'action' => 'register'))}">开启</a>
                                {/if}
                            </td>
            
                            <td>
                                <a href="javascript:;" onclick="channel_open(this,'{:url(\'complex_channel_frozen/cancelBan\',[\'id\'=>$vo.id, \'action\'=>\'all\'])}')" class="layui-btn  layui-btn-danger">
                                    <i class="layui-icon">&#xe640;</i>解冻
                                </a>
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            
                <div class="pager-container">
                    <span>
                      {$total}条记录
                    </span>
                    {$page}
                    <div class="layui-inline" style="margin:0;margin-left:10px;width:80px;">
                        <input type="text" style="height: 30px;" class="layui-input" placeholder="跳转至" name="page" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
                    </div>
                </div>








            
        </form>
   
 


</div>
{/block}

{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>
$("#J_gameid").FuzzySearch({
     inputID    : 'J_gameid',
     title      : '请输入游戏名称',
     data       :{:json_encode($game_list)},
     searchBtn  :'J_search_submit'
});

$("#J_channel_id").FuzzySearch({
     inputID    : 'J_channel_id',
     title      : '请输入渠道名称',
     data       : {:json_encode($channel_list)},
     searchBtn  :'J_search_submit'

});
</script>
{/block}