{extend name="layout/content" /}
{block name="header"}
<title>添加冻结渠道</title>
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
{/block}
{block name="content"}
<div class="x-body">
        <form method="post" class="layui-form" action="{:url('addPost')}">
            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>游戏：
                </label>
                <!-- <div class="layui-input-inline">
                    <select name="game_id" lay-search>
                        <option value="">请选择游戏名称</option>
                        {foreach name="game_list" item="vo"}
                        <option value="{$vo.id}">{$vo.name}</option>
                        {/foreach}
                    </select>
                </div> -->
                <div class="layui-input-inline FuzzySearch_Container">
                    <div>
                        <input type="hidden" id='game_id' name="game_id" value="{:input('game_id')}"/>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label"><span class="x-red">*</span>渠道：</label>
                <div class="layui-input-inline FuzzySearch_Container">
                     <div>
                        <input type="hidden" id='J_channel_id' name="channel_id" value="{:input('channel_id')}"/>
                     </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>消费：
                </label>
                <div class="layui-input-inline">
                    <select class="form-control" name="consume">
                        <option value="0">开启</option>
                        <option value="1">禁止</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>登录：
                </label>
                <div class="layui-input-inline">
                    <select class="form-control" name="member_login">
                        <option value="0">开启</option>
                        <option value="1">禁止</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label for="" class="layui-form-label">
                    <span class="x-red">*</span>新增：
                </label>
                <div class="layui-input-inline">
                    <select class="form-control" name="register">
                        <option value="0">开启</option>
                        <option value="1">禁止</option>
                    </select>
                </div>
            </div>


            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit">提交</button>
                    <a class="layui-btn layui-btn-primary" href="{:url('index')}">返回</a>
                    <input type="hidden" name="grant_money" value="1"/>
                    <input type="hidden" name="subpackage" value="1"/>
                </div>
            </div>
        </form>
</div>
{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script>

    $("#game_id").FuzzySearch({
	 	inputID     : 'game_id',
	 	title   	: '请输入游戏名',
	 	data        :{:json_encode($game_list)},
	 	searchBtn	:'J_submit',
    });
    
    $("#J_channel_id").FuzzySearch({
        inputID    : 'J_channel_id',
        title   	: '请选择渠道名称',
        data       :{:json_encode($channel_list)},
        searchBtn	:'J_submit',
    });
</script>
{/block}