<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="__STATIC__/css/admin/font.css">
    <link rel="stylesheet" href="__STATIC__/css/admin/xadmin.css">
    <link rel="stylesheet" href="__STATIC__/css/admin/paginator.css">
    <link rel="stylesheet" href="__STATIC__/css/admin/Font-Awesome-4.7.0.css">
    <link rel="stylesheet" href="__STATIC__/lib/layui-v2.9.16/css/layui.css?v={$Think.STATIC_VERSION}">

    {block name="header"} {/block}
    <title>{:getEnvs('brand_name')}管理后台</title>
</head>

<body>
    {block name="content"} {/block}

    <script type="text/javascript" src="__STATIC__/js/jquery_v3.3.1.js"></script>
    <script type="text/javascript" src="__STATIC__/lib/layui/layui.js?v={$Think.STATIC_VERSION}" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/admin/xadmin.js?v={$Think.STATIC_VERSION}"></script>

    <script type="text/javascript" src="__STATIC__/lib/layui-v2.9.16/layui.js?v={$Think.STATIC_VERSION}" charset="utf-8"></script>
    <script>
        /*用户-停用*/
        function isShow(obj, url) {
            layer.confirm('确认要修改吗？', {
                shade: 0.4
            }, function (index) {
                $.getJSON(url, function (res) {

                    if (res.code) {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        }, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        });
                    }
                });

            });
        }
        /*用户-停用*/
        function member_stop_open(obj, url) {
            layer.confirm('确认要冻结或者解冻吗？', {
                shade: 0.4
            }, function (index) {
                $.getJSON(url, function (res) {

                    if (res.code) {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        }, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        });
                    }
                });

            });
        }

        /*渠道解冻*/
        function channel_open(obj, url) {
            layer.confirm('确认要解冻吗？', {
                shade: 0.4
            }, function (index) {
                $.getJSON(url, function (res) {

                    if (res.code) {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        }, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        });
                    }
                });

            });
        }
        /*用户-删除*/
        function del_info(obj, url) {
            layer.confirm('确认要删除吗？', {
                shade: 0.4
            }, function (index) {
                $.getJSON(url, function (res) {

                    if (res.code) {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        }, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        });
                    }
                });

            });
        }

        /*用户-发送-驳回*/
        function send_info(obj, url, msg) {
            layer.confirm(msg, {
                shade: 0.4
            }, function (index) {
                $.getJSON(url, function (res) {

                    if (res.code) {
                        layer.msg(res.msg, {
                            icon: 1,
                            shade: 0.4,
                            time: 1000
                        }, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {
                            icon: 0,
                            shade: 0.4,
                            time: 1000
                        }, function () {
                            location.href = res.url;
                        });
                    }
                });

            });
        }

        function CompareDate(d1, d2) {
            var day = ((new Date(d1.replace(/-/g, "\/"))) - (new Date(d2.replace(/-/g, "\/")))) / (1000 * 60 * 60 * 24);
            return Math.abs(day);

        }
    </script>
    {block name="footer"} {/block}
</body>

</html>