{extend name="layout/content" /}
{block name="header"}
<link rel="stylesheet" href="__STATIC__/css/admin/bootstrap.min.css">
<link rel="stylesheet" href="__STATIC__/css/bootstrap-table.min.css">
<link rel="stylesheet" href="__STATIC__/lib/Viewer/css/viewer.min.css">
<link rel="stylesheet" href="__STATIC__/css/FuzzySearch.css?v={$Think.STATIC_VERSION}">
<title>添加礼包</title>
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
{/block}

{block name="content"}
<div class="x-body">
    <form class="layui-form" action="{:url('add')}" method="post">




        <div class="layui-form-item">
            <label class="layui-form-label"> <span class="x-red">*</span>所属游戏：</label>
            <div class="layui-input-inline" style="width: 40%">
                <div id="game"></div>
            </div>
            <input type="hidden" name="game_id" id="game_id" value="">
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">
                <span class="x-red">*</span>发放类型：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="grant_type_id" id="grant_type_id"  class="layui-form-select"
                            lay-search="true" lay-filter="selctOnchange" lay-verify=“required”>
                        <option value="">请选择类型</option>
                        {foreach name="grant_type_ids" item="vo" key='k'}
                        <option value="{$k}">{$vo}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"> <span class="x-red">*</span>福利：</label>
            <div class="layui-input-inline" style="width: 40%">
                <select lay-search name="welfare_id" id="welfare_id" class="layui-form-select" lay-verify=“required”>

                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">
                <span class="x-red">*</span>发放规则：
            </label>
            <div class="layui-input-inline FuzzySearch_Container">
                <div>
                    <select lay-search name="type_id" id="type_id"  class="layui-form-select"
                            lay-search="true" lay-verify=“required”>
                        <option value="">请选择类型</option>
                        {foreach name="type" item="vo" key='k'}
                        <option value="{$k}">{$vo}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red"></span>条件：
            </label>
            <div class="layui-input-inline" style="width: 80%">
                <input type="text" id="condition" name="condition" value="" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label for="" class="layui-form-label">
                <span class="x-red"></span>扩展参数：
            </label>
            <div class="layui-input-inline" style="width: 80%">
                <input type="text" id="ext" name="ext" value="" class="layui-input">
            </div>
        </div>


        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo" id="J_submit_btn">提交</button>
                <button type="button" class="layui-btn layui-btn-primary" onClick="javascript:history.back(-1);">返回
                </button>
            </div>
        </div>

    </form>
</div>

{/block}


{block name="footer"}
<script type="text/javascript" src="__STATIC__/js/FuzzySearch.js?v={$Think.STATIC_VERSION}"></script>
<script type="text/javascript" charset="utf-8" src="__STATIC__/lib/xm-select.js"></script>
<script type="text/javascript" src="__STATIC__/js/admin/bootstrap.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__STATIC__/js/bootstrap-table.min.js"></script>
<script type="text/javascript" src="__STATIC__/lib/Viewer/js/viewer.min.js"></script>
<script>
    var tagData1 = {:json_encode($game_list)}; // 获取到后台给出的数据-PHP写法

    var tagIns1 = xmSelect.render({
        el: '#game', // div的id值
        toolbar: { // 工具条【‘全选’，‘清空’】
            show: true, // 开启工具条
            showIcon: false, // 隐藏工具条的图标
        },
        autoRow: true,
        // tips: '选择校区', // 让默认值不是“请选择”，而是“选择校区”
        filterable: true, // 开启搜索模式，默认按照name进行搜索
        paging: true, // 启用分页
        pageSize: 100, // 每页的数据个数
        data: tagData1,
        prop: { // 也许你的数据库返回的并不是name和value, 也许你提交的时候不止name和value, 怎么办? 自定义就行
            name: 'name',
            value: 'id'
        },
        on: function(data){

            var arr = data.arr;
            if(!arr){
                return;
            }
            gameArr = []
            for (i=0;i<arr.length;i++){
                gameArr[i]=arr[i].id;
            }

            $('#game_id').val(gameArr.toString())
        },
    })

    layui.use(['form', 'layer', 'laydate', 'jquery'], function () {

        var form = layui.form,
            layer = layui.layer
        var laydate = layui.laydate;

        form.on('select(selctOnchange)', function (data) {

            game_id = $('#game_id').val()
            grant_type_id = $('#grant_type_id').val()
            getWelfare(game_id,grant_type_id)

        });

        function getWelfare(game_id,grant_type_id){
            if(!game_id){
                return;
            }
            if(!grant_type_id){
                return;
            }

            $.ajax({
                type: "post",
                url: '/welfare_grant/getWelfare',
                async: false,
                data: {"game_id":game_id,"grant_type_id":grant_type_id},
                dataType: 'json',
                timeout: 5000,
                success: function (data) {
                    $('#welfare_id').empty();
                    if(data.code){
                        console.log(data.data)

                        $.each(data.data, function(index, item) {
                            if(parseInt(welfare_id) == item.id){
                                $('#welfare_id').append(new Option(item.name, item.id,false,true));
                            }else{
                                $('#welfare_id').append(new Option(item.name, item.id));
                            }

                        });
                        layui.form.render("select");
                    }else{
                        alert(data.msg)
                    }
                },
                error: function () {
                    layer.alert('网络错误，请重试');
                }
            });
        }
    });
</script>
{/block}
