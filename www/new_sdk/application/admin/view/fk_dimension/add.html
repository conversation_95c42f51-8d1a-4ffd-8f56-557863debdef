{extend name="layout/content" /}

{block name="header"}
<title>添加异常维度</title>
<link rel="stylesheet" href="__STATIC__/lib/layui-v2.9.16/css/layui.css?v={$Think.STATIC_VERSION}">
{/block}

{block name="content"}
<div class="layui-form" lay-filter="form-add-dimension" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item">
        <label class="layui-form-label">维度名</label>
        <div class="layui-input-block">
            <input type="text" name="title" required lay-verify="required" placeholder="请输入维度名" autocomplete="off" class="layui-input">
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">监控对象</label>
        <div class="layui-input-block">
            <select name="monitor_object" required lay-verify="required">
                <option value="">请选择监控对象</option>
                <option value="ip">IP</option>
                <option value="imei">IMEI</option>
                <option value="username">用户名</option>
                <option value="id_card">身份证</option>
                <option value="phone">手机号</option>
            </select>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">异常对象</label>
        <div class="layui-input-block">
            <input type="checkbox" name="abnormal_object[ip]" value="ip" title="IP">
            <input type="checkbox" name="abnormal_object[imei]" value="imei" title="IMEI">
            <input type="checkbox" name="abnormal_object[username]" value="username" title="用户名">
            <input type="checkbox" name="abnormal_object[id_card]" value="id_card" title="身份证">
            <input type="checkbox" name="abnormal_object[phone]" value="phone" title="手机号">
        </div>
        <div class="layui-form-mid layui-word-aux">可多选监控对象，用于记录异常的对象</div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">异常判定类型</label>
        <div class="layui-input-block">
            <select name="abnormal_judge_type" required lay-verify="required">
                <option value="">请选择判定类型</option>
                <option value="gt">大于</option>
                <option value="lt">小于</option>
                <option value="eq">等于</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">执行方式类型</label>
        <div class="layui-input-block">
            <select name="execute_type" required lay-verify="required">
                <option value="">请选择执行方式类型</option>
                <option value="hour">小时</option>
                <option value="day">天</option>
            </select>
            <div class="layui-form-mid layui-word-aux">选择定时任务类型</div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">异常判定值</label>
        <div class="layui-input-block">
            <input type="number" name="abnormal_judge_value" value="" required lay-verify="required|number" placeholder="请输入异常判定值" autocomplete="off" class="layui-input">
            <div class="layui-form-mid layui-word-aux">用于判断是否异常的阈值</div>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">异常程度</label>
        <div class="layui-input-block">
            <input type="number" name="abnormal_judge_level" value="" required lay-verify="required|number" placeholder="请输入异常程度百分比" autocomplete="off" class="layui-input" max="100" min="0">
            <div class="layui-form-mid layui-word-aux">异常程度百分比（0-100）</div>
        </div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">异常移除</label>
        <div class="layui-input-block">
            <input type="number" name="lifespan_time" value="3600" required lay-verify="required|number" placeholder="请输入有效期（秒）" autocomplete="off" class="layui-input">
            <div class="layui-form-mid layui-word-aux">异常移除时间，单位：秒（默认3600秒=1小时）</div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">状态</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value="1" title="启用" checked>
            <input type="radio" name="status" value="2" title="关闭">
        </div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" id="form-submit-dimension-add">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 监听提交
    $('#form-submit-dimension-add').on('click', function(data){
        form.submit('form-add-dimension', function(data){
            // 更新表单数据
            $.ajax({
                url: '{:url("add")}',
                type: 'post',
                data: data.field,
                dataType: 'json',
                success: function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1}, function(){
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
            return false;
        });
    })
});
</script>
{/block}