{extend name="layout/content" /}

{block name="header"}
<title>异常维度管理</title>
{/block}

{block name="content"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">异常维度管理</div>
        <div class="layui-card-body">
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">维度名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="title" placeholder="请输入维度名" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">监控对象</label>
                        <div class="layui-input-inline">
                            <select name="monitor_object">
                                <option value="">全部</option>
                                <option value="ip">IP地址</option>
                                <option value="imei">IMEI设备号</option>
                                <option value="username">用户名</option>
                                <option value="id_card">身份证号</option>
                                <option value="phone">手机号</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">异常对象</label>
                        <div class="layui-input-inline">
                            <select name="abnormal_object">
                                <option value="">全部</option>
                                <option value="ip">IP地址</option>
                                <option value="imei">IMEI设备号</option>
                                <option value="username">用户名</option>
                                <option value="id_card">身份证号</option>
                                <option value="phone">手机号</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">判定类型</label>
                        <div class="layui-input-inline">
                            <select name="abnormal_judge_type">
                                <option value="">全部</option>
                                <option value="gt">大于</option>
                                <option value="lt">小于</option>
                                <option value="eq">等于</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="1">启用</option>
                                <option value="2">关闭</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layuiadmin-btn-list" lay-submit lay-filter="table-search">
                            <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>查询
                        </button>
                        <button class="layui-btn layuiadmin-btn-list" id="btn-reset">
                            <i class="layui-icon layui-icon-refresh layuiadmin-button-btn"></i>重置
                        </button>
                    </div>
                </div>
            </div>

            <table id="table-list" lay-filter="table-list"></table>

            <div class="layui-btn-group">
                <button class="layui-btn" id="btn-add">
                    <i class="layui-icon layui-icon-add-1"></i>添加维度
                </button>
                <button class="layui-btn layui-btn-danger" id="btn-batch-delete">
                    <i class="layui-icon layui-icon-delete"></i>批量删除
                </button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script type="text/html" id="toolbar">
    <div>
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    </div>
</script>

<script type="text/html" id="statusTpl">
    {{# if(d.status == 1){ }}
        <span class="layui-badge layui-bg-green">启用</span>
    {{# } else { }}
        <span class="layui-badge">关闭</span>
    {{# } }}
</script>

<script type="text/html" id="monitorObjectTpl">
    {{# if(d.monitor_object == 'ip'){ }}
        IP
    {{# } else if(d.monitor_object == 'imei'){ }}
        IMEI
    {{# } else if(d.monitor_object == 'username'){ }}
        用户名
    {{# } else if(d.monitor_object == 'id_card'){ }}
        身份证
    {{# } else if(d.monitor_object == 'phone'){ }}
        手机号
    {{# } else { }}
        {{d.monitor_object}}
    {{# } }}
</script>

<script type="text/html" id="judgeTypeTpl">
    {{# if(d.abnormal_judge_type == 'gt'){ }}
        <span class="layui-badge layui-bg-blue">大于</span>
    {{# } else if(d.abnormal_judge_type == 'lt'){ }}
        <span class="layui-badge layui-bg-orange">小于</span>
    {{# } else if(d.abnormal_judge_type == 'eq'){ }}
        <span class="layui-badge layui-bg-cyan">等于</span>
    {{# } else { }}
        {{d.abnormal_judge_type}}
    {{# } }}
</script>

<script type="text/html" id="abnormalJudgeType">
    {{# if(d.abnormal_judge_type){ }}
        <span> > {{d.abnormal_judge_value}}</span>
    {{# } else if(d.abnormal_judge_type == 'lt'){ }}
        <span> < {{d.abnormal_judge_value}}</span>
    {{# } else if(d.abnormal_judge_type == 'eq'){ }}
        <span> = {{d.abnormal_judge_value}}</span>
    {{# } else { }}
        {{d.abnormal_judge_value}}
    {{# } }}
</script>

<script type="text/html" id="handleExecuteType">
    {{# if(d.execute_type == 'hour'){ }}
        <span>小时</span>
    {{# } else if(d.execute_type == 'day'){ }}
        <span>天</span>
    {{# } else { }}
        <span>{{d.execute_type}}</span>
    {{# } }}
</script>

<script type="text/html" id="abnormalObjectTpl">
    {{# 
        var objectMap = {
            'ip': 'IP地址',
            'imei': 'IMEI设备号', 
            'username': '用户名',
            'id_card': '身份证号',
            'phone': '手机号'
        };
        var objects = d.abnormal_object.split(',');
        var displayNames = [];
        for(var i = 0; i < objects.length; i++) {
            if(objectMap[objects[i]]) {
                displayNames.push(objectMap[objects[i]]);
            } else {
                displayNames.push(objects[i]);
            }
        }
    }}
    {{displayNames.join('、')}}
</script>

<script>
$(function() {
    layui.use(['table', 'form', 'layer'], function(){
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;

        // 渲染表格
        table.render({
            elem: '#table-list',
            url: '{:url("index")}',
            page: true,
            limit: 20,
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field: 'dimension_id', title: 'ID', width: 80, sort: true, fixed: 'left'},
                {field: 'title', title: '维度名', minWidth: 120},
                {field: 'monitor_object', title: '监控对象', width: 100, templet: '#monitorObjectTpl'},
                {field: 'abnormal_object', title: '异常对象', minWidth: 130, templet: '#abnormalObjectTpl'},
                {field: 'execute_type', title: '执行类型(小时/天)', width: 130, templet: '#handleExecuteType'},
                {field: 'abnormal_judge_value', title: '判定值', width: 100, templet: '#abnormalJudgeType'},
                {field: 'abnormal_judge_level', title: '异常程度(%)', width: 120},
                {field: 'lifespan_time', title: '异常移除(秒)', width: 120},
                {field: 'status', title: '状态', width: 80, templet: '#statusTpl'},
                {field: 'create_time', title: '创建时间', width: 160},
                {field: 'update_time', title: '更新时间', width: 160},
                {title: '操作', align: 'center', fixed: 'right', toolbar: '#toolbar'}
            ]]
        });

        // 监听搜索
        form.on('submit(table-search)', function(data){
            table.reload('table-list', {
                where: data.field,
                page: {curr: 1}
            });
            return false;
        });

        // 重置
        $('#btn-reset').click(function(){
            $('.layui-form')[0].reset();
            table.reload('table-list', {
                where: {},
                page: {curr: 1}
            });
        });

        // 添加
        $('#btn-add').click(function(){
            layer.open({
                type: 2,
                title: '添加异常维度',
                content: '{:url("add")}',
                area: ['800px', '700px'],
                maxmin: true,
                end: function(){
                    table.reload('table-list');
                }
            });
        });

        // 批量删除
        $('#btn-batch-delete').click(function(){
            var checkStatus = table.checkStatus('table-list');
            var data = checkStatus.data;
            if(data.length === 0){
                layer.msg('请先选择要删除的数据');
                return;
            }

            layer.confirm('确定删除选中的 ' + data.length + ' 条数据吗？', function(index){
                var ids = [];
                $.each(data, function(i, item){
                    ids.push(item.dimension_id);
                });

                $.post('{:url("batchDelete")}', {ids: ids}, function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1});
                        table.reload('table-list');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });

                layer.close(index);
            });
        });

        // 监听工具条
        table.on('tool(table-list)', function(obj){
            var data = obj.data;
            if(obj.event === 'edit'){
                layer.open({
                    type: 2,
                    title: '编辑异常维度',
                    content: '{:url("edit")}?id=' + data.dimension_id,
                    area:['800px', '700px'],
                    maxmin: true,
                    end: function(){
                        table.reload('table-list');
                    }
                });
            } else if(obj.event === 'delete'){
                layer.confirm('确定删除该异常维度吗？', function(index){
                    $.post('{:url("delete")}', {id: data.dimension_id}, function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                    layer.close(index);
                });
            }
        });
    });
});
</script>
{/block}