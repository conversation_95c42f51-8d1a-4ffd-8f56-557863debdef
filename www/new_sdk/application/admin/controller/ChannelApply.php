<?php
/**
 * 渠道注册申请控制器
 */

namespace app\admin\controller;

use app\common\model\ChannelApply as ChannelApplyModel;
use app\common\model\Channel;
use app\common\model\CoinSetting;
use app\common\model\Admin as AdminModel;
use app\common\library\MakeReport;
use app\common\logic\Coin as CoinLogic;
use think\Db;

class ChannelApply extends Admin
{
    private $applyModel;
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
        
        $this->applyModel = new ChannelApplyModel;
    }
    
    /**
     * 发币申请列表
     */
    public function index()
    {
        $where = $this->getListCondition();
        
        $param = input('request.');		//分页带条件
        
        $list = $this->applyModel
                    ->field('*')
                    ->where($where)
                    ->order('status asc,create_time desc')
                    ->paginate(10,false,array('query' => $param));
        
        // 获取分页显示的HTML
        $page = $list->render();
        

        //后台管理员列表
        $adminList = model('Admin')->field('id,username as name')->where(['status'=>1,'type'=>1])->order('username asc')->select();

        $this->assign('page', $page);
        $this->assign('list',$list);
        $this->assign('admin_list',$adminList);

        return $this->fetch('list');
    }
    

     /**
    * 发币申请列表的查询条件
    *
    * @return array $where 查询条件
    */
    private function getListCondition()
    {
        $start_time = input('request.start_time',date('Y-m-d'));
        
        //渠道名
        if(input('request.username')!='')
        {
            $where['username'] = input('request.username','','trim');
        }
        
        //手机号
        if(input('request.linkman_mobile')!='')
        {
            $where['linkman_mobile'] = input('request.linkman_mobile','','trim');
        }

        //审核状态
        if(input('request.status')!='')
        {
            $where['status'] = input('request.status',0,'intval');
        }
        
        //开始时间和结束时间不为空时
        if ($start_time != '' && input('request.end_time') != '') {
            $where['create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime(input('request.end_time').' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time!= '') {
            $where['create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif (input('request.end_time') != '') {
            $where['create_time'] = ['<=', strtotime(input('request.end_time').' 23:59:59')];
        }
        
        return $where;
    }
    
    
    /**
     * 审核
     *
     */
    public function audit()
    {
        if (request()->isAjax()) {
            $id = input('id',0,'intval');
            $contact_admin_id = input('contact_admin_id',0,'intval');
            $beizhu = input('beizhu','','trim');
            $status = input('status','0','intval');

            if(empty($id)){
                $this->error('ID不能为空');
            }
	        if($status<>1 && $status<>2){
                $this->error('非法审核状态');
            }
			if($status==1 &&  !$contact_admin_id){
                $this->error('请选择对接人');
			}
			
			$applyInfo =  model('ChannelApply')->where(['id' => $id])->find();
			if(empty($applyInfo)){
				$this->error('渠道注册申请不存在');
			}

			if($applyInfo['status'] <> 0){
				$this->error('该渠道注册申请已审核过');
			}

			if($status==1){
                $channelInfo = model('Channel')->where(['name' => $applyInfo['username']])->find();
                if ( $channelInfo ) {
					$this->error('该渠道信息已存在');
                }
                $userInfo = model('Admin')->field('id')->where(['username' => $applyInfo['username']])->find();
                if ($userInfo) {
                    $this->error("该用户名已存在! ");
                }
			}

			$auditData = array();
			$auditData['id'] = $id;
			$auditData['status'] = $status;
			$auditData['audit_time'] = NOW_TIMESTAMP;
			$auditData['beizhu'] = $beizhu;
			$auditData['contact_admin_id'] = $contact_admin_id;
			$auditData['audit_admin_id'] = session('ADMIN_ID');

            Db::startTrans();
            try {
				if($status==1){
					$channelInfo = model("Channel")->where(['name' => $applyInfo['username']])->find();
					if ( $channelInfo ) {
						throw new Exception("该渠道信息已存在");
					}
					$userInfo = model("Admin")->field('id')->where(['username' => $applyInfo['username']])->find();
					if ($userInfo) {
						throw new Exception("用户名已存在! ");
					}

					//添加渠道信息
					$channelModel = model('channel');
					$channelModel->save([
						'name'          => $applyInfo['username'],
						'parent_id'     => config('TOP_CHANNEL_ID'),
						'id_path'       => ','.config('TOP_CHANNEL_ID').',',
						'create_time'   => $applyInfo['create_time'],
						'flag'          => 3,
						'show_full_account' => 0,
						'remark'        => $beizhu,
						'status'        => 1]);
				//	echo $channelModel->id."----channelModel->id-----<br>";
					if ( !$channelModel->id) {
						throw new Exception("添加渠道信息失败");
					}
					
					$channelData = array(); 
					$channelData['channel_id']		= $channelModel->id;
					$channelData['real_name']		= $applyInfo['real_name'];
					$channelData['account_wechat']	= $applyInfo['account_wechat'];
					$channelData['account_qq']		= $applyInfo['account_qq'];
					$channelData['account_type']    = $applyInfo['account_type'];
					$channelData['alipay_realname']    = $applyInfo['alipay_realname'];
					$channelData['alipay_account']    = $applyInfo['alipay_account'];
					$channelData['bank_account']    = $applyInfo['bank_account'];
					$channelData['company_name']    = $applyInfo['company_name'];
					$channelData['bank_name']    = $applyInfo['bank_name'];
					$channelData['linkman_email']    = $applyInfo['linkman_email'];
					$channelData['business_mode_public']    = $applyInfo['business_mode_public'];
					$channelData['business_mode_private']    = $applyInfo['business_mode_private'];
					$channelData['company_scale']    = $applyInfo['company_scale'];
					$channelData['promote_games']    = $applyInfo['promote_games'];
					$channelData['promote_platforms']    = $applyInfo['promote_platforms'];
					$channelData['company_has_shop']    = $applyInfo['company_has_shop'];
					$channelData['shop_platform']    = $applyInfo['shop_platform'];
					$channelData['shop_address']    = $applyInfo['shop_address'];
					$channelData['shop_name']    = $applyInfo['shop_name'];
					$channelData['linkman_address']    = $applyInfo['linkman_address'];
					$channelData['linkman_mobile']    = $applyInfo['linkman_mobile'];
					$channelData['create_time']   = $applyInfo['create_time'];
					$channelData['audit_time']   = NOW_TIMESTAMP;
					$channelData['contact_admin_id']   = $contact_admin_id;
					$channelData['audit_admin_id']   = session('ADMIN_ID');
					$channelData['update_time'] = NOW_TIMESTAMP;
					$channelData['admin_id'] = session('ADMIN_ID');
					$insertChannelResult = model("ChannelInfo")->insertGetId($channelData);
				//	echo $insertChannelResult."----insertChannelResult-----<br>";

					if ($insertChannelResult === false) {
						throw new Exception("添加渠道更多信息失败");
					}

					$adminData = array(); 
					$adminData['username']      = $applyInfo['username'];
					$adminData['password']      = $applyInfo['password'];
					$adminData['pay_password']  = $applyInfo['pay_password'];
					$adminData['type']          = 2;
					$adminData['channel_id']    = $channelModel->id;
					$adminData['beizhu']		= $beizhu;
					$adminData['status']		= 1;
					$adminData['mobile']		= $applyInfo['linkman_mobile'];
					$adminData['create_time']   = $applyInfo['create_time'];
					$adminData['password_update_time'] = NOW_TIMESTAMP;
					$insertAdminResult = model("Admin")->insertGetId($adminData);
				//	echo $insertAdminResult."----insertAdminResult-----<br>";

					if ($insertAdminResult === false) {
						throw new Exception("添加管理员账号失败");
					}

					$auditData['channel_id'] = $channelModel->id;
					$result = $this->applyModel->where(['id'=>$id])->update($auditData);
				//	echo $result."---result-----<Br>";
					if ($result === false) {
						throw new Exception("审核修改状态失败");
					}
					$this->insertLog($this->current_node, "渠道审核表审核通过新增子渠道：".$applyInfo['username']."，状态：正常",41);
				}
				else if($status==2){
					$result = $this->applyModel->where(['id'=>$id])->update($auditData);
					if ($result === false) {
						throw new Exception("审核修改状态失败");
					}
				}
                Db::commit();


			//	$this->success('审核成功');
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }
			$this->success('审核成功');
        }
        else{
            $this->error('非法请求');
        }
    }
    

}
