<?php

/**
 * 后台的公共控制器类
 *
 */

namespace app\admin\controller;

use app\common\controller\Base;
use app\common\logic\Websocket;
use app\common\model\Admin as AdminModel;
use think\Db;
use think\Session;

class Admin extends Base
{
    // 不验证权限的action
    protected $noCheckAuth = [];

    protected $admin_info;      //后台登录用户资料

    protected $current_node;

    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
        $session_admin_id = session('ADMIN_ID');

        if (!empty($session_admin_id)) {
            if (!$this->checkAccess($session_admin_id)) {
                $this->error("您没有访问权限！");
            }

            // 登陆账户的session到期后，重置token
            if (!session('WS_CONNECT_KEY')) {
                $ws = new Websocket;
                $ws->resettingToken(session_id(), 1);
            }
        } else {
            if ($this->request->isPost()) {
                $this->error("您还没有登录！", '/login/index');
            } else {
                return $this->redirect('/login/index');
            }
        }

        $this->current_node = strtolower($this->request->module() . '/' . $this->request->controller() . '/' . $this->request->action());
    }

    /**
     *  检查后台用户访问权限
     * @param int $userId 后台用户id
     * @return boolean 检查通过返回true
     */
    private function checkAccess($userId)
    {
        // 如果用户id是1，则无需判断
        if ($userId == 1) {
            return true;
        }

        $module     = $this->request->module();
        $controller = $this->request->controller();
        $action     = $this->request->action();
        $rule       = $module . $controller . $action;

        // action检测
        $actionCheck = in_array(strtolower($action),
            array_map(function ($val){
                return strtolower($val);
                }, $this->noCheckAuth)
        );

        $notRequire = ["adminIndexindex", "adminMainindex"];
        if (!in_array($rule, $notRequire) &&
            ! $actionCheck) {
            return cmf_auth_check($userId);
        } else {
            return true;
        }
    }

    /**
     * 返回 json
     * @access protected
     * @param mixed $data 要返回的数据
     * @param int $code 返回的 code
     * @param mixed $msg 提示信息
     * @return void
     * @throws HttpResponseException
     */
    public function jsonResult($data, $code = 0, $msg = '')
    {
        $this->result($data, $code, $msg, 'json');
    }

    /**
     * 初始化nw前缀 model
     */
    public function nwModel($dbName)
    {
        return Db::name('nw_' . $dbName);
    }

    /**
     * 初始化cy前缀 model
     */
    public function cyModel($dbName)
    {
        return Db::name('cy_' . $dbName);
    }

    /**
     * 发送短信验证码
     */
    public function sendSmsCode()
    {
        if (request()->isAjax()) {

            $action = input('post.action', '', 'trim');

            switch ($action) {
                case 'coinSend':           //发币时短信验证

                    $mobile = (new AdminModel)->where(['id' => session('ADMIN_ID')])->value('mobile');

                    break;

                default:

                    $mobile = input('post.mobile', '', 'trim');

                    break;
            }

            $result = (new \app\common\library\Sms)->sendCode($mobile);

            if ($result['status']) {
                $this->success('发送成功，请查收');
            } else {
                $this->error($result['msg']);
            }
        } else {
            $this->error('非法请求');
        }
    }

    protected function insertLog($node = '', $content = '', $type = 0, $admin_id = '', $username = '')
    {
        if (empty($admin_id)) {
            $admin_id = session('ADMIN_ID') ?: 1;
        }
        if (empty($username)) {
            $username = session('USERNAME') ?: '';
        }
        
        return \app\common\logic\Log::insertOperateLog($node, $content, $type, $admin_id, $username);
    }

    /**
     *  排序 排序字段为list_orders数组 POST 排序字段为：list_order
     */
    protected function listOrders($model ,$pk = 'id')
    {
        if (!is_object($model)) {
            return false;
        }

        if ($pk !== 'id') {
            $pk  = $model->pk; //获取主键名称
        }

        $ids = $this->request->post("list_orders/a");

        if (!empty($ids)) {
            foreach ($ids as $key => $r) {
                $data['list_order'] = $r;
                $model->where([$pk => $key])->update($data);
            }

        }

        return true;
    }
	/**
	 * 下载成exel表格
	 *
	 * @param array $list 数组
	 *
	 * @return null
	 */
	protected function downloadexls($list,$title,$type,$list_more=array()) {
        Vendor('PHPExcel.PHPExcel');//调用类库,路径是基于vendor文件夹的
        Vendor('PHPExcel.PHPExcel.Worksheet.Drawing');
        Vendor('PHPExcel.PHPExcel.Writer.Excel2007');
        $objExcel = new \PHPExcel();
        //set document Property
        $objWriter = \PHPExcel_IOFactory::createWriter($objExcel, 'Excel2007');

		if ($type == 'finance') {
            $objActSheet = $objExcel->getActiveSheet();
            $key = ord("A");
            $letter =explode(',',"A,B,C,D,E,F,G,H,I");
            $arrHeader =  array('开始时间','结束时间','游戏名称','渠道(包括子渠道)','结算类型','补点起止时间','消费金额','补点(%)','补点金额');
            //填充表头信息
            $lenth =  count($arrHeader);
            for($i = 0;$i < $lenth;$i++) {
                $objActSheet->setCellValue("$letter[$i]1","$arrHeader[$i]");
            };
            //填充表格信息
            foreach($list as $k=>$v){
                $k +=2;
                $objActSheet->setCellValue('A'.$k,$v['starttime']);
                $objActSheet->setCellValue('B'.$k, $v['endtime']);
                $objActSheet->setCellValue('C'.$k, $v['game_name']);
                $objActSheet->setCellValue('D'.$k, $v['channel_name']);
                $objActSheet->setCellValue('E'.$k, $v['settle_type']);
                $objActSheet->setCellValue('F'.$k, $v['begin_date'].'~'.$v['end_date']);
                $objActSheet->setCellValue('G'.$k, floatval($v['total_amount']));
                $objActSheet->setCellValue('H'.$k, $v['point']);
                $objActSheet->setCellValue('I'.$k, floatval($v['point_amt']));
                // 表格高度
                $objActSheet->getRowDimension($k)->setRowHeight(20);
            }
		}
		else if($type=='queryChildChannel'){
            $objActSheet = $objExcel->getActiveSheet();
            $key = ord("A");
            $letter =explode(',',"A,B");
            $arrHeader =  array('计数','渠道名称');
            //填充表头信息
            $lenth =  count($arrHeader);
            for($i = 0;$i < $lenth;$i++) {
                $objActSheet->setCellValue("$letter[$i]1","$arrHeader[$i]");
            };
            //填充表格信息
            foreach($list as $k=>$v){
                $k +=2;
                $objActSheet->setCellValue('A'.$k,$k-1);
                $objActSheet->setCellValue('B'.$k, $v['name']);
                // 表格高度
                $objActSheet->getRowDimension($k)->setRowHeight(20);
            }
		}
		else if($type=='listGame'){
            $objActSheet = $objExcel->getActiveSheet();
            $key = ord("A");
            $letter =explode(',',"A,B,C,D,E,F");
            $arrHeader =  array('游戏ID','游戏名称','游戏原名','渠道名称','创建时间','备注');
            //填充表头信息
            $lenth =  count($arrHeader);
            for($i = 0;$i < $lenth;$i++) {
                $objActSheet->setCellValue("$letter[$i]1","$arrHeader[$i]");
            };
            //填充表格信息
            foreach($list as $k=>$v){
                $k +=2;
                $objActSheet->setCellValue('A'.$k, $v['game_id']);
                $objActSheet->setCellValue('B'.$k, $v['game_name']);
				$objActSheet->setCellValue('C'.$k, $v['origin_name']);
                $objActSheet->setCellValue('D'.$k, $v['channel_name']);
                $objActSheet->setCellValue('E'.$k, $v['create_time']);
                $objActSheet->setCellValue('F'.$k, $v['remark']);
                // 表格高度
                $objActSheet->getRowDimension($k)->setRowHeight(20);
            }

		}
		else if($type=='listGameFrozen'){
			//聚合游戏冻结配置
            $objActSheet = $objExcel->getActiveSheet();
            $key = ord("A");
            $letter =explode(',',"A,B,C,D,E,F,G,H,I,J");
            $arrHeader =  array('游戏名称','游戏原名','预付状态','当前流水','分成点位','实际流水','禁止新增','禁止登录','禁止充值','时间');
            //填充表头信息
            $lenth =  count($arrHeader);
            for($i = 0;$i < $lenth;$i++) {
                $objActSheet->setCellValue("$letter[$i]1","$arrHeader[$i]");
            };
            //填充表格信息
            foreach($list as $k=>$v){
				$index = $k + 2;
                $objActSheet->setCellValue('A'.$index, $v['game_name']);
                $objActSheet->setCellValue('B'.$index, $v['game_name']);
                $objActSheet->setCellValue('C'.$index, $v['status'] ? '开启' : '关闭');
                $objActSheet->setCellValue('D'.$index, $v['total_amount']);
                $objActSheet->setCellValue('E'.$index, $v['divide_point'] ?$v['divide_point']: '--');
                $objActSheet->setCellValue('F'.$index, $v['divide_point'] ? $v['total_amount']*$v['divide_point']/10 : '--');
                $objActSheet->setCellValue('G'.$index, $v['depf_register'] ? '是' : '否');
                $objActSheet->setCellValue('H'.$index, $v['depf_login'] ? '是' : '否');
                $objActSheet->setCellValue('I'.$index, $v['depf_consume'] ? '是' : '否');
                $objActSheet->setCellValue('J'.$index, $v['create_time']);
                // 表格高度
                $objActSheet->getRowDimension($index)->setRowHeight(20);
            }
				
			$index = $index+2;
			$objActSheet->setCellValue('B' . $index, '预付状态未开启的游戏流水，不计入渠道总流水（实际总流水）。');
			$index = $index+1;
			$objActSheet->setCellValue('B' . $index, '总流水：');
			$objActSheet->setCellValue('C' . $index, $list_more['count_total_amount']);
			$index = $index+1;
			$objActSheet->setCellValue('B' . $index, '实际总流水：');
			$objActSheet->setCellValue('C' . $index, $list_more['count_real_amount']);
			$index = $index+1;
			$objActSheet->setCellValue('B' . $index, '累计预付款：');
			$objActSheet->setCellValue('C' . $index, $list_more['total_advance']);
			$index = $index+1;
			$objActSheet->setCellValue('B' . $index, '当前余额：');
			$objActSheet->setCellValue('C' . $index, $list_more['balance']);
			$index = $index+1;
			$objActSheet->setCellValue('B' . $index, '最后预付款：');
			$objActSheet->setCellValue('C' . $index, $list_more['last_advance']);
		}
		else if($type=='channelWithdraw'){
			//聚合游戏冻结配置
            $objActSheet = $objExcel->getActiveSheet();
            $key = ord("A");
            $letter =explode(',',"A,B,C,D,E,F,G,H,I");
            $arrHeader =  array('订单编号','公会名称','会长银行开户名','银行名称','会长银行开户行','会长银行卡号','会长支付宝','提现金额(元)','审核时间');
            //填充表头信息
            $lenth =  count($arrHeader);
            for($i = 0;$i < $lenth;$i++) {
                $objActSheet->setCellValue("$letter[$i]1","$arrHeader[$i]");
            };
            //填充表格信息
            foreach($list as $k=>$v){
				$index = $k + 2;
                $objActSheet->setCellValueExplicit('A'.$index, $v['orderid'],'s');
                $objActSheet->setCellValueExplicit('B'.$index, $v['channel_name'],'s');
                $objActSheet->setCellValueExplicit('C'.$index, $v['real_name'],'s');
                $objActSheet->setCellValueExplicit('D'.$index, $v['bank_name'],'s');
                $objActSheet->setCellValueExplicit('E'.$index, $v['bank_open_name'],'s');
                $objActSheet->setCellValueExplicit('F'.$index, $v['bank_number'],'s');
                $objActSheet->setCellValueExplicit('G'.$index, $v['zfb_account'],'s');
                $objActSheet->setCellValue('H'.$index, floatval($v['withdraw_amt']));
                $objActSheet->setCellValue('I'.$index, date("Y-m-d H:i:s",$v['audit_time']));
                // 表格高度
                $objActSheet->getRowDimension($index)->setRowHeight(20);
            }
				
			$index = $index+3;
			$objActSheet->setCellValue('G' . $index, '汇总：');
			$objActSheet->setCellValue('H' . $index, $list_more['total_withdraw_amt']);
			$index = $index+3;
			$objActSheet->setCellValue('A' . $index, '制表人：');
			$objActSheet->setCellValue('C' . $index, '财务专员：');
			$objActSheet->setCellValue('E' . $index, '财务经理：');
		}
		else if($type=='settleMonthData'){
			//聚合游戏冻结配置
            $objActSheet = $objExcel->getActiveSheet();
            $key = ord("A");
            $letter =explode(',',"A,B,C,D,E,F,G");
            $arrHeader =  array('会长ID','会长账号','未结算订单总额','结算中订单总额','未提现余额','提现中总额','汇总金额');
            //填充表头信息
            $lenth =  count($arrHeader);
            for($i = 0;$i < $lenth;$i++) {
                $objActSheet->setCellValue("$letter[$i]1","$arrHeader[$i]");
            };
            //填充表格信息
            foreach($list as $k=>$v){
				$index = $k + 2;
                $objActSheet->setCellValueExplicit('A'.$index, $v['channel_id'],'s');
                $objActSheet->setCellValueExplicit('B'.$index, $v['channel_name'],'s');
                $objActSheet->setCellValue('C'.$index, floatval($v['unsettle_amt']));
                $objActSheet->setCellValue('D'.$index, floatval($v['settle_apply_amt']));
                $objActSheet->setCellValue('E'.$index, floatval($v['unwithdraw_amt']));
				$objActSheet->setCellValue('F'.$index, floatval($v['withdraw_apply_amt']));
                $objActSheet->setCellValue('G'.$index, floatval($v['total_amt']));
                // 表格高度
                $objActSheet->getRowDimension($index)->setRowHeight(20);
            }
		}
		else if($type=='channelExtraPoint'){
			//聚合游戏冻结配置
            $objActSheet = $objExcel->getActiveSheet();
			$objActSheet->setCellValue('A1', '月补点汇总表');
			$objActSheet->mergeCells('A1:K1');
			$objActSheet->getStyle('A1:K1')->getAlignment()->setHorizontal('center');
			$objActSheet->setCellValue('A2', '序列');
			$objActSheet->mergeCells('A2'.':'.'A3');
			$objActSheet->getStyle('A2:A3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('B2', '时间');
			$objActSheet->mergeCells('B2'.':'.'B3');
			$objActSheet->getStyle('B2:B3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('C2', '游戏名');
			$objActSheet->mergeCells('C2'.':'.'C3');
			$objActSheet->getStyle('C2:C3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('D2', '公会名');
			$objActSheet->mergeCells('D2'.':'.'D3');
			$objActSheet->getStyle('D2:D3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('E2', '充值流水');
			$objActSheet->mergeCells('E2:G2');
			$objActSheet->getStyle('E2:G2')->getAlignment()->setHorizontal('center');
			$objActSheet->setCellValue('E3', '充值流水');
			$objActSheet->setCellValue('F3', '点位（阶梯）');
			$objActSheet->setCellValue('G3', '公会所得金额');
			$objActSheet->setCellValue('H2', '收款名称');
			$objActSheet->mergeCells('H2'.':'.'H3');
			$objActSheet->getStyle('H2:H3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('I2', '支付宝账号');
			$objActSheet->mergeCells('I2'.':'.'I3');
			$objActSheet->getStyle('I2:I3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('J2', '银行账号');
			$objActSheet->mergeCells('J2'.':'.'J3');
			$objActSheet->getStyle('J2:J3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('K2', '银行名称');
			$objActSheet->mergeCells('K2'.':'.'K3');
			$objActSheet->getStyle('K2:K3')->getAlignment()->setVertical('center');
			$objActSheet->setCellValue('L2', '开户行');
			$objActSheet->mergeCells('L2'.':'.'L3');
			$objActSheet->getStyle('L2:L3')->getAlignment()->setVertical('center');

            //填充表格信息
			$index = 4;
			$channelRank = 1;
			$totalPayAmt = $totalDivideAmt = 0;
            foreach($list_more as $k=>$v){
				$totalPayAmt += $v['channel_pay_amt'];
				$totalDivideAmt += $v['channel_divide_amt'];
				$channelCnt = $v['channel_game_cnt'];
				$channelIndex = 1;
				foreach($list[$v['channel_id']] as $k2=>$v2){
					if($channelIndex==1){
						$objActSheet->setCellValue('A'.$index, $channelRank);
						$objActSheet->getStyle('A'.$index)->getAlignment()->setHorizontal('center');
						$objActSheet->mergeCells('A'.$index.':A'.($index+$channelCnt));
						$objActSheet->getStyle('A'.$index.':A'.($index+$channelCnt))->getAlignment()->setVertical('center');
					}
					$objActSheet->setCellValueExplicit('B'.$index, $v2['start_date'].' ~ '.$v2['end_date'],'s');
					$objActSheet->setCellValueExplicit('C'.$index, $v2['game_name'],'s');
					$objActSheet->setCellValueExplicit('D'.$index, $v2['channel_name'],'s');
					$objActSheet->setCellValue('E'.$index, floatval($v2['pay_amt']));
					$objActSheet->setCellValueExplicit('F'.$index, $v2['rate']."%",'s');
					$objActSheet->getStyle('F'.$index)->getAlignment()->setHorizontal('center');
					$objActSheet->setCellValue('G'.$index, floatval($v2['divide_amt']));
					
					if($channelIndex==1){
						$objActSheet->setCellValueExplicit('H'.$index, $v2['real_name'],'s');
						$objActSheet->mergeCells('H'.$index.':H'.($index+$channelCnt-1));
						$objActSheet->getStyle('H'.$index.':H'.($index+$channelCnt-1))->getAlignment()->setVertical('center');

						$objActSheet->setCellValueExplicit('I'.$index, $v2['zfb_account'],'s');
						$objActSheet->mergeCells('I'.$index.':I'.($index+$channelCnt-1));
						$objActSheet->getStyle('I'.$index.':I'.($index+$channelCnt-1))->getAlignment()->setVertical('center');

						$objActSheet->setCellValueExplicit('J'.$index, $v2['bank_number'],'s');
						$objActSheet->mergeCells('J'.$index.':J'.($index+$channelCnt-1));
						$objActSheet->getStyle('J'.$index.':J'.($index+$channelCnt-1))->getAlignment()->setVertical('center');

						$objActSheet->setCellValueExplicit('K'.$index, $v2['bank_name'],'s');
						$objActSheet->mergeCells('K'.$index.':K'.($index+$channelCnt-1));
						$objActSheet->getStyle('K'.$index.':K'.($index+$channelCnt-1))->getAlignment()->setVertical('center');

						$objActSheet->setCellValueExplicit('L'.$index, $v2['bank_open_name'],'s');
						$objActSheet->mergeCells('L'.$index.':L'.($index+$channelCnt-1));
						$objActSheet->getStyle('L'.$index.':L'.($index+$channelCnt-1))->getAlignment()->setVertical('center');

						$objActSheet->getRowDimension($k)->setRowHeight(20);
					}
					$index++;
					$channelIndex++;
				}
				$objActSheet->setCellValueExplicit('B'.$index, '合计','s');
				$objActSheet->mergeCells('B'.$index.':D'.$index);
				$objActSheet->getStyle('B'.$index.':D'.$index)->getAlignment()->setHorizontal('center');

				$objActSheet->setCellValue('E'.$index, $v['channel_pay_amt']);
				$objActSheet->setCellValueExplicit('F'.$index, '-','s');
				$objActSheet->setCellValue('G'.$index, $v['channel_divide_amt']);
				$objActSheet->setCellValueExplicit('H'.$index, '','s');
				$objActSheet->setCellValueExplicit('I'.$index, '','s');
				$objActSheet->setCellValueExplicit('J'.$index, '','s');
				$objActSheet->setCellValueExplicit('K'.$index, '','s');
				$objActSheet->getRowDimension($k)->setRowHeight(20);
				$index++;
				$channelRank++;
            }
				
			$objActSheet->setCellValueExplicit('A' . $index, '', 's');
			$objActSheet->setCellValueExplicit('B'.$index, '总计','s');
			$objActSheet->mergeCells('B'.$index.':D'.$index);
			$objActSheet->getStyle('B'.$index.':D'.$index)->getAlignment()->setHorizontal('center');
			$objActSheet->setCellValue('E' . $index, $totalPayAmt);
			$objActSheet->setCellValueExplicit('F' . $index, '','s');
			$objActSheet->setCellValue('G' . $index, $totalDivideAmt);
		}

		if(trim($title)){
			$outfile = trim($title).".xls";
		}
		else{
			$outfile = date('YmdHis').".xls";
		}
        ob_end_clean();
        header("Content-Type: application/force-download");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="'.urlencode($outfile).'"');
        header("Content-Transfer-Encoding: binary");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $objWriter->save('php://output');
		exit;
	}

    // 空方法
    public function _empty()
    {
        return $this->redirect('/index/index');
    }
}
