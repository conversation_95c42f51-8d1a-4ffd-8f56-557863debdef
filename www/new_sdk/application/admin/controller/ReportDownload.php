<?php

namespace app\admin\controller;

use app\common\library\AliyunOSS;
use Obs\ObsClient;
use think\Db;

class ReportDownload extends Admin
{

    // OBS配置
    protected $config = [
        'key' => '',
        'secret' => '',
        'endpoint' => 'https://obs.cn-east-3.myhuaweicloud.com',
        'ssl_verify' => false,
    ];
    protected $obsClient;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->obsClient = new ObsClient($this->config);
    }


    public function index()
    {
        $list = $this->getBucketObject("njqfwlkj", "excel");
        $this->assign("list", $list);
        return $this->fetch();
    }


    /**
     * 获取bucket 中对象
     */
    public function getBucketObject($bucketName, $object)
    {
        try {
            // ## OSS
            $prefix = 'excel_';
            if (APP_STATUS != "stable"){
                $prefix.='dev';
            }else{
                $prefix.='stable';
            }
            $res = (new AliyunOSS())->listObjects($prefix);
            $result = [];
            if ($res != ""){
                $data = array_reverse($res['data']);
                foreach ($data as $k => $v){
                    $result[$k] = [
                        'DownLoadUrl' => APK_DOWN_DOMAIN."/".$v['key'],
                        'LastModified' => date("Y-m-d H:i:s", strtotime($v['LastModified']))
                        // 'LastModified' => $v['LastModified']
                    ];
                }
            }
            return $result;

            // ## OBS
            // $resp = $this->obsClient->listObjects([
            //     'Bucket' => $bucketName,
            //     'Key' => $object,
            //     'Prefix' => $object . "/",
            //     'MaxKeys' => 10000
            // ]);
            // if (!empty($resp) && $resp['HttpStatusCode'] == 200) {
            //     $result = $resp['Contents'];
            //     $lastModified = array_column($result, "LastModified");
            //     array_multisort($lastModified, SORT_DESC, $result);
            //     foreach ($result as $key => $val) {
            //         $result[$key]['LastModified'] = ;
            //         $result[$key]['DownLoadUrl'] = APK_DOWN_DOMAIN."/" . $val['Key'];
            //         unset($result[$key]['StorageClass'], $result[$key]['Owner']);
            //     }
            //     return  array_slice($result,0,20);
            // } else {
            //     return [];
            // }
        } catch (\Obs\Common\ObsException $obsException) {
            printf("ExceptionCode:%s\n", $obsException->getExceptionCode());
            printf("ExceptionMessage:%s\n", $obsException->getExceptionMessage());
        }
    }
}
