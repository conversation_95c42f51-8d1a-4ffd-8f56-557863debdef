<?php

/**
 * 运营素材管理
 *
 */

namespace app\admin\controller;

use app\common\library\MakeReportGo;
use think\Db;
use think\Exception;
use app\common\library\MakeReport;

class Server extends Admin
{
    public function __construct()
    {
        parent::__construct();

        $this->serverModel = Db::name('cy_serverinfo');
    }

    public function serverList()
    {
        $whereArr = $this->_beforeGetParams();

        $where  = $whereArr['where'];
        $mapStr = $whereArr['mapStr'];

        //查询参数
        $param = input('get.');

        $list = $this->serverModel
            ->alias('s')
            ->join('cy_game g', 's.gameid=g.id', 'left')
            ->field('s.*,g.name game_name')
            ->where('s.type = 0 and s.isdelete = 0')
            ->where($where)
            ->whereOr($mapStr)
            ->order('s.id desc')
            ->paginate(10,false, array('query' => $param))
            ->each(function($newlist, $key){
                if ($newlist['sercertype'] == 1){
                    if (date('Ymd') == date('Ymd' , $newlist['sertime'])) {
                        $newlist['color']   = '#ff6600';
                        $newlist['sertime'] = '今天' . date("H:i" , $newlist['sertime']);
                    }else {
                        $newlist['sertime'] = date("Y-m-d H:i" , $newlist['sertime']);
                    }
                }elseif ($newlist['sercertype'] == 2) {
                    $newlist['sertime'] = '长期有效';
                }

                return $newlist;
            });

        $tmpGameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();
      	$game_list = array();
		foreach ($tmpGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        $this->assign('list', $list);
        $this->assign('page', $list->render());

        return $this->fetch('server_list');
    }

    /**
     * 新增开服
     * @return mixed|string
     */
    public function serverCreate()
    {
        if (request()->isPost()) {
            $gameIds = $_POST['gameid'];
            $sernames = $_POST['sername'];
            $sertimes = $_POST['sertime'];
            $sercertype = $_POST['sercertype'];


            for ($i=0; $i< count($gameIds); $i++) {
                $data = [
                    'gameid'     => $gameIds[$i] ,
                    'sername'    => $sernames[$i] ,
                    'sertime'    => $sertimes[$i] ,
                    'sercertype' => $sercertype[$i]
                ];
                if ($sercertype[$i] == 2){
                    $data['sertime'] = null;
                    $result = $this->validate($data,
                                              [
                                                  ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                                                  ['sername', 'require|max:50', '新服名称不能为空|新服名称最大不能超过50个字符'],
                                              ]);
                }else{

                    $result = $this->validate($data,
                                              [
                                                  ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                                                  ['sername', 'require|max:50', '新服名称不能为空|新服名称最大不能超过50个字符'],
                                                  ['sertime', 'require|date|after:'.date('Y-m-d H:i:s'), '开服时间不能为空|开服时间格式不正确|开服时间需大于当前时间'],
                                              ]);
                    $data['sertime'] = strtotime($data['sertime']);
                }

                if (true !== $result) {
                    $this->error($result);
                }


                $data['type']       = 0;
                $data['isdelete']   = 0;
                $data['serstatus']  = 1;

                $this->serverModel->insertGetId($data);
            }

            $this->success('新增成功', url('serverList'));

        }

        $tmpGameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();
      	$game_list = array();
		foreach ($tmpGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        return $this->fetch('server_new');
    }


    /**
     * 编辑开服
     * @return mixed|string
     */
    public function serviceEdit()
    {
        $id = input('id', 0, 'intval');

        if(empty($id))
            $this->error('记录ID不能为空');

        $serverInfo = $this->serverModel->where(['id'=>$id, 'isdelete'=>0])->find();

        if (request()->isPost()) {

            $data = [
             //   'gameid' 	=> input('post.gameid'),
                'sername'   => input('post.sername','','trim'),
                'sertime'   => input('post.sertime'),
                'sercertype'   => input('post.sercertype'),
            ];

            if ($data['sercertype'] == 1){
                $result = $this->validate($data,
                                          [
                                    //          ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                                              ['sername', 'require|max:50', '新服名称不能为空|新服名称最大不能超过50个字符'],
                                              ['sertime', 'require|date', '开服时间不能为空|开服时间格式不正确'],
                                          ]);
                $data['sertime'] = strtotime($data['sertime']);
            }else{
                $data['sertime'] = null;
                $result = $this->validate($data,
                                          [
                                    //          ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                                              ['sername', 'require|max:50', '新服名称不能为空|新服名称最大不能超过50个字符'],
                                          ]);
            }


            if (true !== $result) {
                $this->error($result);
            }


            if($this->serverModel->where(['id'=>$id])->update($data))
                $this->success('修改成功','serverList');
            else
                $this->error('修改失败');
        }

        $tmpGameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();
      	$game_list = array();
		foreach ($tmpGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        $this->assign('serverInfo', $serverInfo);

        return $this->fetch('service_edit');
    }



    /**
     * 开服列表删除
     */
    public function serverDel()
    {
        $id = input('id', 0, 'intval');
        if(empty($id)) $this->error('记录ID不能为空');

        $ret = $this->serverModel->where([
            'id' => $id,
            'type' => 0
        ])->setField('isdelete', 1);

        if (!$ret) {
            $this->error('删除错误！');
        }

        $this->success('删除成功');
    }


    /**
     * 开服表批量删除
     */
    public function serverBatchDel()
    {
        $ids = input('ids', '');
        if (empty($ids)) {
            $this->error('id不能为空');
        }

        $idsArr = explode(',', $ids);

        Db::startTrans();

        try {
            foreach ($idsArr as $id) {
                $ret = $this->serverModel->where([
                    'id' => $id,
                    'type' => 0
                ])->setField('isdelete', 1);

                if (!$ret) {
                    throw new Exception('删除错误! 错误id: ' . $id);
                }
            }

            Db::commit();
            $this->success('删除成功!', url('serverList') );
        } catch (Exception $e) {
            Db::rollback();
            $this->error('删除出错! ' . $e->getMessage());
        }
    }


    /**
     * 获取开服列表请求参数
     *
     * @return array
     */
    private function _beforeGetParams()
    {
        $where  = [];
        $mapStr = '';
        $runable = true;

        $start      = input('start');
        $end        = input('end');
        $gameId     = input('game_id');

        if ($start) {
            $where['s.sertime'] = ['>=', strtotime($start)];
        }

        if ($end) {
            $where['s.sertime'] = ['<', strtotime($end)];
        }

        if ($start && $end) {
            $where['s.sertime'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end)],
            ];
            if ($start > $end){
                $runable = false;
            }
        }

        /*if ($gameId) {
            $where['s.gameid'] = $gameId;
        }*/

        if ($gameId) {
            $where['s.gameid'] = $gameId;
            $mapStr = "(s.gameid = $gameId and s.type = 0 and s.isdelete=0 and s.sercertype=2)";
        }else{
            $mapStr = "(s.type = 0 and s.isdelete=0 and s.sercertype=2)";
        }

        $where['sercertype'] = 1;

        if (!$runable){
            $mapStr = '';
        }

        return ['where'=>$where,'mapStr'=>$mapStr];
    }

    /**
     * 开服列表 报表-下载
     */
    public function download()
    {
        if (request()->isAjax()) {



            $where = [];
            $mapStr = '';
            $runable = true;

            $start      = input('start');
            $end        = input('end');
            $gameId     = input('game_id');

            if ($start) {
                $where['sertime'] = ['>=', strtotime($start)];
            }

            if ($end) {
                $where['sertime'] = ['<', strtotime($end)];
            }

            if ($start && $end) {
                $where['sertime'] = [
                    ['>=', strtotime($start)],
                    ['<=', strtotime($end)],
                ];
                if ($start > $end){
                    $runable = false;
                }
            }

            if ($gameId) {
                $where['gameid'] = $gameId;
                $mapStr = "(gameid = $gameId and type = 0 and isdelete=0 and sercertype=2)";
            }else{
                $mapStr = "(type = 0 and isdelete=0 and sercertype=2)";
            }

            $where['sercertype'] = 1;

            if (!$runable){
                $mapStr = '';
            }

            $sql = $this->serverModel
                ->field('id,gameid,sername,sertime')
                ->where('type = 0 and isdelete = 0')
                ->where($where)
                ->whereOr($mapStr)
                ->order('id desc')
                ->fetchSql(true)
                ->select();


            if ((new MakeReportGo())->addTask('serverList',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
        }
        else{
            $this->error('非法请求');
        }
    }

    /**
     * 开测列表
     */
    public function testList()
    {
        $where = [];

        if(input('request.serstatus')!='')
        {
            $where['s.serstatus'] = input('request.serstatus');
        }

        if(input('request.gameid')!='')
        {
            $where['s.gameid'] = input('request.gameid/d');
        }

        //查询参数
        $param = input('get.');

        $list = $this->serverModel->table('cy_serverinfo s,cy_game g')
            ->field('s.*,g.name game_name')
            ->where('s.gameid=g.id and s.type = 1 and s.isdelete = 0')
            ->where($where)
            ->order('s.id desc')->paginate(10,false, array('query' => $param));

        $tmpGameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();
      	$game_list = array();
		foreach ($tmpGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        $this->assign('list', $list);
        $this->assign('page', $list->render());

        return $this->fetch('test_list');
    }


    /**
     * 新增开测
     * @return mixed|string
     */
    public function testCreate()
    {
        if (request()->isPost()) {

            $gameIds = $_POST['gameid'];
            $serstatus = $_POST['serstatus'];
            $sertimes = $_POST['sertime'];

			$insertCnt = 0;
			$errorMsg = '';
            for ($i=0; $i< count($gameIds); $i++) {
                $data = [
                    'gameid' 	=> $gameIds[$i],
                    'serstatus'   => $serstatus[$i],
                    'sertime'   => $sertimes[$i]
                ];

                $result = $this->validate($data,
                    [
                        ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                        ['serstatus', 'require|integer', '开测状态不能为空|开测状态必须为整型'],
                        ['sertime', 'require|date|after:'.date('Y-m-d H:i:s'), '开测时间不能为空|开测时间格式不正确|开测时间需大于当前时间'],
                    ]);

                if (true !== $result) {
                    $this->error($result);
                }

                $data['sertime']    = strtotime($data['sertime']);
                $data['type']       = 1;
                $data['isdelete']   = 0;

				$condis = array();
				$condis['gameid'] = $data['gameid'];
				$condis['serstatus'] = $data['serstatus'];
				$condis['sertime'] = $data['sertime'];
				$existServerInfo = $this->serverModel->where($condis)->find();
				if(empty($existServerInfo)){
					$insertCnt++;
					$this->serverModel->insertGetId($data);
				}
				else{
					$errorMsg .= "游戏《".get_game_name($data['gameid'])."》,";
				}
            }
			if($insertCnt<count($gameIds)){
				$this->success($errorMsg." 已经存在该开测信息", url('testList'));
			}
			else{
				$this->success('新增成功', url('testList'));
			}
        }

        $tmpGameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();
      	$game_list = array();
		foreach ($tmpGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        $this->assign('game_list', $game_list);

        return $this->fetch('test_new');
    }


    /**
     * 编辑开测
     * @return mixed|string
     */
    public function testEdit()
    {
        $id = input('id', 0, 'intval');

        if(empty($id))
            $this->error('记录ID不能为空');

        $serverInfo = $this->serverModel->where(['id'=>$id,'isdelete'=>0])->find();

        if (request()->isPost()) {

            $data = [
              //  'gameid' 	=> input('post.gameid'),
                'serstatus' => input('post.serstatus'),
                'sertime'   => input('post.sertime')
            ];

            $result = $this->validate($data,
                [
               //     ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                    ['serstatus', 'require|integer', '开测状态不能为空|开测状态必须为整型'],
                    ['sertime', 'require|date|after:'.date('Y-m-d H:i'), '开测时间不能为空|开测时间格式不正确|开测时间需大于当前时间'],
                ]);

            if (true !== $result) {
                $this->error($result);
            }

            $data['sertime'] = strtotime($data['sertime']);

			$condis = array();
			$condis['id'] = array('neq',$id);
			$condis['gameid'] = intval(input('post.gameid'));
			$condis['serstatus'] = $data['serstatus'];
			$condis['sertime'] = $data['sertime'];
			$existServerInfo = $this->serverModel->where($condis)->find();
		//	var_dump($existServerInfo);
			if(!empty($existServerInfo)){
				$this->error('已经存在该开测信息！');
			}

            if($this->serverModel->where(['id'=>$id])->update($data))
                $this->success('修改成功','testList');
            else
                $this->error('修改失败');
        }

        $game_list = Db::name('cy_game')->field('id,name')->order('name asc')->select();

        $this->assign('game_list', $game_list);
        $this->assign('serverInfo', $serverInfo);

        return $this->fetch('test_edit');
    }


    /**
     * 测试列表删除
     */
    public function testDel()
    {
        $id = input('id', 0, 'intval');
        if(empty($id)) $this->error('记录ID不能为空');

        $ret = $this->serverModel->where([
            'id' => $id,
            'type' => 1
        ])->setField('isdelete', 1);

        if (!$ret) {
            $this->error('删除错误！');
        }

        $this->success('删除成功');
    }
}
