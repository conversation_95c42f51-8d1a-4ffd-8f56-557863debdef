<?php


/**
 * 区服管理控制器
 */

namespace app\admin\controller;


use think\Db;

class Region extends Admin
{

    public function index()
    {
        $where = [];
        if (input('request.game_id') != '') {
            $where['gs.game_id'] = input('request.game_id', '', 'trim');
        }

/*        if (input('request.game_name') != '') {
            $where['gs.game_id'] = input('request.game_name', '', 'trim');
        }*/

        //游戏名称
        if (input('request.game_name') != '') {
            $where['g.name'] = ['like', '%' . input('request.game_name') . '%'];
        }

        if (input('request.serverid') != '') {
            $where['gs.serverid'] = input('request.serverid', '', 'trim');
        }
        if (input('request.servername') != '') {
            $where['gs.servername'] = input('request.servername', '', 'trim');
        }
        if (input('request.status') != '') {
            $where['gs.status'] = input('request.status', '', 'trim');
        }

//        dump($where);
        //查询参数
        $param = input('get.');


        $list = Db::name('nw_game_server gs')
            ->join('cy_game g', 'gs.game_id = g.id', 'left')
            ->field('gs.id,gs.servername,gs.serverid,gs.status,gs.create_time,g.name')
            ->where($where)
            ->paginate(10, false, array('query' => $param));

        $this->assign('list', $list);
        $gameList = $this->cyModel('game')->field('id,name')->select();
        $this->assign('gameList', $gameList);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    /**
     *是否显示
     */
    public function show()
    {
        $id = $this->request->param('id', '', 'intval');
        $status = $this->request->param('is_show', '', 'intval');
        $data['status'] = $status;
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $result = $this->validate($data, [
            ['is_show', 'in:0,1', '状态参数错误'],
        ]);
        if (true !== $result) {
            $this->error($result);
        }
        $gameServerModel = model("GameServer");
        if (!$gameServerModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($gameServerModel->update($data, ['id' => $id])) {
            $msg = $status ? '显示' : '隐藏';
            $this->insertLog($this->current_node, $msg . '游戏：' . get_game_name($id), 11);
            $this->success('修改成功');
        }
        $error = $gameServerModel->getError();
        $this->error($error ?: '修改失败');
    }
}