<?php

namespace app\admin\controller;


use app\common\library\OneFileUpload;
use app\common\logic\Member as MemberService;
use think\Loader;
use think\Db;

class WelfareResources extends Admin
{
    protected $gameList;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }

    /**
     *礼包资源列表
     */
    public function index()
    {
        $where = [];
        $order = 'create_time desc';
        //查询参数
        $name = input('name', '', 'trim');
        $game_id = input('game_id', '', 'trim');
        if (isset($name) && !empty($name)) {
            $where['a.name'] = ['like', "%$name%"];
        }

        $tmpGameList = model('Common/Game')->getAllByCondition('id,name',['is_welfare'=>2]);
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }


        cache('welfarweResources:index',input(),3600);

        $resourcesModel = model('WelfareResources');
        $list = $resourcesModel->alias('a')
            ->where($where)
            ->where(function ($query) use ($game_id) {
                if (isset($game_id) && !empty($game_id)) {
                    $query->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $game_id));
                }
            })
            ->order($order)
            ->paginate(10, false, ['query' => input('get.')])->each(function ($item, $key) use($gameList) {
                if ($item['type_id'] == 2) {
                    $item['surplus'] = model('WelfareResourcesData')->where(['welfare_resources_id' => $item['id']])->where('status',1)->count();
                } else {
                    $item['total'] = $item['surplus'] = '--';
                }
                if($item['game_id']){
                    $name = [];
                    foreach (explode(',',$item['game_id']) as $k=>$v){
                        $name[] = $gameList[$v]['name'];
                    }
                    $item['game_name'] = implode(',',$name);
                }else{
                    $item['game_name'] = '';
                }

                return $item;
            });
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', ['game_kind' => 1, 'cooperation_status' => ['neq', 0], 'is_welfare' => 2], 'id desc', 'self');

        $selfGameList = [['id' => 0, 'name' => '']];
        foreach ($tmpSelfGameList as $game) {
            $selfGameList[$game['id']] = $game;
        }
        $this->assign('game_list', $selfGameList);
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }


    /**
     *增
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if(!$data['select']){
                $this->error('请选择游戏');
            }
            $data['game_id'] = $data['select'];
            unset($data['select']);
            $data['update_time'] = $data['create_time'] = time();
            $m = Db::name('cy_welfare_resources')->insertGetId($data);

            if (!empty($m)) {
                $this->success('添加成功', url('index',cache('welfarweResources:index')));
            }
            $this->error('添加失败');
        }
        $gameList = model('Common/Game')->getAllByCondition('id,name',['is_welfare'=>2]);
        $this->assign('game_list', $gameList);
        return $this->fetch();
    }


    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $resourcesModel = Db::name('cy_welfare_resources');
        if (!$resourcesModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($resourcesModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $resourcesModel->getError();
        $this->error($error ?: '删除失败');
    }


    /**
     *编辑SDK密钥
     */
    public function edit()
    {
        $id = $this->request->param('id', '', 'intval');
        $appModel = Db::name('cy_welfare_resources');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if(!$data['select']){
                $this->error('请选择游戏');
            }
            $data['game_id'] = $data['select'];
            unset($data['select']);
            $data['update_time'] = time();
            if (Db::name('cy_welfare_resources')->where(['id' => $id])->update($data)) {
                $this->success('修改成功', url('index',cache('welfarweResources:index')));
            } else {
                $this->error($appModel->getError() ?: '修改失败');
            }
        }
        $gameList = model('Common/Game')->getAllByCondition('id,name',['is_welfare'=>2]);
        $this->assign('game_list', $gameList);
        $this->assign('data', $data);
        return $this->fetch();
    }

    /**
     * 上传txt文件
     */
    public function uplode()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $resourcesModel = Db::name('cy_welfare_resources');
        if (!$data = $resourcesModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($data['type_id'] != 2) {
            $this->error('参数错误，不存在该数据');
        }
        $resources_data = Db::name('cy_welfare_resources_data')->where(['welfare_resources_id' => $data['id'], 'status' => 1])->column('code', 'id');
        $this->assign('code', implode("\r\n", $resources_data));


        if ($this->request->isPost()) {
            $data = $this->request->post();

            $code = $this->request->param('code');

            empty($code) ? $this->error('礼包码不能为空') : true;


            $codeArray = explode("\r\n", $code);
            $codeArray = array_map('filterAndTrimInput', $codeArray);
            $codeArray = array_unique(array_filter($codeArray));
            Db::startTrans();
            try {

                model('WelfareResourcesData')->where(['welfare_resources_id' => $id, 'status' => 1])->delete();
                $codes = [];
                foreach ($codeArray as $v) {
                    $codes[] = ['code' => $v, 'welfare_resources_id' => $id];
                }

                $res = model('WelfareResourcesData')->saveAll($codes);
                $num = model('WelfareResourcesData')->where(['welfare_resources_id' => $id])->count();
                $update['update_time'] = time();
                $update['total'] = $num;

                if (Db::name('cy_welfare_resources')->where(['id' => $id])->update($update)) {
                    // 提交事务
                    Db::commit();

                } else {
                    // 回滚事务
                    Db::rollback();
                    $this->error('修改失败');
                }


            } catch (\Exception $e) {

                // 回滚事务
                Db::rollback();
                $this->error('修改失败' . $e->getMessage());
            }
            $this->success('修改成功', url('index'));
        }


        $this->assign('data', $data);
        return $this->fetch();
    }


}
