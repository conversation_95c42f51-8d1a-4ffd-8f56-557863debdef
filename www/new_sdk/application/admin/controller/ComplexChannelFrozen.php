<?php
/**
 * 聚合渠道冻结管理
 *
 * @category Controller
 * <AUTHOR>
 * @since    2018/9/11
 */

namespace app\admin\controller;

use think\Db;
use app\common\model\Game;
use think\Exception;

class ComplexChannelFrozen extends Admin {

    public function _initialize() {
        parent::_initialize();

        $this->complexChannelFrozenModel = $this->nwModel('complex_channel_frozen');

        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;
    }

    public function index() {
        $channelId = input('channel_id');
        $gameId = input('game_id');

        $where = ['channel_type' => 1];
        if ($channelId) {
            $where['channel_id'] = $channelId;
        }

        if ($gameId) {
            $where['game_id'] = $gameId;
        }

        $channleFrozen = $this->complexChannelFrozenModel
            ->where($where)
            ->order("id DESC")
            ->paginate(10, false, ['query' => $where]);

        $gameList = $this->selfGameList;
        $channelList = model('Common/ComplexChannelModel')->getAllByCondition('id,name',['flag'=>4]);

        $this->assign('channel_list', $channelList);
        $this->assign('game_list', $gameList);
        $this->assign('channel_id', $channelId);
        $this->assign('game_id', $gameId);
        $this->assign('total', $channleFrozen->total());
        $this->assign("page", $channleFrozen->render());
        $this->assign("channle_frozen", $channleFrozen);
        return $this->fetch();
    }

    /**
     * 添加冻结渠道
     *
     * @return mixed
     */
    public function add() {
        $gameList = $this->selfGameList;
        $channelList = model('Common/ComplexChannelModel')->getAllByCondition('id,name',['flag'=>4]);

        $this->assign('channel_list', $channelList);
        $this->assign('game_list', $gameList);

        return $this->fetch();
    }

    public function addPost() {
        $result = $this->validate($this->request->param(), 'ChannelFrozen');

        if ($result !== true) {
            $this->error($result);
        } else {
            $gameId     = input('game_id', 0, 'intval');
            $channelId  = input('channel_id', 0, 'intval');

            $result = $this->complexChannelFrozenModel
                ->field('id')
                ->where(['game_id' => $gameId, 'channel_id' => $_POST['channel_id']])
                ->find();
            if (!empty($result)) {
                $this->error("数据已存在！id: " . $result['id']);
            }

            $_POST['channel_type']  = 1;
            $_POST['create_time']   = time();

            $result = $this->complexChannelFrozenModel->insertGetId($_POST);

            if ($result !== false) {
                $gameName    = model('Game')->where(['id' => $gameId])->value('name');
                $channelName = model('ComplexChannelModel')->where(['id' => $channelId])->value('name');

                $loginFlag      = intval($_POST['member_login']) ? '已禁止' : '未禁止';
                $registerFlag   = intval($_POST['register']) ? '已禁止' : '未禁止';
                $consumeFlag    = intval($_POST['consume']) ? '已禁止' : '未禁止';

                //写入日志
                $this->insertLog($this->current_node, "冻结聚合渠道：{$channelName}，游戏：{$gameName}，登录：{$loginFlag}，新增：{$registerFlag}，消费：{$consumeFlag}", 53);

                $this->success("添加成功！", url("complex_channel_frozen/index"));
            } else {
                $this->error("添加失败！");
            }
        }
    }

    /**
     * 操作禁止
     */
    public function ban()
    {
        $id = $this->request->param('id', 0, 'intval');
        $action = input('action');

        if (!empty($id) && !empty($action)) {
            $result = $this->complexChannelFrozenModel->where(["id" => $id])->setField($action, '1');

            if ($result !== false) {
                //写入日志
                $data = $this->complexChannelFrozenModel->where(["id"=>$id])->find();
                $gameName    = model('Game')->where(['id' => $data['game_id']])->value('name');
                $channelName = model('ComplexChannelModel')->where(['id' => $data['channel_id']])->value('name');
                $loginFlag      = intval($data['member_login']) ? '已禁止' : '未禁止';
                $registerFlag   = intval($data['register']) ? '已禁止' : '未禁止';
                $consumeFlag    = intval($data['consume']) ? '已禁止' : '未禁止';
                $this->insertLog($this->current_node, "冻结聚合渠道：{$channelName}，游戏：{$gameName}，登录：{$loginFlag}，新增：{$registerFlag}，消费：{$consumeFlag}", 56);

                $this->success("禁止成功！");
            } else {
                $this->error("禁止失败！");
            }
        } else {
            $this->error('数据传入失败！');
        }
    }


    /**
     * 操作开启
     */
    public function cancelBan()
    {
        $id = $this->request->param('id', 0, 'intval');
        $action = input('action');

        if (!empty($id) && !empty($action)) {
            if ($action !== 'all') {
                $result = $this->complexChannelFrozenModel->where(["id" => $id])->setField($action, '0');
            } else {
                $result = $this->complexChannelFrozenModel->where(["id" => $id])->update([
                    'consume' => 0,
                    'member_login' => 0,
                    'register' => 0,
                ]);
            }

            if ($result !== false) {
                //写入日志
                $data = $this->complexChannelFrozenModel->where(["id"=>$id])->find();
                $gameName    = model('Game')->where(['id' => $data['game_id']])->value('name');
                $channelName = model('ComplexChannelModel')->where(['id' => $data['channel_id']])->value('name');
                $loginFlag      = intval($data['member_login']) ? '已禁止' : '未禁止';
                $registerFlag   = intval($data['register']) ? '已禁止' : '未禁止';
                $consumeFlag    = intval($data['consume']) ? '已禁止' : '未禁止';
                $this->insertLog($this->current_node, "冻结聚合渠道：{$channelName}，游戏：{$gameName}，登录：{$loginFlag}，新增：{$registerFlag}，消费：{$consumeFlag}", 56);

                $this->success("开启成功！");
            } else {
                $this->error("开启失败！");
            }
        } else {
            $this->error('数据传入失败！');
        }
    }
}