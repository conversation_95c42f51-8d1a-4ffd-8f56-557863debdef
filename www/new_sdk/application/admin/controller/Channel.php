<?php
/**
 * Created by PhpStorm.
 * User: edison
 * Date: 2018/3/26
 * Time: 上午11:50
 */

namespace app\admin\controller;

use app\common\library\MakeReport;
use app\common\library\MakeReportGo;
use think\Config;
use think\Db;
use think\Exception;
use app\common\logic\SubPackage as SubChannel;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use app\common\logic\Websocket;
use app\common\library\FileUpload;

class Channel extends Admin
{
    public function _initialize()
    {
        parent::_initialize();

        $this->adminModel = Db::table('nw_channel_admin');
        $this->channelModel = Db::table('nw_channel');
    }

    public function index()
    {
        $condition = $this->getListCondition();

        // $condition['channel.level'] = ['neq',0];
        $gh_status   = input('request.gh_status'); // 是否聚合用户：0=否、1=是
        if ($gh_status<>'') {
            $condition['channel.gh_status'] = $gh_status;
        }

        // $status   = input('request.status');
        $list = $this->adminModel->alias('a')
            ->join('nw_channel channel', 'a.channel_id = channel.id')
            ->join('nw_channel top', 'channel.id_path like CONCAT(",",top.id,",%") and top.level = 0','left')
            ->join('nw_channel_info info', 'channel.id = info.channel_id','left')
            ->join('nw_channel_info_apply apply', 'channel.id = apply.channel_id and apply.status = 0','left')
            ->where($condition)
            ->field('a.id as adminid,a.channel_id as channelid,channel.level as channel_level,channel.amount as ptb_amt,channel.js_amount,a.mobile,a.username as username,a.login_check as login_check,a.beizhu as beizhu,a.create_time as created_time,a.status as status,channel.show_full_account,info.*,channel.level,apply.apply_info,apply.status as apply_status,top.name as top_name,apply.remark as audit_remark,info.apply_status as info_status,apply.id as applyId,channel.deposit_amt, channel.gh_status, channel.yql_id')
            ->order("a.id DESC")
            ->paginate(10,false, ['query' => input('get.')]);
       // if ($list->total()>0) {
       //      foreach ($list as $key => $item) {
       //          $channel = get_top_second_channel_name($item['channelid']);
       //          $list[ $key ]['channel_name']   = $channel['channel_name'];
       //          $list[ $key ]['second_channel'] = $channel['second_name'];
       //          $list[ $key ]['top_channel']    = $channel['top_name'];
       //      }
       //  }


        // $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        // $channelWhere = [];
        // if ($business != -1) {
        //     if ($business) {
        //         $channelWhere['id'] = ['in', $business];
        //     } else {
        //         $channelWhere['id'] = -1;
        //     }
        // }
        // $uniom = $this->channelModel->where(['level'=>0])->where($channelWhere)->field('id,name')->select();

        // 获取分页显示
        $page = $list->render();
        $this->assign("page", $page);
        $this->assign("total", $list->total());     //总记录数
        $this->assign("channles", $list);
        // $this->assign("uniom", $uniom);
        return $this->fetch();
    }
    //  联盟管理页面
    public function leaguesmanage()
    {
        $channles = $this->adminModel->alias('a')
            ->join('nw_channel channel', 'a.channel_id = channel.id','left')
            ->join('nw_channel_info info', 'channel.id = info.channel_id','left')
            ->join('nw_channel_info_apply apply', 'channel.id = apply.channel_id and apply.status = 0','left')
            ->where($this->getListCondition('leaguesmanage'))
            ->where(['channel.level'=>0])
            ->field('a.id as adminid,a.channel_id as channelid,channel.level as channel_level,channel.amount as ptb_amt,channel.js_amount,a.mobile,a.username as username,a.login_check as login_check,a.beizhu as beizhu,a.create_time as created_time,a.status as status,channel.show_full_account,info.*,apply.status as apply_status,apply.apply_info,info.apply_status as info_status,apply.id as applyId')
            ->order("a.id DESC")
            ->paginate(10,false, ['query' => input('get.')]);
        //  var_dump($channles);
        // 获取分页显示
        $page = $channles->render();
        $this->assign("page", $page);
        $this->assign("total", $channles->total());     //总记录数
        $this->assign("channles", $channles);
        return $this->fetch();
    }
    // 报表下载
    public function download()
    {
        $type = request()->get('type');
        $type == 'union'?$action = 'unionList':$action = 'channelList';
        $where = $this->getListCondition('leaguesmanage');
        if (request()->isAjax()) {
            $fields = 'a.channel_id as channel_id,a.username as username,a.beizhu as beizhu,FROM_UNIXTIME(a.create_time) as create_time,info.real_name as real_name,info.linkman_email as linkman_email,info.person_id,info.company_name,info.us_code,info.leson_name,info.leson_person_id,info.address,info.apply_status as info_status,info.id as info_id,apply.status as apply_status,';

            //是否有渠道联系方式明文查看权限
            if($this->checkHasActionPriv(session('ADMIN_ID'),'Admin','Channel','viewlinkinfo')){
                $fields .= 'a.mobile as mobile,info.zfb_account,info.bank_ban_mobile,info.bank_number,info.bank_name';
            }
            else{
                $fields .= 'CASE WHEN a.mobile<>"" THEN concat(substr(trim(a.mobile),1,3),"***",substr(trim(a.mobile),7)) ELSE "" END as mobile,
                CASE WHEN info.zfb_account<>"" THEN "***" ELSE "" END as zfb_account,
                CASE WHEN info.bank_ban_mobile<>"" THEN concat(substr(trim(info.bank_ban_mobile),1,3),"***",substr(trim(info.bank_ban_mobile),7)) ELSE "" END as bank_ban_mobile,
                CASE WHEN info.bank_number<>"" THEN "***" ELSE "" END as bank_number,
                CASE WHEN info.bank_name<>"" THEN "***" ELSE "" END as bank_name';
            }

            if($action == 'channelList'){
                $fields .= ',CASE WHEN channel.level =1 THEN "会长" WHEN channel.level = 2 THEN "子会长" ELSE "推广员" END as level_name,CASE WHEN info.id IS NOT NULL AND info.apply_status =1 THEN if( apply_status = 0 ,"待审核","已身份认证") ELSE if( apply_status = 0 ,"待审核","未身份认证") END as apply_name,channel.deposit_amt';
                $where['channel.level'] = ['neq', 0];
            }else{
                $where['channel.level'] = ['=', 0];
            }


            $sql = $this->adminModel->alias('a')
                ->join('nw_channel channel', 'a.channel_id = channel.id','left')
                ->join('nw_channel top', 'channel.id_path like CONCAT(",",top.id,",%") and top.level = 0','left')
                ->join('nw_channel_info info', 'channel.id = info.channel_id','left')
                ->join('nw_channel_info_apply apply', 'channel.id = apply.channel_id and apply.status = 0','left')
                ->where($where)
                ->field($fields)
                ->order("a.id DESC")
                ->fetchSql(true)
                ->select();
//            if ((new MakeReport())->addTask($action, $sql, session_id())){
            if ((new MakeReportGo())->addTask($action, $sql, session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }

        } else {
            $this->error('非法请求');
        }
    }

    // 获取推广四级(商务/会长/子会长/推广员)
    public function getSelectList(){
        $type = input('type', 'sw');
        $pid = input('pid', '');
        $newList = [];
        if($type == 'sw'){
            $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
            $channelWhere = [];
            if ($business != -1) {
                if ($business) {
                    $channelWhere['id'] = ['in', $business];
                } else {
                    $channelWhere['id'] = -1;
                }
            }
            $newList = $this->channelModel->where(['level'=>"0"])->where($channelWhere)->field("CAST(id as char) as id,name")->select();
        }else if($type == 'hz'){
            $where = [
                'level' => 1,
            ];
            if(!empty($pid)){
                $where['parent_id'] = $pid;
            }
            $newList = model('Common/Channel')->field('CAST(id as char) as id,name,parent_id')->where($where)->select();
        }else if ($type == 'zhz') {
            $where = [
                'level' => 2,
            ];
            if (!empty($pid)) {
                $where['parent_id'] = $pid;
            }
            $newList = model('Common/Channel')->field('CAST(id as char) as id,name,parent_id')->where($where)->select();
        } else if ($type == 'tgy') {
            $where = [
                'level' => 3,
            ];
            if (!empty($pid)) {
                $where['parent_id'] = $pid;
            }
            $newList = model('Common/Channel')->field('CAST(id as char) as id,name,parent_id')->where($where)->select();
        }

        $this->jsonResult($newList);
    }

    /**
     * 渠道管理列表的查询条件
     */
    private function getListCondition($type = 'index')
    {
        $username   = input('username', '', 'trim');
        //   $start_time = input('request.start_time',date('Y-m-d'));
        $start_time = input('request.start_time');
        $end_time   = input('request.end_time');
        $level   = input('level', '', 'trim');
        $unioid   = input('request.unioid');// 联盟
        $status   = input('request.status');//
        $channel_status   = input('request.channel_status');//
        // $where['a.type'] = 2;

        $end_time = $end_time ? $end_time : $start_time ; // 查询日期当天
        $end_time .= ' 23:59:59';
        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $where['a.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time)],
            ];
        }
        //开始时间不为空时
        elseif ($start_time != '') {
            $where['a.create_time'] = ['>=', strtotime($start_time)];
        }
        //结束时间不为空时
        elseif ($end_time != '') {
            $where['a.create_time'] = ['<=', strtotime($end_time)];
        }

        if ($username) {
            $where['a.username'] = ['like', "%$username%"];
        }

        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        $channelWhere = [];
        if($type=='index'){
            if ($unioid) {
                if ($business != -1) {
                    if (in_array($unioid,$business)) {
                        $where['top.id'] = ['in', $unioid];
                    } else {
                        $where['top.id'] = -1;
                    }
                }elseif($business == -2){
                    $where['top.id'] = -1;
                }
            }else{
                if ($business != -1) {
                    if ($business) {
                        $where['top.id'] = ['in', $business];
                    } else {
                        $where['top.id'] = -1;
                    }
                }elseif($business == -2){
                    $where['top.id'] = -1;
                }
            }
        }else{
            if ($unioid) {
                if ($business != -1) {
                    if (in_array($unioid,$business)) {
                        $where['channel.id'] = ['in', $unioid];
                    } else {
                        $where['channel.id'] = -1;
                    }
                }elseif($business == -2){
                    $where['channel.id'] = -1;
                }
            }else{
                if ($business != -1) {
                    if ($business) {
                        $where['channel.id'] = ['in', $business];
                    } else {
                        $where['channel.id'] = -1;
                    }
                }elseif($business == -2){
                    $where['channel.id'] = -1;
                }
            }
        }
        if ($level) {
            $where['channel.level'] = ['=', $level];
        }
        if ($status == '0') {
            $where['apply.status'] = $status;
        }elseif ($status == '1') {
            $where['info.apply_status'] = 1;
        }elseif($status == '2'){
            $where['info.id'] = ['EXP',Db::raw('IS NULL')];
        }

        if ($channel_status<>'') {
            $where['channel.status'] = $channel_status;
        }

        $business_type   = input('business_type', '');
        $president_type   = input('president_type', '');
        $president_type_son   = input('president_type_son', '');
        if ($business_type<>'') {
            $where['channel.id_path'] = ['like', ",{$business_type},%"];
        }
        if ($president_type<>'') {
            // $where['channel.id_path'] = ['like', ",{$business_type},{$president_type},%"];
            $where['channel.parent_id'] = $president_type;
        }
        if ($president_type_son<>'') {
            unset($where['channel.id_path']);
            $where['channel.parent_id'] = $president_type_son;
        }

        return $where;
    }

    /**
     * 添加渠道
     *
     * @return mixed
     */
    public function add()
    {
        $list = Db::name('nw_channel')->where(['level' => 0])->column('name','id');
        $this->assign('list', $list);
        return $this->fetch();
    }
    /**
     * 获取历史审核记录
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function checklog()
    {

        $channelid = request()->post('channelid');
        if (empty($channelid)) {
            $this->error("无效用户id");
        }
        $list = db::name('nw_channel_info_apply')->field('id,FROM_UNIXTIME(apply_time) as apply_time,apply_info,CASE status WHEN 0 THEN "待审核" WHEN 1 THEN "审核通过" ELSE "审核失败" END AS status,status as apply_status, CASE WHEN check_time <> 0 THEN FROM_UNIXTIME(check_time)  ELSE "--" END as check_time,check_admin_name,channel_id')->where(['channel_id'=>$channelid])->order('id desc')->select();
        $this->success('查询记录',null,$list);

    }
    /**
     * 渠道审核
     *
     * @return mixed
     */
    public function examine()
    {
        $channel_id = request()->get('channelid');
        $apply_status = request()->get('apply_status');
        $applyId = request()->get('cid');
        $log = request()->get('log');
        $status = 0;
        if ($apply_status == 1) {
            $data = Db::name('nw_channel_info')->field('real_name as name,person_id,zfb_account as zhifubao_number,bank_ban_mobile as mobile ,bank_number as bankNum,bank_province_city as provinceCity,bank_name as bankName,bank_open_name as openBank,bank_open_number as bankId,back_image_url,front_image_url')->where(['channel_id'=>$channel_id])->find();
            $status = 1;
        }else{
            $data = Db::name('nw_channel_info_apply')->where(['channel_id'=>$channel_id,'id'=>$applyId])->value('apply_info');
            $data = json_decode($data,1);
        }
        $this->assign('channel_id', $channel_id);
        $this->assign('list', $data);
        $this->assign('log', $log);
        $this->assign('applyId', $applyId);
        $this->assign('status', $status);
        return $this->fetch();
    }

    /**
     * 查看个人身份认证信息
     *
     * @return mixed
     */
    public function checkInfo()
    {
        $channel_id = request()->get('channelid');
        $data = Db::name('nw_channel_info')->field('real_name as name,person_id,zfb_account as zhifubao_number,bank_ban_mobile as mobile ,bank_number as bankNum,bank_province_city as provinceCity,bank_name as bankName,bank_open_name as openBank,bank_open_number as bankId,back_image_url,front_image_url')->where(['channel_id'=>$channel_id])->find();
        $this->assign('list', $data);
        return $this->fetch();
    }
    /**
     * 更改审核状态
     */
    public function doExamine()
    {
        $data = request()->post();
        $status = $data['status'];
        $channel_id = $data['channel_id'];
        $applyId = $data['applyId'];
        if (empty($status)) {
            $this->error("系统错误");
        }
        $data['check_time'] = NOW_TIMESTAMP;
        $data['check_admin_type'] = 1;
        $data['check_admin_name'] = session('USERNAME');
        unset($data['channel_id']);
        unset($data['applyId']);
        Db::startTrans();
        try {
            $resultChannel = Db::name('nw_channel_info_apply')->where(['channel_id'=>$channel_id,'id'=>$applyId])->update($data);//更新审核状态
            if (!$resultChannel) {
                $this->error("更新审核状态失败,请刷新后重试");
            }
            if ($status == 1 ) {//审核内容通过后写入 info表
                $apply_data = Db::name('nw_channel_info_apply')->where(['channel_id'=>$channel_id,'id'=>$applyId])->value('apply_info');
                if (!$apply_data) {
                    $this->error("审核内容获取失败,请刷新后重试");
                }
                $apply_data                 = json_decode($apply_data,1);
                $cont['real_name']          = isset($apply_data['name'])?$apply_data['name']:'';//真实姓名
                $cont['person_id']          = isset($apply_data['person_id'])?$apply_data['person_id']:'';//身份证
                $cont['zfb_account']        = isset($apply_data['zhifubao_number'])?$apply_data['zhifubao_number']:'';//支付宝账号
                $cont['bank_ban_mobile']    = isset($apply_data['mobile'])?$apply_data['mobile']:'';//手机号码
                $cont['bank_number']        = isset($apply_data['bankNum'])?$apply_data['bankNum']:'';//银行卡号码
                $cont['bank_province_city'] = isset($apply_data['provinceCity'])?$apply_data['provinceCity']:'';//省市
                $cont['bank_name']          = isset($apply_data['bankName'])?$apply_data['bankName']:'';//银行名称
                $cont['bank_open_name']     = isset($apply_data['openBank'])?$apply_data['openBank']:'';//开户行
                $cont['bank_open_number']   = isset($apply_data['bankId'])?$apply_data['bankId']:'';//开户行行号
                $cont['back_image_url']     = isset($apply_data['back_image_url'])?$apply_data['back_image_url']:'';//身份证正面
                $cont['front_image_url']    = isset($apply_data['front_image_url'])?$apply_data['front_image_url']:'';//身份证背面
                $cont['apply_status']       = 1;//审核通过标示
                if (Db::name('nw_channel_info')->where(['channel_id'=>$channel_id])->value('id')) {
                    $resultUpdate = Db::name('nw_channel_info')->where(['channel_id'=>$channel_id])->update($cont);
                }else{
                    $cont['channel_id'] =$channel_id;
                    $resultUpdate = Db::name('nw_channel_info')->insert($cont);
                }
                if (!$resultUpdate) {
                    $this->error("更新系统参数错误,请刷新后重试");
                }
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error("添加失败： " . $e->getMessage());
        }

        $adminInfo = model('ChannelAdmin')->where(['channel_id'=>$channel_id,'mobile'=>['neq','']])->find();
        if (!empty($adminInfo) && $adminInfo['mobile']){
            $result = (new \app\common\library\Sms)->sendIdentifyAuditInfo($adminInfo['username'], $adminInfo['mobile'], $status);
        }

        $this->success("审核成功");
        if ($resultChannel) {//ws通知
            $ws = new Websocket();
            $str = '{"error_code":0,"data":{"action":"applyFnish","data":{"status":'.$status.', "message":"审核完成"}}}';
            $ws->sayToUid('cps'.$channel_id, $str);
        }
    }
    /**
     * 添加联盟
     *
     * @return mixed
     */
    public function league_add()
    {
        return $this->fetch();
    }
    /**
     * 添加渠道接口
     *
     */
    public function add_league_post()
    {
        $postParam = $this->request->param();
        $postParam['username'] = trim($postParam['username']);

        $result = $this->validate($postParam, 'channel.add');

        if ($result !== true) {
            $this->error($result);
        }
        elseif ($_POST['password'] != $_POST['repassword']) {
            $this->error("两次密码输入不一致");
        }
        /*
        elseif ($_POST['pay_password'] != $_POST['pay_repassword']) {
            $this->error("两次支付密码输入不一致");
        }
        */
        else if(intval($_POST['login_check']) && empty($_POST['mobile'])){
            $this->error("登录验证开启时手机号必须填写");
        }
        else {
            Db::startTrans();

            try {
                //1. 添加渠道信息
                $channelModel = model('channel');
                $channelInfo = $channelModel->where(['name' => $postParam['username']])->find();
                if ( $channelInfo ) {
                    throw new Exception("该联盟信息已存在");
                }

                $userInfo = model('ChannelAdmin')->field('id')->where(['username' => $postParam['username']])->find();
                if ($userInfo) {
                    throw new Exception("用户名已存在! ");
                }

                //添加渠道信息
                $channelModel->save([
                    'name'              => $postParam['username'],
                    'parent_id'         => 0,
                    'id_path'           => '',
                    'level'             => 0,
                    'create_time'       => NOW_TIMESTAMP,
                    'flag'              => 3,
                    'show_full_account' => $postParam['show_full_account'],
                    'channel_tg_type' => $postParam['channel_tg_type'],
                    'status'            => $_POST['status']]);
                if ( !$channelModel->id ) {
                    throw new Exception("添加联盟信息失败");
                }
                unset($_POST['repassword']);
                unset($_POST['channel_tg_type']);
                unset($_POST['pay_repassword']);
                unset($_POST['show_full_account']);

                $_POST['username']      = $postParam['username'];
                $_POST['password']      = mg_password($_POST['password']);
                //    $_POST['pay_password']  = mg_password($_POST['pay_password']);
                $_POST['channel_id']    = $channelModel->id;
                $_POST['create_time']   = NOW_TIMESTAMP;
                $_POST['password_update_time'] = NOW_TIMESTAMP;

                $result = $this->adminModel->insertGetId($_POST);

                if ($result === false) {
                    throw new Exception("添加渠道账号失败");
                }

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }

            // 写日志
            $messageStr = '冻结';
            if($_POST['status']){
                $messageStr = '正常';
            }
            $this->insertLog($this->current_node, "新增子渠道：".$postParam['username']."，状态：".$messageStr,41);

            $this->success("添加成功！", url("channel/leaguesmanage"));
        }
    }
    /**
     * 添加渠道接口
     *
     */
    public function addPost()
    {
        $postParam = $this->request->param();
        $postParam['username'] = trim($postParam['username']);
        $result = $this->validate($postParam, 'channel.add');

        if ($result !== true) {
            $this->error($result);
        }
        elseif ($_POST['password'] != $_POST['repassword']) {
            $this->error("两次密码输入不一致");
        }
        /*
        elseif ($_POST['pay_password'] != $_POST['pay_repassword']) {
            $this->error("两次支付密码输入不一致");
        }
        */
        else if(intval($_POST['login_check']) && empty($_POST['mobile'])){
            $this->error("登录验证开启时手机号必须填写");
        }
        else if(intval($_POST['belong_lm']) && empty($_POST['belong_lm'])){
            $this->error("所属联盟必须填写");
        }
        else {
            Db::startTrans();

            try {
                //1. 添加渠道信息
                $channelModel = model('channel');
                $channelInfo = $channelModel->where(['name' => $postParam['username']])->find();
                if ( $channelInfo ) {
                    throw new Exception("该渠道信息已存在");
                }

                $userInfo = model('ChannelAdmin')->field('id')->where(['username' => $postParam['username']])->find();
                if ($userInfo) {
                    throw new Exception("用户名已存在! ");
                }
                if ($postParam['mobile']) {
                    $flag = Db::table('nw_channel_admin')->where(['mobile'=>$postParam['mobile']])->find();
                    if ($flag) {
                        throw new Exception("该手机号码已在其他账号上绑定");
                    }
                }
                //添加渠道信息
                $channelModel->save([
                    'name'              => $postParam['username'],
                    'parent_id'         => $postParam['belong_lm'],
                    'id_path'           => ','.$postParam['belong_lm'].',',
                    'create_time'       => NOW_TIMESTAMP,
                    'level'             => 1,
                    'flag'              => 3,
                    'show_full_account' => $postParam['show_full_account'],
                    'cps_settle_period' => $postParam['cps_settle_period'],
                    'mcps_settle_period' => $postParam['mcps_settle_period'],
                    'status'            => $_POST['status']]);
                if ( !$channelModel->id ) {
                    throw new Exception("添加渠道信息失败");
                }

                unset($_POST['repassword']);
                unset($_POST['pay_repassword']);
                unset($_POST['show_full_account']);
                unset($_POST['belong_lm']);
                unset($_POST['cps_settle_period']);
                unset($_POST['mcps_settle_period']);
                $_POST['username']      = $postParam['username'];
                $_POST['password']      = mg_password($_POST['password']);
                //      $_POST['pay_password']  = mg_password($_POST['pay_password']);
                $_POST['channel_id']    = $channelModel->id;
                $_POST['create_time']   = NOW_TIMESTAMP;
                $_POST['password_update_time'] = NOW_TIMESTAMP;

                $result = $this->adminModel->insertGetId($_POST);

                if ($result === false) {
                    throw new Exception("添加渠道账号失败");
                }

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }

            // 写日志
            $messageStr = '冻结';
            if($_POST['status']){
                $messageStr = '正常';
            }
            $cpsStr = '日结';
            if ($postParam['cps_settle_period'] == 1) {
                $cpsStr = '周结';
            }
            $mcpsStr = '日结';
            if ($postParam['mcps_settle_period'] == 1) {
                $mcpsStr = '周结';
            }
            $this->insertLog($this->current_node, "新增子渠道：".$postParam['username']."，状态：".$messageStr."，专服结算方式：".$cpsStr."，混服结算方式：".$mcpsStr,41);

            $this->success("添加成功！", url("channel/index"));
        }
    }

    /**
     * 编辑渠道
     *
     * @return mixed
     */
    public function edit()
    {
        $type = input('type', 0, 'intval');//类型 判断是联盟管理还是渠道管理
        $id = input('id', 0, 'intval');
        $channel = $this->adminModel->alias('a')
            ->join('nw_channel channel', 'a.channel_id = channel.id','left')
            ->join('nw_channel_info info', 'channel.id = info.channel_id','left')
            ->field('a.id as adminid,a.channel_id as channelid,a.*,channel.show_full_account,channel.level as channel_level,channel.mcps_settle_period,channel.cps_settle_period,channel.channel_tg_type,channel.amount as ptb_amt,channel.js_amount,info.*')
            ->where(["a.id" => $id])->find();
        $this->assign('channel', $channel);
        //	var_dump($channel);

        //后台管理员列表
        $adminList = model('ChannelAdmin')->field('id,username as name')->where(['status'=>1])->order('username asc')->select();
        $this->assign('admin_list',$adminList);
        $this->assign('type',$type);
        return $this->fetch();
    }

    /**
     * 编辑渠道接口
     *
     */
    public function editPost()
    {
        set_time_limit(0);
        $type = input('type', 0, 'intval');
        if (empty($_POST['password'])) {
            unset($_POST['password']);
        } else {
            $result = $this->validate($this->request->param(), 'Channel.password');

            if (true !== $result) {
                $this->error($result);
            }

            if ($_POST['password'] != $_POST['repassword']) {
                $this->error('两次登录密码输入不一致!');
            }

            $_POST['password']              = mg_password($_POST['password']);
            $_POST['password_update_time']  = NOW_TIMESTAMP;
        }

        if (empty($_POST['pay_password'])) {
            unset($_POST['pay_password']);
        } else {
            $result = $this->validate($this->request->param(), 'Channel.pay_password');

            if (true !== $result) {
                $this->error($result);
            }

            if ($_POST['pay_password'] != $_POST['pay_repassword']) {
                $this->error('两次支付密码输入不一致!');
            }

            $_POST['pay_password'] = mg_password($_POST['pay_password']);
        }

        unset($_POST['repassword']);
        unset($_POST['pay_repassword']);

        $result = $this->validate($this->request->param(), 'Channel.edit');

        if ($result !== true) {
            $this->error($result);
        }
        else if(intval($_POST['login_check']) && empty($_POST['mobile'])){
            $this->error("登录验证开启时手机号必须填写");
        }

        if ($result !== true) {
            // 验证失败 输出错误信息
            $this->error($result);
        } else {
            $show_full_account = intval($_POST['show_full_account']);
            $id = intval($_POST['channel_id']);
            $parent_id = Db::table('nw_channel')->where(['id' => $id])->value('parent_id');
            $ParentChannel = Db::table('nw_channel')->where(['id' => $parent_id])->field('id,id_path,level,show_full_account')->find();
            // 判断上级渠道的账号显示是否开启
            if ($ParentChannel['show_full_account']==0 && $ParentChannel['level']<>0){
                $show_full_account = 0;
            }
            $numArr = model('Channel')->getChildIds($id);
            $list  = [];
            $list[] = ['id'=>$id,'show_full_account'=>$show_full_account];

            if($show_full_account==0 && $numArr){
                foreach($numArr as $v){
                    $list[] = ['id'=>$v,'show_full_account'=>$show_full_account];
                }
            }
            unset($_POST['show_full_account']);

            Db::startTrans();
            $updChannel = array();
            //	$updChannel['show_full_account'] = $show_full_account;
            $updChannel['id'] = $_POST['channel_id'];
            $updChannel['status'] = $_POST['status'];
            if ($_POST['mobile']) {
                $flag = Db::table('nw_channel_admin')->where(['channel_id'=>['neq',$_POST['channel_id']],'mobile'=>$_POST['mobile']])->find();
                if ($flag) {
                    $this->error("保存失败！该手机号码已在其他账号上绑定");
                }
            }

            //会长的时候增加结算周期
            if (Db::table('nw_channel')->where(['id' => $id])->value('level') == 1) {
                $updChannel['cps_settle_period'] = $_POST['cps_settle_period'];
                $updChannel['mcps_settle_period'] = $_POST['mcps_settle_period'];
                unset($_POST['cps_settle_period']);
                unset($_POST['mcps_settle_period']);
            }

            $result = $this->adminModel->update($_POST);
            $resultChannel = model('channel')->where('id', '=', intval($_POST['channel_id']))->update($updChannel);

            $result2 = model('Channel')->saveAll($list);

            /*
            if(get_channel_level(intval($_POST['channel_id']))==1){  //是否是一级渠道
                $ChannelInfoData['update_time'] = time();
                $ChannelInfo =  model('ChannelInfo')->where('channel_id', '=', intval($_POST['channel_id']))->find();
                if(!empty($ChannelInfo)){
                    $result3 = model('ChannelInfo')->save($ChannelInfoData,array('channel_id'=>intval($_POST['channel_id'])));
                }
                else{
                    $ChannelInfoData['admin_id'] = session('ADMIN_ID');
                    $ChannelInfoData['create_time'] = time();
                    $result3 = model('ChannelInfo')->insert($ChannelInfoData);
                }
            }
            else{
                $result3 = true;
            }
            if ($result !== false && $result2 !== false && $result3 !== false) {
            */

            if ($result !== false && $result2 !== false) {
                Db::commit();
                // 写日志
                $channelMessageStr = '密文';
                if($show_full_account){
                    $channelMessageStr = '明文';
                }
                $messageStr = '冻结';
                if($_POST['status']){
                    $messageStr = '正常';
                }

                $channel_name = get_channel_name($_POST['channel_id']);
                if (Db::table('nw_channel')->where(['id' => $id])->value('level') == 1) {
                    $cpsStr = '日结';
                    if ($updChannel['cps_settle_period'] == 1) {
                        $cpsStr = '周结';
                    }
                    $mcpsStr = '日结';
                    if ($updChannel['mcps_settle_period'] == 1) {
                        $mcpsStr = '周结';
                    }
                    $this->insertLog($this->current_node, "编辑子渠道：".$channel_name."，用户账号：".$channelMessageStr."，状态：".$messageStr."，专服结算方式：".$cpsStr."，混服结算方式：".$mcpsStr,42);
                } else{
                    $this->insertLog($this->current_node, "编辑子渠道：".$channel_name."，用户账号：".$channelMessageStr."，状态：".$messageStr,42);
                }

                $this->request->param('id', 0, 'intval');
                if ($type == 1) {
                    $this->success("保存成功！",'Channel/leaguesmanage');
                }else{
                    $this->success("保存成功！",'Channel/index');
                }

            } else {
                Db::rollback();
                $this->error("保存失败！");
            }
        }

    }

    public function delete()
    {
        $id = $this->request->param('id', 0, 'intval');

        Db::startTrans();

        try {
            if ($this->adminModel->delete($id) === false) {
                throw new Exception('删除失败');
            }

            $channelModel = model('channel');
            $channelModel->where('id', '=', $id)->delete();

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();

            $this->error("删除失败！");
        }

        $this->success("删除成功！");
    }

    /**
     * 渠道停用
     *
     */
    public function ban()
    {
        $id = $this->request->param('id', 0, 'intval');
        if (!empty($id)) {
            $adminInfo = model('ChannelAdmin')->where(["id" => $id])->find();
            if(empty($adminInfo)){
                $this->error('不存在该记录,渠道停用失败！');
            }
            Db::startTrans();
            $result = $this->adminModel->where(["id" => $id])->setField('status', '0');
            if($adminInfo['channel_id']){
                $result2 =  model('channel')->where(["id" => $adminInfo['channel_id']])->setField('status', '0');
            }
            else{
                $result2 = true;
            }
            if ($result !== false && $result2 !== false) {
                Db::commit();
                if($adminInfo['channel_id']){
                    // 写日志
                    $channelInfo = model('channel')->where(["id" => $adminInfo['channel_id']])->find();
                    $channelMessageStr = '密文';
                    if($channelInfo['show_full_account']){
                        $channelMessageStr = '明文';
                    }
                    $channel_name = get_channel_name($adminInfo['channel_id']);
                    $this->insertLog($this->current_node, "编辑子渠道：".$channel_name."，用户账号：".$channelMessageStr."，状态：冻结",42);
                }
                $this->success("渠道停用成功！");
            } else {
                Db::rollback();
                $this->error('渠道停用失败！');
            }
        } else {
            $this->error('数据传入失败！');
        }
    }

    /**
     * 渠道开启
     *
     */
    public function cancelBan()
    {
        $id = $this->request->param('id', 0, 'intval');
        if (!empty($id)) {
            $adminInfo = model('ChannelAdmin')->where(["id" => $id])->find();
            if(empty($adminInfo)){
                $this->error('不存在该记录,渠道启用失败！');
            }
            Db::startTrans();
            $result = $this->adminModel->where(["id" => $id])->setField('status', '1');
            if($adminInfo['channel_id']){
                $result2 =  model('channel')->where(["id" => $adminInfo['channel_id']])->setField('status', '1');
            }
            else{
                $result2 = true;
            }
            if ($result !== false && $result2 !== false) {
                Db::commit();
                if($adminInfo['channel_id']){
                    // 写日志
                    $channelInfo = model('channel')->where(["id" => $adminInfo['channel_id']])->find();
                    $channelMessageStr = '密文';
                    if($channelInfo['show_full_account']){
                        $channelMessageStr = '明文';
                    }
                    $channel_name = get_channel_name($adminInfo['channel_id']);
                    $this->insertLog($this->current_node, "编辑子渠道：".$channel_name."，用户账号：".$channelMessageStr."，状态：正常",42);
                }
                $this->success("渠道启用成功！");
            } else {
                Db::rollback();
                $this->error('渠道启用失败！');
            }
        } else {
            $this->error('数据传入失败！');
        }
    }

    public function down() {
        $channel_id = input('channel_id/d');
        $game_id    = input('game_id/d');
        $downChannel = new SubChannel;
        $downChannel->down($channel_id, $game_id);
    }

    /**
     * 渠道登录页管理
     */
    public function loginPage(){
        if ($this->request->isPost()){
            Db::startTrans();
            $pictureModel = model('ChannelLoginPicture');
            $res = [true,true,true,true];
            for ($i=1; $i<5; $i++){
                $operation_type = input('post.operation_type_' . $i, 0 , 'intval');
                $operation_id   = input('post.operation_id_' . $i, 0 , 'intval');
                $address        = input('post.address_' . $i, '', 'trim');
                $ifJump         = input('post.ifJump_' . $i);

                $picture_arr = $this->upFile('carrousel_' . $i);
                if (!preg_match("/^http(s)?:\\/\\/.+/",$address) && $address != ''){
                    // 回滚事务
                    Db::rollback();
                    $this->error('请输入正确的网址');
                }
                if (!$picture_arr['code'] && $address != '' && $operation_id == 0 && $operation_type != 2){
                    // 回滚事务
                    Db::rollback();
                    $this->error('未上传图片，不能填写跳转地址！');
                }
                $condition = [];
                if ($ifJump == 'on'){
                    $condition['isjump'] = 0;
                }else{
                    $condition['isjump'] = 1;
                }

                $condition['url'] = $address;

                if ($picture_arr['code']){
                    $condition['filename'] = $picture_arr['data'];
                }

                // 新增
                if ($operation_type == 1 && $operation_id == 0){
                    $condition['create_time'] = time();
                    $res[$i-1] = Db::table('nw_channel_login_picture')->insertGetId($condition);
                }
                // 删除
                if ($operation_type == 2 && $operation_id != 0){
                    $res[$i-1] = $pictureModel::destroy(['id' => $operation_id]);
                }
                // 修改
                if (($operation_type == 3 || $operation_type == 0) && $operation_id != 0){
                    $condition['update_time'] = time();
                    $res[$i-1] = Db::table('nw_channel_login_picture')->where('id', $operation_id)->update($condition);
                }
                //$res[$i-1] = ( $res[$i-1]==0 ? true :$res[$i-1] );
            }

            if ($res[0] && $res[1] && $res[2] && $res[3]){
                // 提交事务
                Db::commit();
                $this->success('提交成功！');
            }else{
                // 回滚事务
                Db::rollback();
                $this->error('提交失败！');
            }
            exit();
        }
        $list = model('ChannelLoginPicture')->field('id,filename,url,isjump')->limit(4)->select();

        $arr = [];
        foreach ($list as $v){
            $arr[] = ['id'=>$v['id'], 'filename'=>$v['filename'], 'url'=>$v['url'], 'isjump'=>$v['isjump']];
        }
        for ($i=1; $i<=4-count($list); $i++){
            $arr[] = ['id'=>0, 'filename'=>'', 'url'=>'', 'isjump'=>0];
        }

        $this->assign('list', $arr);
        return $this->fetch('loginpage');
    }

    /**
     * 渠道反馈管理
     */
    public function feedback(){

        $condition = [];
        $order_num = input('order_num','','trim');
        $title = input('title','','trim');
        $channelid = input('channel_id',0,'intval');
        $start          = $this->request->param('start_time');
        $end            = $this->request->param('end_time');
        $reply = input('is_reply');
        if (!empty($order_num)){
            $condition['a.order_num'] = $order_num;
        }

        if (!empty($title)){
            $condition['a.title'] = ['LIKE', '%'.$title.'%'];
        }

        if (!empty($channelid)){
            $condition['a.channel_id'] = $channelid;
            $condition['a.is_anonymous'] = 0;
        }
        if ($reply != ''){
            $condition['a.is_reply'] = $reply;
        }

        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition['a.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition['a.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition['a.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
            /*$start = date('Y-m-d', time());
            $end   = date('Y-m-d', time());

            $condition['a.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];*/
        }


        $feedbackList = model('ChannelFeedback')->alias('a')
            ->join('nw_channel channel', 'a.channel_id = channel.id')
            ->join('nw_channel_admin admin', 'admin.id = a.operate_id', 'left')
            ->where($condition)
            ->field('a.*,channel.name as channel_name,admin.username')
            ->order("a.is_reply asc, a.create_time DESC")
            ->paginate(10,false, ['query' => input('get.')]);
        // 获取分页显示
        $page = $feedbackList->render();
        $this->assign("page", $page);
        $this->assign("total", $feedbackList->total());     //总记录数
        $this->assign("list", $feedbackList);
        return $this->fetch();
    }

    public function feedbackReply(){
        $id = input('id',0,'intval');
        if (empty($id)) $this->error('网络出现错误！');
        if ($this->request->isPost()){
            $reply = input('post.reply','','trim');
            if (empty($reply)) $this->error('请输入回复内容');
            model('ChannelFeedback')->save(['reply'=>$reply,'is_reply'=>1,'operate_id'=>session('ADMIN_ID')],['id'=>$id]);
            $this->success('编辑成功！','feedback');
        }

        $info = model('ChannelFeedback')->alias('a')
            ->join('nw_channel channel', 'a.channel_id = channel.id')
            ->where(['a.id'=>$id])
            ->field('a.*,channel.name as channel_name')
            ->find();

        // 文件路径拼接处理
        $imgarr = $filearr = [];
        if (!empty($info['file'])){
            $allFileArr = explode(",", $info['file']);
            foreach ($allFileArr as $v){
                $nameArr = explode("/", $v);
                $name = $nameArr[count($nameArr)-1];
                if (strstr($v,'jpg') || strstr($v,'png') || strstr($v,'bmp') ){
                    $imgarr[] = ['path'=>$v,'name'=>$name];
                }else{
                    $filearr[] = ['path'=>$v,'name'=>$name];
                }
            }
        }
        $this->assign('info',$info);
        $this->assign('imgarr',$imgarr);
        $this->assign('filearr',$filearr);
        return $this->fetch('feedback_reply');
    }

    /**
     * 渠道游戏推荐管理
     */
    public function gameRecommend(){

        // 游戏数据，页面渲染用
        $arr = ['newGame'=>[],'hotGame'=>[],'typeGame'=>[]];
        /*if ($this->request->isGet()){}*/
        $list = Db::name('nw_game_recommend')->alias('a')
            ->join('cy_game b', 'a.gameid=b.id')
            ->join('cy_gameinfo info', 'a.gameid = info.game_id', 'left')
            //->join('cy_gameinfo info', 'a.id = info.game_id', 'left')
            /*->where('b.cooperation_status <> 3')*/
            ->field('a.id,a.gameid,a.type,b.name,b.type as game_type,b.subject,info.mobileicon')->select();

        $gameType = Db::table('cy_gametype')->column('name', 'id');
        foreach ($list as $v) {
            $v['game_type'] = isset($gameType[$v['game_type']]) ? $gameType[$v['game_type']] : '';
            if ($v['type'] == 1) $arr['newGame'][] = $v;
            if ($v['type'] == 2) $arr['hotGame'][] = $v;
            if ($v['type'] == 3) $arr['typeGame'][] = $v;
        }


        // 所有的游戏列表
        $gameList = model('Common/Game')->getAllByCondition('id,name',['cooperation_status'=>['NEQ',3]]);
        // 游戏题材列表
        $gameSubject_List = Db::table('cy_gamesubject')->select();
        /*dump($gameSubject_List);dump($arr);*/
        // 当前要推荐的游戏题材id
        $current_subject = $gameSubject_List[0]['id'];
        if (count($arr['typeGame'])){
            $current_subject = $arr['typeGame'][0]['subject'];
        }
        // 默认题材的所有游戏列表
        $gameList_Subject = model('Common/Game')->getAllByCondition('id,name',['subject'=>$current_subject]);

        // 未来渲染页面，对每个类型的游戏推荐进行数量补足，补足5个
        foreach ($arr as &$value){
            $num = 5 - count($value);
            if ($num){
                for ($i=0; $i<$num; $i++){
                    $value[] = ['id'=>0,'gameid'=>0,'type'=>0,'name'=>0,'game_type'=>0,'subject'=>0,'mobileicon'=>0];
                }
            }
        }
        /*dump(count($list));
        dump($arr);*/
        $this->assign('list',$arr);
        $this->assign('game_list',$gameList);
        $this->assign('subject_List',$gameSubject_List);
        $this->assign('game_list_subject',$gameList_Subject);
        $this->assign('current_subject',$current_subject);

        return $this->fetch('game_recommend');
    }

    /**
     * 设置 推荐游戏
     */
    public function setGameRecommend(){
        $gameRecommendModel = Db::name('nw_game_recommend');
        /*$param = $this->request->post();
        dump($param);
        exit;*/

        Db::startTrans();
        $recommend = $this->request->post('type',0,'intval');  // 游戏推荐类型
        $delall = $this->request->post('recommIfReset',0,'intval');  // 是否重置

        // todo 测试
        //$recommend = 3;
        //$delall = 1;

        if (empty($recommend)) $this->error('参数错误');
        // 若重置，则删除该类型的所有推荐游戏
        $res_del = true;
        if($delall == 1){
            $res_del = $gameRecommendModel->where('type',$recommend)->delete();
            $res_del = $res_del==0?true:$res_del;
        }

        // 新增 或 更新 推荐游戏
        if ($res_del){
            // todo 测试 todo 测试 todo 测试 todo 测试
            /*$idArr = [7,9,13,14,15];
            $gameidArr = [584,590,591,592,590];
            $operateArr = [0,0,0,0,2];*/
            for ($i=1;$i<6; $i++){
                // 获取参数
                $gameid     = $this->request->post('gameid_' . $i , 0 , 'intval');
                $id = $this->request->post('id_' . $i , 0 , 'intval');
                $operate    = $this->request->post('ope_' . $i , 0 , 'intval');
                $res        = true;
                // todo 测试 todo 测试 todo 测试
                /*$gameid     = $gameidArr[$i - 1];
                $id = $idArr[$i - 1];
                $operate    = $operateArr[$i - 1];*/

                if ($operate == 0){
                    continue;
                }

                // 判断参数是否有误
                if (empty($gameid)){
                    Db::rollback();
                    $this->error('参数错误');
                }

                // 判断 要新增 或者 更新的 游戏 是否已经存在 推荐列表
                $info = $gameRecommendModel->where(['gameid' =>$gameid])->find();

                if ($info) {
                    Db::rollback();
                    $this->error('游戏重复，请重新选择');
                }

                $newData = ['gameid' =>$gameid,'operate_id'=>session('ADMIN_ID')];
                // 新增  || ($res_del && $operate == 2)
                if ($operate == 1 || ($delall && $operate == 2)){
                    $newData['create_time'] = time();
                    $newData['type'] = $recommend;
                    $res = $gameRecommendModel->insertGetId($newData);
                }
                // 更新  && !$res_del
                if ($operate == 2 && $delall != 1){
                    $newData['update_time'] = time();
                    $newData['id'] = $id;
                    $res = $gameRecommendModel->update($newData);
                }

                if (!$res){
                    Db::rollback();
                    $this->error('提交失败 '.$operate);
                }
            }

            DB::commit();
            $this->success('修改成功');
        }else{
            Db::rollback();
            $this->error('提交失败');
        }
    }

    /**
     * 上传文件
     * @param $fileName 上传的文件流key
     * @return array
     */
    public function upFile($fileName)
    {
        $fileUpload    = new FileUpload();

        if (empty(request()->file($fileName))) return ['code' => 0 , 'data' => '请上传文件！'];
        $file_path = $fileUpload->set('allowExt' , 'jpg,png,bmp')->set('maxsize' , 1024000 * 3)->set('dir' , 'image/channel_login/')->upload(request()->file($fileName));

        if ( !$file_path) {
            return ['code' => 0 , 'data' => '上传失败，' . $fileUpload->getError()];
        }

        return ['code' => 1 , 'data' => $file_path];
    }
    /**
     * 渠道管理--渠道联系方式明文查看
     */
    public function viewlinkinfo(){
        $Id = $this->request->param('id', 0, 'intval');
        $file = $this->request->param('file' , '' , 'trim');
        if (empty($Id) || empty($file)){
            $this->result('',0,'参数错误！');
        }
        $file = trim($file);
        if ($file == 'mobile'){
            $action = '手机号';
            $info = $this->adminModel->where(['id'=>$Id])->value('mobile');
            $ChannelId = model('ChannelAdmin')->where(['id'=>$Id])->value('channel_id');
            if($ChannelId){
                $ChannelName = $this->channelModel->where(['id'=>$ChannelId])->value('name');
            }
            else{
                $ChannelName = model('ChannelAdmin')->where(['id'=>$Id])->value('username');
            }
        }elseif ($file=='account_qq' || $file=='account_wechat'){
            if($file=='account_qq'){
                $action = 'QQ号';
            }
            else if($file=='account_wechat'){
                $action = '微信号';
            }
            $info = model('ChannelInfo')->where(['channel_id'=>$Id])->value($file);
            $ChannelName =  $this->channelModel->where(['id'=>$Id])->value('name');
        }else{
            $this->result('',0,'参数错误！');
        }

        // 记录操作日志
        $this->insertLog($this->current_node,'查看渠道“'.$ChannelName.'”'.$action.'：'.$info,45);
        $this->result($info,1,$info);
    }
    /**
     * 渠道管理--渠道支付类型明文查看
     */
    public function viewpayinfo(){
        $Id = $this->request->param('id', 0, 'intval');
        $file = $this->request->param('file' , '' , 'trim');
        //	echo $Id."---id----".$file."--file----<br>";
        if (empty($Id) || empty($file)){
            $this->result('',0,'参数错误！');
        }
        $file = trim($file);
        if ($file=='account_type' ||$file=='alipay_realname' || $file=='alipay_account' || $file=='bank_account' || $file=='company_name' || $file=='bank_name' || $file=='linkman_email' || $file=='account_qq' ||$file=='account_wechat'){
            $ChannelInfo = model('ChannelInfo')->where(['channel_id'=>$Id])->find();
            //	var_dump($ChannelInfo);
            if($file=='account_type'){
                $action = '账户类型';
                if($ChannelInfo['account_type']=='private'){
                    $info = '对私';
                }
                else if($ChannelInfo['account_type']=='public'){
                    $info = '对公';
                }
                else{
                    $info = '';
                }
            }
            else if($file=='alipay_account'){
                $action = '支付宝账号';
                if($ChannelInfo['account_type']=='private'){
                    $info = $ChannelInfo['alipay_account'];
                }
                else{
                    $info = '';
                }
            }
            else if($file=='alipay_realname'){
                $action = '支付宝姓名';
                if($ChannelInfo['account_type']=='private'){
                    $info = $ChannelInfo['alipay_realname'];
                }
                else{
                    $info = '';
                }
            }
            else if($file=='bank_account'){
                $action = '银行账号';
                if($ChannelInfo['account_type']=='public'){
                    $info = $ChannelInfo['bank_account'];
                }
                else{
                    $info = '';
                }
            }
            else if($file=='company_name'){
                $action = '公司全称';
                if($ChannelInfo['account_type']=='public'){
                    $info = $ChannelInfo['company_name'];
                }
                else{
                    $info = '';
                }
            }
            else if($file=='bank_name'){
                $action = '开户银行';
                if($ChannelInfo['account_type']=='public'){
                    $BanknameArrs = array("1"=>"工商银行","2"=>"建设银行","3"=>"农业银行","4"=>"中国银行","5"=>"招商银行","6"=>"民生银行","7"=>"光大银行","8"=>"交通银行","9"=>"中信银行","10"=>"平安银行","11"=>"兴业银行","12"=>"华夏银行","13"=>"广发银行","14"=>"浦发银行","15"=>"日照银行");
                    if(array_key_exists($ChannelInfo['bank_name'],$BanknameArrs)){
                        $info = $BanknameArrs{$ChannelInfo['bank_name']};
                    }
                    else{
                        $info = '';
                    }
                }
                else{
                    $info = '';
                }
            }
            else if($file=='linkman_email'){
                $action = '联系人邮箱';
                if($ChannelInfo['account_type']=='public'){
                    $info = $ChannelInfo['linkman_email'];
                }
                else{
                    $info = '';
                }
            }
        }else{
            $this->result('',0,'参数错误！');
        }

        $ChannelName =  $this->channelModel->where(['id'=>$Id])->value('name');

        // 记录操作日志
        $this->insertLog($this->current_node,'查看渠道“'.$ChannelName.'”'.$action.'：'.$info,46);
        $this->result($info,1,$info);
    }

    private function checkHasActionPriv($userId,$module,$controller,$action)
    {
        if (empty($userId)) {
            return false;
        }
        // 如果用户id是1，则无需判断
        if ($userId == 1) {
            return true;
        }
        if(!$module || !$controller || !$action){
            return false;
        }
        $rule       = $module . $controller . $action;
        // action检测
        $actionCheck = in_array(strtolower($action),
            array_map(function ($val){
                return strtolower($val);
            }, $this->noCheckAuth)
        );

        $notRequire = ["adminIndexindex", "adminMainindex"];
        if (!in_array($rule, $notRequire) &&
            ! $actionCheck) {
            $authObj = new \app\common\logic\Auth();
            $name       = strtolower($module . "/" . $controller . "/" . $action);
            return $authObj->check($userId, $name, 'or');
        } else {
            return true;
        }
    }

    public function getGaneByType(){
        $current_subject = input('type',0,'intval');
        if (empty($current_subject)){
            $this->error('请选择游戏题材！');
        }
        // 默认题材的所有游戏列表
        $gameList_Subject = model('Common/Game')->getAllByCondition('id,name',['subject'=>$current_subject]);
        $this->success('','',$gameList_Subject);
    }

    /**
     * 公会保证金
     *
     * @return mixed
     */
    public function deposit() {
        if ($this->request->isPost()){
            $channel_id = input('post.channel_id',0,'intval');
            $deposit_amt = input('post.deposit_amt',0,'floatval');
            $remark = input('post.remark','','trim');

            if(!$channel_id){
                $this->error("请选择要操作的公会");
            }
            $channelInfo = model("Channel")->field('id,name,level,status,deposit_amt')->where(['id'=>$channel_id])->find();
            if(!$channelInfo || $channelInfo['level']<>1){
                $this->error("您选择设置保证金的公会不存在");
            }

            Db::startTrans();
            try {
                $updData['deposit_amt']  = $deposit_amt;
                if($remark){
                    $updData['remark']   = $remark;
                }
                $updData['update_time']    = time();
                $updResult = model("Channel")->where(['id'=>$channel_id])->update($updData);
                if (!$updResult) {
                    $this->error("更新系统参数错误,请刷新后重试");
                }
                Db::commit();

                $message = '设置公会保证金：管理员"'.session('USERNAME').'" 设置了公会保证金金额，公会账号：'.$channelInfo['name'].'，金额：'.$deposit_amt.'元 。操作时间：'.date('Y-m-d H:i:s');
                $this->insertLog($this->current_node,$message, 401);
            } catch (Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }

            $this->success("公会保证金设置成功");
        }

        $channel_id = intval(request()->get('channelid'));
        if(!$channel_id){
            $this->error("请选择要操作的公会");
        }
        $channelInfo = model("Channel")->field('id,name,level,status,deposit_amt')->where(['id'=>$channel_id])->find();
        if(!$channelInfo || $channelInfo['level']<>1){
            $this->error("您选择设置保证金的公会不存在");
        }
        $this->assign('channel_id', $channel_id);
        $this->assign('channelInfo', $channelInfo);
        return $this->fetch();
    }

    // 修改是否聚合用户状态
    public function editGhStatus(){
        $id = input('channelid', '');
        $status = input('gh_status', '');
        if(!isset($id)){
            $this->error("渠道标识ID参数有误！");
        }
        if(!isset($status) && !in_array($status, [0, 1])){
            $this->error("状态参数有误!");
        }
        $data['gh_status'] = $status;
        $data['update_time'] = time();
        if ($this->channelModel->where(['id' => $id])->update($data)){
            $this->success('修改成功！');
        }else{
            $this->error('修改失败！');
        }
    }
}
