<?php

namespace app\admin\controller;


use app\common\library\MakeReportGo;
use app\common\library\OneFileUpload;
use app\common\logic\Member as MemberService;
use app\service\WelfareService;
use think\Loader;
use think\Db;

use app\common\model\Channel;

class WelfareGrant extends Admin
{
    public function index()
    {
        $data = input();
        $where = [];
        if (isset($data['channel_id']) && $data['channel_id']) {
            $where['a.channel_id'] = ['in', $data['channel_id']];
        }
        if (isset($data['game_id']) && $data['game_id']) {
            $where['a.game_id'] = $data['game_id'];
        }
//
        if (isset($data['grant_type_id']) && $data['grant_type_id']) {
            $where['a.grant_type_id'] = $data['grant_type_id'];
        }
//        if ($data['welfare_id']) {
//            $where['a.welfare_id'] = $data['welfare_id'];
//        }
//        if ($data['server_id']) {
//            $gameServer = model('GameServer')->where(['game_id' => $data['game_id'], 'id' => $data['server_id']])->find();
//            $where['a.server_id'] = ['in', $gameServer['serverid']];
//        }
        if (isset($data['examine']) && $data['examine']) {
            $where['a.examine'] = $data['examine'];
        }
        if (isset($data['grant_result']) && $data['grant_result']) {
            $where['a.grant_result'] = $data['grant_result'];
        }
        if (isset($data['username']) && $data['username']) {
            $where['a.username'] = $data['username'];
        }
        if (isset($data['roleid']) && $data['roleid']) {
            $where['a.roleid'] = $data['roleid'];
        }
        if (isset($data['role_name']) && $data['role_name']) {
            $where['a.role_name'] = $data['role_name'];
        }
//        if ($data['start_time'] && $data['end_time']) {
//            $start_time = strtotime($data['start_time']);
//            $end_time = strtotime($data['end_time'] . ' 23:59:59');
//            $where['a.apply_time'] = ['between', [$start_time, $end_time]];
//        }

        $list = model('common/WelfareGrant')
            ->alias('a')
            ->join('cy_welfare b', 'a.welfare_id=b.id')
            ->where($where)
            ->order('a.examine asc,a.id desc')
            ->field('a.*,b.name')
            ->paginate(10, false, ['query' => input('get.')]);
        $games = model('Common/Game')->getAllByCondition('id,name', ['game_kind' => 1, 'cooperation_status' => ['neq', 0], 'is_welfare' => 2], 'id desc', 'self');
        $servers = model('common/GameServer')->cache('welfare:welfareservice:server', 300)->field('servername,game_id,serverid')->select();
        $serversArr = [];
        foreach ($servers as $k => $v) {
            $serversArr[$v['game_id'] . '_' . $v['serverid']] = $v['servername'];
        }
        $channelListArr = [];
        $channelList = model('common/Channel')->cache('welfare:welfareservice:channel', 300)->field('id,name as value')->select();
        foreach ($channelList as $k => $v) {
            $channelListArr[$v['id']] = $v['value'];
        }
        //        if ($data['data']) {
//            $channelListArr = [];
//            $channelList = model('common/Channel')->field('id,name as value')->select();
//            foreach ($channelList as $k => $v) {
//                $channelListArr[$v['id']] = $v['value'];
//            }
//
//            $games = model('common/Game')->cache('welfare:welfareservice:game', 300)->column('name', 'id');
//            $servers = model('common/GameServer')->cache('welfare:welfareservice:server', 300)->field('servername,game_id,serverid')->select();
//            $serversArr = [];
//            foreach ($servers as $k => $v) {
//                $serversArr[$v['game_id'] . '_' . $v['serverid']] = $v['servername'];
//            }
//            foreach ($data['data'] as $k => $v) {
//                $data['data'][$k]['channel_name'] = isset($channelListArr[$v['channel_id']]) ? $channelListArr[$v['channel_id']] : '';
//                $data['data'][$k]['server_name'] = isset($serversArr[$v['game_id'] . '_' . $v['server_id']]) ? $serversArr[$v['game_id'] . '_' . $v['server_id']] : '';
//                $data['data'][$k]['game_name'] = $games[$v['game_id']];
//                $data['data'][$k]['grant_type_game'] = $this->grant_type_id[$v['grant_type_id']];
//
//                $data['data'][$k]['examine'] = $v['examine'] == 1 ? '未审核' : ($v['examine'] == 2 ? '审核通过' : '拒绝');
//                $data['data'][$k]['grant_result'] = $v['grant_result'] == 1 ? '未发放' : ($v['grant_result'] == 2 ? '发放中' : ($v['grant_result'] == 3 ? '发放完成' : '发放失败'));
//            }
//        }

        $this->assign('list', $list);

        $this->assign('serversArr', $serversArr);
        $this->assign('channelListArr', $channelListArr);
        $this->assign('page', $list->render());

        $this->assign('grant_type_ids', model('Welfare')->grant_type_id);


        $selfGameList = array();
        foreach ($games as $game) {
            $selfGameList[$game['id']] = $game;
        }
        $this->assign('games', $selfGameList);
        $game_list = array(array('id' => 0, 'name' => '请选择'));
        $game_list = array_merge($game_list, $selfGameList);
        $this->assign('game_list', $game_list);

//        $this->assign('grant_type_ids', model('Welfare')->grant_type_id);
        return $this->fetch();

    }

    /**
     * 审核界面
     * @param string $value [description]
     * @return [type]        [description]
     */
    public function examine()
    {
        $applyid = request()->get('applyid');
        $data = model('common/WelfareGrant')->where(['id' => $applyid])->find();
        $this->assign('applyid', $applyid);
        $this->assign('list', $data);
        return $this->fetch();
    }

    public function doExamine()
    {
        $applyid = input('applyid');
        $examine = input('apply_status');
        if (!in_array($examine, [2, 3])) {
            $this->error('审核失败,审核状态错误');
        }
        $data = model('common/WelfareGrant')->where(['id' => $applyid])->find();
        if ($data) {
            if ($data['examine'] == 1) {
                if (model('common/WelfareGrant')->where(['id' => $applyid])->update(['examine' => $examine])) {
                    log_message('$applyid:' . $applyid . ' $examine:' . $examine, 'info', LOG_PATH . 'welfareGrant/');
                    if ($examine == 2) {
                        log_message('$applyid:' . $applyid . ' $examine:' . $examine, 'info', LOG_PATH . 'welfareGrant/');
                        $makeReportGo = new MakeReportGo();
                        $makeReportGo->welfareTask('welfareList', $applyid);
                    }
                    $this->success("审核成功");
                } else {
                    $this->error('审核失败');
                }
            } else {
                $this->error('记录已审核');
            }
        } else {
            $this->error('记录不存在');
        }
    }

    public function approved()
    {
        $data = input();
        $examine = $data['apply_status'];
        $ids = explode(',', $data['ids']);
        unset($data['ids']);
        if (empty($examine)) {
            $this->error("系统错误");
        }
        if (!in_array($examine, [2, 3])) {
            $this->error('审核失败,审核状态错误');
        }
        if (empty($ids)) {
            $this->error("请选择记录");
        }
        $data = model('common/WelfareGrant')->whereIn('id', $ids)->column('id');
        if ($data) {
            if (model('common/WelfareGrant')->whereIn('id', $data)->update(['examine' => $examine])) {
                log_message('ids:' . implode(',', $ids) . ' $examine:' . $examine, 'info', LOG_PATH . 'welfareGrant/');
                if ($examine == 2) {
                    log_message('ids:' . implode(',', $ids) . ' $examine:' . $examine, 'info', LOG_PATH . 'welfareGrant/');
                    foreach ($data as $k => $v) {
                        $makeReportGo = new MakeReportGo();
                        log_message('ids:' . implode(',', $ids) . ' $v:' . $v, 'info', LOG_PATH . 'welfareGrant/');
                        $makeReportGo->welfareTask('welfareList', $v);
                    }
                }
                $this->success("审核成功");
            } else {
                $this->error('审核失败');
            }
        }

    }

    /**
     * 补发
     */
    public function reissue()
    {

        if (!input('id')) {
            $this->error('记录id不存在');
        }

        $makeReportGo = new MakeReportGo();
        $makeReportGo->welfareTask('welfareList', input('id'), 0, 3600, true);
        $this->success("申请成功");
    }

    public function info()
    {
        $welfare_grant_id = input('id', 0);
        $status = input('status', 0);
        $where = [];
        if ($status > 0) {
            $where['a.status'] = $status;
        }
        $list = model('welfareGrantData')
            ->alias('a')
            ->join('cy_welfare_resources b', 'a.welfare_resources_id=b.id')
            ->where('welfare_grant_id', $welfare_grant_id)
            ->where($where)
            ->field('a.*,b.type_id,b.name')
            ->order('grant_date asc,grant_time desc')
            ->paginate(10, false, ['query' => input('get.')]);
        $this->assign('id', $welfare_grant_id);
        $this->assign('list', $list);

        $this->assign('page', $list->render());

        return $this->fetch();
    }

    public function apply()
    {
        $data = input();
        $data['page'] = isset($data['page']) ? $data['page'] : 1;
        $data['pageSize'] = isset($data['pageSize']) ? $data['pageSize'] : 10;
        $channelListArr = [];
        $channelList = $this->getChannelList(input('game_id', 0));

        $servers = model('common/GameServer')->cache('welfare:welfareservice:server', 300)->field('servername,game_id,serverid')->select();
        $serversArr = [];
        foreach ($servers as $k => $v) {
            $serversArr[$v['game_id'] . '_' . $v['serverid']] = $v['servername'];
        }
        if (isset($data['search']) && $data['search'] == 1) {


            switch ($data['grant_type_id']) {
                case 1:
                    $checkResult = $this->validate($data, 'Welfare.enterGameWelfare');
                    break;
                case 2:
                    $checkResult = $this->validate($data, 'Welfare.everydayWelfare');
                    break;
                case 3:
                    $checkResult = $this->validate($data, 'Welfare.singleWelfare');
                    break;
                case 4:
                    $checkResult = $this->validate($data, 'Welfare.grandWelfare');
                    break;
                case 5:
                    $checkResult = $this->validate($data, 'Welfare.singleGrandWelfare');
                    break;
                case 6:
                    $checkResult = $this->validate($data, 'Welfare.sevenDayWelfare');
                    break;
                case 7:
                    $checkResult = $this->validate($data, 'Welfare.initialWelfare');
                    break;
                case 8:
                    $checkResult = $this->validate($data, 'Welfare.monthWelfare');
                    break;
                case 9:
                    $checkResult = $this->validate($data, 'Welfare.weekWelfare');
                    break;
                default:
                    $this->error('发放类型必选');
            }


            if (true !== $checkResult) {
                $this->error($checkResult);
            }

            $welfareService = new WelfareService();
            if ($data['channel_id']) {
                $data['channel_id'] = [$data['channel_id']];
            }
            $result = $welfareService->wefare($data['grant_type_id'], $data['game_id'], array_column(json_decode(json_encode($channelList), true), 'id'), $channelList, $data['page'], $data['pageSize'], $data, false, true);

            $this->assign('page', $result->render());
            $this->assign('list', $result);
            $this->assign('serversArr', $serversArr);
        } else {
            $this->assign('list', []);
        }

        foreach ($channelList as $k => $v) {
            $channelListArr[$v['id']] = $v['value'];
        }
        $this->assign('channelListArr', $channelListArr);
        $this->assign('grant_type_ids', model('Welfare')->grant_type_id);


        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', ['game_kind' => 1, 'cooperation_status' => ['neq', 0], 'is_welfare' => 2], 'id desc', 'self');

        $selfGameList = [['id' => 0, 'name' => '']];
        foreach ($tmpSelfGameList as $game) {
            $selfGameList[$game['id']] = $game;
        }
        $this->assign('game_list', $selfGameList);

        return $this->fetch();
    }

    public function getWelfare()
    {
        $game_ids = input('game_id', '');
        $whereRaw = [];
        foreach (explode(',', $game_ids) as $k => $v) {
            $whereRaw [] = sprintf('FIND_IN_SET(%s,game_id)', $v);
        }
        $str = implode(' or ', $whereRaw);
        $welfare = model('Welfare')->whereRaw($str)
            ->where(['grant_type_id' => input('grant_type_id', 0)])
            ->field('id,name')
            ->order('id desc')->select();
        $this->jsonResult($welfare, 1, '成功');
    }

    public function getServer()
    {
        $list = model('GameServer')->where(['game_id' => input('game_id', 0)])->field('id,servername as value,create_time')->order('id desc')->select();
        if (input('grant_type_id') == 6) {
            foreach ($list as $k => $v) {
                $list[$k]['value'] = $v['value'] . ' (开服时间：' . date('Y-m-d', $v['create_time']) . ')';
            }
        }

        $this->jsonResult($list, 1, '成功');
    }

    public function apply_welfare()
    {

        $data = input();
        if (!isset($data['grant_type_id'])) {
            $this->jsonResult([], 0, '发放类型必选');
        }
        switch ($data['grant_type_id']) {
            case 1:
                $checkResult = $this->validate($data, 'Welfare.applyEnterGameWelfare');
                break;
            case 2:
                $checkResult = $this->validate($data, 'Welfare.applyEverydayWelfare');
                break;
            case 3:
                $checkResult = $this->validate($data, 'Welfare.applySingleWelfare');
                break;
            case 4:
                $checkResult = $this->validate($data, 'Welfare.applyGrandWelfare');
                break;
            case 5:
                $checkResult = $this->validate($data, 'Welfare.applySingleGrandWelfare');
                break;
            case 6:
                $checkResult = $this->validate($data, 'Welfare.applySevenDayWelfare');
                break;
            case 7:
                $checkResult = $this->validate($data, 'Welfare.applyInitialWelfare');
                break;
            case 8:
                $checkResult = $this->validate($data, 'Welfare.applyMonthWelfare');
                break;
            case 9:
                $checkResult = $this->validate($data, 'Welfare.applyWeekWelfare');
                break;
            default:
                $this->jsonResult([], 0, '类型错误');
        }


        if (true !== $checkResult) {
            $this->jsonResult('', 0, $checkResult);
        }


        $page = isset($data['page']) ? $data['page'] : 1;
        $pageSize = isset($data['pageSize']) ? $data['pageSize'] : 10;
        $welfareService = new WelfareService();
        if ($data['channel_id']) {
            $data['channel_id'] = [$data['channel_id']];
        }

        $channelList = $this->getChannelList(input('game_id', 0));


        $result = $welfareService->wefare($data['grant_type_id'], $data['game_id'], array_column(json_decode(json_encode($channelList), true), 'id'), $channelList, $page, $pageSize, $data, true, false);
        if ($result['code'] == 20000) {
            $this->jsonResult([], 1, '申请成功');
        } else {
            $this->jsonResult([], 0, $result['msg']);
        }

    }

    public function getChannelId()
    {

        $result = $this->getChannelList(input('game_id', 0));

        $this->jsonResult($result, 1, '获取成功');
    }

    private function getChannelList($game_id)
    {

        if ($gameBand = model('GameBand')->whereOr('android_game_id', $game_id)->whereOr('ios_game_id', $game_id)->find()) {

            $where = [$gameBand['android_game_id'], $gameBand['ios_game_id']];
        } else {
            $where = [$game_id];
        }

        return model('SdkGameList')->join('nw_channel', 'nw_channel.id=cy_sdkgamelist.channel_id')->whereIn('cy_sdkgamelist.gameid', $where)->cache('welfare:welfareGrant:getChannelList:' . md5(json_encode($where)), 300)->field('nw_channel.id,nw_channel.name as value')->group('id')->select();
    }
}
