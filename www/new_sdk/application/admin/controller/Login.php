<?php
/**
 * 登录控制器
 */
namespace app\admin\controller;

use app\common\model\Admin as AdminModel;
use app\common\model\Setting;
use app\common\library\Sms;
use app\common\logic\Websocket;
use think\Db;

class Login extends Admin
{
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {}

    /**
     * 用户登录页面
     */
    public function index()
    {
        //删除需要短信验证时的临时session
        if(!empty(session('admin_loginsms_info'))){
            log_message('index: session delete, session_id='.session_id(),'log',LOG_PATH . 'adminlogin/');
            session('admin_loginsms_info', null);
        }
        
        // 已经登录，直接跳转到首页
        $admin_id = session('ADMIN_ID');
        if (!empty($admin_id)) {
            return $this->redirect('/admin/index/index');
        }

        if (request()->isPost()) {
            $result = $this->loginProcess();
        }
        return $this->fetch();
    }


    /**
     * 验证码登录
     */
    public function loginSms()
    {
        $code     = input('post.sms_code');

        if($code==''){
            $this->jsonResult('',0,'短信验证码不能为空');
        }
        
        //需要短信验证时的临时session不存在时
        if(empty(session('admin_loginsms_info'))){
            log_message('loginSms: no session post_data:'.print_r(input(),true).'  session_id='.session_id(),'log',LOG_PATH . 'adminlogin/');
            $this->result('', 0, '短信辅助验证的用户不存在，请重新操作');
        }
        
        $mobile = session('admin_loginsms_info')['mobile'];

        if($mobile==''){
            $this->jsonResult('',0,'手机号不能为空');
        }

        $sms = new Sms();
        $codeResult = $sms->checkCode($mobile, $code);

        if(!$codeResult['status']) {
            $this->jsonResult('', 0, $codeResult['msg']);
        }

        $adminModel     = new AdminModel();
        $adminInfo = $adminModel->where(['status' => 1,'id' => session('admin_loginsms_info')['id'], 'type' => config('ADMIN')])->find();

        if ( empty($adminInfo) ) {
            $this->jsonResult('', 0, '用户名不存在或已被禁用');
        }
        
        session('admin_loginsms_info',null);

        // 登录
        $this->doLogin($adminInfo);
    }


    /**
     * 登录处理
     */
    private function loginProcess()
    {
        $username = input('post.username','','trim');
        $password = input('post.password');

        $result = $this->validate(['username' => $username,'password' => $password],
            [
                ['username', 'require', '请输入用户名'],
                ['password', 'require', '请输入密码'],
            ]);

        if (true !== $result) {
            $this->jsonResult('', 0, $result);
        }

        $adminModel     = new AdminModel();
        $settingModel   = new Setting();
        
        $adminInfo = $adminModel->where(['status' => 1,'username' => $username, 'type' => config('ADMIN')])->find();

        //后台登录密码有效期
        //$password_valid_day = $settingModel::getSetting('PASSWORD_VALID_DAY');
        //短信验证码登录开关
        $isSmsLogin         = (int)$settingModel::getSetting('ADMIN_SMS_SWITCH');

        if ( empty($adminInfo) ) {
            $this->jsonResult('', 0, '用户名不存在或已被禁用');
        }
        elseif ( $adminInfo['password'] != mg_password($password) ) {
            $this->jsonResult('', 0, '用户名或密码错误');
        // } else if (empty($adminInfo['password_update_time']) || (time() - $adminInfo['password_update_time'] > $password_valid_day*86400)) {
        //     $this->jsonResult(['id'=>$adminInfo['id']], 2, '密码已过有效期,请重新设置');
        } else if ( $adminInfo['login_check'] && $isSmsLogin) {
            
            if(empty($adminInfo['mobile'])){
                $this->result('', 0, '用户的手机号未填写，无法进行短信辅助验证');
            }
            else{
                
                session('admin_loginsms_info',['id'=>$adminInfo['id'],'mobile'=>$adminInfo['mobile']]);   //需要短信验证时的临时session
                
                log_message('loginProcess:sms username='.$username.'  mobile='.$adminInfo['mobile'].' session_id='.session_id(),'log',LOG_PATH . 'adminlogin/');
                
                if(empty(session('admin_loginsms_info'))){
                    log_message('loginProcess: no session username='.$username.'  mobile='.$adminInfo['mobile'].' session_id='.session_id(),'log',LOG_PATH . 'adminlogin/');
                }
                
                $this->jsonResult(['mobile' => $adminInfo['mobile']], 3, '');
            }
        }

        // 登录
        $this->doLogin($adminInfo);
    }


    /**
     * 登录处理
     */
    private function doLogin($adminInfo)
    {
        $ws = new Websocket;
        
        //记录Session
        session('ADMIN_ID', $adminInfo->id);
        session('USERNAME', $adminInfo->username);

        //写入登入日志
        Db::name('cy_loginlog')->insert([
            'username'		=> $adminInfo['username'],
            'create_time'	=> NOW_TIMESTAMP,
            'admin_id'		=> $adminInfo['id'],
			'type'			=> 1,
            'ip'			=> $this->request->ip()
        ]);
        
        //Websocket绑定token和后台uid
        $ws->registerToken(session_id(), $adminInfo->id);

        $this->jsonResult('', 1, '登录成功');

        return $this->redirect('/admin/index/index');
    }

    /**
     * 发送验证码
     */
    public function sendSmsCode()
    {
        //需要短信验证时的临时session不存在时
        if(empty(session('admin_loginsms_info'))){
            log_message('sendSmsCode: no session post_data:'.print_r(input(),true).'  session_id='.session_id(),'log',LOG_PATH . 'adminlogin/');
            $this->result('', 0, '短信辅助验证的用户不存在，请重新操作');
        }
        
        $mobile = session('admin_loginsms_info')['mobile'];

        if (empty($mobile)) {
            $this->jsonResult('',0,'手机号不能为空');
        }

        $result = (new \app\common\library\Sms)->sendCode($mobile);
        
        if ($result['status']) {
            $this->success('发送成功，请查收');
        } else {
            $this->error($result['msg']);
        }
    }


    /**
     * 修改登录密码
     */
    public function changePassword(){

        $id             = input('post.id','','intval');
        $old_password   = input('post.old_password');
        $new_password   = input('post.new_password');
        $re_password    = input('post.re_password');

        $data = [
            'password' 			=> $new_password,
            'confirm_password' 	=> $re_password
        ];

        $result = $this->validate($data,
            [
                ['password', 'require|length:6,30|passwordStrength:2', '请输入新密码|新密码长度为 6 - 30|新密码强度不足,至少要包含字母、数字、符号中的两种'],
                ['confirm_password', 'require|length:6,30', '请输入确认密码|确认密码长度为 6 - 30']
            ]);


        if (true !== $result) {
            $this->jsonResult('', 0, $result);
        }
        elseif($new_password!=$re_password){
            $this->jsonResult('', 0, '两次密码不一致');
        }

        $adminModel = new AdminModel;

        $adminInfo = $adminModel->where(['status'=>1,'id'=>$id, 'type' => config('ADMIN')])->find();

        if(empty($adminInfo)) {

            $this->jsonResult('', 0, '用户名不存在或已被禁用');
        }
        elseif($adminInfo['password'] != mg_password($old_password)){

            $this->jsonResult('', 0, '旧密码不正确');
        }
        else{
            if($adminModel->where(['id'=>$id])->update(['password'=>mg_password($new_password),'password_update_time'=>time()]))
            {
                $this->jsonResult('', 1, '密码修改成功，请重新登录');
            }
            else{
                $this->jsonResult('', 0, '密码修改失败');
            }
        }
    }

    /**
     * 用户登出
     */
    public function logout()
    {
        $ws = new Websocket;
        
        //Websocket注销token和后台uid的绑定关系
        $ws->destroyToken(session_id(), session('ADMIN_ID'));
        
        session('ADMIN_ID', null);
        return redirect(url('login/index', [], false, true));
    }
}
