<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/25
 * Time: 10:14
 */

namespace app\admin\controller;
use app\common\controller\Base;
use think\Db;

class CscProblem extends Admin
{
    private $_type = [
        '1' => '账号问题',
        '2' => '充值问题',
        '3' => '申诉问题',
        '4' => '人工服务',
        '5' => '其他',
    ];


    protected function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->cscModel = model('CpsProblem');
    }

    protected function getParam(){
        // 搜索条件
        $where = [];
        $title = $this->request->param('title', '', 'trim');
        if (!empty($title)){
            $where['title'] = ['like','%'.$title.'%'];
        }
        $type = $this->request->param('type', 0, 'intval');
        if (!empty($type)){
            $where['type'] = $type;
        }
        $start_time = input('request.cr_start');
        //开始时间和结束时间不为空时
        if ($start_time != '' && input('request.cr_end') != '') {
            $where['create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime(input('request.cr_end').' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time!= '') {
            $where['create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif (input('request.cr_end') != '') {
            $where['create_time'] = ['<=', strtotime(input('request.cr_end').' 23:59:59')];
        }

        return $where;
    }

    /**
     * 列表
     */
    public function index(){
        // 排序条件
        $orderStr = 'create_time desc';
        $orderUrl = 'asc';
        $order = $this->request->param('order');

        if ($order == 'desc'){
            $orderUrl = 'asc';
            $orderStr = 'create_time desc';
        }elseif ($order == 'asc'){
            $orderStr = 'order asc,create_time asc';
            $orderUrl = 'desc';
        }

        $where = $this->getParam();

        $list = $this->cscModel
            ->where($where)
            ->order($orderStr)
            ->paginate(10, false, array('query' => input('get.')));

        $this->assign('orderUrl',$orderUrl);
        $this->assign('typeList',$this->_type);
        $this->assign('list',$list);
        $this->assign('page',$list->render());
        return $this->fetch();
    }

    /**
     * 查看问题答案
     */
    public function seeAnswer(){
        $id = $this->request->param('id',0,'intval');
        if (empty($id)) $this->error('参数错误！');

        $info = $this->cscModel->where('id',$id)->find();
        if (empty($info)) $this->error('查无数据');

        $this->success($info);
    }

    /**
     * 删除
     */
    public function del(){
        $id = $this->request->param('id',0,'intval');
        if (empty($id)) $this->error('参数错误！');

        $res = $this->cscModel->where('id',$id)->delete();

        if ($res) $this->success('删除成功');
        $this->error('删除失败');
    }

    /**
     * 添加
     */
    public function add(){
        if ($this->request->isPost()){
            $data = [
                'title'=> input('post.title'),
                'answer'=> input('post.answer'),
                'order'=> input('post.order'),
                'type'=> input('post.type'),
            ];
            $result = $this->validate($data,'CscProblem.add');
            if(true !== $result){
                // 验证失败 输出错误信息
                $this->error($result);
            }
            $data['create_time'] = NOW_TIMESTAMP;

            $res = $this->cscModel->insertGetId($data);

            if ($res) $this->success('添加成功！',url('index'));
            $this->error('添加失败！');

        }
        $this->assign('typeList',$this->_type);
        return $this->fetch();
    }

    /**
     * 编辑
     */
    public function edit(){
        $id = $this->request->param('id',0,'intval');
        if (empty($id)) $this->error('参数错误！');

        if ($this->request->isPost()){
            $data = [
                'title'=> input('post.title'),
                'answer'=> input('post.answer'),
                'order'=> input('post.order'),
                'type'=> input('post.type'),
            ];
            $result = $this->validate($data,'CscProblem.edit');
            if(true !== $result){
                // 验证失败 输出错误信息
                $this->error($result);
            }

            if ($this->cscModel->allowField(true)->save($data, ['id' => $id]) !== false ) {
                $this->success('编辑成功',url('index'));
            }
            $this->error($this->cscModel->getError() ?: '编辑失败');
        }

        $info = $this->cscModel->where('id',$id)->find();

        $this->assign('info',$info);
        $this->assign('typeList',$this->_type);
        return $this->fetch();

    }



}