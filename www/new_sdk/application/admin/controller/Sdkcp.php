<?php

/**
 * SDK CP管理
 * Date: 2018/1/22
 * Time: 10:18
 */

namespace app\admin\controller;

use think\Db;
use think\Loader;

class Sdkcp extends Admin
{

    protected $gameList;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $this->selfGameList  = $selfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
    }

    /**
     *SDK密钥列表
     */
    public function index()
    {
        $condition = [];
        $gameId    = $this->request->param('appid', '', 'intval');
        if (!empty($gameId)) {
            $condition ['appid'] = $gameId;
        }
        $appList  = model('Common/Cpurl')->where($condition)->order('id desc')->paginate(20);
        $gameList = $this->relationData(1);
        $gameName = array_column($gameList['applist'], 'name', 'appid');

        $tmpSelfGameList = $gameList['glist'];
		$game_list = array();
		foreach ($tmpSelfGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        $this->assign('game_name', $gameName);
        $this->assign('appList', $appList);
        $this->assign('page', $appList->render());
        return $this->fetch();
    }


    /**
     *
     * appid - gameid 关联数据
     * @return array
     */
    public function relationData($tr = 0)
    {
        $appList  = Db::table('cy_app')->order('id desc')->column('gameid,id');
        $gameList = array_column($this->selfGameList, 'name', 'id');

        if (empty($tr)){
            foreach ($appList as $key => $v) {
                $tmp             = [];
                $tmp['appid']    = $v;
                $tmp['name']     = isset($gameList[ $key ]) ? $gameList[ $key ] : '';
                $appList[ $key ] = $tmp;
            }
            return $appList;
        }else{
            $GList = [];
            foreach ($appList as $key => $v) {
                $tmp             = [];
                $tmp['appid']    = $v;
                $tmp['name']     = isset($gameList[ $key ]) ? $gameList[ $key ] : '';
                $appList[ $key ] = $tmp;
                $GList[]         = ['id'=>$v,'name'=>$tmp['name']];
            }

            return ['applist'=>$appList,'glist'=>$GList];
        }
    }

    /**
     *增
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
			$data['url']	= trim($data['url']);
            if (true !== ($res = $this->validate($data, 'sdkcp'))) {
                $this->error($res);
            }
            $appModel = model('Common/Cpurl');
            if ($appModel->save($data)) {
                $this->success('添加成功', url('index'));
            }
            $this->error($appModel->getError() ?: '添加失败');
        }

        $tmpSelfGameList = $this->relationData(1)['glist'];
		$game_list = array();
		foreach ($tmpSelfGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        return $this->fetch();
    }


    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = model('Common/Cpurl');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }


    /**
     *编辑SDK密钥
     */
    public function edit()
    {
        $id       = $this->request->param('id', '', 'intval');
        $appModel = model('Common/Cpurl');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data           = $this->request->post();
			$data['url']	= trim($data['url']);
            $appKeyValidate = Loader::validate('sdkcp');
            if (!$res = $appKeyValidate->check($data)) {
                $this->error($appKeyValidate->getError());
            }
            if ($appModel->save($data, ['id' => $id]) !== false) {
                $this->success('修改成功', url('index'));
            }
            $this->error($appModel->getError() ?: '修改失败');
        }

        $tmpSelfGameList = $this->relationData(1)['glist'];
		$game_list = array();
		foreach ($tmpSelfGameList as $game) {
              $game_list[ $game['id']] = $game;
        }
        $this->assign('game_list', $game_list);

        $this->assign('data', $data);
        return $this->fetch();
    }

}