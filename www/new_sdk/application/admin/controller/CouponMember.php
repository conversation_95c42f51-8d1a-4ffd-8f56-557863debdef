<?php

namespace app\admin\controller;

use app\common\model\ComplexMembers as MembersModel;
use app\common\model\ComplexChannelModel;
use app\common\model\ComplexPay;
use GuzzleHttp\Exception\RequestException;
use think\Db;
use app\common\library\MakeReport;
use GuzzleHttp\Client;

class CouponMember extends Admin
{
    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }

    public function index()
    {
        $where = [];

        //查询参数
        $username = input('username');
        $name = input('name');
        $is_use = input('is_use');
        $state = input('state');
        if (isset($name) && !empty($name)) {
            $where['c.name'] = ['like', "$name%"];
        }
        if (isset($username) && !empty($username)) {
            $where['b.username'] = $username;
        }


        if (isset($is_use) && !empty($is_use)) {
            $where['a.is_use'] = $is_use;
        }
        if (isset($state) && !empty($state)) {
            $where['a.state'] = $state;
        }
        $couponMemberModel = model('CouponMember');
        $list = $couponMemberModel
            ->alias('a')
            ->join('cy_members b', 'a.member_id=b.id')
            ->join('cy_coupon c', 'a.coupon_id=c.id')
            ->where($where)
            ->field('a.*,b.username,c.name,c.money,c.min_money,c.start_time as coupon_start_time,c.end_time as coupon_end_time,c.type_id,a.yql_id')
            ->order('id desc')
            ->paginate(10, false, ['query' => input('get.')])->each(function ($item, $key) {
                $dayTime = strtotime(date('Y-m-d'));

                if ($item['type_id'] == 1) {
                    if ($item['coupon_end_time'] < $dayTime) {
                        $item['state_str'] = '<span style="color: red">(已过期.)</span>';
                    } else {
                        $item['state_str'] = '';
                    }
                } else {
                    if (strtotime($item['end_time']) < $dayTime) {
                        $item['state_str'] = '<span style="color: red">(已过期)</span>';
                    } else {
                        $item['state_str'] = '';
                    }
                }
                return $item;
            });


        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    /**
     *增
     */
    public function add()
    {

        if ($this->request->isPost()) {
            $data = $this->request->post();
            $coupon = model('Common/Coupon')->where('id', $data['coupon_id'])
                ->whereRaw(sprintf('(type_id=1 and end_time>=%s) or (type_id=2)', Db::quote(date('Y-m-d'))))->find();
            if (!$coupon) {
                $this->error('记录不存在');
            }

            $member_ids = explode("\r\n", $data['member_ids']);
            $member_ids = array_map('filterAndTrimInput', $member_ids);
            $member_ids = array_unique(array_filter($member_ids));
            $member = model('Members')->whereIn('username', $member_ids)->column('id', 'username');
            $arr = [];
            foreach ($member_ids as $k => $v) {
                if (isset($member[$v])) {
                    $arr[] = [
                        'username' => $v,
                        'state' => '<span style="color: #0bb20c">正常</span>'
                    ];
                } else {
                    $arr[] = [
                        'username' => $v,
                        'state' => '<span style="color: red">账号不存在</span>'
                    ];
                }
            }
            $this->assign('ids', implode(',', $member));
            $this->assign('members', $arr);
            $this->assign('coupon', $coupon);
            return $this->fetch('confirm');
        }

        $coupon = model('Coupon')->where('state', 1)
            ->whereRaw(sprintf('(type_id=1 and end_time>=%s) or (type_id=2)', Db::quote(date('Y-m-d'))))
            ->order('id desc')->select();
        $this->assign('coupon', $coupon);
        return $this->fetch();
    }

    public function confirm()
    {
        $data = $this->request->post();

        if (!$data['ids']) {
            $this->error('账号不存在');
        }
        if (!$data['coupon_id']) {
            $this->error('代金券不存在');
        }

        $coupon = model('Common/Coupon')->where('id', $data['coupon_id'])
            ->whereRaw(sprintf('(type_id=1 and end_time>=%s) or (type_id=2)', Db::quote(date('Y-m-d'))))->find();
        if (!$coupon) {
            $this->error('记录不存在');
        }


        $member_ids = array_map('filterAndTrimInput', explode(',', $data['ids']));
        $member_ids = array_unique(array_filter($member_ids));
        $member = model('Members')->whereIn('id', $member_ids)->column('id');

        if (!$member) {
            $this->error('账号不存在');
        }
        $members = [];

        if ($coupon['type_id'] == 1) {
            $start_time = 0;
            $end_time = 0;
        } else {
            $start_time = strtotime(date('Y-m-d'));
            $end_time = strtotime(date('Y-m-d')) + 86400 * $coupon['day'] - 1;
        }
        foreach ($member as $k => $v) {
            $members[] = [
                'coupon_id' => $coupon['id'],
                'member_id' => $v,
                'start_time' => $start_time,
                'end_time' => $end_time,
                'examine' => 2,
                'code' => makeUniqueid('Q') . 'A',
            ];
        }

        model('Common/Coupon')->startTrans();
        if (!model('Common/Coupon')->where('id', $coupon['id'])->update(['grant_num' => $coupon['grant_num'] + count($member),'receive_num' => $coupon['receive_num'] + count($member)])) {
            model('Common/Coupon')->rollback();
            $this->error('生成失败');
        }
        if (model('couponMember')->insertAll($members)) {
            model('Common/Coupon')->commit();
            $this->success('生成成功', url('index'));
        } else {
            model('Common/Coupon')->rollback();
            $this->error('生成失败');
        }
    }

    public function frozen()
    {
        $id = input('id');
        $coupon = model('Common/CouponMember')->where('id', $id)->find();

        if (!$coupon) {
            $this->error('记录不存在');
        }

        if ($coupon['state'] == 2) {
            $this->success('冻结成功', url('index'));
        }

        if (model('common/CouponMember')->where('id', $id)->update(['state' => 2])) {
            $this->success('冻结成功', url('index'));
        } else {
            $this->error('冻结失败');
        }
    }

    public function thaw()
    {
        $id = input('id');
        $coupon = model('common/CouponMember')->where('id', $id)->find();

        if (!$coupon) {
            $this->error('记录不存在');
        }

        if ($coupon['state'] == 1) {
            $this->success('解冻成功', url('index'));
        }

        if (model('Common/CouponMember')->where('id', $id)->update(['state' => 1])) {
            $this->success('解冻成功', url('index'));
        } else {
            $this->error('解冻失败');
        }
    }

    public function approved()
    {
        $data = input();
        $state = $data['state'];
        $ids = explode(',', $data['ids']);
        unset($data['ids']);
        if (empty($state)) {
            $this->error("系统错误");
        }
        if (!in_array($state, [1, 2])) {
            $this->error('状态错误');
        }
        if (empty($ids)) {
            $this->error("请选择记录");
        }
        $data = model('common/CouponMember')->whereIn('id', $ids)->column('id');
        if ($data) {
            if (model('common/CouponMember')->whereIn('id', $data)->update(['state' => $state])) {

                $this->success("审核成功");
            } else {
                $this->error('审核失败');
            }
        }

    }
}
