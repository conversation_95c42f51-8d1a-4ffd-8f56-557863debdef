<?php

namespace app\admin\controller;
use app\common\model\Channel;
use app\common\library\FileUpload;
use think\Db as DB;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use think\Exception;

class ChannelManage extends Admin
{
    protected function _initialize()
    {
        parent::_initialize();

        $this->gameList  = $gameList = model('Common/Game')->getAllByCondition('id,name');

        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;

    }

    /**
     * 礼包列表
     * @return mixed
     */
    public function search()
    {
        $gameList = $this->selfGameList;
        $this->assign('game_list', $gameList);

        return $this->fetch();
    }

    //渠道修改页面
    public function change()
    {
        $game_list = model('Common/Game')->getAllByCondition('id,name');

        //外部渠道列表
        $outer_channel_list = model('Common/Channel')->getAllByCondition('id,name',['flag'=>3]);
        //内外部渠道列表
        $channel_list       = model('Common/Channel')->getAllByCondition('id,name',['flag'=>['in','2,3'],'status' => 1]);

        $this->assign('game_list', $this->selfGameList);
        $this->assign('outer_channel_list', $outer_channel_list);
        $this->assign('channel_list', $channel_list);

        return $this->fetch();
    }

    /**
     * 查询渠道的父级渠道
     */
    public function queryParentChannelName()
    {

        $channel = input('post.channel', 0, 'intval');
        if (empty($channel)) {

            $this->error('查询的渠道不能为空');
        }

        $channel_info = model('channel')->where(['id' => $channel])->field('id,parent_id,name')->find();
        //查询父渠道名称
        if (empty($channel_info)) {
            $this->error('查询的渠道无父级渠道');
        }
        $parent_channel_name = model('channel')->where(['id' => $channel_info['parent_id']])->field('name')->find();
        if (empty($parent_channel_name)) {
            $this->error('查询父级渠道名称出错');
        }

        $this->success('ok', '', $parent_channel_name['name']);
    }

    /**
     * 修改指定渠道的父级，同时修正它的所有下级的id_path数据
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function ChangeChannelRelation()
    {
		die();
        $channel_id = input('channel_id', 0, 'intval'); // 要修改的渠道id
        $new_pid    = input('new_pid', 0, 'intval');    // 要挂载到哪个渠道下

        if (empty($channel_id) || empty($new_pid)) {
            $this->error('填写完整信息');
        }

        if($channel_id == config('TOP_CHANNEL_ID')) {
            $this->error('顶级渠道不可修改');
        }
        $current_channel = model('channel')->where(['id' => $channel_id])->field('name,id_path,parent_id')->find();
        if (empty($current_channel)) {
            $this->error('渠道id错误');
        }
        // 先查出新的父级渠道的id_path信息，不然会导致层级错乱
        $new_parent = model('channel')->field('flag,name,id_path,point_show,taptap,show_full_account')->where(['id' => $new_pid])->find();
        if (empty($new_parent)) {
            $this->error('新的归属渠道信息错误，查无此渠道');
        }
        elseif($new_parent['flag']==4){
            $this->error('新的归属渠道不能为聚合渠道');
        }

        $channelIdList = get_child_channel_arr($channel_id);
        if (in_array($new_pid, $channelIdList)) {
            $this->error('禁止将子渠道改为归属渠道');
        }

        $new_data['parent_id']  = $new_pid;
        $new_data['id_path']    = ($new_parent['flag']==2) ? ','.$new_pid.',' : $new_parent['id_path'] . $new_pid . ',';
        // 判断是否要更改 渠道推广后台【游戏点位】和【游戏推荐】和用户账号显示明密文的菜单权限
        $point_show = $taptap = $show_full_account = 1;
        if ($new_parent['point_show'] == 0){
            $new_data['point_show'] = 0;
            $point_show = 0;
        }
        if ($new_parent['taptap'] == 0){
            $new_data['taptap'] = 0;
            $taptap = 0;
        }
        if ($new_parent['show_full_account'] == 0){
            $new_data['show_full_account'] = 0;
            $show_full_account = 0;
        }

        // 启动事务
        Db::startTrans();

        $result = true;
        /**
         * 新的上级渠道的 渠道推广后台【游戏点位】和【游戏推荐】的菜单权限 为显示状态，不去改变子渠道的菜单权限
           要更改的渠道的子渠道 的 渠道推广后台【游戏点位】和【游戏推荐】的菜单权限，也一起发生变化
         * 父级某个菜单权限关闭，则所有子渠道该菜单权限关闭
         * 父级某个菜单权限开启，则所有子渠道的菜单权限不发生变化
         */
        if ($new_parent['point_show'] == 0 && $new_parent['taptap'] == 0 && $new_parent['show_full_account'] == 0){
            $channel_ChildArr = model('Channel')->getChildIds($channel_id);
            $list = ['id'=>$channel_id,'point_show'=>$point_show,'taptap'=>$taptap,'show_full_account'=>$show_full_account];
            foreach($channel_ChildArr as $v){
                $list[] = ['id'=>$v,'point_show'=>$point_show,'taptap'=>$taptap,'show_full_account'=>$show_full_account];
            }
            $result = model('Channel')->saveAll($list);
        }
        else if($new_parent['point_show'] == 0 && $new_parent['taptap'] == 0){
            $channel_ChildArr = model('Channel')->getChildIds($channel_id);
            $list = ['id'=>$channel_id,'point_show'=>$point_show,'taptap'=>$taptap];
            foreach($channel_ChildArr as $v){
                $list[] = ['id'=>$v,'point_show'=>$point_show,'taptap'=>$taptap];
            }
            $result = model('Channel')->saveAll($list);
        }
        else if($new_parent['point_show'] == 0 && $new_parent['show_full_account'] == 0){
            $channel_ChildArr = model('Channel')->getChildIds($channel_id);
            $list = ['id'=>$channel_id,'point_show'=>$point_show,'show_full_account'=>$show_full_account];
            foreach($channel_ChildArr as $v){
                $list[] = ['id'=>$v,'point_show'=>$point_show,'show_full_account'=>$show_full_account];
            }
            $result = model('Channel')->saveAll($list);
        }
        else if($new_parent['taptap'] == 0 && $new_parent['show_full_account'] == 0){
            $channel_ChildArr = model('Channel')->getChildIds($channel_id);
            $list = ['id'=>$channel_id,'taptap'=>$taptap,'show_full_account'=>$show_full_account];
            foreach($channel_ChildArr as $v){
                $list[] = ['id'=>$v,'taptap'=>$taptap,'show_full_account'=>$show_full_account];
            }
            $result = model('Channel')->saveAll($list);
        }
		elseif ($new_parent['point_show'] == 0){
            $channel_ChildArr = model('Channel')->getChildIds($channel_id);
            $list = ['id'=>$channel_id,'point_show'=>$point_show];
            foreach($channel_ChildArr as $v){
                $list[] = ['id'=>$v,'point_show'=>$point_show];
            }
            $result = model('Channel')->saveAll($list);
        }elseif ( $new_parent['taptap'] == 0){
            $channel_ChildArr = model('Channel')->getChildIds($channel_id);
            $list = ['id'=>$channel_id,'taptap'=>$taptap];
            foreach($channel_ChildArr as $v){
                $list[] = ['id'=>$v,'taptap'=>$taptap];
            }
            $result = model('Channel')->saveAll($list);
        }
		elseif ( $new_parent['show_full_account'] == 0){
            $channel_ChildArr = model('Channel')->getChildIds($channel_id);
            $list = ['id'=>$channel_id,'show_full_account'=>$show_full_account];
            foreach($channel_ChildArr as $v){
                $list[] = ['id'=>$v,'show_full_account'=>$show_full_account];
            }
            $result = model('Channel')->saveAll($list);
        }

        /**
         * 改渠道下的游戏补点 :
         *      是否有没有配置补点：若无，渠道转移成功；若有判断是否和上级发送冲突
         *          1、上级配置了：失效（需要判断生效时间和失效时间）
         *          2、上级未配置，不动补点规则
         */
        $pointRatioModel = model("GameExtraPointRatio");
        $point_ratio_arr = $pointRatioModel->where(['channel_id'=>$channel_id,'status'=>['in','0,1,2,3']])->field('id,year_month,begin_time,end_time,game_extra_point_id,game_id,status')->select();
        $run_point = true;

        if (count($point_ratio_arr)){
            // 获取今日00：00：00 时间戳
            $time_line = strtotime(date('Y-m-d',time()));
            // 判断 新上级是否配置 补点规则
            foreach ($point_ratio_arr as $v){
                $numArr = $pointRatioModel->where(['game_id'=>$v['game_id'],'year_month'=>$v['year_month'],'channel_id'=>['in',$new_data['id_path']],'game_extra_point_id'=>$v['game_extra_point_id']])->column('channel_id');
                // 新上级没有配置补点
                if ($run_point && !count($numArr)){
                    break;
                }
                // status= 2 or 3 （有参与结算统计）, 要区分实际正在生效、实际已失效、实际还未生效
                // 新上级配置补点，status = 0 OR 1 , 实际还未生效:  设为 5失效
                if ($run_point && ( $v['status'] == 0 || $v['status'] == 1 || $v['begin_time'] >= $time_line)){
                    $run_point = $pointRatioModel->save(['status'=>5,'update_time'=>time(),'update_user'=>mg_get_current_admin_id()],['id'=>$v['id']]);
                    $run_point = $run_point ? true : false;
                }
                // 实际正在生效 : 有参与结算，失效实际设为 $time_line-1 status=3
                if ($run_point &&  $v['begin_time'] < $time_line && $v['end_time'] >= $time_line){
                    $run_point = $pointRatioModel->save(['end_time'=>$time_line-1,'status'=>3,'update_time'=>time(),'update_user'=>mg_get_current_admin_id()],['id'=>$v['id']]);
                    $run_point = $run_point ? true : false;
                }

                // 实际已失效: 不参与结算，不另作 5失效处理
                /*if ($v['end_time'] < $time_line){

                }*/

                if (!$run_point){
                    break;
                }
            }
        }

        // 更新当前渠道的记录
        if ($channel_result = model('channel')->where(['id' => $channel_id])->update($new_data) && $result && $run_point) {
            $old_name = model('channel')->field('name')->where(['id' => $current_channel['parent_id']])->find();
            $message  = '渠道：' . $current_channel['name'] . '，归属渠道由：' . $old_name['name'] . ',修改至:' . $new_parent['name'];
            $this->insertLog($this->current_node, $message,24);

            // 提交事务
            Db::commit();
            $this->success('修改成功', '', $message);
        }

        // 回滚事务
        Db::rollback();
        $this->error('修改失败');
    }


    public function getSubMember()
    {
        $data = input('post.');
        $info = model('MemberChannelGame')->where(['username' => $data['username']])->field('id,channel_id')->find();
        if (!$info) {
            $this->jsonResult([], '当前玩家不存在！', -100);
        }

        $info = model('Subaccount')->where(['game_id' => $data['game_id'], 'member_id' => $info['id']])->field('id,sub_username')->find();
        if (!$info) {
            $this->jsonResult([], '当前玩家没有子账户！', -100);
        }
        $this->jsonResult(['channel_id' => $info[''], 'info' => $info], 'success', 200);
    }

    /**
     * 修改玩家游戏绑定渠道
     *
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function changeMemberRelationChannel()
    {
        $fileUpload = new FileUpload();
        $data = input('post.');
        $username = isset($data['username']) ? $data['username'] : '';
        $game_id = isset($data['game_id']) ? $data['game_id'] : '';
        $channel_name = isset($data['channel_name']) ? $data['channel_name'] : ''; // 渠道名称
        $channel_id = isset($data['channel_id']) ? $data['channel_id'] : '';       // 渠道id
        $remark = isset($data['remark']) ? $data['remark'] : '';                   // 渠道id
        $type = isset($data['type']) ? $data['type'] : 1;                          // 换绑类型
        $action = $data['action'];
        $sub_id = isset($data['sub_id']) ? $data['sub_id'] : '';  // 子账户ID

        // halt($file);
        if (empty($username) || empty($game_id)) {
            $this->error('请填写玩家账号');
        }
        if (empty($game_id)) {
            $this->error('请选择对应游戏');
        }

        $memberModel = model("Members");
        $member_info = $memberModel->field('id,gameid,channel_id')->where('username', $username)->find();
        if (empty($member_info)) {
            $this->error('无该玩家账号信息');
        }

        $gameAccountInfo = model('Subaccount')->where(['game_id' => $game_id, 'member_id' => $member_info['id']])->field('id,sub_username')->find();
        if (empty($gameAccountInfo)) {
            $this->error('无关联的子账户信息！');
        }

        $cid = model('MemberChannelGame')->where(['member_id' => $member_info['id'], 'game_id' => $game_id])->column('channel_id');
        if (empty($cid)) {
            $this->error('无关联的渠道信息');
        }
        $condition['id'] = $cid[0];
        $channel_info = [];
        //查询渠道名称
        if ($action == 'query') {
            $status = 2;
            $channel_info = model('channel')->where($condition)->field('name')->find();

            // 查询子账户
            $this->success('ok', '', ['name' => $channel_info['name'], 'sub_info' => $gameAccountInfo ?? []]);
        }
        // halt($channel_id);
        //修改绑定的渠道
        if ($action == 'change') {
            if (empty($channel_id)) {
                $this->error('请选择新的渠道');
            }

            // $data['channel_id'] = $channel_id;
            $new_channel_info = model('Channel')->where(['id' => $channel_id])->field('name,level')->find();
			if($new_channel_info['level'] <> 3){
				$this->error('玩家只能换绑到推广员账号');
			}

            $old_channel_info = model('Channel')->where(['name' => $channel_name])->field('id,name,level')->find();
			if(!$old_channel_info){
				$this->error('玩家所属原推广员账号不存在');
			}

			$MemberChannelGameInfo = model('MemberChannelGame')->where(['member_id' => $member_info['id'], 'game_id' => $game_id])->find();
			if($MemberChannelGameInfo){
				if($MemberChannelGameInfo['channel_id'] == $channel_id){
					$this->error('玩家所属原推广员账号和要换绑的推广员账号相同，您未做任何绑定修改');
				}
			}
			else{
				$this->error('玩家尚未进入该游戏，不属于任何推广员');
			}

            $rebindApplyCnt = model('PlayerRebindApply')->where(['userid' => $member_info['id'],'game_id' => $game_id,'status' => 0])->count();
			if($rebindApplyCnt){
				$this->error('玩家在该游戏已存在待审核换绑申请,不能重复提交');
			}
            switch ($type) {
                case '1':
                    $old_partent = ltrim(db::name('nw_channel')->where(['id'=>$cid[0]])->value('id_path'),',');
                    $new_partent = ltrim(db::name('nw_channel')->where(['id'=>$channel_id])->value('id_path'),',');
                    if (substr($old_partent,0,stripos($old_partent,','))!=substr($new_partent,0,stripos($new_partent,','))) {
                        $this->error('必须在同一个联盟下');
                    }
                    $file1_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file1'));//公会或联盟同意换绑截图或不同B账户同属于他的说明
                    if ( !$file1_path) {
                        $this->error('附图上传失败，' . $fileUpload->getError());
                    }
                break;
                case '2':
                    $file1_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file1'));//发链接,带时间引导图
                    if ( !$file1_path) {
                        $this->error('附图1上传失败，' . $fileUpload->getError());
                    }
                    $file2_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file2'));//玩家说角色名图
                    if ( !$file2_path) {
                        $this->error('附图2上传失败，' . $fileUpload->getError());
                    }
                    $file3_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file3'));//原公会同意截图
                    if ( !$file3_path) {
                        $this->error('附图3上传失败，' . $fileUpload->getError());
                    }
                break;
                case '3':
                    $oldPoint   = isset($data['oldPoint']) ? $data['oldPoint'] : '';  // 原点位
                    $deductionAmount   = isset($data['deductionAmount']) ? $data['deductionAmount'] : '';  // 扣款
                    $newPoint   = isset($data['newPoint']) ? $data['newPoint'] : '';  // 新点位
                    $getAmount   = isset($data['getAmount']) ? $data['getAmount'] : '';  // 打款金额
                    $playerAmount   = isset($data['playerAmount']) ? $data['playerAmount'] : '';  // 玩家产生流水
                    if ($oldPoint === '' || $newPoint === '') {
                        $this->error('请填写点位信息');
                    }
                    if ($deductionAmount === '' || $getAmount === '') {
                        $this->error('请填写金额信息');
                    }
                    if ($playerAmount === '') {
                        $this->error('请填写产生流水信息');
                    }
                    $file1_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file1'));//发链接,带时间引导图
                    if ( !$file1_path) {
                        $this->error('附图1上传失败，' . $fileUpload->getError());
                    }
                    $file2_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file2'));//玩家说角色名图
                    if ( !$file2_path) {
                        $this->error('附图2上传失败，' . $fileUpload->getError());
                    }
                    $file3_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file3'));//原公会同意截图
                    if ( !$file3_path) {
                        $this->error('附图3上传失败，' . $fileUpload->getError());
                    }

                break;
	        case '4':
//                    $file1_path = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000 * 3)->set('dir','image/changeBindPic/')->upload(request()->file('file1'));//公会或联盟同意换绑截图或不同B账户同属于他的说明
//                    if ( !$file1_path) {
//                        $this->error('附图上传失败，' . $fileUpload->getError());
//                    }
                if(empty($remark)){
                        $this->error("手动换绑备注必填");
                }
                break;
                default:
                    $this->error('请选择绑定方式');
                    break;
            }
			Db::startTrans();
			try{
				/*
				if ($result = model('MemberChannelGame')->where(['member_id' => $member_info['id'], 'game_id' => $game_id])->update($data)) {

					$gamename         = get_game_name($game_id);

					$message = '玩家' . $username . ',在游戏:' . $gamename . '的归属渠道由：' . $channel_name . ',变更为:' . $new_channel_info['name'] . '的信息;';
					$this->insertLog($this->current_node, $message,24);
					// 插入玩家历史记录
					Db::table('cy_member_history')->insert([
						'userid'      => $member_info['id'],
						'channel'    => $gamename . "，" . $new_channel_info['name'],
						'ip'          => request()->ip(),
						'create_time' => time(),
						'admin_id'    => session('ADMIN_ID')
					]);
					*/
                $gamename = get_game_name($game_id);
                $message = '您的申请：玩家' . $username . ',在游戏"' . $gamename . '"的归属渠道由："' . $channel_name . '",变更为"' . $new_channel_info['name'] . '" 已成功提交，请耐心等待审核;';

                $rebindData = array();
                $rebindData['userid'] = $member_info['id'];
                $rebindData['username'] = $username;
                $rebindData['game_id'] = $game_id;
                $rebindData['game_name'] = $gamename;
                $rebindData['origin_cuserid'] = $old_channel_info['id'];
                $rebindData['origin_cusername'] = $channel_name;
                $rebindData['cuserid'] = $channel_id;
                $rebindData['cusername'] = $new_channel_info['name'];
                $rebindData['create_time'] = time();
                $rebindData['cmmt'] = '后台管理员申请换绑';
                $rebindData['status'] = 0;
                $rebindData['sub_id'] = $sub_id;
                $rebindData['admin_id'] = session('ADMIN_ID');
                $rebindData['admin_name'] = session('USERNAME');
                $rebindData['remark'] = $remark;
                switch ($type) {
                    case '1':
                        $rebindData['picture1'] = $file1_path;
                        break;
                    case '2':
                        $rebindData['picture1'] = $file1_path;
                        $rebindData['picture2'] = $file2_path;
                        $rebindData['picture3'] = $file3_path;
                        break;
                    case '3':
                        $rebindData['picture1'] = $file1_path;
                        $rebindData['picture2'] = $file2_path;
                        $rebindData['picture3'] = $file3_path;
                        $rebindData['oldPoint'] = floatval($oldPoint);
                        $rebindData['deductionAmount'] = floatval($deductionAmount);
                        $rebindData['newPoint'] = floatval($newPoint);
                        $rebindData['getAmount'] = floatval($getAmount);
                        $rebindData['playerAmount'] = floatval($playerAmount);
                        break;
                }
					$insertRebindId = model('PlayerRebindApply')->insert($rebindData);

//					if($insertRebindId){
//						$template = '有新的玩家换绑申请：管理员 "'.session('USERNAME').'" 发起玩家换绑申请，请及时安排处理，时间：'.date('Y-m-d H:i:s');
//						$ddurl = OPERATE_DINGDING_URL;
//						curlDD($template, $ddurl,true);
//					}
					/*
					if($insertRebindId){
						$rebindPayFromTime = strtotime(date('Y-m-d',(time()-((date('w',time())==0?7:date('w',time()))-1)*24*3600)));
						$updPayData = array();
						$updPayData['channel_id']	=	$rebindData['cuserid'];
						$updPayResult = model('Pay')->where(['userid' => $rebindData['userid'],'gameid' => $rebindData['game_id'],'channel_id' => $rebindData['origin_cuserid'],'create_time'=> ['egt', $rebindPayFromTime]])->update($updPayData);
						if ($updPayResult) {
							$message .= '该玩家本周后消费订单也已做换绑。';
						}
					}
					*/
					Db::commit();
				/*
				}
				else{
					throw new Exception("您未做任何绑定修改");
				}
				*/
			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();
				$this->error('玩家换绑操作失败'.$e->getMessage());
			}
			$this->success($message);
        }
    }

    /**
     * 渠道查询方法
     */
    public function queryChannelList()
    {

        $username     = input('post.username', '', 'trim');   // 玩家账号
        $game_id      = input('post.game_id', 0, 'intval');
        $channel      = input('post.channel', 0, 'trim');  // 渠道名称
        $channel_id   = input('post.channel_id', 0, 'intval');  // 渠道id
        $account_type = input('post.account_type', 0, 'trim');
        $channel_type = input('post.channel_type', 0, 'filterAndTrimInput'); // 按渠道信息查询时，按渠道名还是按渠道id

        $condition = $result = [];


        if ('channel' == $account_type) { //直接查询渠道信息
            if ('name' == $channel_type) {
                if (empty($channel)) {
                    $this->error('渠道名不能为空');
                }
                $condition['name'] = $channel;

                $info = DB::table('nw_channel')->field('id')->where($condition)->find();
                if (empty($info)) {
                    $this->error('渠道名对应的渠道商不存在');
                }
                $condition['id'] = $info['id'];
            }
            if ('id' == $channel_type) {
                if (empty($channel_id)) {
                    $this->error('渠道ID不能为空');
                }
                $condition['id'] = $channel_id;
            }
            $result = get_channel_arr($condition['id']);
            if (empty($result)) {
                $this->error('无相关渠道信息');
            }

        }


        $memberModel = model("Members");
        if ('player' == $account_type) { // 通过关联关系查询渠道信息
            if (empty($username) || empty($game_id)) {
                $this->error('玩家账号或者游戏id不能为空');
            }
            $member_id = $memberModel->field('id')->where('username', $username)->find();
            if (empty($member_id)) {
                $this->error('无该玩家账号信息');
            }
            $channel_id = DB::table('cy_member_channel_game_rel')->field('channel_id')->where([
                'member_id' => $member_id['id'],
                'game_id'   => $game_id,
            ])->find();

            if (empty($channel_id) || !isset($channel_id['channel_id'])) {
                $this->error('无关联的渠道信息');
            }

            $result = get_channel_arr($channel_id['channel_id']);
            if (empty($result)) {
                $this->error('无相关渠道信息');
            }
        }

        $this->success('修改成功', '', $result);
    }
    /**
     * 查询下级渠道页面
     */
    public function queryChildChannel()
    {
        $channel_id         = input('request.channel_id', '', 'intval');
        $is_down = input('request.download', '0', 'intval');

		$list = array();
		if(!empty($channel_id)){
			$condition = $result = [];
			$condition['parent_id'] = $channel_id;

			$list = DB::table('nw_channel')->field('id,name')->where($condition)->select();
			$this->assign('list', $list);          //渠道列表

			if($is_down>0){
				if(!empty($list)){
					$title = "下级渠道列表_".date('YmdHis');
					$this->downloadexls($list,$title,'queryChildChannel');
					exit();
				}
				else{
					echo '<script>alert("报表暂无数据");history.back();</script>';
					exit();
				}
			}
		}

        $channel_list = (new Channel())->getAllByCondition('id,name',[],'name asc');
        $this->assign('channel_list', $channel_list);          //渠道列表

		return $this->fetch("queryChildChannel");;
    }
    public function channelConfig() {
        if (request()->isPost()) {
            $register_period = $this->request->param('register_period', 0, 'intval');
            $mobile       = $this->request->param('mobile', '', 'trim');
            $register_most   = $this->request->param('register_most', 0, 'intval');
            $status   = $this->request->param('status', 0, 'intval');
         //   $free_channel       = $this->request->param('free_channel', '', 'trim');
            $remark       = $this->request->param('remark', '', 'filterAndTrimInput');

			//功能开启时必须配置正确
			if($status>0){
				if ( 0 >= $register_period) {
					$this->error('注册预警时间段必须配置,且必须为正整数');
				}
				if ($register_most < 0) {
					$this->error('注册预警个数必须配置,且必须为正整数');
				}
				if (!$mobile) {
					$this->error('预警手机必须配置');
				}
			}

			$mobileArray = explode("|",$mobile);
			while(list($key,$val)=@each($mobileArray)){
				if (trim($val)) {
					if(! preg_match("/^1\d{10}$/", trim($val))){
						$this->error('存在手机号码不正确,请确认');
					}
					else{
						$mobileArray[$key] = trim($val);
					}
				}
				else{
					unset($mobileArray[$key]);
				}
			}
			$mobileArray = array_unique($mobileArray);
			$mobile = implode("|",$mobileArray);

          //  $id   = $this->request->param('id', 0, 'intval');
			//该配置表暂时默认只有一笔记录,直接写定
			$id = 1;
            $data = [
                'mobile'        => $mobile,
                'register_period'    => $register_period,
                'register_most'    => $register_most,
				'status'    => $status,
                'remark'        => $remark,
                'update_time'   => time()
            ];
            $data['free_channel'] = implode(',', input('free_channel/a', [])) . ',';
		//	var_dump($data);

            $update = model('channelConfig')->where(['id' => $id])->update($data);

            if ( false === $update) {
                $this->error('更新失败');
            } else {
                $content  = '功能状态：'.$status;
                $content .= "，注册预警时间段：{$register_period}分钟";
                $content .= "，注册预警个数：{$register_most}";
                $content .= "，预警手机：".$mobile;
                $content .= "，备注：".$remark;
                $content .= "，白名单渠道：".$data['free_channel'];
				$this->insertLog($this->current_node, $content, 52);
                $this->success('更新成功', url('ChannelManage/channelConfig') );
            }
        } else {
          //  $id = $this->request->param('id', 0, 'intval');
		  	//该配置表暂时默认只有一笔记录,直接写定
			$id = 1;
            if ( empty($id) ) {
                $this->error('参数出错');
            }

            $info = model('channelConfig')->field(['id', 'status', 'register_period', 'free_channel', 'register_most', 'mobile', 'remark'])->find($id);
            if ( empty($info) ) {
                $this->error('配置数据不存在');
            }
			$free_channel = '';
            $this->assign('vo', $info);
			$this->assign('free_channel', ltrim($info['free_channel'], ','));

            return $this->fetch("channelConfig");
        }
    }
}
