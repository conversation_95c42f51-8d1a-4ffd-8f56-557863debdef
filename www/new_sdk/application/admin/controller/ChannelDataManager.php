<?php
/**
 * Created by PhpStorm.
 * User: wxm
 * Date: 2019/5/7
 * Time: 11:06
 */

namespace app\admin\controller;

use think\Db;

class ChannelDataManager extends Admin{

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 游戏点位 查询条件获取
     */
    public function getIndexParam(){
        $game_id           = $this->request->request('game_id'); //游戏id
        $username          = $this->request->request('username'); //玩家账号
        $order             = $this->request->request('order_id');// 订单编号
        $channel_id        = $this->request->request('channel_id');// 渠道编号
        $type              = input('request.recharge_type', 0, 'intval');
        $where             = [];

//        // 未软删除的数据
//        $where['delete_time'] = 0;

        if (input('request.recharge_type') != '') {

           // dump(  $where['v.content'] = '' );
            $where['v.content'] = ['LIKE', "%\"type\";i:{$type};%"];
        }

        if (!empty($game_id)){
            $where['his.game_id'] = $game_id;
        }

        if (!empty($order) ) {
            $where['v.bill_id'] = $order;
        }

        if (!empty($username) ) {
            $account_id = model('Common/Members')->where(['username' => $username])->value('id');
            if ( ! empty($account_id) ) {
                $where['his.account_id'] = (int)$account_id;
            }
        }

        if (!empty($channel_id) ) {
            if ( ! empty($channel_id) ) {
                $where['his.src_id'] = (int)$channel_id;
            }
        }


        //开始时间和结束时间都有时---预计上线时间
        if(input('request.on_start') != '' && input('request.on_end') != ''){
            $where['v.uptime'] = [
                ['>=', strtotime(input('request.on_start'))],
                ['<=', strtotime(input('request.on_end').' 23:59:59')],
            ];
        }
        //开始时间
        elseif (input('request.on_start') != '') {
            $where['v.uptime'] = ['>=', strtotime(input('request.on_start'))];
        }
        //结束时间
        elseif (input('request.on_end') != '') {
            $where['v.uptime'] = ['<=', strtotime(input('request.on_end').' 23:59:59')];
        }

        //开始时间和结束时间都有时---创建时间
        if(input('request.cr_start') != '' && input('request.cr_end') != ''){
            $where['v.create_time'] = [
                ['>=', strtotime(input('request.cr_start'))],
                ['<=', strtotime(input('request.cr_end').' 23:59:59')],
            ];
        }
        //开始时间
        elseif (input('request.cr_start') != '') {
            $where['v.create_time'] = ['>=', strtotime(input('request.cr_start'))];
        }
        //结束时间
        elseif (input('request.cr_end') != '') {
            $where['v.create_time'] = ['<=', strtotime(input('request.cr_end').' 23:59:59')];
        }
        $where['v.platform'] =  1;

        return $where;
    }

    public function index(){

        $where = $this->getIndexParam();
//        $param = input('request.');		//分页带条件 ??/

        $channelList = model('Common/Channel')->getAllByCondition('id,name');
        $gameList = model('Common/Game')->getAllByCondition('id,name');
        $this->assign('game_list', $gameList);
        $this->assign('channel_list',$channelList);

        $list = Db::table('cy_third_recharge_log')->alias('v')
            ->join('cy_ttbhistory his', 'his.id = v.ttbhis_id')
            ->join('cy_members c', 'c.id= v.userid')
            ->join('nw_channel n', 'n.id= his.src_id', 'left')
            ->field("v.*,c.username as user_name ,
                            his.ptb as ptb,
                            his.ptb_real as ptb_real,
                            n.name as channel_name")->order('v.create_time desc')->where($where)
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($item, $key){
                if(isset(unserialize($item['content'])['data'])){
                    $item += unserialize($item['content'])['data'];
                    if(isset($item['channelGameFlag'])){
                        $item['game_name'] = model('Common/Game')->where(['id'=>(int)$item['channelGameFlag']])->value('name');
                    }
                    $item['account'] = aesDecode($item['account'], 'CIFCB1UIEN5D52ZZ');
                    if(isset($item['type'])){
                        $item['type']  = $item['type'] == 1? '首充':'续充';
                    }
                }

                unset($item['content']);
                return $item;
            });


        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('total', $list->total());
        return $this->fetch('index');
    }

}
