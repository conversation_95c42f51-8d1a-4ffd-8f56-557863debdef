<?php

/**
 * 分包控制器
 *
 * Created by PhpStorm.
 * User: Admin
 * Date: 2018/5/10
 * Time: 10:27
 */

namespace app\admin\controller;

use app\common\model\SdkGameList;
use app\common\model\Setting;
use think\Db;
use app\common\model\Channel;
use app\common\model\Game as GameModel;
use app\common\model\GameInfo;
use app\common\model\Logininfo;
use app\common\logic\SubPackage as SubChannel;
use app\common\model\AndroidSdk;
use app\common\model\AutoPackage;
use app\common\library\ValidateExtend;
use think\Env;


class SubPackage extends Admin
{
    protected $departmentModel;
    protected $gameModel;
    protected $subChannel;
    protected $channelModel;
    protected $sdkgamelistModel;
    protected $gameList;
    protected $channel;
    protected $gameIOS;  // 特殊处理的 IOS游戏id 数组

    protected function _initialize()
    {
        parent::_initialize();
        $this->subChannel       = new SubChannel;
        $this->channelModel     = new Channel;
        $this->gameModel        = new GameModel;
        $this->sdkgamelistModel = $this->cyModel('sdkgamelist');

        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');

        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;

        $this->channel  = $channelList = $this->nwModel('channel')->where(['flag' => 3])->field('id,name')->select();
        // $this->childChannel = $this->nwModel('channel')->where(['flag' => 3,'level' => 3])->field('id,name')->select();

        // 推广部门列表：部门权限=外部部门, 层级=推广员
		$tmpChildChannel = $this->nwModel('channel')->where(['flag' => 3,'level' => 3])->field('id,name')->select();
		$childChannel = array();
		foreach ($tmpChildChannel as $game) {
              $childChannel[ $game['id']] = $game;
        }
		$this->childChannel  = $childChannel;

        // 特殊处理的 IOS游戏id 数组
        $this->gameIOS = config('gameIOS');
    }

    /**
     * 分包列表
     * @return mixed
     */
    public function index()
    {
        $condition = array();
        $top_channel = input('top_channel');
        $channel_type = input('channel_type');
        $channelId = input('channel_id');
        $gameId = input('game_id');
        $package_type = input('package_type');
        $game_kind = input('game_kind');
        $upload_status = input('upload_status');
        $platform_type = input('platform_type');
        $page_size = input('page_size', 15);

        if (!empty($gameId)) {
            $condition['gameid'] = $gameId;
        }
        if ($package_type != '') {
            $condition['package_type'] = $package_type;
        }
        if ($game_kind != '') {
            $condition['game_kind'] = $game_kind;
        }

        if ($upload_status != '') {
            $condition['upload_status'] = $upload_status;
        }
        if ($platform_type != '') {
            $condition['game.type'] = $platform_type;
        }

        if ($channel_type != 1 && $channel_type != 2 && $channel_type) {
            $this->error('查询参数有误！');
        }

        if ($channel_type == 1 && !$channelId) {
            //$this->error('渠道id参数有误！');
        }

        if ($channel_type == 2 && !$top_channel) {
            //$this->error('顶级渠道参数有误！');
        }

        if (!empty($channelId) && $channel_type == 1) {
            $condition['channel_id'] = $channelId;
        }

        // 按顶级渠道进行检索
        if(!empty($top_channel) && $channel_type == 2) {
            // 查出该顶级渠道下的所有渠道
            $channel_info =$this->channelModel->where('name', $top_channel)->field('id,parent_id,id_path')->find();
            if(!empty($channel_info)) {
                // 组成id_path路径，便于查出所有下级
                $id_path = $channel_info['id_path'].$channel_info['id'].',';
                $map = array();
                $map['id_path'] = array('LIKE', $id_path.'%');
				$map['level'] = 3;
                $channel_list   = $this->channelModel->where($map)->field('id')->select();
                if (!empty($channel_list)) {
                    foreach ($channel_list as $item) {
                        $channel_ids[] = $item['id'];
                    }
                    $channel_ids[]  = $channel_info['id'];
                    $condition['channel_id'] = array('IN', $channel_ids);
                }else {
                    $condition['channel_id'] = array('IN', null);
                }
            }else {
                $condition['channel_id'] = array('IN', null);
            }
        }

        $conditions['game_id'] = $gameId;
        $conditions['channel_id'] = $channelId;
        $conditions['top_channel'] = $top_channel;
        $conditions['channel_type'] = $channel_type;
        $conditions['package_type'] = $package_type;
        $conditions['game_kind'] = $game_kind;
        $conditions['upload_status'] = $upload_status;
        $conditions['platform_type'] = $platform_type;
        $subPackageList = $this->sdkgamelistModel->alias('sdk')
            ->join('cy_game game', 'game.id = sdk.gameid')
            ->where($condition)
            ->field(['game.pinyin,game.name as game_name,game.game_kind, game.type', 'sdk.*'])
            ->order("update_time desc,id DESC")
            ->paginate($page_size, false, ['query' => $conditions]);

        $appList = [];
        if (!empty($subPackageList->items())) {
            foreach ($subPackageList->items() as $v) {
                
                if($v['type'] == 3){
                    // H5
                    $v['app_url'] = getChannelH5Url($v['filename'], $v['gameid']);
                }else{
                    // 安卓/IOS
                    $v['app_url'] = getHandleCdnGamesByIds($v['gameid']) . '/sygame/' . $v['pinyin'] . '/' . $v['filename'];
                }
                
                // 推广页
                $short_link = db('nw_promotion_short_link')->where(['game_id' => $v['gameid'], 'channel_id' => $v['channel_id'], 'package_type' => $v['package_type']])->value('short_link');
                if($short_link){
                    $short_link = T_DOMAIN .'/'. $short_link;
                }else{
                    $short_link = API_DOMAIN . "/game_promotion/index/game_id/{$v['gameid']}/channel_id/{$v['channel_id']}/package_type/{$v['package_type']}";
                }
                
                $v['short_link'] = $short_link;
                $appList[] = $v;
            }
        }

        // 获取分页显示
        $page = $subPackageList->render();

        $this->assign('channel_list', $this->childChannel);
        $this->assign('game_list', $this->selfGameList);
        $this->assign('channel_id', $channelId);
        $this->assign('game_id', $gameId);
        $this->assign('page', $page);
        $this->assign('sub_package', $subPackageList);
        $this->assign('app_list', $appList);

        $this->assign('gameIOS', $this->gameIOS);
        return $this->fetch();
    }


    /**
     * 新增分包
     * @return mixed
     */
    public function add()
    {
        if (request()->isPost()) {

            $gameId     = input('post.gameid');
            $channel_id = input('post.channel_id');

            $data = [
                'gameid'        => $gameId,
                'channel_id'    => $channel_id,
                'package_type'  => input('post.package_type'),
            ];

            $result = $this->validate($data,
                [
                    ['gameid', 'require|integer|gt:0', '请选择游戏|游戏ID必须为整型|游戏ID必须大于0'],
                    ['channel_id', 'require|integer|gt:0', '请选择渠道|渠道ID必须为整型|渠道ID必须大于0'],
                    ['package_type', 'require|integer|in:0,1', '请选择游戏包类型|游戏包类型必须为整型|游戏包类型参数错误'],
                ]);

            if (true !== $result) {
                $this->error($result);
            }

            $channelFrozenModel = Db::name('nw_channel_frozen');
            $gameFrozenModel    = Db::name('nw_game_frozen');

            // 对3款IOS游戏进行特殊处理
            if(in_array($gameId,$this->gameIOS)){
                $data['upload_status'] = 1;
                // aes 加密
                $register_url = 'channel/register.html?k=' . aesEncode("{$channel_id}@.@{$gameId}", '3EFCB15CCC5D525B');
                $data['filename'] = $register_url;
            }else{

                $check = $this->sdkgamelistModel->where($data)->find();
                if ($check) {
                    $this->error('新增失败，该游戏渠道分包已存在！');
                }
            }

            $version = $this->gameModel->where('id', $gameId)->field('channel_version')->find();

            // 启动事务
            Db::startTrans();

            try{
                $data['update_time']    = NOW_TIMESTAMP;
                $data['create_time']    = NOW_TIMESTAMP;
                $data['channel_version']= $version['channel_version'] ? $version['channel_version'] : 0;
                $this->sdkgamelistModel->insertGetId($data);

                //保存短链接地址
                model('PromotionShortLink')->insertData($data['gameid'],$data['channel_id'],$data['package_type']);

                //游戏冻结信息
                $gameFrozenInfo = $gameFrozenModel->where(['game_id'=>$gameId])->find();
                if($gameFrozenInfo){
                    //无该渠道、该游戏的冻结信息时，添加
                    if(!$channelFrozenModel->where(['game_id'=>$gameId,'channel_id'=>$channel_id])->find()){
                        $frozenData['game_id']      = $gameId;
                        $frozenData['channel_id']   = $channel_id;
                        $frozenData['subpackage']   = $gameFrozenInfo['subpackage'];
                        $frozenData['grant_money']  = $gameFrozenInfo['grant_money'];
                        $frozenData['consume']      = $gameFrozenInfo['consume'];
                        $frozenData['member_login'] = $gameFrozenInfo['member_login'];
                        $frozenData['register']     = $gameFrozenInfo['register'];
                        $frozenData['create_time']  = NOW_TIMESTAMP;
                        $frozenData['update_time']  = NOW_TIMESTAMP;

                        $channelFrozenModel->insert($frozenData);
                    }
                }

                // 提交事务
                Db::commit();
            }
            catch (\Exception $e) {
                // 回滚事务
                Db::rollback();

                $this->error('新增操作失败, ' . $e->getMessage());
            }

            $this->success('新增成功', url('index'));
        }

        $this->assign('channel_list', $this->childChannel);
        $this->assign('game_list', $this->selfGameList);
        return $this->fetch();
    }

    /**
     * 分包
     */
    public function subPackIndex()
    {
        if (request()->isPost()) {
            $id         = input('post.id');
            $gameId     = input('post.gameid');
            $channel_id = input('post.channel_id');

            $msg = '成功!';
            $code= 200;

            $condition['id']        = $id;
            $condition['gameid']    = $gameId;
            $condition['channel_id']= $channel_id; // 部门ID

            $subPackageInfo = $this->sdkgamelistModel->where($condition)->find();

            if ($subPackageInfo['upload_status'] == 1) {
                $msg = '抱歉已经分包，请删包后再分包！';
                $code = 400;
            }

            if (!empty($subPackageInfo['filename'])) {
                $msg = '抱歉文件名已生成无法分包,请等待后端分包完成！';
                $code = 400;
            }

            $type = (new GameModel())->where(['id'=>$gameId])->value('type');
            $subPath = $this->subChannel->index($channel_id, $gameId,session_id(), $type, $subPackageInfo['package_type']);
            if ($subPath['code'] == 400) {
                $code = $subPath['code'];
                $msg  = $subPath['msg'];
            }
            $data = [
                'msg' => $msg,
                'code'=> $code
            ];

            return json($data);
        }
    }

    /**
     * 删除分包
     */
    public function deletePack()
    {
        $id         = input('post.id');
        $gameId     = input('gameid');
        $channel_id = input('channel_id');
        $msg = '成功!';
        $code = 200;

        //删包功能
        if(empty($id)){

            $this->error('分包ID不能为空');
        }

        $condition['id']        = $id;
        $condition['gameid']    = $gameId;
        $condition['channel_id']= $channel_id;
        $subPackageInfo = $this->sdkgamelistModel->where($condition)->find();

        if (empty($subPackageInfo['filename'])) {
            $msg = '抱歉没有文件名无法删包！';
            $code = 400;
        }
        $subChannel = $this->subChannel;

        $res = $subChannel->deletePackage($channel_id, $gameId,$subPackageInfo['package_type']);  //删包
        if ($res['msg'] != 'success') {
            $msg = $res['msg'];
            $code = 400;
        }
        $data = [
            'msg' => $msg,
            'code'=> $code
        ];

        return json($data);
    }
    
    /**
     * 禁用/解禁
     */
    public function handleStatus()
    {
        $input = input();
        $msg = '操作成功!';
        $code = 200;
        
        if(empty($input['id']) || !in_array($input['status'], [0, 1])){
            $this->error('参数有误！');
        }

        $subPackageInfo = (new SdkGameList())->where(['id' => $input['id']])->find();
        if (empty($subPackageInfo['filename'])) {
            $msg = '操作分包不存在！';
            $code = 400;
        }
        
        $res = (new SdkGameList())->save(['status' => $input['status']], ['id' => $input['id']]);
        if (!$res) {
            $msg = '操作失败！';
            $code = 400;
        }
        return json(['msg' => $msg, 'code'=> $code]);
    }
    
    /**
     * 批量分包
     */
    public function batchsubpackage(){
        if (request()->isPost()) {
            $ids = input('ids');
            $resultLog = [];
            $ids = explode(',', $ids);
            foreach ($ids as $v) {
                if (!$v) {
                    $resultLog[] = ['value' => $v, 'msg' => '参数有误！', 'code' => '-1'];
                    continue;
                }
                $info = explode('_', $v);
                if (!isset($info[0]) || !isset($info[1])) {
                    $resultLog[] = ['value' => $v, 'msg' => '渠道或游戏参数有误！', 'code' => '-2'];
                    continue;
                }

                $platform = model('game')->where(['id' => $info[0]])->value('type');
                $res = $this->subChannel->index($info[1], $info[0], session_id(), $platform);
                if ($res['code'] === 0) {
                    $resultLog[] = ['value' => $v, 'msg' => $res['msg'], 'code' => '1'];
                } else {
                    $resultLog[] = ['value' => $v, 'msg' => $res['msg'], 'code' => '-3'];
                }

            }
            return json([
                'code' => 1,
                'msg' => '操作成功！',
                'data' => $resultLog
            ]);
        }else{
            $this->assign('game_list', $this->selfGameList);
            return $this->fetch();
        }
    }

    /**
     * 是否有光环加速游戏记录
     */
    public function haveSpeedGame(){

        if (request()->isAjax()) {

            $game_id = input('post.game_id', '', 'intval');

            if(empty($game_id)){
                $this->error('游戏ID不能为空');
            }

            $gameaidInfo = Db::name('cy_gameaid')->where(['gameid'=>$game_id])->find();

            $this->result(['haveSpeedGame'=>($gameaidInfo ? true : false)],1);
        }
        else{

            $this->error('非法请求');
        }
    }

    /**
     * 定时更新游戏
     *
     */
    public function autoPackageList()
    {
        $where = [];

        //游戏名称
        if (input('request.game_id') != '') {
            $where['a.game_id'] = input('request.game_id');
        }

        //对接状态
        if (input('request.package_type') != '') {
            $where['a.package_type'] = input('request.package_type');
        }

        //SDK版本号
        if (input('request.status') != '') {
            $where['a.status'] = input('request.status');
        }

        //更新时间的开始时间和结束时间不为空时
        if (input('request.auto_update_time_start') != '' && input('request.auto_update_time_end') != '') {
            $where['a.auto_update_time'] = [
                ['>=', strtotime(input('request.auto_update_time_start').' 00:00:00')],
                ['<=', strtotime(input('request.auto_update_time_end').' 23:59:59')],
            ];
        }
        //更新时间的开始时间不为空时
        elseif (input('request.auto_update_time_start') != '') {
            $where['a.auto_update_time'] = ['>=', strtotime(input('request.auto_update_time_start').' 00:00:00')];
        }
        //更新时间的结束时间不为空时
        elseif (input('request.auto_update_time_end') != '') {
            $where['a.auto_update_time'] = ['<=', strtotime(input('request.auto_update_time_end').' 23:59:59')];
        }


        //更新时间的开始时间和结束时间不为空时
        if (input('request.create_time_start') != '' && input('request.create_time_end') != '') {
            $where['a.create_time'] = [
                ['>=', strtotime(input('request.create_time_start').' 00:00:00')],
                ['<=', strtotime(input('request.create_time_end').' 23:59:59')],
            ];
        }
        //更新时间的开始时间不为空时
        elseif (input('request.create_time_start') != '') {
            $where['a.create_time'] = ['>=', strtotime(input('request.create_time_start').' 00:00:00')];
        }
        //更新时间的结束时间不为空时
        elseif (input('request.create_time_end') != '') {
            $where['a.create_time'] = ['<=', strtotime(input('request.create_time_end').' 23:59:59')];
        }


        $autoPackageModel = new AutoPackage;

        $list = $autoPackageModel->table('nw_auto_package a,cy_game g')->field('a.*,g.name game_name')
                ->where('a.game_id=g.id')->where($where)->where("g.is_default = 0")->order('a.status asc,a.create_time desc')->paginate(10, false, ['query' => input('get.')]);

        $tmpGameList = $this->gameModel->field('id,name')->where(['cooperation_status'=>['in','1,2'],'id'=>['not in',$this->gameIOS]])->select();
		$gameList = array();
		foreach ($tmpGameList as $game) {
              $gameList[ $game['id']] = $game;
        }

        $versionList = (new AndroidSdk())->column('version', 'id');

        $this->assign('gameList',$gameList);
        $this->assign('list',$list);
        $this->assign('versionList',$versionList);
        $this->assign('page',$list->render());

        return $this->fetch('auto_package_list');
    }

    /**
     * 新增定时更新游戏
     *
     */
    public function autoPackageNew()
    {
        if(request()->isPost()){

            $autoPackageModel   = new AutoPackage;
            $validate           = new ValidateExtend();

            $data = [
                'game_id'           => input('post.game_id'),
                'package_type'      => input('post.package_type'),
                'android_sdk_id'    => input('post.android_sdk_id'),
                'update_type'       => input('post.update_type'),
                'active_day'        => input('post.active_day'),
                'auto_update_time'  => input('post.auto_update_time'),
                'file_name'         => input('post.file_name'),
                'remark'            => input('post.remark'),
                'channel_version'   => input('post.channel_version'),
            ];

            $data['channel_version']    = $data['update_type']==0 ? 0 : $data['channel_version'];

            $result = $this->validate($data, [
                ['game_id', 'require|integer', '请输入游戏名称|游戏ID必须为整型'],
                ['package_type', 'require|in:0,1', '请选择游戏包类型|游戏包类型参数错误'],
                ['android_sdk_id', 'require|integer', '请选择待更新SDK版本号|待更新SDK版本号必须为整型'],
                ['update_type', 'require|integer', '请选择更新类型|更新类型必须为整型'],
                ['active_day', 'require|integer', '请选择渠道活跃天数|渠道活跃天数必须为整型'],
                ['auto_update_time', 'require|date', '更新时间不能为空|更新时间格式不正确'],
                ['file_name', 'require|max:100|regex:/^[A-Za-z0-9._-]+$/', '待更新包名不能为空|待更新包名不能超过100个字符|待更新包名只能输入字符数字._-符号'],
                ['remark', 'max:800', '备注不能超过800个字符'],
            ]);

            if (true !== $result) {

                $this->error($result);
            }
            elseif(NOW_TIMESTAMP>strtotime($data['auto_update_time']))
            {
                $this->error('更新时间需大于当前时间');
            }
            elseif(mb_substr($data['file_name'],-4,NULL,'UTF-8')!='.apk' && mb_substr($data['file_name'],-4,NULL,'UTF-8')!='.ipa'){

                $this->error('待更新包名文件后缀错误');
            }
            elseif(mb_substr($data['file_name'],0,-4,'UTF-8')==$this->gameModel->where(['id'=>$data['game_id']])->value('pinyin')){

                $this->error('待更新包名文件不能与游戏拼音相同');
            }
            elseif($data['update_type']==1){

                if($data['channel_version']==''){
                    $this->error('强更版本不能为空');
                }
                elseif (!$validate->is($data['channel_version'], 'positiveInteger') ) {

                    $this->error('强更版本必须为正整数');
                }
            }


            if($autoPackageModel->where(['game_id'=>$data['game_id'],'status'=>0,'package_type'=>$data['package_type']])->find()){

                $this->error('已存在该更新内容');
            }

            $data['auto_update_time']   = strtotime($data['auto_update_time']);
            $data['update_time']        = NOW_TIMESTAMP;
            $data['create_time']        = NOW_TIMESTAMP;
            $data['admin_id']           = session('ADMIN_ID');

            $autoPackageModel->insert($data);

            $this->success('定时更新游戏新增成功', 'autoPackageList');
        }

        $tmpGameList = $this->gameModel->field('id,name')->where(['cooperation_status'=>['in','1,2'],'id'=>['not in',$this->gameIOS]])->select();
      	$gameList = array();
		foreach ($tmpGameList as $game) {
              $gameList[ $game['id']] = $game;
        }

        $this->assign('version', (new AndroidSdk())->getAllVersion());
        $this->assign('gameList',$gameList);

        return $this->fetch('auto_package_new');
    }

    /**
     * 编辑定时更新游戏
     *
     */
    public function autoPackageEdit()
    {
        $id = input('id');

        $autoPackageModel = new AutoPackage;

        if(empty($id)){

            $this->error('id不能为空');
        }

        $info = $autoPackageModel->where(['id'=>$id,'status'=>0])->find();

        if(empty($info)){

            $this->error('定时更新游戏记录不存在');
        }

        //表单提交
        if(request()->isPost()){

            $validate = new ValidateExtend();

            $data = [
                'package_type'      => input('post.package_type'),
                'android_sdk_id'    => input('post.android_sdk_id'),
                'update_type'       => input('post.update_type'),
                'active_day'        => input('post.active_day'),
                'auto_update_time'  => input('post.auto_update_time'),
                'file_name'         => input('post.file_name'),
                'remark'            => input('post.remark'),
                'channel_version'   => input('post.channel_version'),
            ];

            $data['channel_version']    = $data['update_type']==0 ? 0 : $data['channel_version'];

            $result = $this->validate($data, [
                ['package_type', 'require|in:0,1', '请选择游戏包类型|游戏包类型参数错误'],
                ['android_sdk_id', 'require|integer', '请选择待更新SDK版本号|待更新SDK版本号必须为整型'],
                ['update_type', 'require|integer', '请选择更新类型|更新类型必须为整型'],
                ['active_day', 'require|integer', '请选择渠道活跃天数|渠道活跃天数必须为整型'],
                ['auto_update_time', 'require|date', '更新时间不能为空|更新时间格式不正确'],
                ['file_name', 'require|max:100|regex:/^[A-Za-z0-9._-]+$/', '待更新包名不能为空|待更新包名不能超过100个字符|待更新包名只能输入字符数字._-符号'],
                ['remark', 'max:800', '备注不能超过800个字符'],
            ]);

            if (true !== $result) {

                $this->error($result);
            }
            elseif(NOW_TIMESTAMP>strtotime($data['auto_update_time']))
            {
                $this->error('更新时间需大于当前时间');
            }
            elseif(mb_substr($data['file_name'],-4,NULL,'UTF-8')!='.apk' && mb_substr($data['file_name'],-4,NULL,'UTF-8')!='.ipa'){

                $this->error('待更新包名文件后缀错误');
            }
            elseif(mb_substr($data['file_name'],0,-4,'UTF-8')==$this->gameModel->where(['id'=>$info['game_id']])->value('pinyin')){

                $this->error('待更新包名文件不能与游戏拼音相同');
            }
            elseif($data['update_type']==1){

                if($data['channel_version']==''){
                    $this->error('强更版本不能为空');
                }
                elseif (!$validate->is($data['channel_version'], 'positiveInteger') ) {

                    $this->error('强更版本必须为正整数');
                }
            }

            if($autoPackageModel->where(['game_id'=>$info['game_id'],'status'=>0,'package_type'=>$data['package_type'],'id'=>['<>',$id]])->find()){

                $this->error('已存在该更新内容');
            }

            $data['auto_update_time']   = strtotime($data['auto_update_time']);
            $data['update_time']        = NOW_TIMESTAMP;
            $data['admin_id']           = session('ADMIN_ID');

            if($autoPackageModel->update($data,['id'=>$id])){

                $this->success('定时更新游戏编辑成功', 'autoPackageList');
            }
            else{
                $this->error('定时更新游戏编辑失败');
            }
        }


        $gameaidInfo = Db::name('cy_gameaid')->where(['gameid'=>$info['game_id']])->find();

        $info['haveSpeedGame'] = ($gameaidInfo ? true : false);

        $gameInfo = $this->gameModel->field('cooperation_status,pinyin,name')->where(['id'=>$info['game_id']])->find();

        $gameList = $this->gameModel->field('id,name')->where(['cooperation_status'=>['in','1,2'],'id'=>['not in',$this->gameIOS]])->select();

        $this->assign('version', (new AndroidSdk())->getAllVersion());
        $this->assign('gameList',$gameList);
        $this->assign('info',$info);
        $this->assign('gameInfo',$gameInfo);

        return $this->fetch('auto_package_edit');
    }

    /**
     * 删除定时更新游戏
     *
     */
    public function autoPackageDelete()
    {
        $id = input('id');

        $autoPackageModel = new AutoPackage;

        if(empty($id)){

            $this->error('id不能为空');
        }

        if($autoPackageModel->where(['id'=>$id])->delete()){

            $this->success('删除成功');
        }
        else{
            $this->error('删除失败');
        }
    }

    /**
     * 通过ajax获取游戏信息
     */
    public function ajaxGetGameInfo()
    {
        if(request()->isAjax()){

            $game_id = input('post.game_id');

            if(empty($game_id)){

                $this->error('游戏ID不能为空');
            }

            $info = $this->gameModel->field('cooperation_status,pinyin,channel_version,android_sdk_id')->where(['cooperation_status'=>['in','1,2'],'id'=>$game_id])->find();

            $gameaidInfo = Db::name('cy_gameaid')->where(['gameid'=>$game_id])->find();

            $info['haveSpeedGame'] = ($gameaidInfo ? true : false);

            $this->result($info,1);
        }
        else{

            $this->error('非法请求');
        }

    }

    // 批量更新
    public function approved(){


        $ids = input('ids');
        if(!$ids){
            $this->error('请选择要更新的记录');
        }
        $arr = explode(',',$ids);
        $packagelModel = model('PackageApply');
        foreach ($arr as $k=>$v){
            $data = explode('_',$v);
            $cont = [];
            if(!(isset($data[0]) && $data[0])){
                $this->error('非法请求');
            }
            if(!(isset($data[1]) && $data[1])){
                $this->error('非法请求');
            }

            $cont['game_id'] = $data[0];
            $cont['channel_id'] =  $data[1];
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['apply_time'] = NOW_TIMESTAMP;
            $cont['apply_status'] = 0;

            if ($packagelModel->where(['game_id'=>$data[0],'channel_id'=>$data[1]])->find()) {
                $packagelModel->where(['game_id'=>$data[0],'channel_id'=>$data[1]])->update($cont);
                $this->sdkgamelistModel->where(['gameid'=>$data[0],'channel_id'=>$data[1]])->update(['upload_status' => 2]);
            }
        }
        $this->success("已经提交分包任务");
    }
}
