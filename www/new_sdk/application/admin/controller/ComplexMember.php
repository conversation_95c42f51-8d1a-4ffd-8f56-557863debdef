<?php
/**
 * 聚合玩家用户管理控制器
 */

namespace app\admin\controller;

use app\common\library\MakeReportGo;
use app\common\model\ComplexMembers as MembersModel;
use app\common\model\ComplexChannelModel;
use app\common\model\ComplexPay;
use GuzzleHttp\Exception\RequestException;
use think\Db;
use app\common\library\MakeReport;
use GuzzleHttp\Client;

class ComplexMember extends Admin
{
    protected $membersModel;
    protected $where;
    protected $start_time;
    protected $end_time;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->membersModel = new MembersModel;
        $this->where        = [];
    }

    public function getWhere()
    {
        $where          = [];
        $start_time     = input('request.start_time');
        $end_time       = input('request.end_time');
        $channel_id     = input('request.channel_id');
        $gameid         = input('request.gameid');
        $username       = input('request.username');
		$complex_username = input('request.complex_username');


        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $where['reg_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $where['reg_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $where['reg_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {
			/*
            $start_time = $end_time = date('Y-m-d', time());

            $where['reg_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
			*/
        }

        //渠道
        if ($channel_id != '') {
            $where['channel_id'] = $channel_id;
        }

        //游戏
        if ($gameid != '') {
            $where['gameid'] = $gameid;
        }

        //用户名
        if ($username != '') {
            $where['m.mg_username'] = $username;
        }

        //聚合渠道ID/用户名
        if ($complex_username != '') {
            $where['m.complex_username'] = $complex_username;
        }

        $this->where      = $where;
        $this->start_time = $start_time;
        $this->end_time   = $end_time;
    }

    /**
     * 注册用户列表
     */
    public function index()
    {
        $this->getWhere();
        $param = input('get.');

        $list  = $this->membersModel->table('nw_complex_members m,nw_complex_channel c')
                    ->field('m.id,m.mg_username,m.complex_username,m.gameid,m.channel_id,m.total_pay_amount,m.reg_time,m.login_time,c.name channel_name')
                    ->where('m.channel_id=c.id')
                    ->where($this->where)->order('m.reg_time desc')
                    ->paginate(10, false, array('query' => $param));

        $data = $list->toArray()['data'];

        if ( ! empty($data) ) {
            foreach ($data as $v) {
                 $gameIdArr[] = $v['gameid'];
             }

             $gameNameArr = model('Common/Game')->whereIn('id', $gameIdArr)->column('name', 'id');

             foreach ($data as $key => $item) {
                 $data[ $key ]['game_name'] = isset($gameNameArr[ $item['gameid'] ]) ? $gameNameArr[ $item['gameid'] ] : '--';
             }
        }

        $channel_list = (new ComplexChannelModel())->getAllByCondition('id,name',['flag'=>4],'name asc');

        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');
		$selfGameList = array();
		foreach ($gameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }

        //$gameList = array_column($gameList, 'name', 'id');

        $this->assign('list', $data);
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('total', $list->total());
        $this->assign('page', $list->render());
        $this->assign('channel_list', $channel_list);
        $this->assign('game_list', $selfGameList);

        return $this->fetch();
    }

    /**
     * 报表下载
     *
     */
    public function download()
    {
        if (request()->isAjax()) {

            $auth       = new \app\common\logic\Auth();

            $is_show = input('is_show');

            if($is_show>(int)$auth->check(session('ADMIN_ID'), 'admin/complex_member/memberDowmExcel'))
            {
                $this->error('您没有访问权限！');
            }

            $this->getWhere();

            $sql  = $this->membersModel->table('nw_complex_members m,nw_complex_channel c')
                        ->field('m.id,m.mg_username,m.complex_username,m.gameid,m.total_pay_amount,m.channel_id,m.reg_time,m.login_time,c.name channel_name')
                        ->where('m.channel_id=c.id')
                        ->where($this->where)->order('m.reg_time desc')
                        ->fetchSql(true)->select();

            if ((new MakeReportGo())->addTask('admin.complexMemberList',$sql,session_id(),['is_show'=>boolval($is_show)])){
                //高级报表下载
                if($is_show==1){
                    $this->insertLog($this->current_node,'注册用户高级报表下载',58);
                }
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
			else{
				$this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
			}
        }
        else{
            $this->error('非法请求');
        }

    }

    /**
     * 登录用户列表
     */
    public function loginList()
    {
        $list = Db::table('nw_complex_loginlog l,nw_complex_members m,nw_complex_channel c,cy_game g')
                    ->field("l.ip,l.channel_id as channel_id,l.userid,l.login_time,m.mg_username,c.name as channel_name,g.name game_name,g.origin_name")
                    ->where('l.userid = m.id and l.channel_id=c.id and l.gameid=g.id')
                    ->where($this->_getLoginListCondition())
                    ->order('l.id desc')
                    ->paginate(20, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];

        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');
		$selfGameList = array();
		foreach ($gameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
        //$gameList = array_column($gameList, 'name', 'id');

        $this->assign('list', $data);
        $this->assign('total', $list->total());
        $this->assign('page', $list->render());
        $this->assign('game_list', $selfGameList);

        return $this->fetch('login_list');
    }

    /**
     * 登录用户 报表下载
     */
    public function loginListDownload()
    {
        if (request()->isAjax()) {

            $sql = Db::table('nw_complex_loginlog l,nw_complex_members m,nw_complex_channel c,cy_game g')
                        ->field("l.ip,l.channel_id as channel_id,l.userid,l.login_time,m.mg_username,c.name as channel_name,g.name game_name,g.origin_name")
                        ->where('l.userid = m.id and l.channel_id=c.id and l.gameid=g.id')
                        ->where($this->_getLoginListCondition())
                        ->order('l.id desc')
                        ->fetchSql(true)
                        ->select();

            if((new MakeReportGo())->addTask('admin.complexMemberLoginList', $sql, session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');

        } else {

            $this->error('非法请求');
        }
    }

    /**
     * 登录用户  条件查询
     * @return array
     */
    protected function _getLoginListCondition()
    {
        $where = [];

        $start_time       = input('request.start_time', '', 'trim');
        $end_time         = input('request.end_time', '', 'trim');
        $channel_name     = input('request.channel_name', '', 'trim');
        $username         = input('request.mg_username', '', 'trim');
        $gameid           = input('request.gameid', '', 'intval');

        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $where['l.login_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $where['l.login_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $where['l.login_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {
			/*
            $start_time            = $end_time = date('Y-m-d', time());
            $where['l.login_time'] = ['>=', strtotime($start_time)];
			*/
        }

        //渠道名称
        if ($channel_name != '') {
            $where['c.name'] = ['like', '%' . $channel_name . '%'];
        }

        //用户名
        if ($username != '') {
            $where['m.mg_username'] = $username;
        }

        //游戏名称
        if (!empty($gameid)) {
            $where['l.gameid'] = $gameid;
        }

        return $where;
    }

    /**
     * 充值记录
     *
     */
    public function payList()
    {
        $condition = $this->_beforeGetPayParams();

        //充值完成总金额
        $totalAmount = 0;

        $payModel = new ComplexPay;
        $cp_status         = $this->request->param('cp_status');
        $where = [];
        if (is_numeric($cp_status)) {
            $where['y.status'] = (int)$cp_status;
        }
        $list = $payModel->table('nw_complex_pay p,nw_complex_channel c,cy_paycpinfo y')->field('p.orderid,sub_orderid,gameid,username,userid,amount,p.status,p.create_time,attach,c.name channel_name,y.status as cp_status,p.roleid,p.rolename')
                         ->where('p.channel_id=c.id')
                         ->where('p.orderid=y.orderid')
                         ->where($where)
                         ->where($condition)
                         ->order('p.id desc')
                         ->paginate(10, false, ['query' => input('get.')]);
        if ($list->total()>0) {
                 $data      = $list->toArray()['data'];
                 $userIdArr = $gameIdArr = [];
                 foreach ($data as $v) {
                     $userIdArr[]    = $v['userid'];
                     $gameIdArr[]    = $v['gameid'];
                 }

                 $gameNameArr = model('Common/Game')->whereIn('id', $gameIdArr)->column('name', 'id');
                 $regTimeArr  = $this->membersModel->whereIn('id', array_unique($userIdArr))->column('reg_time', 'id');

                 foreach ($list as $key => $item) {
                     $default                        = '--';
                     $list[ $key ]['game_name']      = isset($gameNameArr[ $item['gameid'] ]) ? $gameNameArr[ $item['gameid'] ] : $default;

                     $list[ $key ]['reg_time']       = isset($regTimeArr[ $item['userid'] ]) ? date("Y-m-d H:i:s", $regTimeArr[ $item['userid'] ]) : $default;
                 }


                 //支付成功 或者支付查询条件未选择时
                 if(input('status')==1 || input('status')==''){
                     //充值完成总金额
                     $totalAmount = $payModel->alias('p')->field('sum(amount) as total_amount')->where($condition)->where('p.status=1')->find()['total_amount'];
                 }
        }


        $channel_list = (new ComplexChannelModel())->getAllByCondition('id,name',['flag'=>4],'name asc');

        $this->assign('list', $list);
        $this->assign('total', $list->total());
        $this->assign('page', $list->render());

        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');
		$selfGameList = array();
		foreach ($gameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
        $this->assign('game_list', $selfGameList);

        $this->assign('channel_list', $channel_list);          //渠道列表
        $this->assign('totalAmount', $totalAmount);

        return $this->fetch('pay_list');
    }

    /**
     * 补单通知CP
     *
     */
    public function reCallback()
    {
        if($this->request->isPost()){

            $orderid = input('post.orderid','','trim');

            if(empty($orderid)){
                $this->error('订单号不能为空');
            }

            $payCpInfoModel = model('Common/PayCpinfo');

            $where['orderid'] = $orderid;

            $paycpInfo = $payCpInfoModel->field('id,fcallbackurl,params,payflag')->where($where)->find();

            if(empty($paycpInfo)){
                $this->error('订单通知回调记录不存在');
            }
            elseif($paycpInfo['payflag']!=1){
                $this->error('订单未完成支付或支付失败');
            }

            $guzzle = new Client();

            //将字符串转换成数组
            parse_str($paycpInfo['params'], $param);

            try {
                $response = $guzzle->request('post', $paycpInfo['fcallbackurl'], ['form_params' => $param, 'timeout' => 30]);
                log_message($paycpInfo['fcallbackurl'] . '?' . http_build_query($param) . '  ' . $response->getBody(), 'log', LOG_PATH . 'reCallback/');
                //通知成功
                if (0 == strcasecmp($response->getBody(), 'success')) {

                    $payCpInfoModel->save(['status' => 1, 'update_time' => NOW_TIMESTAMP], ['id' => $paycpInfo['id']]);

                    $this->success('订单通知成功');
                }
                else{
                    $this->error('订单通知已发送给对方，但对方未返回[success]信息');
                }

            } catch (RequestException $e) {

                $this->error("订单通知失败： " . $e->getMessage());
            }

        }

        return $this->fetch('re_callback');
    }

    /**
     * 充值记录 报表下载
     */
    public function payListDownload()
    {
        if (request()->isAjax()) {

            $payModel = new ComplexPay;

//            $sql = $payModel->table('nw_complex_pay p,nw_complex_channel c,cy_game g,nw_complex_members m')
//                            ->field('orderid,p.sub_orderid,p.gameid,p.username,amount,p.status,p.create_time,attach,c.name channel_name,g.name game_name,g.origin_name,m.reg_time')
//                            ->where('p.channel_id=c.id and p.gameid=g.id and p.userid=m.id')
//                            ->where($this->_beforeGetPayParams())
//                            ->order('p.id desc')
//                            ->fetchSql(true)
//                            ->select();
            $condition = $this->_beforeGetPayParams();
            $cp_status         = $this->request->param('cp_status');
            $where = [];
            if (is_numeric($cp_status)) {
                $where['y.status'] = (int)$cp_status;
            }
            $sql = $payModel->table('nw_complex_pay p,nw_complex_channel c,cy_paycpinfo y')->field('p.orderid,sub_orderid,gameid,username,userid,amount,p.status,p.create_time,attach,c.name channel_name,y.status as cp_status,p.roleid,p.rolename')
                ->where('p.channel_id=c.id')
                ->where('p.orderid=y.orderid')
                ->where($where)
                ->where($condition)
                ->order('p.id desc')
                ->fetchSql(true)
                ->select();

            if ( (new MakeReportGo())->addTask('admin.complexPayList', $sql, session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');

        } else {

            $this->error('非法请求');
        }
    }

    /**
     * 前置方法(参数获取)
     *
     * @return $condition array 查询条件
     */
    protected function _beforeGetPayParams()
    {
        $start          = $this->request->param('start');
        $end            = $this->request->param('end');
        $channel_id     = $this->request->param('channel_id', 0, 'intval');
        $gameid         = $this->request->param('gameid', 0, 'intval');
        $status         = $this->request->param('status');
        $orderid        = $this->request->param('orderid', '', 'trim');
        $sub_orderid    = $this->request->param('sub_orderid', '', 'trim');
        $username       = $this->request->param('username', '', 'trim');
        $rolename = $this->request->param('rolename', '', 'trim');
        $roleid = $this->request->param('roleid', '', 'trim');
        $condition = [];
        if (!empty($gameid)) {
            $condition['p.gameid'] = $gameid;
        }
        if (!empty($orderid)) {
            $condition['p.orderid'] = $orderid;
        }
        if (!empty($sub_orderid)) {
            $condition['p.sub_orderid'] = $sub_orderid;
        }
        if (is_numeric($status)) {
            $condition['p.status'] = (int)$status;
        }
        if (!empty($username)) {
            $condition['p.username'] = $username;
        }
        if (!empty($channel_id)) {
            $condition['p.channel_id'] = $channel_id;
        }

        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition['p.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition['p.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition['p.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
			/*
            $start = date('Y-m-d', time());
            $end   = date('Y-m-d', time());

            $condition['p.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
			*/
        }
        if ($rolename != '') {
            $condition['p.rolename'] = $rolename;
        }
        if ($roleid != '') {
            $condition['p.roleid'] = $roleid;
        }
        return $condition;
    }

    /**
     * ajax 获取cp订单号
     */
    public function showAttach(){
        $order_id = input('post.orderid', '', 'filterAndTrimInput');     // 订单号
        $this->_showAttach($order_id);
    }

    /**
     * 查看订单的attach信息
     * @param $orderid string 订单号
     */
    private function _showAttach($orderid) {
        $runnable = true;
        $msg      = '';
        $status   = false;
        if(empty($orderid)) {
            $runnable = false;
            $msg      = '参数错误';
        }
        if($runnable) {
            $result = model('ComplexPay')
                ->where(['orderid'=>$orderid])
                ->field('attach')
                ->find();
            if(empty($result)) {
                $runnable = false;
                $msg      = '订单不存在';
            }
        }
        // 查看attach
        if($runnable) {
            $status = 1;
            $result['attach'] = 'attach='.$result['attach'];
            parse_str($result['attach'],$arr);
            $msg = $arr['attach']; // 变量由上一个函数解析出来的
        }
        echo json_encode(['code'=>$status,'msg'=>$msg]);
    }

    /*
     * 聚合用户充值列表
     */
    public function rechargeList()
    {
        $condition = $this->_rechargePay();
        $payModel = new ComplexPay;
        $count = $payModel->where($condition)
            ->where('status',1)
            ->distinct(true)
            ->field('userid')
            ->order('id desc')
            ->select();
        $list = $payModel->where($condition)
            ->where('status',1)
            ->distinct(true)
            ->field('userid')
            ->order('id desc')
            ->paginate(10, count($count), ['query' => input('get.')]);
        foreach ($list as &$item){
            $item['amount'] = $payModel->where(['userid'=> $item['userid'],'status'=> 1])
                            ->where($condition)
                            ->sum('amount');
            $item['frequency'] = $payModel->where(['userid'=> $item['userid'],'status'=> 1])
                                ->where($condition)
                                ->count('amount');
            $amount_data = $payModel->where(['userid'=> $item['userid'],'status'=> 1])
                        ->where($condition)
                        ->order('create_time desc')
                        ->find();
            $item['ramount'] = $amount_data['amount'];
            $item['username'] = $amount_data['username'];
        }
        $this->assign('list', $list);
        $this->assign('total', $list->total());
        $this->assign('page', $list->render());

        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');
		$selfGameList = array();
		foreach ($gameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
        $this->assign('game_list', $selfGameList);

        return $this->fetch('recharge_list');
    }
    /*
     * 聚合用户充值查询
     */
    protected function _rechargePay()
    {
        $start = $this->request->param('start');
        $end = $this->request->param('end');
        $username = $this->request->param('username','','trim');
        $gameid = $this->request->param('gameid',0,'trim');
        $condition = [];
        if (!empty($gameid)){
            $condition['gameid'] = $gameid;
        }
        if (!empty($username)){
            $condition['username'] = $username;
        }
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition['create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition['create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition['create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
			/*
            $start = date('Y-m-d', time());
            $end   = date('Y-m-d', time());

            $condition['create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
			*/
        }
        return $condition;
    }

    /**
     * 查看聚合渠道ID/账号
     */
    public function showUsername()
    {
        $userid = input('userid');

        if(empty($userid)){

            $this->error('用户ID不能为空');
        }

        $username= model('ComplexMembers')->where(['id'=>$userid])->value('complex_username');

        if(!empty($username)){

            $this->insertLog($this->current_node,'查看账号：'.$username,57);

            $this->result($username,1);
        }
        else{
            $this->error('用户信息不存在');
        }
    }
}
