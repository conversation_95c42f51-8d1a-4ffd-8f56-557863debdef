<?php

namespace app\admin\controller;


class ChannelGame extends Admin
{
    public function index(){

        $channelId = input('channel_id');
        $gameId = input('game_id');

        $where = [];
        if ($channelId) {
            $where['channel_id'] = ['in',$channelId];
        }

        if ($gameId) {
            $where['game_id'] = ['in',$gameId];
        }



        $list = model('common/ChannelGame')
            ->where($where)
            ->order("id DESC")
            ->paginate(10, false, ['query' =>input()]);
        // 获取分页显示
        $page = $list->render();

        $tmpGameList = model('Common/Game')->field('id,name')->order('id desc')->select();
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }
        $this->assign('gameArr', $tmpGameList);
        $this->assign('game_list', $gameList);

        $channels = model('common/Channel')->field('id,name')->where(['level'=>1,'status'=>1])->select();

        $channelList = array();
        foreach ($channels as $game) {
            $channelList[$game['id']] = $game;
        }
        $this->assign('channels', $channels);
        $this->assign('channelList', $channelList);

        $this->assign('channel_id', $channelId);
        $this->assign('game_id', $gameId);
        $this->assign("page", $page);
        $this->assign("list", $list);
        return $this->fetch();
    }
    public function add(){

        if ($this->request->isPost()) {
            $gameIds = input('game_id','');

            if(!$gameIds){
                $this->error('请选择游戏');
            }
            $channelIds = input('channel_id','');

            if(!$channelIds){
                $this->error('请选择会长');
            }
            $data = [];
            foreach (explode(',',$gameIds) as $k=>$v){
                foreach (explode(',',$channelIds) as $kk=>$vv){

                    if (!$info = model('Common/ChannelGame')->where(['game_id'=>$v,'channel_id'=>$vv])->find()) {
                        $data[] = [
                            'game_id'=>$v,
                            'channel_id'=>$vv,
                            'create_time'=>time(),
                        ];
                    }
                }
            }
            if(!$data){
                $this->error('无可添加记录');
            }
            if(model('common/ChannelGame')->insertAll($data)){
                $this->success('添加成功', url('index'));
            }else{
                $this->error('添加失败');
            }
        }
        $games = model('Common/Game')->getAllByCondition('id,name', ['cooperation_status' => ['neq', 3]], 'id desc', 'self');
        $this->assign('games', $games);

        $channels = model('common/Channel')->field('id,name')->where(['level'=>1,'status'=>1])->select();
        $this->assign('channels', $channels);

        return $this->fetch();
    }

    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $channelGameModel = model('Common/ChannelGame');
        if (!$info = $channelGameModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }

        if ($channelGameModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $channelGameModel->getError();
        $this->error($error ?: '删除失败');
    }
}
