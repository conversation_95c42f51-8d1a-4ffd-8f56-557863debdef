<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Request;

// 异常维度管理
class FkDimension extends Controller
{
    /**
     * 获取异常对象选项（监控对象类型）
     */
    public function getAbnormalObjectOptions()
    {
        // 定义监控对象类型选项
        $options = [
            'ip' => 'IP地址',
            'imei' => 'IMEI设备号',
            'username' => '用户名',
            'id_card' => '身份证号',
            'phone' => '手机号'
        ];
        
        return json(['code' => 0, 'data' => $options]);
    }

    /**
     * 异常维度列表页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $page = input('get.page', 1);
            $limit = input('get.limit', 20);
            $where = [];
            
            // 搜索条件
            $title = input('get.title', '');
            if ($title) {
                $where['title'] = ['like', "%{$title}%"];
            }
            
            $monitorObject = input('get.monitor_object', '');
            if ($monitorObject) {
                $where['monitor_object'] = $monitorObject;
            }
            
            $abnormalJudgeType = input('get.abnormal_judge_type', '');
            if ($abnormalJudgeType) {
                $where['abnormal_judge_type'] = $abnormalJudgeType;
            }
            
            $abnormalObject = input('get.abnormal_object', '');
            if ($abnormalObject) {
                $where['abnormal_object'] = ['like', "%{$abnormalObject}%"];
            }
            
            $status = input('get.status', '');
            if ($status !== '') {
                $where['status'] = $status;
            }
            
            $count = Db::name('fk_dimension')->where($where)->count();
            $data = Db::name('fk_dimension')
                ->where($where)
                ->page($page, $limit)
                ->order('dimension_id desc')
                ->select();
                // dump($data);
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 添加异常维度
     */
    public function add()
    {
        if ($this->request->isPost()) {
            // 处理异常对象多选数据
            $abnormalObjectArray = input('post.abnormal_object/a', []);
            $abnormalObject = is_array($abnormalObjectArray) ? implode(',', $abnormalObjectArray) : input('post.abnormal_object', '');

            $data = [
                'title' => input('post.title'),
                'monitor_object' => input('post.monitor_object'),
                'abnormal_object' => $abnormalObject,
                'abnormal_judge_type' => trim(input('post.abnormal_judge_type')),
                'execute_type' => trim(input('post.execute_type')),
                'abnormal_judge_value' => input('post.abnormal_judge_value', 0),
                'abnormal_judge_level' => input('post.abnormal_judge_level', 0),
                'lifespan_time' => input('post.lifespan_time', 0),
                'status' => input('post.status', 1),
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            // 验证
            if (empty($data['title']) || empty($data['monitor_object']) || empty($data['abnormal_object'])) {
                return json(['code' => 1, 'msg' => '维度名、监控对象和异常对象不能为空']);
            }

            if (!in_array($data['abnormal_judge_type'], ['gt', 'lt', 'eq'])) {
                return json(['code' => 1, 'msg' => '异常判定类型无效']);
            }

            if (!in_array($data['execute_type'], ['hour', 'day'])) {
                return json(['code' => 1, 'msg' => '执行类型无效']);
            }
            
            try {
                Db::name('fk_dimension')->insert($data);
                return json(['code' => 0, 'msg' => '添加成功']);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '添加失败：' . $e->getMessage()]);
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 编辑异常维度
     */
    public function edit($id)
    {
        if ($this->request->isPost()) {
            // 处理异常对象多选数据
            $abnormalObjectArray = input('post.abnormal_object/a', []);
            $abnormalObject = is_array($abnormalObjectArray) ? implode(',', $abnormalObjectArray) : input('post.abnormal_object', '');
            
            $data = [
                'title' => input('post.title'),
                'monitor_object' => input('post.monitor_object'),
                'abnormal_object' => $abnormalObject,
                'abnormal_judge_type' => trim(input('post.abnormal_judge_type')),
                'execute_type' => trim(input('post.execute_type')),
                'abnormal_judge_value' => input('post.abnormal_judge_value', 0),
                'abnormal_judge_level' => input('post.abnormal_judge_level', 0),
                'lifespan_time' => input('post.lifespan_time', 0),
                'status' => input('post.status', 1),
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 验证
            if (empty($data['title']) || empty($data['monitor_object']) || empty($data['abnormal_object'])) {
                return json(['code' => 1, 'msg' => '维度名、监控对象和异常对象不能为空']);
            }
            
            if (!in_array($data['abnormal_judge_type'], ['gt', 'lt', 'eq'])) {
                return json(['code' => 1, 'msg' => '异常判定类型无效']);
            }
            if (!in_array($data['execute_type'], ['hour', 'day'])) {
                return json(['code' => 1, 'msg' => '执行类型无效']);
            }
            
            try {
                Db::name('fk_dimension')->where('dimension_id', $id)->update($data);
                return json(['code' => 0, 'msg' => '修改成功']);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '修改失败：' . $e->getMessage()]);
            }
        }
        
        $info = Db::name('fk_dimension')->where('dimension_id', $id)->find();
        if($info){
            $info['abnormal_object'] = explode(',', $info['abnormal_object']);
        }
        $this->assign('info', $info);
        return $this->fetch();
    }
    
    /**
     * 删除异常维度
     */
    public function delete()
    {
        $id = input('post.id');
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        try {
            Db::name('fk_dimension')->where('dimension_id', $id)->delete();
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 批量删除
     */
    public function batchDelete()
    {
        $ids = input('post.ids/a', []);
        if (empty($ids)) {
            return json(['code' => 1, 'msg' => '请选择要删除的数据']);
        }
        
        try {
            Db::name('fk_dimension')->whereIn('dimension_id', $ids)->delete();
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
} 