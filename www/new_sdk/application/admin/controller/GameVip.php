<?php

namespace app\admin\controller;

use think\Db;
use app\common\model\Game;
use app\common\library\FileUpload;

class GameVip extends Admin
{
    protected function _initialize()
    {
        parent::_initialize();

        $this->gameVipModel = Db::name('cy_game_vip_price');
    }


    public function index()
    {

        $where = [];

        //游戏名称
        if (input('request.game_id') != '') {
            $where['v.gameid'] = ['=', input('request.game_id')];
        }

        //开始时间和结束时间都有时
        if(input('request.start') != '' && input('request.end') != ''){
            $where['v.online_time'] = [
                ['>=', strtotime(input('request.start'))],
                ['<=', strtotime(input('request.end').' 23:59:59')],
            ];
        }
        //开始时间
        elseif (input('request.start') != '') {
            $where['v.online_time'] = ['>=', strtotime(input('request.start'))];
        }
        //结束时间
        elseif (input('request.end') != '') {
            $where['v.online_time'] = ['<=', strtotime(input('request.end').' 23:59:59')];
        }

        $param = input('get.');

        $gameVip = $this->gameVipModel
            ->alias('v')
            ->join('cy_game g', 'v.gameid=g.id', 'left')
            ->field('v.*,g.id as gameid2,g.name as game_name')
            ->where($where)
            ->order('v.id desc')->paginate(10, false, array('query' => $param));

        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');
        $this->assign('game_list', $gameList);
        $this->assign('game_vip_list', $gameVip);
        $this->assign('page', $gameVip->render());

        return $this->fetch();
    }


    public function add()
    {
        if (request()->isPost()) {
            $data = [
                'gameid'      => input('post.game_id'),
                'online_time' => input('post.game_online_date'),
            ];

            $result = $this->validate($data, [
                ['gameid', 'require', '游戏名不能为空'],
                ['online_time', 'require', '上线时间不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            } else {
                $where['gameid'] = $data['gameid'];
                $where['online_time'] = $where['update_time'] = $where['create_time'] = strtotime($data['online_time']);

                $file = request()->file('img');
                if (!$file) {
                    $this->error('价格图片不能为空! ');
                }

                $fl = new FileUpload();
                // 1.校验
                $fl->set('allowExt', 'jpg,png')->set('dir','image/vip/pic/');
                // 2.上传
                $path = $fl->upload($file);

                if ($path) {
                    $where['image'] = $path;
                } else {
                    $this->error('上传失败! ' . $fl->getError());
                };

                $id = $this->gameVipModel->insertGetId($where);

                if (empty($id)) {
                    $this->error('添加失败!');
                }

                $this->success('VIP信息添加成功!', 'index');
            }

        }

        // 已经添加过VIP信息的游戏，不会出现在下拉框中。
        $vipIds = $this->gameVipModel->distinct(true)->column('gameid');

        $gameList = model('Common/Game')->getAllByCondition('id,name',['id' => ['not in', $vipIds]],'name asc','self');
        $this->assign('game_list', $gameList);
        return $this->fetch();
    }


    public function edit($id)
    {
        $id = (int)$id;

        if (empty($id)) {
            $this->error('游戏ID不能为空');
        }

        $gameVipInfo = $this->gameVipModel->where(['id' => $id])->find();
        if (empty($gameVipInfo)) {
            $this->error('vip信息不能为空! ');
        }

        if (request()->isPost()) {
            $data = [
                'online_time' => input('post.game_online_date'),
            ];

            $result = $this->validate($data, [
                ['online_time', 'require', '上线时间不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            } else {
                $where['online_time'] = $where['update_time'] = strtotime($data['online_time']);

                $file = request()->file('img');
                if ($file) {
                    $fl = new FileUpload();
                    // 1.校验
                    $fl->set('allowExt', 'jpg,png')->set('dir','image/vip/pic/');
                    // 2.上传
                    $path = $fl->upload($file);

                    if ($path) {
                        $where['image'] = $path;
                    } else {
                        $this->error('上传失败! ' . $fl->getError());
                    };
                }

                $this->gameVipModel->where(['id' => $id])->update($where);

                $this->success('VIP信息编辑成功!', 'index');
            }
        }

        $gameInfo = Db::name('cy_game')->field('id,name,image')->where(['id' => $gameVipInfo['gameid']])->find();

        $this->assign('gamevip_info', $gameVipInfo);
        $this->assign('game_info', $gameInfo);

        return $this->fetch();
    }

    /**
     * 删除
     */
    public function delete()
    {
        $id = input('id', 0, 'intval');

        if(empty($id))
            $this->error('删除记录的ID不能为空');

        $info = $this->gameVipModel->where(['id'=>$id])->find();
        if(empty($info)) {
           $this->error('记录不存在');
        }

        if($this->gameVipModel->where(['id'=>$id])->delete()){
            $this->success('删除成功');
        }else{
            $this->error('删除失败');
        }
    }
}