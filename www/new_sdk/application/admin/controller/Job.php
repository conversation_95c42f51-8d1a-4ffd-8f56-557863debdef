<?php


namespace app\admin\controller;


use think\Env;

class Job
{
    /**
     * 企业签到期提醒
     */
    public function signature()
    {
        $signatureModel = new \app\common\model\Signature();


        //今天到期
        $today_list = $signatureModel->alias('a')->join('cy_game b', 'a.game_id=b.id')
            ->where('a.end_date', '=', date('Y-m-d'))->column('b.name');

        //一天后到期
        $date = date('Y-m-d', strtotime("+1 day"));
        $tomorrow_list = $signatureModel->alias('a')->join('cy_game b', 'a.game_id=b.id')
            ->where('a.end_date', '=', $date)
            ->column('b.name');

        //三天后到期
        $date = date('Y-m-d', strtotime("+3 day"));
        $three_list = $signatureModel->alias('a')->join('cy_game b', 'a.game_id=b.id')
            ->where('a.end_date', '=', $date)
            ->column('b.name');

        //五天后到期
        $date = date('Y-m-d', strtotime("+5 day"));
        $seven_list = $signatureModel->alias('a')->join('cy_game b', 'a.game_id=b.id')
            ->where('a.end_date', '=', $date)
            ->column('b.name');


        if ($today_list) {
            $msg = '【倾枫】请注意：' . implode(',', $today_list) . ' 企业签今天到期';
            $url = Env::get('notic_url');
            curlDD($msg, $url);
        }

        if ($tomorrow_list) {
            $msg = '【倾枫】请注意：' . implode(',', $tomorrow_list) . ' 企业签明天到期';
            $url = Env::get('notic_url');
            curlDD($msg, $url);
        }


        if ($three_list) {
            $msg = '【倾枫】请注意：' . implode(',', $three_list) . ' 企业签三天到期';
            $url = Env::get('notic_url');
            curlDD($msg, $url);
        }


        if ($seven_list) {
            $msg = '【倾枫】请注意：' . implode(',', $seven_list) . ' 企业签五天到期';
            $url = Env::get('notic_url');
            curlDD($msg, $url);
        }
    }

}
