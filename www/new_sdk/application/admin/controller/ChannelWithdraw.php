<?php
/**
 * 渠道提现管理相关控制器
 */

namespace app\admin\controller;
use app\common\library\MakeReportGo;
use app\common\model\Channel;
use app\common\model\Admin as AdminModel;
use app\common\model\Setting;
use app\common\library\MakeReport;
use app\common\library\FileUpload;
use think\Db;

class ChannelWithdraw extends Admin
{

	protected $redis;                       //redis的句柄对象

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
		$this->redis    = \think\Cache::store('default')->handler();
    }

    /**
     * 渠道提现列表
     */
    public function index()
    {
		$status			= input('status',0,'intval');
		$begin_time     = input('start','','trim');
		$end_time       = input('end','','trim');

        $channelModel       = new Channel;

        $where = [];
        //渠道名
        if(input('request.channel_id') <> '')
        {
            $where['a.channel_id'] = input('request.channel_id',0,'intval');
        }
		//一级审核状态
		if(input('request.status') <> ''){
			$where['a.status']	= $status;
		}
	    //申请开始时间和结束时间不为空时
        if ($begin_time != '' && $end_time != '') {
            $where['a.create_time'] = [
                ['>=', strtotime($begin_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($begin_time != '') {
            $where['a.create_time'] = ['>=', strtotime($begin_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $where['a.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }

        $channelList = $channelModel->getAllByCondition('id,name',['level'=>1,'flag'=>3],'name asc');


		if(input('download')==2){
			$list =	model('ChannelWithdraw')->alias('a')
											->join('nw_channel c','a.channel_id=c.id','left')
											->field('a.*,c.name as channel_name,a.id as id,a.create_time as create_time')
											->where($where)
											->where("a.status=1")
											->order('a.create_time desc')
											->select();

			$title = "提现管理(财务)-".date('YmdHis');
			if(!$list){
			    $this->error('暂无数据');
			}

			$total_withdraw_amt = floatval(model('ChannelWithdraw')->alias('a')
											->join('nw_channel c','a.channel_id=c.id','left')
											->where($where)
											->where("a.status=1")->sum('withdraw_amt')); // 总提现金额
			$list_more = array();
			$list_more['total_withdraw_amt'] = $total_withdraw_amt;
			$this->downloadexls($list,$title,'channelWithdraw',$list_more);
			exit();
		}
		else if(input('download')==3){
			$where = array();
			$where['a.month'] = date('Y-m',mktime(0,0,0,date('m'),01)-1);
			$channelGameDataList =	model('ChannelExtraDivideData')->alias('a')
											->join('nw_channel c','a.channel_id=c.id','left')
											->join('nw_extra_game g','a.extra_game_id=g.id','left')
											->field('a.*,c.name as channel_name,g.extra_game_name as game_name,a.id as id,a.create_time as create_time')
											->where($where)
											->order('a.channel_type asc,a.channel_id asc,a.extra_game_id asc')
											->select();
			reset($channelGameDataList);
			$list = array();
			while(list($key,$val)=@each($channelGameDataList)){
				$channelGameData = array();
				$channelGameData['game_name']		= $val['game_name'];
				$channelGameData['channel_name']	= $val['channel_name'];
				$channelGameData['start_date']		= $val['start_date'];
				$channelGameData['end_date']		= $val['end_date'];
				$channelGameData['pay_amt']			= $val['pay_amt'];
				$channelGameData['rate']			= $val['rate'];
				$channelGameData['divide_amt']		= $val['divide_amt'];
				$channelGameData['real_name']		= $val['real_name'];
				$channelGameData['zfb_account']		= $val['zfb_account'];
				$channelGameData['bank_number']		= $val['bank_number'];
				$channelGameData['bank_name']		= $val['bank_name'];
				$channelGameData['bank_open_name']	= $val['bank_open_name'];
				$list[$val['channel_id']][$val['extra_game_id']]	= $channelGameData;
			}

			$title = "上月补点数据(财务)-".date('YmdHis');
			if(!$list){
			    $this->error('暂无数据');
			}

			$channelDataList = model('ChannelExtraDivideData')->alias('a')
											->join('nw_channel c','a.channel_id=c.id','left')
											->field('channel_id,count(1) as channel_game_cnt,sum(pay_amt) as channel_pay_amt,sum(divide_amt) as channel_divide_amt')
											->where($where)
											->group('a.channel_id')
											->order('a.channel_type asc,a.channel_id asc')
											->select();
			reset($channelDataList);
			$list_more = array();
			while(list($key,$val)=@each($channelDataList)){
				$channelData = array();
				$channelData['channel_id']			= $val['channel_id'];
				$channelData['channel_game_cnt']	= $val['channel_game_cnt'];
				$channelData['channel_pay_amt']		= $val['channel_pay_amt'];
				$channelData['channel_divide_amt']	= $val['channel_divide_amt'];
				$list_more[$val['channel_id']]		= $channelData;
			}

			$this->downloadexls($list,$title,'channelExtraPoint',$list_more);
			exit();
		}

		if (request()->isAjax() && input('download')==1) {


			$sql = model('ChannelWithdraw')->alias('a')
									    ->join('nw_channel c','a.channel_id=c.id','left')
                                        ->field('a.*,c.name as channel_name,a.id as id,a.create_time as create_time')
                                        ->where($where)
                                        ->order('a.create_time desc')
                                        ->fetchSql(true)->select();

            if((new MakeReportGo())->addTask('admin.withdrawIndex',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }

		}

        $param = input('request.');		//分页带条件

        $list       =	model('ChannelWithdraw')->alias('a')
									    ->join('nw_channel c','a.channel_id=c.id','left')
                                        ->field('a.*,c.name as channel_name,a.id as id,a.create_time as create_time')
                                        ->where($where)
                                        ->order('a.create_time desc')
                                        ->paginate(10,false,array('query' => $param));
        // 获取分页显示的HTML
        $page = $list->render();

        $this->assign('channel_list',$channelList);
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('start', $begin_time);
        $this->assign('end', $end_time);

        return $this->fetch();
    }

	//审核
    public function audit()
    {
        if (request()->isAjax()) {
            $id = input('id',0,'intval');
            $beizhu = input('beizhu','','trim');
            $status = input('status','0','intval');

            if(empty($id)){
                $this->error('ID不能为空');
            }
	        if($status<>1 && $status<>2){
                $this->error('非法审核状态');
            }

			$withdrawInfo =  model('ChannelWithdraw')->where(['id' => $id])->find();
			if(empty($withdrawInfo)){
				$this->error('提现申请不存在');
			}
			else if($withdrawInfo['status'] <> 0){
				$this->error('提现申请已审核过，不能再进行审核');
			}

			if($status == $withdrawInfo['status']){
				$this->error('您未更改订单状态,请选择要更改的状态');
			}

			//检查账号
			if(!$withdrawInfo['channel_id']){
				$this->error('提现申请异常:无归属渠道');
			}
            $channelInfo = model('Channel')->where(['id'=>$withdrawInfo['channel_id']])->find();
            if (empty($channelInfo)){
				$this->error('提现申请异常:提现申请归属渠道不存在');
            }
			else if($channelInfo['level'] <> 1){
				$this->error('提现申请异常:提现申请归属渠道层级有误');
			}

			$auditData = array();
			$auditData['id'] = $id;
			$auditData['status'] = $status;
			$auditData['audit_time'] = NOW_TIMESTAMP;
			$auditData['audit_remark'] = $beizhu;
			$auditData['audit_admin_name'] = session('USERNAME');

            Db::startTrans();
            try {
				if($status==2 ){  //审核不通过
					//添加账户金额变动信息
					$detData = array();
					$detData['channel_id']      = $channelInfo['id'];
					$detData['channel_name']    = $channelInfo['name'];
					$detData['change_amount']	= $withdrawInfo['withdraw_amt'];
					$detData['account_type']	= 2;   //结算账户
					$detData['type']			= 10;   //提现审核不通过返回
					$detData['out_orderid']		= $withdrawInfo['orderid'];
					$detData['create_time']     = NOW_TIMESTAMP;
					$insertDetId = model('ChannelAccountDet')->insertGetId($detData);
					if ( !$insertDetId) {
						throw new Exception("添加账户变动明细失败");
					}
					$updData = array();
					$updData['js_amount'] = Db::raw("js_amount+".$withdrawInfo['withdraw_amt']);
					$updData['update_time'] = time();
					$updResult = model('Channel')->where(['id'=>$channelInfo['id']])->update($updData);
					if (!$updResult) {
						throw new Exception("账户金额变动失败");
					}

					$result = model('ChannelWithdraw')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
					if ($result === false) {
						throw new Exception("更改提现申请状态失败");
					}
					$this->insertLog($this->current_node, "渠道提现审核,提现单号ID:".$withdrawInfo['id']."，审核状态：审核不通过",172);
				}
				else if($status==1){  //审核通过
					$result = model('ChannelWithdraw')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
					if ($result === false) {
						throw new Exception("更改提现订单状态失败");
					}

					$this->insertLog($this->current_node, "渠道提现审核,提现单号ID:".$withdrawInfo['id']."，审核状态：审核通过",172);
				}
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }
			$this->success('审核成功');
        }
        else{
            $this->error('非法请求');
        }
    }
   /**
     * 提现处理
     */
    public function doWithdraw()
    {
		if ($this->request->isPost()) {
			$channel_id		= input('channel_id',0,'intval');			//公会ID
			$amount			= input('amount',0,'floatval');			//提现金额
			$remark			= '外部投放公会手动提现';                   //备注

			$orderid    = 'W'.makeOrderid();

			if(!$amount){
				$this->jsonResult('', 0, '请填写提现金额！');
			}
			else if(abs(intval($amount*100)/100-$amount) > 0.01){
				$this->jsonResult('', 0, '请正确填写提现金额！');
			}

			$channelInfo   = model("Channel")->field("id,name,parent_id,level,cps_settle_period,mcps_settle_period,status,js_amount")->where(['id'=>$channel_id,'level'=>1])->find();
			if(empty($channelInfo)){
				$this->jsonResult('', 0, '公会账号异常,请选择正确的公会账号');
			}
			else if($channelInfo['js_amount']<$amount){
				$this->jsonResult('', 0, '您的结算余额不足');
			}
			//只能结算外放的公会
			$parentChannelInfo = model("Channel")->field("id,name,level,cps_settle_period,mcps_settle_period,status,channel_tg_type")->where(['id'=>$channelInfo['parent_id'],'level'=>0])->find();
			if(empty($parentChannelInfo)){
				$this->jsonResult('', 0, '公会所属商务账号异常');
			}
			else if($parentChannelInfo['channel_tg_type']<>1){
				$this->jsonResult('', 0, '非外放商务下公会不能使用该功能');
			}

			/*
			$relChannelInfo   = model("ChannelInfo")->where(['channel_id'=>$channel_id])->find();
			if(!$relChannelInfo){
				$this->jsonResult('', 0, '请先补充完提现相关信息后再进行提现');
			}

			$channelInfoApply   = model("ChannelInfo")->field("id,channel_id,real_name,apply_status,type,person_id,zfb_account,bank_ban_mobile,bank_number,bank_province_city,bank_name,bank_open_name,bank_open_number")->where(['channel_id'=>$channel_id,'apply_status'=>1])->find();
			if(empty($channelInfoApply)){
				$this->jsonResult('', 0, '您的身份尚未认证通过,不能进行提现');
			}
			*/

			//指定时间内，禁止重复下单
			if(!requestDuplicateCheck('channel_withdraw_duplicate_'.$channel_id,10)){
				$this->jsonResult('', 0, '提现请求过多，请于10s以后，再次进行提现操作');
			}

			$withdrawData = array();
			$withdrawData['orderid']		= $orderid;
			$withdrawData['channel_id']     = $channel_id;
			$withdrawData['channel_name']   = $channelInfo['name'];
			$withdrawData['withdraw_amt']	= $amount;
			$withdrawData['remark']			= $remark;
			$withdrawData['status']			= 0;
			$withdrawData['create_time']	= time();

			$withdrawData['real_name']		= '张智博';
			$withdrawData['person_id']		= '';
			$withdrawData['zfb_account']	= '';
			$withdrawData['bank_ban_mobile']= '';
			$withdrawData['bank_number']	= '6236684220002075555';
			$withdrawData['bank_province_city']	= '陕西,西安';
			$withdrawData['bank_name']		= '中国建设银行';
			$withdrawData['bank_open_name']	= '中国建设银行股份有限公司西安龙首北路支行';
			$withdrawData['bank_open_number']	= '';

			// 启动事务
			Db::startTrans();

			try{
				$result = '';   //返回值

				$insertWithdrawId = model("ChannelWithdraw")->insert($withdrawData);
				if(!$insertWithdrawId){
					throw new Exception("创建提现订单失败! ");
				}

				$detData = array();
                $detData['channel_id']      = $channel_id;
                $detData['channel_name']    = $channelInfo['name'];
                $detData['change_amount']	= -$amount;
                $detData['account_type']	= 2;   //结算账户
                $detData['type']			= 9;   //提现
                $detData['out_orderid']		= $orderid;
                $detData['create_time']     = NOW_TIMESTAMP;
                $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
				if ( !$insertDetId) {
					throw new Exception("添加账户变动明细失败");
				}
				$updData = array();
				$updData['js_amount'] = Db::raw("js_amount-".$amount);
				$updData['update_time'] = time();
				$updResult = model('Channel')->where(['id'=>$channel_id,'js_amount'=>array('egt',$amount)])->update($updData);
				if (!$updResult) {
					throw new Exception("账户金额变动失败");
				}

				$this->redis->del('channel_withdraw_duplicate_'.$channel_id);
				// 提交事务
				Db::commit();

			} catch (\Exception $e) {
				// 回滚事务
				Db::rollback();

				$this->jsonResult('', 0, '提现订单生成失败'.$e->getMessage());
			}

			$result['orderid']      = $orderid;

			$this->jsonResult($result, 20000, '提现订单生成成功');
			exit;
		}
		else{
			$this->jsonResult('', 0, '非法请求');
			exit;
		}
    }
}
