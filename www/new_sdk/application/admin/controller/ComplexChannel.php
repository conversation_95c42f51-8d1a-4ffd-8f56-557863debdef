<?php
/**
 * 聚合渠道控制器
 *
 */

namespace app\admin\controller;

use think\Db;
use think\Exception;
use app\common\model\ComplexChannelModel;
use app\common\model\Game;
use app\common\model\Polychannel;
use app\common\model\PolychannelGame;
use app\common\model\PolyChannelDeposit;
use app\common\model\PolyChannelDepositRecord;
use app\common\model\PolyChannelGameFrozen;
use app\common\logic\Complex;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class ComplexChannel extends Admin
{
    public function _initialize()
    {
        parent::_initialize();

        $this->complexChannelModel      = new ComplexChannelModel;
		$this->_gameModel               = new Game;
        $this->_polyChannelDeposit      = new PolyChannelDeposit;
		$this->_polyChannelDepositRecord= new polyChannelDepositRecord;
        $this->_polyChannelGameFrozen   = new PolyChannelGameFrozen;
        $this->complexLogic             = new Complex;
    }

    /**
     * 聚合渠道列表
     * @return mixed|string
     */
    public function index()
    {
        /**搜索条件**/
        $name   = input('name');
        $status = input('status');
        $register = input('register');
        $login = input('login');
        $consume = input('consume');

        $where['a.flag'] = 4;

        if ($name) {
            $where['a.name'] = ['like', "%$name%"];
        }

        if ($status!='') {
            $where['a.status'] = $status;
        }

        if ($register!='') {
			if($register==1){
				$where['b.register'] = $register;
			}
			else{
			    $where['b.register'] = [['eq',0],['exp',Db::raw('is null')],'or'];
			}
        }
        if ($login!='') {
			if($login==1){
				$where['b.login'] = $login;
			}
			else{
			    $where['b.login'] = [['eq',0],['exp',Db::raw('is null')],'or'];
			}
        }
        if ($consume!='') {
			if($consume==1){
				$where['b.consume'] = $consume;
			}
			else{
			    $where['b.consume'] = [['eq',0],['exp',Db::raw('is null')],'or'];
			}
        }

        $channles = $this->complexChannelModel
						->alias('a')
						->join("nw_polychannel b","a.id=b.channel_id","left")
						->field('a.id,a.name,a.mark,a.remark,a.create_time,a.status,b.channel_id,b.param_field,b.login,b.register,b.consume')
                        ->where($where)
                        ->order("a.id DESC")
                        ->paginate(30,false, ['query' => input('request.')]);
        // 获取分页显示
        $page = $channles->render();

        $this->assign('total', $channles->total());
        $this->assign("page", $page);
        $this->assign("channles", $channles);

        return $this->fetch();
    }

    /**
     * 添加渠道
     *
     * @return mixed
     */
    public function add()
    {
        return $this->fetch();
    }

    /**
     * 添加渠道
     *
     */
    public function addPost()
    {
        $polychannelModel = new Polychannel;

        $param_field = input('post.param_field','','trim');
        $field_client = input('post.field_client','','trim');
        $register= input('post.register',0,'intval');
        $login= input('post.login',0,'intval');
        $consume= input('post.consume',0,'intval');

        $data = ['name' 	   => input('post.name','','trim'),
                'remark'       => input('post.remark'),
                'mark'         => input('post.mark','','trim'),
                'status'       => input('post.status'),
	            'register'     => input('post.register'),
                'login'        => input('post.login'),
                'consume'      => input('post.consume'),
                'param_field'  => $param_field,
                'field_client'  => $field_client,
            ];

        $result = $this->validate($data,
            [
                ['name', 'require', '请填写渠道名称'],
                ['remark', 'max:255', '渠道描述最大不超过255个字符'],
                ['mark', 'require|alpha|max:20', '请填写渠道标识|渠道标识必须为英文|渠道标识最大不超过20个字符'],
                ['status', 'require|integer', '渠道状态不能为空|渠道状态必须为整数'],
                ['register', 'require|integer', '渠道新增状态不能为空|渠道新增状态必须为整数'],
                ['login', 'require|integer', '渠道登录状态不能为空|渠道登录状态必须为整数'],
                ['consume', 'require|integer', '渠道消费状态不能为空|渠道消费状态必须为整数'],
                ['param_field', 'max:500', '服务端参数字段名最大不超过500个字符'],
                ['field_client', 'max:500', '客户端参数字段名最大不超过500个字符'],
            ]);

        if (true !== $result) {
            $this->error($result);
        }
        // elseif(!empty($param_field) && count(explode(';',$param_field))!=2){
        //     $this->error('参数字段超过两个或不足两个，请联系技术员进行特殊处理');
        // }
        else {
            Db::startTrans();

            try {

                //1. 添加渠道信息
                $channelInfo = $this->complexChannelModel->where(['name' => $data['name']])->find();
                if ($channelInfo) {
                    throw new Exception("该渠道信息已存在");
                }

                if ( $this->complexChannelModel->where(['mark' => $data['mark']])->find() ) {
                    throw new Exception("渠道标识不能重复");
                }

                unset($data['param_field']);
                unset($data['field_client']);
				unset($data['register']);
				unset($data['login']);
				unset($data['consume']);

                $data['flag']           = 4;
                $data['create_time']    = NOW_TIMESTAMP;

                //添加渠道信息
                $this->complexChannelModel->save($data);
                if ( !$this->complexChannelModel->id ) {
                    throw new Exception("添加渠道信息失败");
                }

                //2. 添加聚合渠道参数
				$polyChannelData = array();
				$polyChannelData['channel_id'] = $this->complexChannelModel->id;
				if(!empty($param_field)){
					$polyChannelData['param_field'] = $param_field;
				}
				if(!empty($field_client)){
					$polyChannelData['field_client'] = $field_client;
				}
				$polyChannelData['register'] = $register?1:0;
				$polyChannelData['login'] = $login?1:0;
				$polyChannelData['consume'] = $consume?1:0;
				$polyChannelData['update_time'] = NOW_TIMESTAMP;
                $polychannelModel->save($polyChannelData);

                $status = $data['status'] ? '正常' : '冻结';
				$register = $register ? '冻结' : '正常';
                $login = $login ? '冻结' : '正常';
                $consume = $consume ? '冻结' : '正常';

                //写入日志
                $this->insertLog($this->current_node, "新增聚合渠道：{$data['name']}，渠道标识：{$data['mark']}，渠道状态：{$status}，新增状态：{$register}，登录状态：{$login}，消费状态：{$consume}", 54);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }

            $this->success("添加成功！", "ComplexChannel/index");
        }
    }

    /**
     * 编辑渠道
     *
     * @return mixed
     */
    public function edit()
    {
        $polychannelModel = new Polychannel;

        $id = input('id', 0, 'intval');
        $channel = $this->complexChannelModel->where(["id" => $id])->find();

        if(empty($channel)){

            $this->error('聚合渠道信息不存在');
        }

        $polychannel = $polychannelModel->where(["channel_id" => $id])->find();
        if(!empty($channel)){
			$param_field = $polychannel['param_field'];
			$field_client = $polychannel['field_client'];
			$register = $polychannel['register'];
			$login = $polychannel['login'];
			$consume = $polychannel['consume'];
		}
		else{
			$param_field = '';
			$field_client = '';
			$register = 0;
			$login = 0;
			$consume = 0;
		}

        $this->assign('channel', $channel);
        $this->assign('param_field', $param_field);
        $this->assign('field_client', $field_client);
        $this->assign('register', $register);
        $this->assign('login', $login);
        $this->assign('consume', $consume);

        return $this->fetch();
    }

    /**
     * 编辑渠道接口
     *
     */
    public function editPost()
    {
        $id         = input('post.id',0,'intval');
        $param_field= input('post.param_field','','trim');
        $field_client= input('post.field_client','','trim');
        $register= input('post.register',0,'intval');
        $login= input('post.login',0,'intval');
        $consume= input('post.consume',0,'intval');

        $polychannelModel = new Polychannel;

        $data = ['name' 	   => input('post.name','','trim'),
                'remark'       => input('post.remark'),
                'status'       => input('post.status'),
                'register'     => input('post.register'),
                'login'        => input('post.login'),
                'consume'      => input('post.consume'),
                'param_field'  => $param_field,
                'field_client'  => $field_client
            ];

        $result = $this->validate($data,
            [
                ['name', 'require', '请填写渠道名称'],
                ['remark', 'max:255', '渠道描述最大不超过255个字符'],
                ['status', 'require|integer', '渠道状态不能为空|渠道状态必须为整数'],
				['register', 'require|integer', '渠道新增状态不能为空|渠道新增状态必须为整数'],
				['login', 'require|integer', '渠道登录状态不能为空|渠道登录状态必须为整数'],
				['consume', 'require|integer', '渠道消费状态不能为空|渠道消费状态必须为整数'],
                ['param_field', 'max:500', '服务端参数字段名最大不超过500个字符'],
                ['field_client', 'max:500', '客户端参数字段名最大不超过500个字符'],
            ]);

        if (true !== $result) {
            $this->error($result);
        } elseif (empty($this->complexChannelModel->where(['id' => $id])->find())) {
            $this->error('聚合渠道不存在');
        }
        // elseif(!empty($param_field) && count(explode(';',$param_field))!=2){
        //     $this->error('参数字段超过两个或不足两个，请联系技术员进行特殊处理');
        // }
        else {
            Db::startTrans();

            try {
                //1. 添加渠道信息
                $channelInfo = $this->complexChannelModel->where(['name' => $data['name'], 'id' => ['<>', $id]])->find();
                if ($channelInfo) {
                    throw new Exception("渠道名称不能重复");
                }

                unset($data['param_field']);
                unset($data['field_client']);
                unset($data['register']);
                unset($data['login']);
                unset($data['consume']);

                //添加渠道信息
                $this->complexChannelModel->save($data, ['id' => $id]);


                //2. 添加聚合渠道参数

                $polyChannelData = array();
                if (!empty($param_field)) {
                    $polyChannelData['param_field'] = $param_field;
                }
                if (!empty($field_client)) {
                    $polyChannelData['field_client'] = $field_client;
                }
                $polyChannelData['register'] = $register ? 1 : 0;
                $polyChannelData['login'] = $login ? 1 : 0;
                $polyChannelData['consume'] = $consume ? 1 : 0;
                $polyChannelData['update_time'] = NOW_TIMESTAMP;

                //更新
                if($polychannelModel->where(['channel_id'=>$id])->find())
                {
                    $result = $polychannelModel->save($polyChannelData,['channel_id'=>$id]);
                }
                //新增
                else{
					$polyChannelData['channel_id'] = $id;
                    $result = $polychannelModel->save($polyChannelData);
                }

                $status = $data['status'] ? '正常' : '冻结';
                $mark   = $this->complexChannelModel->where(['id' => $id])->value('mark');
				$register = $register ? '冻结' : '正常';
                $login = $login ? '冻结' : '正常';
                $consume = $consume ? '冻结' : '正常';
                //写入日志
                $this->insertLog($this->current_node, "修改聚合渠道：{$data['name']}，渠道标识：{$mark}，渠道状态：{$status}，新增状态：{$register}，登录状态：{$login}，消费状态：{$consume}", 55);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error("编辑失败： " . $e->getMessage());
            }

            $this->success("编辑成功！", "ComplexChannel/index");
        }
    }

    //变更渠道状态
    public function updateStatus()
    {
        $polychannelModel = new Polychannel;

        //渠道ID
        $id     = input('id', 0, 'intval');
        $status = input('status', 0, 'intval');

        if (!empty($id)) {
            $result = $this->complexChannelModel->where(["id" => $id,'flag'=>4])->setField('status', $status);
            if ($result !== false) {
				//添加日志
                $frozenInfo   = $polychannelModel->where(['id' => $id])->find();
                $channelInfo   = $this->complexChannelModel->where(['id' => $id])->find();
                $channel_name = $channelInfo['name'];
				$mark		  = $channelInfo['mark'];
				$register = $frozenInfo['register'] ? '冻结' : '正常';
                $login = $frozenInfo['login'] ? '冻结' : '正常';
                $consume = $frozenInfo['consume'] ? '冻结' : '正常';
                $statusStr = $status ? '正常' : '冻结';
                $this->insertLog($this->current_node, "修改聚合渠道：{$channel_name}，渠道标识：{$mark}，渠道状态：{$statusStr}，新增状态：{$register}，登录状态：{$login}，消费状态：{$consume}", 55);

                $this->success("更改渠道状态成功！", url("ComplexChannel/index"));
            } else {
                $this->error('更改渠道状态失败！');
            }
        } else {
            $this->error('数据传入失败！');
        }
    }


    // +----------------------------------------------------------------------
    // | 聚合游戏
    // +----------------------------------------------------------------------

    /**
     * 聚合游戏列表
     */
    public function gameList()
    {
        $is_down = input('request.download', '0', 'intval');
        $channel_id = input('request.channel_id', '0', 'intval');
        $gameid = $this->request->param('gameid', 0, 'intval');
        $type = input('request.type', '');

        $condition = [];
        if (!empty($channel_id)) {
            $condition['cg.channel_id'] = $channel_id;
        }
        if (!empty($gameid)) {
            $condition['cg.game_id'] = $gameid;
        }
        if (!empty($type)) {
            $condition['g.type'] = $type;
        }

        $polychannelGameModel = new PolychannelGame;
        if ($is_down) {
            $list = $polychannelGameModel->table('cy_polychannel_game cg,nw_complex_channel c,cy_game g')
                ->field('cg.game_id,cg.id,cg.create_time,cg.remark,cg.param,g.name game_name,g.origin_name,c.name channel_name,c.mark')
                ->where('cg.channel_id=c.id and g.id=cg.game_id')
                ->where($condition)
                ->order("cg.id DESC")
                ->select();
        } else {
            $list = $polychannelGameModel->table('cy_polychannel_game cg,nw_complex_channel c,cy_game g')
                ->field('cg.game_id,cg.id,cg.create_time,cg.remark,cg.param,g.name game_name,g.origin_name,c.name channel_name,c.mark')
                ->where('cg.channel_id=c.id and g.id=cg.game_id')
                ->where($condition)
                ->order("cg.id DESC")
                ->paginate(30, false, ['query' => input('get.')]);
        }

        if($is_down>0){
			$title = "聚合游戏列表-".date('YmdHis');
			if(!$list){
			    $this->error('暂无数据');
			}
			$this->downloadexls($list,$title,'listGame');
			exit();
		}

        // 获取分页显示
        $page = $list->render();

		$channelList = $this->_getChannelList();
		$selfChannelList = array();
		foreach ($channelList as $channel) {
              $selfChannelList[ $channel['id']] = $channel;
        }
        $this->assign('channel_list', $selfChannelList);

        $this->assign('total', $list->total());
        $this->assign("page", $page);
        $this->assign('list',$list);

        $SelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$GameList = array();
		foreach ($SelfGameList as $game) {
              $GameList[ $game['id']] = $game;
        }
        $this->assign('game_list', $GameList);

        return $this->fetch('game_list');
    }

    /**
     * 获得聚合渠道的参数
     */
    public function ajaxGetChannelGameParam()
    {
        if(request()->isAjax())
        {
            $polychannelGameModel   = new PolychannelGame;
            $polychannelModel       = new Polychannel;

            $id = input('post.id');     //渠道游戏ID

            $info = $polychannelGameModel->field('channel_id,game_id,param,param_client')
                    ->where(['id'=>$id])
                    ->find();

            if(empty($info)){

                $this->error('聚合渠道游戏信息不存在');
            }

            $return_html = '服务端参数<br>';
            $return_html_client = '客户端参数<br>';

            $return_html .= $info['param_client'].'<br>';
            $return_html_client .= $info['param'].'<br>';

            $this->result(['return_html'=>$return_html_client.'<br>'.$return_html],1);
        }
        else{

            $this->error('非法请求');
        }
    }

    /**
     * 聚合渠道游戏新增
     */
    public function gameAdd()
    {
        $complexChannelModel = new ComplexChannelModel;
        $gameModel = new Game;

        //表单提交
        if (request()->isPost()) {
            $polychannelGameModel = new PolychannelGame;
            $polychannelModel = new Polychannel;

            $input = input();
            // $param = input('post.param');                 //参数
            $param = $input['param'];                        //参数
            $param_client = $input['param_client']??"";          //参数

            $data = ['game_id' => input('post.game_id', 0, 'intval'),
                'channel_id' => input('post.channel_id', 0, 'intval'),
                'remark' => input('post.remark'),
            ];

            $result = $this->validate($data,
                [
                    ['game_id', 'require|positiveInteger', '请选择游戏|游戏ID必须为整型'],
                    ['channel_id', 'require|positiveInteger', '请选择渠道|渠道ID必须为整型'],
                    ['remark', 'max:500', '备注最大不超过500个字符'],
                ]);

            if (true !== $result) {
                $this->error($result);
            } elseif ($polychannelGameModel->where(['game_id' => $data['game_id'], 'channel_id' => $data['channel_id']])->find()) {
                $this->error('渠道游戏信息已经存在');
            } else {
                $data['create_time'] = NOW_TIMESTAMP;

                $paramArr = $polychannelModel->field('param_field,field_client')->where(['channel_id' => $data['channel_id']])->find();
                if ($param && !empty($paramArr['param_field'])) {
                   // if(count($param)!=2){
                   //     $this->error('参数字段超过两个或不足两个，请联系技术员进行特殊处理');
                   // }

                    $paramHandle = explode(';', $paramArr['param_field']);
                    $newParam = [];
                    for ($i = 0; $i < count($paramHandle); $i++) {
                        $newParam[$paramHandle[$i]] = '';
                        if (!empty($param[$i])) {
                            $newParam[$paramHandle[$i]] = $param[$i];
                        }
                    }
                    if ($newParam) {
                        $newParam = json_encode($newParam);
                    }
                    $data['param'] = $newParam;
                }
                if ($param_client) {
                    $data['param_client'] = $param_client;
                }

                Db::startTrans();

                try {
                    $polychannelGameModel->insert($data);

                    //如果聚合渠道预付款配置表没有记录时，新增
                    if(!$this->_polyChannelDeposit->where(['channel_id'=>$data['channel_id']])->find())
                    {
                        //聚合渠道预付款配置
                        $this->_polyChannelDeposit->insert(['channel_id'=>$data['channel_id'],'mobile'=>'','create_time'=>NOW_TIMESTAMP]);
                    }

                    //聚合渠道游戏冻结配置
                    $this->_polyChannelGameFrozen->insert(['game_id'=>$data['game_id'],'channel_id'=>$data['channel_id'],'create_time'=>NOW_TIMESTAMP]);

                    Db::commit();
                }
                catch (\Exception $e) {
                    Db::rollback();

                    $this->error("渠道游戏添加失败！".$e->getMessage());
                }

                $this->success('渠道游戏添加成功','ComplexChannel/gameList');
            }
        }

        $channelList= $complexChannelModel->field('id,name')->where(['status'=>1,'flag'=>4])->order('name asc')->select();
		$selfChannelList = array();
		foreach ($channelList as $channel) {
              $selfChannelList[ $channel['id']] = $channel;
        }

        $gameList   = $gameModel->getAllByCondition('id,name',['isdelete'=>0],'name asc','self');
		$selfGameList = array();
		foreach ($gameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
        $this->assign('game_list',$selfGameList);
        $this->assign('channel_list',$selfChannelList);

        return $this->fetch('game_add');
    }

    /**
     * 获得聚合渠道的参数字段
     */
    public function ajaxGetChannelParamField()
    {
        if(request()->isAjax())
        {
            $channel_id = input('post.channel_id');

            $polychannelModel = new Polychannel;

            $param_field = $polychannelModel->field('param_field,field_client')->where(['channel_id'=>$channel_id])->find();
            $this->result(['param_field'=>$param_field['param_field'],'field_client'=>$param_field['field_client']],1);
        }
        else{

            $this->error('非法请求');
        }
    }

    /**
     * 聚合渠道游戏编辑
     */
    public function gameEdit()
    {
        $polychannelGameModel = new PolychannelGame;
        $polychannelModel     = new Polychannel;

        $id = input('id',0,'intval');

        $info = $polychannelGameModel->table('cy_polychannel_game cg,nw_complex_channel c,cy_game g')
                    ->field('cg.*,g.name game_name,c.name channel_name')
                    ->where('cg.channel_id=c.id and g.id=cg.game_id')
                    ->where(['cg.id'=>$id])
                    ->find();

        if(empty($info)){
            $this->error('聚合渠道游戏信息不存在');
        }

        //参数字段
        $param_field_arr = $polychannelModel->field('field_client,param_field')->where(['channel_id'=>$info['channel_id']])->find();
        $param_field = $param_field_arr['param_field'];
        $field_client = $param_field_arr['field_client'];

        //表单提交
        if(request()->isPost())
        {
            $param = input('post.param');     //参数
            $param_client = input('post.param_client');     //参数

            $data = [
                'remark' => input('post.remark'),
            ];

            $result = $this->validate($data,
                [
                    ['remark', 'max:500', '备注最大不超过500个字符'],
                ]);

            if (true !== $result) {
                $this->error($result);
            }
            else{
                if($param){

                   // if(count($param)!=2){
                   //     $this->error('参数字段超过两个或不足两个，请联系技术员进行特殊处理');
                   // }
                    if(!is_json($param)){
                        $this->error('json异常');
                    }
                    $data['param'] = $param;
                }

                if($param_client){
                    $data['param_client'] = $param_client;
                }

                $polychannelGameModel->save($data,['id'=>$id]);
                $this->success('渠道游戏编辑成功','ComplexChannel/gameList');
            }
        }

        $this->assign('info',$info);
        $this->assign('param_field',$param_field);
        $this->assign('arrParam',$info['param']);
        $this->assign('field_client',$field_client);
        $this->assign('arrParamClient',$info['param_client']);
        return $this->fetch('game_edit');
    }

    /**
     * 聚合游戏记录删除
     */
    public function gameDelete()
    {
        $id = $this->request->param('id', 0, 'intval');

        $polychannelGameModel = new PolychannelGame;

        Db::startTrans();

        try {
            // 获取游戏id和渠道id
            $info = $polychannelGameModel->where(['id'=>$id])->field('game_id,channel_id')->find();
            $polychannelGameModel->where(['id'=>$id])->delete();

            $this->_polyChannelGameFrozen->where(['game_id'=>$info['game_id'],'channel_id'=>$info['channel_id']])->delete();

            Db::commit();
        } catch (\Exception $e) {

            Db::rollback();
            $this->error("删除失败！".$e->getMessage());
        }

        $this->success("删除成功！");
    }

    // 聚合游戏冻结配置
    public function listGameFrozen(){
        $is_down = input('request.download', '0', 'intval');
        $channel_id = $this->request->param('channel_id', 0, 'intval');
	//	$channel_id = 777;
        $condition = [];
		$condition['channel_id'] = $channel_id;

        $list  = [];
		$count_total_amount = $count_real_amount = $total_advance = $balance = $last_advance = 0;
        $param = input('get.');
        if (!empty($condition)) {
			if($is_down>0){
				$list = $this->_polyChannelGameFrozen->where($condition)->order('id desc')->select();
			}
			else{
				$list = $this->_polyChannelGameFrozen->where($condition)->order('id desc')->paginate(10, false, array('query' => $param));
			}

            foreach ( $list as &$game ) {
                $game['game_name']    = $this->_getGameName($game['game_id']);
				$game['origin_name']    = $this->_getGameOriginName($game['game_id']);
                $game['divide_point'] = (0 != $game['divide_point']) ? $game['divide_point']: false;
            }

            $count_total_amount = floatval($this->_polyChannelGameFrozen->where(['channel_id' => $channel_id, 'status' => 1])->sum('total_amount')); // 总流水
            $count_real_amount  = floatval($this->complexLogic->getChannelTotalAmount($channel_id)); // 实际总流水
            $ChannelDepositInfo = $this->_polyChannelDeposit->where(['channel_id' => $channel_id])->find(); // 已交预付款
			if($ChannelDepositInfo){
				$total_advance = floatval($ChannelDepositInfo->total_advance);
				$last_advance = floatval($ChannelDepositInfo->last_advance); // 已交预付款
			}
            $balance = floatval(bcsub($total_advance, $count_real_amount, 2)); // 当前余额

			if($is_down>0){
				$title = "聚合游戏冻结配置-".date('YmdHis');
				if(!$list){
				   $this->error('暂无数据');
				}
				$list_more = array();
				$list_more['count_total_amount'] = $count_total_amount;
				$list_more['count_real_amount'] = $count_real_amount;
				$list_more['total_advance'] = $total_advance;
				$list_more['last_advance'] = $last_advance;
				$list_more['balance'] = $balance;
				$this->downloadexls($list,$title,'listGameFrozen',$list_more);
				exit();
			}
        }


        $this->assign('list', $list);
        $this->assign('total', $list->total());
		$this->assign('page', $list->render());

	//	echo $count_total_amount."---count_total_amount-------".$count_real_amount."---count_real_amount----".$total_advance."----total_advance-----".$balance."---balance----".$last_advance."----last_advance------<br>";
        $this->assign('count', [$count_total_amount,$count_real_amount,$total_advance,$balance,$last_advance]);

		$channelList = $this->_getChannelList();
		$selfChannelList = array();
		foreach ($channelList as $channel) {
              $selfChannelList[ $channel['id']] = $channel;
        }
        $this->assign('channel_list', $selfChannelList);

        $this->assign('select_channel',$channel_id);

		return $this->fetch("listGameFrozen");
    }

    public function editGameFrozen() {
        if (request()->isPost()) {
            $divide_point   = $this->request->param('divide_point', 0, 'floatval');
            $status         = $this->request->param('status', 0, 'intval');
            $depf_register  = $this->request->param('depf_register', 0, 'intval');
            $depf_login     = $this->request->param('depf_login', 0, 'intval');
            $depf_consume   = $this->request->param('depf_consume', 0, 'intval');

            if (0 >= $divide_point || 50 < $divide_point) {
                $this->error('分成配置只能在0-50之间');
            }

            $switch = [0,1];
            if ( ! in_array($status, $switch) || ! in_array($depf_register, $switch) || ! in_array($depf_login, $switch) || ! in_array($depf_consume, $switch) ) {
                $this->error('预付状态|禁止新增|禁止登录|禁止充值等参数只能设定为1或0');
            }

            $id = $this->request->param('id', 0, 'intval');
            $data = [
                'divide_point'  => $divide_point,
                'depf_register' => $depf_register,
                'depf_login'    => $depf_login,
                'depf_consume'  => $depf_consume,
                'status'        => $status,
                'update_time'   => time()
            ];

            $update = $this->_polyChannelGameFrozen->where(['id' => $id])->update($data);

            if ( false === $update ) {
                $this->error('更新失败');
            } else {
                $info     = $this->_polyChannelGameFrozen->where(['id' => $id])->column('id,channel_id,game_id');

                $content  = '编辑了渠道：'.$this->_getChannelName($info[$id]['channel_id'])."，游戏：".$this->_getGameName($info[$id]['game_id']);
                $content .= "，分成配置：".$divide_point;
                $content .= "，预付状态：".($status ? '开启' : '关闭');
                $content .= "，禁止新增：".($depf_register ? '开启' : '关闭');
                $content .= "，禁止登录：".($depf_login ? '开启' : '关闭');
                $content .= "，禁止充值：".($depf_consume ? '开启' : '关闭');
				$this->insertLog($this->current_node, $content, 51);
                $this->success('更新成功', url('ComplexChannel/listGameFrozen', ['channel_id' => $info[$id]['channel_id'] ] ) );
            }
        } else {
			$id = $this->request->param('id', 0, 'intval');
            if ( empty($id) ) {
                $this->error('参数出错');
            }

            $info = $this->_polyChannelGameFrozen->field(['id', 'game_id', 'channel_id', 'divide_point', 'depf_register', 'depf_login', 'depf_consume', 'status'])->find($id);
            if ( empty($info) ) {
                $this->error('数据不存在');
            }

            $info['game_name']    = $this->_getGameName($info['game_id']);
            $info['divide_point'] = (0 != $info['divide_point']) ? $info['divide_point']: '';

            $this->assign('vo', $info);

            return $this->fetch("editGameFrozen");
        }
    }

    // 聚合渠道预付款配置
    public function listDeposit() {
        $channel_id = $this->request->param('channel_id', 0, 'intval');

        $condition = [];
        if (!empty($channel_id) ) {
            $condition['channel_id'] = $channel_id;
        }

		//查询参数
        $param = input('get.');
		$list = $this->_polyChannelDeposit->where($condition)->order('id desc')->paginate(10, false, array('query' => $param));
	//	var_dump($list);
        foreach ( $list as &$channel ) {
            $channel['channel_name'] = $this->_getChannelName($channel['channel_id']);
            $channel['channel_desc'] = $this->_getChannelDesc($channel['channel_id']);
            $channel['total_amount'] = $this->complexLogic->getChannelTotalAmount($channel['channel_id']);
        }
        $this->assign('total', $list->total());
		$this->assign('page', $list->render());
        $this->assign('list', $list);

		$channelList = $this->_getChannelList();
		$selfChannelList = array();
		foreach ($channelList as $channel) {
              $selfChannelList[ $channel['id']] = $channel;
        }
        $this->assign('channel_list', $selfChannelList);

        return $this->fetch("listDeposit");
    }

    public function editDeposit() {
        if (request()->isPost()) {
            $last_advance = $this->request->param('add2advance', 0, 'intval');
            $mobile       = $this->request->param('mobile', '', 'trim');
            $warn_amount   = $this->request->param('warn_amount', 0, 'floatval');
            $over_limit_amount   = $this->request->param('over_limit_amount', 0, 'floatval');
            $remark       = $this->request->param('remark', '', 'filterAndTrimInput');

            if ( 0 > $warn_amount || 0 > $over_limit_amount ) {
                $this->error('预警额度和可超额度都不能小于零');
            }
			/*
            if ($last_advance < 0) {
                $this->error('新增预付款必须为正整数,不追加请留空');
            }
			*/

			$mobile = str_replace(PHP_EOL, ',', $mobile);
			$mobileArray = explode(",",$mobile);
			while(list($key,$val)=@each($mobileArray)){
				if (trim($val)) {
					if(! preg_match("/^1\d{10}$/", trim($val))){
						$this->error('存在手机号码不正确,请确认');
					}
					else{
						$mobileArray[$key] = trim($val);
					}
				}
				else{
					unset($mobileArray[$key]);
				}
			}
			$mobileArray = array_unique($mobileArray);
			$mobile = implode(",",$mobileArray);

            $id   = $this->request->param('id', 0, 'intval');
            $data = [
                'mobile'        => $mobile,
                'warn_amount'    => $warn_amount,
                'over_limit_amount'    => $over_limit_amount,
                'remark'        => $remark,
                'update_time'   => time()
            ];

			// 启动事务
			$result = true;
			Db::startTrans();
            if ( 0 <> $last_advance ) {
                $data['last_advance']   = $last_advance;
                $data['total_advance']  = Db::raw("total_advance+{$last_advance}");
				$channel_id = $this->_polyChannelDeposit->where(['id' => $id])->value('channel_id');

				$DepositRecord = array();
				$DepositRecord['channel_id'] = $channel_id;
				$DepositRecord['amount'] = $last_advance;
				$DepositRecord['admin_id']    = session('ADMIN_ID') ?: 1;
				$DepositRecord['admin_name']  = session('USERNAME') ?: '';
				$DepositRecord['remark']  = $remark;
				$DepositRecord['create_time'] = time();
				$result = $this->_polyChannelDepositRecord->insert($DepositRecord);
            }

            $update = $this->_polyChannelDeposit->where(['id' => $id])->update($data);

            if ( false === $update || false === $result) {
				Db::rollback();
                $this->error('更新失败');
            } else {
				Db::commit();

                $channel_id = $this->_polyChannelDeposit->where(['id' => $id])->value('channel_id');
                $content  = '编辑了渠道：'.$this->_getChannelName($channel_id);
                ( 0 <> $last_advance ) && $content .= "，增加预付款：".$last_advance;
                $content .= "，手机号码：".$mobile;
                $content .= "，预警额度：{$warn_amount}元";
                $content .= "，可超额度：{$over_limit_amount}元";
                $content .= "，备注：".$remark;
				$this->insertLog($this->current_node, $content, 52);
                $this->success('更新成功', url('ComplexChannel/listDeposit') );
            }
        } else {
            $id = $this->request->param('id', 0, 'intval');
            if ( empty($id) ) {
                $this->error('参数出错');
            }

            $info = $this->_polyChannelDeposit->field(['id', 'channel_id', 'total_advance', 'mobile', 'warn_amount', 'over_limit_amount', 'remark'])->find($id);
            if ( empty($info) ) {
                $this->error('数据不存在');
            }

            $info['channel_name'] = $this->_getChannelName($info['channel_id']);
            $info['channel_desc'] = $this->_getChannelDesc($info['channel_id']);
            $info['total_amount'] = $this->complexLogic->getChannelTotalAmount($info['channel_id']);
            $info['mobile'] = str_replace(',',PHP_EOL, $info['mobile']);  //多个手机号用逗号分隔

            $this->assign('vo', $info);

            return $this->fetch("editDeposit");
        }
    }
    // 聚合渠道预付款记录
    public function listDepositRecord() {
		$channel_id = $this->request->param('channel_id', 0, 'intval');
        $condition = [];
        if ( ! empty($channel_id) ) {
            $condition['channel_id'] = $channel_id;
        }
		$list = $this->_polyChannelDepositRecord->where($condition)->order('id desc')->select();
        $this->assign('list', $list);
        return $this->fetch("listDepositRecord");
    }

    /**
     * 获取聚合渠道列表
     *
     * @return array
     */
    protected function _getChannelList() {
        $list = $this->complexChannelModel->field(['id', 'name'])->where(['flag' => 4])->order('id desc')->select();

        return $list ?: [];
    }
    /**
     * 获取游戏名称
     *
     * @param int $game_id
     * @return string
     */
     protected function _getGameName($game_id) {
         $nickname = $this->_gameModel->getName($game_id);
         return $nickname ?: '';
     }
    /**
     * 获取游戏原名
     *
     * @param int $game_id
     * @return string
     */
     protected function _getGameOriginName($game_id) {
         $nickname = $this->_gameModel->getOriginName($game_id);
         return $nickname ?: '';
     }
    /**
     * 获取渠道名称
     *
     * @param int $channel_id
     * @return string
     */
     protected function _getChannelName($channel_id) {
         $name = $this->complexChannelModel->where(['id' => $channel_id] )->value('name');

         return $name ?: '';
     }

     protected function _getChannelDesc($channel_id) {
        $desc = $this->complexChannelModel->where(['id' => $channel_id] )->value('remark');

         return $desc ?: '';
     }
}
