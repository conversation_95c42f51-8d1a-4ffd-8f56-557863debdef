<?php
/**
 * 新闻列表控制器
 */
namespace app\admin\controller;

use think\Db;
use app\common\library\FileUpload;
use think\Config;

class Article extends Admin
{
    protected function _initialize()
    {
        parent::_initialize();
        $this->articleModel = Db::name('cy_article');
        $this->contentModel = Db::name('cy_content');
    }


    public function index()
    {
        $where = [];

        $start_time = input('request.start_time');
        
        // 类型
        if (input('request.type') != '') {
            $where['a.type'] = ['=', input('request.type')];
        }

        // 标题
        if (input('request.title') != '') {
            $where['a.title'] = ['like', '%' . input('request.title') .'%'];
        }

        // 显示位置
        if (input('request.position') != '') {
            $where['a.position'] = ['like', '%' . input('request.position') .'%'];
        }
        
        
        // 游戏
        if (input('request.gameid') != '') {
            $where['a.gameid'] = input('request.gameid');
        }
        
        //开始时间和结束时间不为空时
        if ($start_time != '' && input('request.end_time') != '') {
            $where['a.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime(input('request.end_time').' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time!= '') {
            $where['a.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif (input('request.end_time') != '') {
            $where['a.create_time'] = ['<=', strtotime(input('request.end_time').' 23:59:59')];
        }

        $where['a.isdelete'] = 0;

        $param = input('get.');

        $article= $this->articleModel
            ->alias('a')
            ->join('cy_game g', 'a.gameid=g.id', 'left')
            ->field('a.*,g.id as gameid2,g.name as game_name')
            ->where($where)
            ->order('a.id desc')->paginate(10, false, array('query' => $param));

        $gameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();

        $this->assign('game_list', $gameList);
        $this->assign('article', $article);
        $this->assign('page', $article->render());

        return $this->fetch();
    }


    public function add()
    {
        if (request()->isPost()) {
            
            $fileUpload = new FileUpload();
            
            //新闻图片
            $image_url      = $fileUpload->set('allowExt', 'jpg,jpeg,png,gif')->set('maxsize', 1024000)->set('dir','image/news/pic/')->upload(request()->file('image'));
            $image_error    = $fileUpload->getError();
            
            $data = [
                'title'         => input('post.title'),
                'gameid'        => input('post.gameid',0,'int'),
                'zhiding'       => input('post.top'),
                'type'          => input('post.type'),
                'content'       => input('post.content'),
                'create_time'   => NOW_TIMESTAMP
            ];

            $result = $this->validate($data, [
                ['title', 'require|max:100', '标题不能为空|标题不能超过100个字符'],
                ['gameid', 'require', '游戏不能为空'],
                ['zhiding', 'require', '是否置顶不能为空'],
                ['type', 'require|integer', '类型不能为空|类型必须为整型'],
                ['content', 'require', '内容不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            } 
            //新闻图片上传出错时
            elseif(!empty(request()->file('image')) && empty($image_url))
            {
                $this->error('新闻图片'.$image_error);
            }
            else {
                unset($data['content']);
                
                if ( !empty(input('post.position/a'))) {
                    $data['position'] = implode('',input('post.position/a'));
                }
                
                //有上传新闻图片时
                if(!empty($image_url))
                    $data['image'] = $image_url;

				if(in_array($data['type'],[1,2,3,4,5]) && empty($image_url)){
					$this->error('请选择新闻图片');
				}
                
                $id = $this->articleModel->insertGetId($data);

                if (empty($id)) {
                    $this->error('添加失败!');
                }
                
                $this->contentModel->insert(['id'=>$id,'content'=>input('post.content')]);

                $this->success('新闻添加成功!', 'article/index');
            }
        }

        $gameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();

        $this->assign('game_list', $gameList);
        return $this->fetch();
    }


    public function edit($id)
    {
        $id = (int)$id;

        if (empty($id)) {
            $this->error('资讯ID不能为空');
        }

        $articleInfo = $this->articleModel->where(['id' => $id])->find();
        if (empty($articleInfo)) {
            $this->error('资讯信息不能为空! ');
        }

        if (request()->isPost()) {
            
            $fileUpload = new FileUpload();
            
            //新闻图片
            $image_url      = $fileUpload->set('allowExt', 'jpg,jpeg,png,gif')->set('maxsize', 1024000)->set('dir','image/news/pic')->upload(request()->file('image'));
            $image_error    = $fileUpload->getError();
            
            $data = [
                'title'         => input('post.title'),
                'gameid'        => input('post.gameid',0,'int'),
                'zhiding'       => input('post.top'),
                'type'          => input('post.type'),
                'content'       => input('post.content'),
            ];

            $result = $this->validate($data, [
                ['title', 'require|max:100', '标题不能为空|标题不能超过100个字符'],
                ['gameid', 'require', '游戏不能为空'],
                ['zhiding', 'require', '是否置顶不能为空'],
                ['type', 'require|integer', '类型不能为空|类型必须为整型'],
                ['content', 'require', '内容不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            } 
            //新闻图片上传出错时
            elseif(!empty(request()->file('image')) && empty($image_url))
            {
                $this->error('新闻图片'.$image_error);
            }
            else {
                unset($data['content']);
                
                if ( !empty(input('post.position/a'))) {
                    $data['position'] = implode('',input('post.position/a'));
                }else{
                    $data['position'] = null;
                }
                
                //有上传新闻图片时
                if(!empty($image_url))
                    $data['image'] = $image_url;
                
				if(in_array($data['type'],[1,2,3,4,5]) && empty($image_url) && !$articleInfo['image']){
					$this->error('请选择新闻图片');
				}

                $this->articleModel->where(['id'=>$id])->update($data);
                
                $this->contentModel->where(['id'=>$id])->update(['content'=>input('post.content')]);

                model('GameActivityRead')->where('game_article_id',$id)->delete();

                $this->success('新闻编辑成功!', 'article/index');
            }
        }
        
        $content = $this->contentModel->where(['id' => $id])->value('content');

        $gameList = Db::name('cy_game')->field('id,name')->order('name asc')->select();

        $this->assign('articleInfo', $articleInfo);
        $this->assign('content', $content);
        $this->assign('game_list', $gameList);

        return $this->fetch();
    }


    public function del()
    {
        $id = input('id', 0, 'intval');
        if(empty($id)) $this->error('记录ID不能为空');

        $ret = $this->articleModel->where([
            'id' => $id,
        ])->setField('isdelete', 1);

        if (!$ret) {
            $this->error('删除错误！');
        }

        $this->success('删除成功', url('index'));
    }

    /**
     * 微信公众号-文章列表
     */
    public function wxArticleList(){

        $where = [];
        // 标题
        if (input('request.title') != '') {
            $where['title'] = ['like', '%' . input('request.title') .'%'];
        }

        $start          = $this->request->param('start_time');
        $end            = $this->request->param('end_time');
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $where['create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $where['create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $where['create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        }

        $wechatDb = Config::get('db_config_wechat.database'); // 微信公众号数据库


        $where['is_del'] = 0;

        $infoList = Db::table($wechatDb . '.nw_wx_article')
            ->field('*')
            ->where($where)
            ->order('create_time', 'desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $this->assign('article', $infoList);
        $this->assign('page', $infoList->render());
        $this->assign('wxlink',Config::get('WX_ARTICLE_LINK'));

        return $this->fetch('wx_article_list');
    }

    /**
     * 微信公众号-文章 添加
     */
    public function wxArticleAdd(){
        if (request()->isPost()){
            $fileUpload = new FileUpload();

            //新闻图片
            $image_url      = $fileUpload->set('allowExt', 'jpg,jpeg,png,gif')->set('maxsize', 1024000 *3)->set('dir','image/wechat/pic/')->upload(request()->file('image'));
            $image_error    = $fileUpload->getError();

            $data = [
                'title'         => input('post.title'),
                'releaser'         => input('post.releaser') ? input('post.releaser') : '祈盟网络游戏',
                'content'       => input('post.content'),
                'create_time'   => NOW_TIMESTAMP
            ];

            $result = $this->validate($data, [
                ['title', 'require', '标题不能为空'],
                ['releaser', 'require', '发布人不能为空'],
                ['content', 'require', '内容不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }
            //新闻图片上传出错时
            elseif(!empty(request()->file('image')) && empty($image_url))
            {
                $this->error('新闻图片'.$image_error);
            }
            //有上传新闻图片时
            if(!empty($image_url))
                $data['image'] = $image_url;

            $wechatDb = Config::get('db_config_wechat.database'); // 微信公众号数据库


            $shortLink = strtolower(random(8));

            while (Db::table($wechatDb.'.nw_wx_article')->field('id')->where(['short_link' => $shortLink])->find()) {
                $shortLink = strtolower(random(8));
            }
            $data['short_link'] = $shortLink;

            Db::table($wechatDb.'.nw_wx_article')->insert($data);

            $this->success('文章添加成功!', 'article/wxArticleList');
        }

        return $this->fetch('wx_article_add');
    }

    /**
     * 微信公众号-文章 删除
     */
    public function wxArticleDel()
    {
        $id         = input('id' , 0 , 'intval');
        $isRelation = input('check' , '' , 'trim');
        if(empty($id)) $this->error('记录ID不能为空');

        $wechatDb = Config::get('db_config_wechat.database'); // 微信公众号数据库

        // 检查是否有关联分享礼包
        if ($isRelation == 'isRelation'){

            $isExsit = Db::table($wechatDb . '.nw_wx_sharelibao')->where(['article_id' => $id,'is_del'=>0])->find();

            if (!empty($isExsit)) {
                $msg = '该文章已关联分享礼包“'.$isExsit['name'].'”，删除后该礼包也将被自动删除，是否确认删除？';
            }else{
                $msg = '该文章未关联分享礼包，是否确认删除？';
            }
            $this->success($msg);
        }

        if ($isRelation != 'sureDel')  $this->error('参数错误！');

        Db::startTrans();
        try{

            Db::table($wechatDb . '.nw_wx_article')->where(['id' => $id])->setField('is_del', 1);
            Db::table($wechatDb . '.nw_wx_sharelibao')->where(['article_id' => $id])->setField('is_del', 1);

            Db::commit();

        }catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            $this->error('操作失败：'.$e->getMessage());
        }

        $this->success('删除成功', url('wxArticleList'));
        
    }

    /**
     * 微信公众号-文章 编辑
     */
    public function wxArticleEdit()
    {
        $id = input('id', 0, 'intval');

        if (empty($id)) {
            $this->error('ID不能为空');
        }
        $wechatDb = Config::get('db_config_wechat.database'); // 微信公众号数据库

        $articleInfo = Db::table($wechatDb . '.nw_wx_article')->where(['id' => $id])->find();
        if (empty($articleInfo)) {
            $this->error('资讯信息不能为空! ');
        }

        if (request()->isPost()) {

            $fileUpload = new FileUpload();

            //新闻图片
            $image_url      = $fileUpload->set('allowExt', 'jpg,jpeg,png,gif')->set('maxsize', 1024000 *3)->set('dir','image/news/pic/')->upload(request()->file('image'));
            $image_error    = $fileUpload->getError();


            $data = [
                'title'         => input('post.title'),
                'releaser'       => input('post.releaser'),
                'content'       => input('post.content'),
                'update_time'       => time(),
                'update_id'       => session('ADMIN_ID'),
            ];

            $result = $this->validate($data, [
                ['title', 'require', '标题不能为空'],
                ['content', 'require', '内容不能为空']
            ]);

            if (true !== $result) {
                $this->error($result);
            }//新闻图片上传出错时
            elseif(!empty(request()->file('image')) && empty($image_url))
            {
                $this->error('新闻图片'.$image_error);
            }

            $addIMG = input('post.addIMG');
            if ($addIMG == 2){
                $data['image'] = '';
            }

            //有上传新闻图片时
            if(!empty($image_url) && $addIMG == 1)
                $data['image'] = $image_url;

            Db::table($wechatDb . '.nw_wx_article')->where(['id'=>$id])->update($data);

            $this->success('编辑成功!', 'article/wxArticleList');
        }

        $this->assign('info', $articleInfo);
        return $this->fetch('wx_article_edit');
    }


}