<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Request;

class FkExceptionObjects extends Controller
{
    /**
     * 异常对象列表页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $page = input('get.page', 1);
            $limit = input('get.limit', 10);
            $where = [];
            
            // 搜索条件
            $exceptionType = input('get.exception_type', '');
            if ($exceptionType) {
                $where['exception_type'] = $exceptionType;
            }
            
            $exceptionValue = input('get.exception_value', '');
            if ($exceptionValue) {
                $where['exception_value'] = ['like', "%{$exceptionValue}%"];
            }
            
            $status = input('get.status', '');
            if ($status !== '') {
                $where['status'] = $status;
            }
            
            $count = Db::name('fk_exception_objects')->where($where)->count();
            $data = Db::name('fk_exception_objects')
                ->where($where)
                ->page($page, $limit)
                ->order('object_id desc')
                ->select();

            foreach($data as $k=>&$v){
                if($v['exception_log']){
                    $v['exception_log'] = json_decode($v['exception_log'], true);
                    $v['exception_log_by_name'] = implode(';', array_column($v['exception_log'], 'rule_name'));
                }
            }

            return json([
                'code' => 200,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 更新状态
     */
    public function updateStatus()
    {
        $id = input('post.id');
        $status = input('post.status');
        
        if (!$id || !in_array($status, [1, 2, 3, 4])) {
            return json(['code' => -100, 'msg' => '参数错误']);
        }
        
        try {
            Db::name('fk_exception_objects')->where('object_id', $id)->update(['status' => $status]);
            return json(['code' => 200, 'msg' => '状态更新成功']);
        } catch (\Exception $e) {
            return json(['code' => -100, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 添加异常对象
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = [
                'exception_type' => input('post.exception_type'),
                'exception_value' => input('post.exception_value'),
                'exception_degree_total' => 0,
                'status' => input('post.status', 1),
                'remark' => ''
            ];
            
            // 验证
            if (empty($data['exception_type']) || empty($data['exception_value'])) {
                return json(['code' => -100, 'msg' => '对象类型和对象值不能为空']);
            }
            
            // 检查是否已存在
            $exists = Db::name('fk_exception_objects')
                ->where('exception_type', $data['exception_type'])
                ->where('exception_value', $data['exception_value'])
                ->find();
            if ($exists) {
                return json(['code' => -100, 'msg' => '该异常对象已存在']);
            }
            
            try {
                Db::name('fk_exception_objects')->insert($data);
                return json(['code' => 200, 'msg' => '添加成功']);
            } catch (\Exception $e) {
                return json(['code' => -100, 'msg' => '添加失败：' . $e->getMessage()]);
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 编辑异常对象
     */
    public function edit($id = null)
    {
        if ($this->request->isGet() && $id) {
            $info = Db::name('fk_exception_objects')->where('object_id', $id)->find();
            if (!$info) {
                return json(['code' => -100, 'msg' => '异常对象不存在']);
            }
            return json(['code' => 200, 'data' => $info]);
        }
        
        if ($this->request->isPost()) {
            $id = input('post.object_id');
            $data = [
                'exception_type' => input('post.exception_type'),
                'exception_value' => input('post.exception_value'),
                'status' => input('post.status', 1)
            ];
            
            // 验证
            if (empty($data['exception_type']) || empty($data['exception_value'])) {
                return json(['code' => -100, 'msg' => '对象类型和对象值不能为空']);
            }
            
            // 检查是否已存在（排除自己）
            $exists = Db::name('fk_exception_objects')
                ->where('exception_type', $data['exception_type'])
                ->where('exception_value', $data['exception_value'])
                ->where('object_id', '<>', $id)
                ->find();
            if ($exists) {
                return json(['code' => -100, 'msg' => '该异常对象已存在']);
            }
            
            try {
                Db::name('fk_exception_objects')->where('object_id', $id)->update($data);
                return json(['code' => 200, 'msg' => '修改成功']);
            } catch (\Exception $e) {
                return json(['code' => -100, 'msg' => '修改失败：' . $e->getMessage()]);
            }
        }
    }
    
    /**
     * 直接封禁异常对象
     */
    public function blockObject()
    {
        $objectId = input('post.object_id');
        $exceptionType = input('post.exception_type');
        $exceptionValue = input('post.exception_value');

        if (!$objectId || !$exceptionType || !$exceptionValue) {
            return json(['code' => -100, 'msg' => '参数错误']);
        }

        Db::startTrans();
        try {
            // 1. 更新异常对象状态为手动封禁（status=4）
            Db::name('fk_exception_objects')
                ->where('object_id', $objectId)
                ->update([
                    'status' => 4,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 2. 检查是否已存在黑名单记录
            $existsBlack = Db::name('fk_roster_lists')
                ->where([
                    'object_type' => $exceptionType,
                    'object_value' => $exceptionValue,
                    'list_type' => 'black'
                ])
                ->find();
            if (!$existsBlack) {
                // 3. 添加到黑名单
                Db::name('fk_roster_lists')->insert([
                    'list_type' => 'black',
                    'object_type' => $exceptionType,
                    'object_value' => $exceptionValue,
                    'remark' => '来自异常对象直接封禁操作',
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
            }

            // 4. 如果存在白名单记录，删除它
            Db::name('fk_roster_lists')
                ->where([
                    'object_type' => $exceptionType,
                    'object_value' => $exceptionValue,
                    'list_type' => 'white'
                ])
                ->delete();

            // 批量更新异常记录表，status=2
            $res = Db::name('fk_exception_records')
                ->where([
                    'exception_type' => $exceptionType,
                    'exception_value' => $exceptionValue
                ])
                ->update([
                    'status' => 2,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            Db::commit();
            return json(['code' => 200, 'msg' => '封禁成功，已添加到黑名单']);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => -100, 'msg' => '封禁失败：' . $e->getMessage().' - '.$e->getFile().':'.$e->getLine()]);
        }
    }

    /**
     * 移除异常（加入白名单）
     * 1. 直接删除此条记录，并增加白名单。
     */
    public function whitelistObject()
    {
        $objectId = input('post.object_id');
        $exceptionType = input('post.exception_type');
        $exceptionValue = input('post.exception_value');

        if (!$objectId || !$exceptionType || !$exceptionValue) {
            return json(['code' => -100, 'msg' => '参数错误']);
        }

        Db::startTrans();
        try {
            // 1. 更新异常对象状态为正常（status=1）
            Db::name('fk_exception_objects')
                ->where('object_id', $objectId)
                ->delete();

            // 2. 检查是否已存在白名单记录
            $existsWhite = Db::name('fk_roster_lists')
                ->where([
                    'object_type' => $exceptionType,
                    'object_value' => $exceptionValue,
                    'list_type' => 'white'
                ])
                ->find();
            if (!$existsWhite) {
                // 3. 添加到白名单
                Db::name('fk_roster_lists')->insert([
                    'list_type' => 'white',
                    'object_type' => $exceptionType,
                    'object_value' => $exceptionValue,
                    'remark' => '来自异常对象移除异常操作',
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
            }

            // 4. 如果存在黑名单记录，删除它
            Db::name('fk_roster_lists')
                ->where([
                    'object_type' => $exceptionType,
                    'object_value' => $exceptionValue,
                    'list_type' => 'black'
                ])
                ->delete();

            // 批量更新异常记录表，status=2
            Db::name('fk_exception_records')
                ->where([
                    'exception_type' => $exceptionType,
                    'exception_value' => $exceptionValue
                ])
                ->update([
                    'status' => 2,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            Db::commit();
            return json(['code' => 200, 'msg' => '移除异常成功，已添加到白名单']);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => -100, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除异常对象
     */
    public function delete()
    {
        $id = input('post.id');
        if (!$id) {
            return json(['code' => -100, 'msg' => '参数错误']);
        }

        try {
            Db::name('fk_exception_objects')->where('object_id', $id)->delete();
            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => -100, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 查看详情
     */
    public function detail($id)
    {
        $info = Db::name('fk_exception_objects')->where('object_id', $id)->find();
        if (!$info) {
            $this->error('异常对象不存在');
        }
        
        // 获取相关异常记录
        $records = Db::name('fk_exception_records')
            ->where('exception_type', $info['exception_type'])
            ->where('exception_value', $info['exception_value'])
            ->order('record_id desc')
            ->limit(20)
            ->select();
            
        $this->assign('info', $info);
        $this->assign('records', $records);
        return $this->fetch();
    }
}

