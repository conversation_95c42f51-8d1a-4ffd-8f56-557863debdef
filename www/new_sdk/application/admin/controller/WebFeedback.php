<?php
/**
 * 官网反馈管理
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/10
 * Time: 15:22
 */

namespace app\admin\controller;


class WebFeedback extends Admin{
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->feedbackModel = model('WebFeedback');
    }

    /**
     * 查询条件
     */
    public function getCondition() {
        $condition = [];
        $order_num = input('order_num','','trim');
        $username = input('username','','trim');
        $start          = $this->request->param('start_time');
        $end            = $this->request->param('end_time');
        $type = input('type',0,'intval');
        $from = input('from',0,'intval');
        if (!empty($order_num)){
            $condition['a.order_num'] = $order_num;
        }

        if (!empty($username)){
            $condition['b.username'] = $username;
        }

        if (!empty($type)){
            $condition['a.type'] = $type;
        }
        if (!empty($from)){
            $condition['a.from'] = $from;
        }

        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition['a.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition['a.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition['a.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {}

        return $condition;
    }

    /**
     * 列表
     */
    public function index(){
        $condition = $this->getCondition();
        $list = $this->feedbackModel->alias('a')
            ->join('cy_members b','a.user_id = b.id','left')
            ->field('a.*,b.username')
            ->where($condition)
            ->order("a.create_time desc, a.id DESC")
            ->paginate(10,false, ['query' => input('get.')]);

        $this->assign('list',$list);
        $this->assign("page", $list->render());
        $this->assign("total", $list->total());     //总记录数
        return $this->fetch();
    }

    /**
     * 获取反馈内容
     */
    public function ajaxGetFeedbackContent(){
        $id = $this->request->param('id', 0, 'intval');
        if (!$id) $this->error('参数错误！');

        $info = $this->feedbackModel->field('content,order_num')->where('id',$id)->find();

        $this->success('','',$info);
    }

    /**
     * 查看详情
     */
    public function details(){
        $id = $this->request->param('id', 0, 'intval');
        if (!$id) $this->error('参数错误！');

        $info = $this->feedbackModel->alias('a')
            ->join('cy_members b','a.user_id = b.id','left')
            ->field('a.*,b.username')
            ->where('a.id',$id)->find();
        if (!empty($info['img_url'])){
            $info['img_url'] = explode(',',$info['img_url']);
        }

        $this->assign('info',$info);
        return $this->fetch();
    }

}