<?php

/**
 * 敏感词管理
 *
 */

namespace app\admin\controller;

use think\Db;
use think\Exception;
use app\common\library\MakeReport;
use think\File;

class SensitiveWord extends Admin
{
	protected $model = null;

    protected function _initialize()
    {
        parent::_initialize();

        $this->model = model('SensitiveWord');
    }
	
	public function index()
	{
		// 查询
		$where = [];

        // 规则名
        if (input('request.word') != '') {
            $where['word'] =['like', '%' . input('request.word') .'%'];
        }

    	$infoList  = $this->model
            ->where($where)
            ->order('create_time', 'desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($item, $key){
	            $item['creator'] = model('admin')->where('id',$item['creator'])->value('username');
	            return $item;
	        });
 

        $this->assign('list', $infoList);
        $this->assign('page', $infoList->render());

    	return $this->fetch();

	}

	public function add()
    {
    	if (request()->isPost()){

    		$word = input('post.')['word'];
            $word = trim($word);

    		// 字段验证
    		$verify = $this->verify($word);

    		if ( !$verify['code']) {
    			$this->error( $verify['msg']);
    		}


	    	$data = [
	    		'word'  => $word,
	    		'creator'  => session('ADMIN_ID'),
	    		'create_time' => time(),
	    	];

	    	$res = $this->model->insert($data);

	    	if ( $res) {
	    		$this->success('添加成功',url('index'));
	    	}else{
	    		$this->error('添加失败');
	    	}

    	}

        return $this->fetch();
    }

    public function edit()
    {
    	$id = $this->request->param('id',0,'intval');
        if (!$id){
            $this->error('ID不能为空');
        }

        if (request()->isPost()){

        	$word = input('post.')['word'];
            $word = trim($word);

    		// 字段验证
    		$verify = $this->verify($word,$id);

    		if ( !$verify['code']) {
    			$this->error( $verify['msg']);
    		}

    		$data = [
    			'id'   => $id,
 	    		'word'  => $word,
 	    		'update_time'  => time(),
	    		'creator'  => session('ADMIN_ID'),
	    	];

    		$res = $this->model->update($data);

	    	if ( $res !== false) {
	    		$this->success('编辑成功',url('index'));
	    	}else{
	    		$this->error('编辑失败');
	    	}

        }

        $info = $this->model->find($id);

        $this->assign('info',$info);

    	return $this->fetch();
    }

    // 删除
    public function del()
    {
    	$id = $this->request->param('id',0,'intval');
        if (!$id){
            $this->error('ID不能为空');
        }

        $res = $this->model->where('id',$id)->delete();

        if ($res) {
        	$this->success('删除成功', url('index'));

        }

        $this->error('删除失败');

    }

    // 批量删除
    public function batchDel()
    {
    	if (request()->isPost()){

        	$ids = input('post.')['ids'];

    		if (!$ids){
            	$this->error('未选择数据');
        	}

        	$array = explode(',',$ids);

	    	$res = $this->model->where('id','in',$array)->delete();

	        if ($res) {
	        	$this->success('批量删除成功', url('index'));

	        }

	        $this->error('批量删除失败');

        }


        $this->error('参数错误！');
    }

    public function import()
    {
    	if (request()->isPost()){

    		$file   = request()->file('file');

    		if($file){

    			$data = [];
    			$error = [];

                $info = $file->move(ROOT_PATH . 'public/upload/txt');

                if($info){

                    $exfile=ROOT_PATH.'/public/upload/txt/'.date('Ymd').'/'.$info->getFilename();

                    $filetxt = file_get_contents($exfile);  //文件路径

                    unset($info); //一定要unset之后才能进行删除操作，否则请求会被拒绝
                	unlink($exfile); //删除上传失败文件

                    $rep = str_replace("\r\n", ',', $filetxt); 

                    $cont = explode(',', $rep);

                    foreach ($cont as $key => $value) {

                    	$value = iconv('GB2312', 'UTF-8', $value);

                    	$verify = $this->verify($value);

                    	if ( $verify['code']) {
                    		$data = [
                    			'word'  => $value,
					    		'creator'  => session('ADMIN_ID'),
					    		'create_time' => time(),
                    		];

                    		try {
                    			$res = $this->model->insert($data);
	                    	} catch (\Exception $e) {
	                    		$error[] = [
				    				'msg' => $value . '：导入失败，原因：' . $e->getMessage() . '。'
				    			];
	                    	}
			    		}else{
			    			$error[] = [
			    				'msg' => $value . '：导入失败，原因：' . $verify['msg'] . '。'
			    			];
			    		}
                    }

                    unset($cont);

                    if ( !empty($error)) {
                    	return json(['code'=>0,'msg'=>'部分导入成功','data' => $error]);
                    }

                    return json(['code'=>200,'msg'=>'导入成功']);

                }else{
                    // 上传失败获取错误信息
                    return json(['code'=>500,'msg'=>$file->getError()]);
                }
        	}

    	}

    	return $this->fetch();
    }

    // 数据验证
    private function verify($word,$id = 0)
    {
    	$result = ['code' => 1,'msg'  => ''];

    	if ( empty($word)) {

    		$result = [
    			'code' => 0,
    			'msg'  => '敏感词不能为空'
    		];
    		
    		return $result;
    	}

        $str = (strlen($word) - mb_strlen($word,'UTF8'))/2;

    	if ( strlen($word) - $str > 30) {

    		$result = [
    			'code' => 0,
    			'msg'  => '超出30个字符'
    		];

    		return $result;
    	}

    	if ( $this->model->existsWord($word,$id)) {

    		$result = [
    			'code' => 0,
    			'msg'  => '已存在该敏感词'
    		];
    		
    		return $result;
    	}

    	return $result;

    }
}