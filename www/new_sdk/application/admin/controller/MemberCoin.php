<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\admin\controller;

use app\common\library\MakeReportGo;
use app\common\model\Members as MembersModel;
use app\common\model\MembersTwo;
use think\Db;
use app\common\logic\Member as MemberService;
use app\common\library\MakeReport;
use app\common\library\FileUpload;
use think\Exception;

class MemberCoin extends Admin
{
    protected $membersModel;
    protected $where;
    protected $start_time;
    protected $end_time;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->membersModel = new MembersModel;
        $this->gameList     = $gameList = model('Common/Game')->getAllByCondition('id,name');
		$this->selfGameList  = $selfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
        $this->where        = [];
    }

    /**
     * 账户明细列表
     */
    public function detList()
    {
        $where = $this->_getDetListCondition();
		//var_dump($where);

		if (request()->isAjax() && input('download')) {


			$sql = model("MemberZscoinDet")->alias('m')
					->join('cy_game g', 'm.game_id = g.id','left')
					->field("m.userid,m.username,m.game_id,m.prev_amount,m.change_amount,m.after_amount,m.type,m.out_orderid,m.create_time,g.name as game_name,g.origin_name")
					->where($where)
					->order('m.create_time desc')
					->fetchSql(true)->select();
		//	echo $sql;
            if((new MakeReportGo())->addTask('admin.memberCoinDetList',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
		}

        $list = model("MemberZscoinDet")->alias('m')
			->join('cy_game g', 'm.game_id = g.id','left')
            ->field("m.userid,m.username,m.game_id,m.prev_amount,m.change_amount,m.after_amount,m.type,m.out_orderid,m.create_time,g.name as game_name,g.origin_name")
            ->where($where)
            ->order('m.create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];

        $gameList = $this->selfGameList;
        $gList = $gameList;
        $gameList = array_column($gameList, 'name', 'id');

        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('game_list', $gameList);
        $this->assign('glist', $gList);

        return $this->fetch('det_list');
    }

    /**
     * 账户变动明细  条件查询
     * @return array
     */
    protected function _getDetListCondition()
    {

        $start_time       = input('request.start_time', '', 'trim');
        $end_time         = input('request.end_time', '', 'trim');
        $username         = input('request.username', '', 'trim');
		$userid			  = input('request.userid', 0, 'intval');
		$type			  = input('request.type', '', 'trim');
		$gameid			  = input('request.gameid', 0, 'intval');

		$where = array();
        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['m.create_time'] = $this->getTimeCondition($start_time,$end_time,false);
        }
        //用户ID
        if ($userid) {
            $where['m.userid'] = $userid;
        }
        //用户名
        if ($username != '') {
            $where['m.username'] = $username;
        }
        //类型
        if ($type != '') {
            $where['m.type'] =  $type;
        }
	    //游戏ID
        if ($gameid != '') {
            $where['m.game_id'] = $gameid;
        }
        return $where;
    }

    /**
     * 玩家平台币余额
     */
    public function index()
    {
        $this->indexWhere();

		if (request()->isAjax() && input('download')) {


			$sql = model("MemberZscoin")->alias('zs')
					->join('cy_game g', 'zs.game_id = g.id','left')
					->field('zs.*,g.name as game_name,g.origin_name')
					->where($this->where)
					->order('id desc')
					->fetchSql(true)->select();
		//	echo $sql;
            if((new MakeReportGo())->addTask('admin.memberCoinIndex',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
		}

        $param = input('get.');
        $list  = model("MemberZscoin")->alias('zs')
					->join('cy_game g', 'zs.game_id = g.id','left')
					->field('zs.*,g.name as game_name,g.origin_name')
					->where($this->where)
					->order('id desc')
					->paginate(10, false, array('query' => $param));

        $gameList = $this->selfGameList;
        $gList = $gameList;
        $gameList = array_column($gameList, 'name', 'id');
        $data     = $list->toArray()['data'];

        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);
        $this->assign('glist', $gList);

        return $this->fetch();
    }

    // 用户信息汇总查询条件
    public function indexWhere()
    {
        $where = array();
        $userid           = input('request.userid');
        $gameid           = input('request.gameid');
        $status           = input('request.status');
        $start_time       = input('request.start_time');
        $end_time         = input('request.end_time');
        $username         = input('request.username');

        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['zs.update_time'] =$this->getTimeCondition($start_time,$end_time,false);
        }

        //用户ID
        if ($userid != '') {
            $where['zs.userid'] = $userid;
        }
        //用户名
        if ($username != '') {
            $where['zs.username'] = $username;
        }

        //游戏ID
        if ($gameid != '') {
            $where['zs.game_id'] = $gameid;
        }

        //账户状态
        if ($status != '') {
            $where['zs.status'] = $status;
        }

        $this->where      = $where;

    }


    /**
     * 获取日期查询条件
     * @param  int  $start_time  开始时间
     * @param  int  $end_time    结束时间
     * @param  bool  $isdefault    是否默认今天日期
     * @return
     */
    private function getTimeCondition($start_time,$end_time,$isdefault = true)
    {
        $this->start_time = $start_time;
        $this->end_time   = $end_time;
        $time = [];
        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $time = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $time = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $time = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {

            if($isdefault){
                $this->start_time = $this->end_time = date('Y-m-d', time());

                $time = [
                    ['>=', strtotime($this->start_time)],
                    ['<=', strtotime($this->end_time . ' 23:59:59')],
                ];
            }
        }

        return $time;
    }

    /**
     * 用户冻结详情
     */
    public function frozenInfo($ids)
    {
        $param = input('get.');

        $where['userid'] = (int)$ids;

        $admin_id         = input('request.admin_id');
        $flag             = input('request.flag');
        $start_time       = input('request.start_time');
        $end_time         = input('request.end_time');

        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['create_time'] =$this->getTimeCondition($start_time,$end_time,false);
        }

        //操作人
        if ($admin_id != '') {
            $where['admin_id'] = $admin_id;
        }
        //冻结状态
        if ($flag != '') {
            $where['status'] = $flag;
        }

        $list  = model('FrozenHistory')
            ->where($where)->order('id desc')
            ->paginate(10, false, array('query' => $param))->each(function($item, $key){
                if ( !$item['admin_id']) {
                    $item['admin_name'] = '申诉冻结';
                }else{
                    $item['admin_name'] =model('admin')->where('id',$item['admin_id'])->value('username');
                }
                return $item;
            });

        $aList = model('Common/Admin')->field('id,username as name')->where(['type'=> 1 ,'status'=> 1])->order('id')->select();

        $adminList = array_merge([['id'=>0,'name'=>'申诉冻结']] ,$aList);


        $this->assign('list', $list);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());
        $this->assign('adminList',$adminList);

        return $this->fetch();
    }


    /**
     * 用户信息汇总冻结账户
     */
    public function informationFrozen()
    {
        $id   = input('id', 0, 'intval');
        $flag = input('flag', 0, 'abs');
        if (empty($id)) {
            $this->error('用户ID不能为空');
        }

        if (!$info = $this->membersModel->field('id,username')->find($id)) {
            $this->error('非法参数');
        }

        if ($flag == 1) $msg = '冻结'; else
            $msg = '解冻';

        if ($this->membersModel->update([
            'flag'        => $flag,
            'update_time' => time(),
        ], ['id' => $id])) {
            // 插入冻结记录
            $res = model('FrozenHistory')->addData($id,$flag);

            $info = $msg . "玩家：{$info['username']}";
            $this->insertLog($this->current_node, $info, 126);

            $this->success($msg . '账户成功');

        } else {
            $this->error($msg . '账户失败');
        }
    }
}
