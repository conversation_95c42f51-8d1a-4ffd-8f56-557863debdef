<?php
/**
 * 游戏折扣
 */

namespace app\admin\controller;

use app\common\library\FileUpload;
use app\common\library\MakeReport;
use app\common\model\AndroidSdk;
use app\common\model\GameBand;
use app\common\model\Game as GameModel;
use app\common\model\GameInfo;
use app\common\model\GameNotify;
use app\common\model\GameBanned;
use app\common\model\GameExtraPointRatio;
use Overtrue\Pinyin\Pinyin;
use think\Db;
use think\Exception;

class GameDiscount extends Admin
{
    protected $gameModel;

    // 游戏等级
    protected $gameLevel = ['S', 'A1', 'A2', 'B1', 'B2', 'B3', 'B4', 'C', 'D'];

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->gameModel = new GameModel;
        $this->gameList  = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $this->selfGameList  = $selfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
    }



    /**
     *是否显示
     */
    public function show()
    {
        $id              = $this->request->param('id', '', 'intval');
        $status          = $this->request->param('is_show', '', 'intval');
        $data['is_show'] = $status;
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $result = $this->validate($data, [
            ['is_show', 'in:0,1', '状态参数错误'],
        ]);
        if (true !== $result) {
            $this->error($result);
        }
        $gameModel = model("Game");
        if (!$gameModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($gameModel->update($data, ['id' => $id])) {
            $msg = $status ? '显示' : '隐藏';
            $this->insertLog($this->current_node, $msg . '游戏：' . get_game_name($id), 11);
            $this->success('修改成功');
        }
        $error = $gameModel->getError();
        $this->error($error ?: '修改失败');
    }

    /**
     * 游戏点位
     * @return mixed|string
     */
    public function gamePointList()
    {
        $where['g.cooperation_status'] = ['not in','0,3'];      //对接中，下架的游戏不显示
        // $where['g.is_default'] = 0;      //外部游戏不显示
        //游戏名称
        if (input('request.game_id') != '') {
            $where['g.id'] = ['=', input('request.game_id')];
            $game          = get_game_name(input('request.game_id'));
            $message       = "查询游戏：{$game}";
            $this->insertLog($this->current_node, $message, 12);
        }

        $list = Db::table('cy_game')->alias('g')->join('cy_game_point p', 'g.id = p.game_id', 'left')
                  ->join("nw_game_channel_pointset b","b.game_id=g.id", "left")
                  ->field("p.*,g.id as game_id,g.name,b.isset_first,b.restricted_list_1,b.isset_second,b.restricted_list_2")->order('g.id desc')->where($where)->where("g.is_default = 0")
                  ->paginate(10, false, ['query' => input('get.')])
                  ->each(function($item, $key){
                        $item['restricted_list_1'] = ltrim($item['restricted_list_1'], ',');
                        $item['restricted_list_2'] = ltrim($item['restricted_list_2'], ',');
                        // 判断首充渠道限制名单是否为空
                        $item['channel_name_1'] = '';
                        $item['channel_name_2'] = '';
                        if(!empty($item['restricted_list_1'])){
                            $channelArr = Db::table('cy_department')
                                    ->field("name")
                                    ->where('id', 'IN', $item['restricted_list_1'])
                                    ->select();
                            $nameStr = '';
                            foreach ($channelArr as $v){
                                $nameStr .=$v['name'].'，';
                            }
                            $nameStr = substr($nameStr,0,strlen($nameStr)-3);
                            $item['channel_name_1'] = $nameStr; //给数据集追加字段num并赋值
                        }
                        // 判断续充渠道限制名单是否为空
                        if(!empty($item['restricted_list_2'])){
                            $channelArr = Db::table('cy_department')
                                    ->field("name")
                                    ->where('id', 'IN', $item['restricted_list_2'])
                                    ->select();
                            $nameStr = '';
                            foreach ($channelArr as $v){
                                $nameStr .=$v['name'].'，';
                            }
                            $nameStr = substr($nameStr,0,strlen($nameStr)-3);
                            $item['channel_name_2'] = $nameStr; //给数据集追加字段num并赋值
                        }
                    return $item;
                });

        // 获取配置表 首充默认最大天数  FIRST_MAX_DAY
        $FIRST_MAX_DAY = model('Setting')->where(['name'=>'FIRST_MAX_DAY'])->field('value')->find()['value'];

        $this->assign('game_list', $this->selfGameList);
        $this->assign('game_point_list', $list);
        $this->assign('FIRST_MAX_DAY', $FIRST_MAX_DAY);
        $this->assign('page', $list->render());
        return $this->fetch('game_point_list');
    }



    public function editPoint()
    {
        $data = $this->request->param();

        if (isset($data['type'])){
            if ($data['type'] == 'editMultiLevel'){
                $this->editMultiLevel();
            }
            if ($data['type'] == 'editLevel'){
                $this->editLevel();
            }
            if ($data['type'] == 'editPercent'){
                $this->editPercent();
            }
        }


        $type = ['third_party_pay_point', 'inner_point', 'remark', 'inner_first_point','first_max_days'];
        if (!in_array($data['name'], $type)) {
            $this->error('选择参数错误');
        }

        //返点
        $gamePointModel = model("GamePoint");
        $oldPoint = $gamePointModel->where("game_id = '{$data['pk']}'")->field('gp_id,inner_point,inner_first_point,third_party_pay_point')->find();
        $nameZK = '';
        if ($data['name'] == 'third_party_pay_point') {
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|点数', 'number|between:0,100'],
                ['name', 'require', '点位参数错误'],
            ]);
        } else if ($data['name'] == 'inner_first_point'){
            // 首充
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|首充折扣', 'number|between:0,10'],
                ['name', 'require', '点位参数错误'],
            ]);
            $nameZK = '首充折扣';
            $point_fla = true;
            if( $data['value'] <=0 || $data['value'] >10 || !is_numeric($data['value'])){
                $result = '折扣点位只能0＜折扣≤10';
                $point_fla = false;
            }elseif( (float)$oldPoint['inner_point'] > 0 && $data['value'] >$oldPoint['inner_point'] && $point_fla){
                $result = '首充折扣要小等于续充折扣';
            }

        } else if ($data['name'] == 'inner_point'){
            // 续充
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|续充折扣', 'number|between:0,10'],
                ['name', 'require', '点位参数错误'],
            ]);
            $nameZK = '续充折扣';
            $point_fla = true;
            if( $data['value'] <=0 || $data['value'] >10 || !is_numeric($data['value'])){
                $result = '折扣点位只能0＜折扣≤10';
                $point_fla = false;
            }elseif( (float)$oldPoint['inner_first_point']>0 && $data['value'] < $oldPoint['inner_first_point'] && $point_fla){
                $result = '续充折扣要大等于首充折扣';
            }

        } else if ($data['name'] == 'first_max_days'){
            $result = true;
            if($data['value'] != '' ){
                if( $data['value'] <=0  || !is_numeric($data['value']) || floor($data['value'])!=$data['value']){
                    $result = '首充最大天数只能是大于0的整数';
                }
            }else{
                $data['value'] = null;
            }
        }
        else {
            // 备注
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|备注', 'require|max:500'],
                ['name', 'require', '点位参数错误'],
            ]);
        }

        if (true !== $result) {
            $this->error($result);
        }

        // 开启事务
        Db::startTrans();

        $insert[ $data['name'] ] = $data['value'];
        $result                  = $gamePointModel->updateData($insert, ['game_id' => $data['pk']]);

        if(!isset($oldPoint['gp_id'])){
            $oldPoint['gp_id'] = $gamePointModel->where("game_id = '{$data['pk']}'")->field('gp_id')->find()['gp_id'];
        }

        $logArr = [
            "gp_id" => $oldPoint['gp_id'] ,
            $data['name'] => $data['value'] ,
            'operate_id' => $admin_id = session('ADMIN_ID') ? : 1
        ];
        $res = true;
        if(($data['name'] == 'inner_point' || $data['name'] == 'inner_first_point' || $data['name'] == 'third_party_pay_point')){
            $res = model('GamePointLog')->add($logArr);
        }

        if ($result && $res) {
            // 提交事务
            Db::commit();
            $game    = get_game_name($data['pk']);
            if ($data['name'] == 'inner_point') {
                $field = '续充点位';
            } else if ($data['name'] == 'third_party_pay_point') {
                $field = '其它支付返点';
            }else if ($data['name'] == 'inner_first_point') {
                $field = '首充点位';
            }

            if (isset($field)) {
                $message = "更新游戏：{$game} 的{$field}：{$data['value']}";
                $this->insertLog($this->current_node, $message, 12);
            }
            $this->success('修改成功');
        } else {
            // 回滚事务
            Db::rollback();
            $this->error('修改失败');
        }
    }

    public function editPointStatus()
    {
        $id                = $this->request->param('gp_id', '', 'intval');
        $status            = $this->request->param('gp_status', '', 'intval');
        $data['gp_status'] = $status;
        if (empty($id)) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置!');
        }
        $result = $this->validate($data, [
            ['gp_status', 'in:0,1', '状态参数错误'],
        ]);
        if (true !== $result) {
            $this->error($result);
        }
        $gamePointModel = model("GamePoint");
        if (!$info = $gamePointModel->where(['gp_id' => $id])->field('gp_id')->find()) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置');
        }
        if ($gamePointModel->update(['gp_status' => $status], ['gp_id' => $info['gp_id']])) {
            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }

    public function editPointDisplay()
    {
        $id                   = $this->request->param('gp_id', '', 'intval');
        $status               = $this->request->param('gp_isdisplay', '', 'intval');
        $data['gp_isdisplay'] = $status;
        if (empty($id)) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置!');
        }
        $result = $this->validate($data, [
            ['gp_isdisplay', 'in:0,1', '状态参数错误'],
        ]);
        if (true !== $result) {
            $this->error($result);
        }
        $gamePointModel = model("GamePoint");
        if (!$info = $gamePointModel->where(['gp_id' => $id])->field('gp_id')->find()) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置');
        }
        if ($gamePointModel->update(['gp_isdisplay' => $status], ['gp_id' => $info['gp_id']])) {
            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }

    /**
     * 获取改游戏历史点位修改记录
     */
    public function showPointHistory(){
        $gp_id                   = $this->request->param('gp_id', '', 'intval');
        $list  = model("GamePointLog")->alias('a')
            ->join('cy_admin b','b.id = a.operate_id','left')
            ->field("a.*, b.username")
            ->where(['a.gp_id'=>$gp_id, 'a.third_party_pay_point'=>['eq',0]])
            ->where('a.inner_first_point <> 0 or a.inner_point <> 0')
            ->order('a.id', 'desc')->select();
        $code = 1;
        if(!count($list)){
            $code = 0;
        }else{
            foreach ($list as &$v){
                if (empty($v['username'])){
                    $v['username'] = '';
                }
            }
        }
        echo json_encode(['code'=>$code,'data'=>$list]);
    }


    // 批量修改等级
    public function editMultiLevel()
    {
        $pks = $this->request->param('pks');
        $level = $this->request->param('value');

        $result = $this->validate(['value' => $level], [
            ['value', 'require|in:' . implode(',', $this->gameLevel), '游戏等级不能为空|游戏等级不存在']
        ]);

        if (true !== $result) {
            $this->error($result);
        }

        $pksArr = explode(',', $pks);
        if (empty($pksArr)) {
            $this->error('请选择游戏');
        }

        $result = model("GamePoint")->updateData(['game_level' => $level], ['game_id' => ['IN', $pksArr]]);

        if ($result) {
            if (isset($field)) {
                $message = "更新游戏：批量更新游戏ID为[{$pks}]游戏等级：{$level}";
                $this->insertLog($this->current_node, $message, 12);
            }

            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }

    // 游戏等级
    public function editLevel()
    {
        $data = [
            'pk' => $this->request->param('pk'),
            'value' => $this->request->param('value')
        ];
        $result = $this->validate($data, [
            ['pk', 'require', '参数错误'],
            ['value', 'require|in:' . implode(',', $this->gameLevel), '游戏等级不能为空|游戏等级不存在']
        ]);

        if (true !== $result) {
            $this->error($result);
        }

        $result = model("GamePoint")->updateData(['game_level' => $data['value']], ['game_id' => $data['pk']]);

        if ($result) {
            $game = get_game_name($data['pk']);
            if (isset($field)) {
                $message = "更新游戏：{$game} 的游戏等级：{$data['value']}";
                $this->insertLog($this->current_node, $message, 12);
            }

            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }

    // 最低折扣
    public function editPercent()
    {
        $data = [
            'pk' => $this->request->param('pk'),
            'value' => $this->request->param('value')
        ];
        $result = $this->validate($data, [
            ['pk|ID', 'require', '参数错误'],
            ['value|折扣', 'number|between:0,10', '必须是数字|折扣在0到10之间']
        ]);

        if (true !== $result) {
            $this->error($result);
        }

        $result = model("GamePoint")->updateData(['min_percent' => $data['value']], ['game_id' => $data['pk']]);

        if ($result) {
            $game = get_game_name($data['pk']);
            if (isset($field)) {
                $message = "更新游戏：{$game} 的最低折扣：{$data['value']}";
                $this->insertLog($this->current_node, $message, 12);
            }

            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }
}
