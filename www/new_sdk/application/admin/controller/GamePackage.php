<?php
/**
 * 游戏管理控制器
 */

namespace app\admin\controller;

use app\common\library\FileUpload;
use app\common\library\MakeReport;
use app\common\model\AndroidSdk;
use app\common\model\GameBand;
use app\common\model\Game as GameModel;
use app\common\logic\SubPackage as SubChannel;
use app\common\model\SdkGameList;
use app\common\model\GameInfo;
use app\common\model\GameNotify;
use app\common\model\GameBanned;
use app\common\model\GameExtraPointRatio;
use Overtrue\Pinyin\Pinyin;
use app\common\model\PromotionShortLink;
use think\Config;
use think\Db;
use think\Env;
use think\Exception;
use think\Request;

class GamePackage extends Admin
{
    protected $gameModel;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
        $this->subChannel = new SubChannel;
        $this->gameModel = new GameModel;
        $this->sdkgamelistModel = $this->cyModel('sdkgamelist');
        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $this->selfGameList = $selfGameList = model('Common/Game')->getAllByCondition('id,name,pinyin,package_name,type,sign_ver', [], '', 'self');
    }

    /**
     * 列表
     */
    public function index()
    {
        //查询参数
        $param = input('get.');
        $page_size = input('page_size', 15);
        $where = [];
        if (isset($param['game_id']) && $param['game_id']) {
            $where['game_id'] = $param['game_id'];
        }
        if (isset($param['game_name']) && $param['game_name']) {
            $where['game.name'] = ['like', '%' . $param['game_name'] . '%'];
        }
        $gamelist = Db::name('nw_game_package_upload')->field('game_id as id,game_name as name')->where('path != ""')->select();
        $list = Db::name('nw_game_package_upload')->alias('a')
            ->join('cy_game game', 'nw_game_package_upload.game_id = game.id', 'left')
            ->field('a.id,game.name as game_name,a.bag_name,a.platform_type,convert((bag_size/1024/1024),decimal(10,2)) as bag_size,from_unixtime(update_time) as create_time,control_name,path,game_id,game.pinyin,game.channel_version,game.sign_ver, from_unixtime(a.update_time) as update_time,game.type')
            ->where($where)
            ->order('id desc')
            ->paginate($page_size, false, array('query' => $param));
        $meb_list = $list->toarray();
        // dump($meb_list);
        foreach ($meb_list['data'] as $key => $value) {
            $meb_list['data'][$key]['path_ext'] = substr(substr($value['path'], strrpos($value['path'], "/") + 1), 0, -4);
        }
        $this->assign('list', $meb_list['data']);
        $this->assign('total', $meb_list['total']);
        $this->assign('game_list', $gamelist);
        $this->assign('page', $list->render());
        $this->assign('gameList', $this->cyModel('game')->field('id,name')->select());

        return $this->fetch();
    }

    /**
     * [FunctionName description]
     *
     * @param string $value [description]
     */
    public function deleted()
    {
        $id = input('id');
        $game_id = input('game_id');
        if (empty($id) || empty($game_id)) {
            $this->error('删除失败,参数错误');
        }
        $game_package_name = db::name('cy_game')->where(['id' => $game_id])->value('pinyin');//文件拼音
        $path = Db::name('nw_game_package_upload')->where(['game_id' => $game_id])->value('path');
        if (!$path) {
            $this->error('无游戏原包 请先上传');
        }
        if (empty($game_package_name)) {
            $this->error('原包错误 请先刷新后重试');
        }
        $filed = substr(strrchr($path, "sygame/"), 6);
        if (empty($filed)) {
            $this->error('原包错误 请先刷新后重试');
        }
        $ups = Env::get('MUBAO_PATH') . 'sygame' . $filed;//服务器文件路劲
        if (file_exists($ups)) {
            @unlink(iconv('UTF-8', 'GBK', $ups));
        }
        Db::startTrans();
        try {
            $data['bag_name'] = '--';
            $data['bag_size'] = 0;
            $data['bag_version'] = '';
            $data['update_time'] = NOW_TIMESTAMP;
            $data['control_name'] = session('USERNAME');
            $data['path'] = '';
            $resultUpdate = Db::name('nw_game_package_upload')->where(['game_id' => $game_id])->update($data);

            if (!$resultUpdate) {
                throw new Exception("数据更新失败");
            }
            $cont = [];
            $cont['game_id'] = $game_id;
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['control_name'] = session('USERNAME');
            $cont['control_type'] = 3;
            $result = Db::name('game_upload_log')->insert($cont);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error("删除失败 " . $e->getMessage());
        }
        $this->success("删除成功");
    }

    /**
     * 查看记录
     *
     * @param string $value [description]
     */
    public function checklog()
    {
        $game_id = input('game_id', '');
        if (empty($game_id)) {
            $this->error('参数错误');
        }
        $list = db::name('game_upload_log')->field('id,game_id,from_unixtime(create_time) as create_time,control_name,CASE WHEN control_type = 1 THEN "新增原包" WHEN control_type = 2 THEN "更新原包" WHEN control_type = 3 THEN "删除原包" END as control_type')->where(['game_id' => $game_id])->order('id desc')->select();
        if ($list) {
            $this->success('添加成功', null, $list);
        } else {
            $this->error("暂无数据");
        }
    }

    /**
     * 新增原包
     */
    public function add()
    {
        $param = input('get.');

        $list = [];
        $exist_ids = Db::name('nw_game_package_upload')->column('game_id');
        $info = ['package_name' => '', 'pinyin' => '', 'type' => '', 'sign_ver' => 0, 'path' => '', 'remark' => ''];
        if (isset($param['game_id'])) {
            $info = Db::name('cy_game')->alias('g')
                ->join("nw_game_package_upload gpu", "g.id = gpu.game_id", 'left')
                ->field('g.id,g.name, g.pinyin,g.package_name, g.channel_version, gpu.path, g.sign_ver, g.type, gpu.remark')
                // ->field('id,name,channel_version,pinyin,package_name,type,sign_ver, "" as path')
                ->where(['g.id' => $param['game_id']])->find();
            $list[] = $info;
        } else {
            $list = Db::name('cy_game')
                ->where('id', 'not in', $exist_ids)
                ->field('id,name,channel_version,pinyin,package_name,type,sign_ver,"" as remark, "" as path')
                ->select();
            // $list = $this->get_diff_array_by_filter($this->selfGameList, $gamelist);
        }

        $this->assign('game_list', $list);
        $this->assign('info', $info);
        $this->assign('upload_url', Env::get('mubao.upload_url'));
        return $this->fetch('add');
    }

    /**
     * 数组取差集
     *
     * @param  [type] $arr1 [description]
     * @param  [type] $arr2 [description]
     *
     * @return [type]       [description]
     */
    public function get_diff_array_by_filter($arr1, $arr2)
    {
        try {
            return array_filter($arr1, function ($v) use ($arr2) {
                return !in_array($v, $arr2);
            });
        } catch (\Exception $exception) {
            return $arr1;
        }
    }

    // 母包上传成功 - 用于新版分包
    public function uploadSuccess()
    {
        $postParam = input('post.');
        $game_package_name = db::name('cy_game')->where(['id' => $postParam['game_id']])->value('pinyin');
        if (empty($game_package_name)) {
            $this->error('游戏有误！');
        }
        if (!isset($postParam['name']) && $postParam['type'] != 3) {
            $this->error('请上传游戏包！');
        }


        //合并后记录数据库
        Db::startTrans();
        try {
            $info = Db::name('nw_game_package_upload')->where(['game_id' => $postParam['game_id']])->field('id,bag_version')->find();
            
            $game_name = db::name('cy_game')->where(['id' => $postParam['game_id']])->value('name');
            $data = [
                'game_name' => $game_name,
                'bag_version' => $postParam['bag_version'],
                'platform_type' => $postParam['type'],
                'create_time' => NOW_TIMESTAMP,
                'update_time' => NOW_TIMESTAMP,
                'control_name' => session('USERNAME'),
                'game_id' => $postParam['game_id'],
                'remark' => $postParam['remark'],
            ];
            
            if($postParam['type'] == 3){
                $data['path'] = $postParam['path'];
            }else{
                if (!empty($postParam['name'])) {
                    $domain = Env::get('mubao.down_url');
                    $ext_suffix = substr($postParam['name'], strripos($postParam['name'], ".") + 1);//后缀
                    
                    $data['bag_size'] = $postParam['size'];
                    $data['bag_name'] = $postParam['bag_name'];
                    $data['path'] = $domain . '/sygame/' . $game_package_name . '/' . $game_package_name . '.' . $ext_suffix;
                }
            }

            if ($info) {//存在记录的情况更新
                $control_type = 2;
                $resultUpdate = Db::name('nw_game_package_upload')->where(['game_id' => $postParam['game_id']])->update($data);
                
                // ## 通知直播平台更新游戏包
                if ($postParam['type'] != 3 && $resultUpdate && $postParam['bag_version'] > $info['bag_version']) {
                    $zb_config = Config::get('zb_str');

                    $data = [
                        "gameid" => $postParam['game_id'],
                        "app_key" => 'qm_sdk',
                        "timestamp" => time(),
                    ];
                    $data['sign'] = $this->getZbSign($data, $zb_config['sign_key']);
                    if (APP_STATUS == 'stable') {
                        $zb_url = $zb_config['pro_url'];
                    } else {
                        $zb_url = $zb_config['dev_url'];
                    }
                    $zb_res = curlHeader($zb_url . "/v1/api/game/pack/notice", $data, ['Content-Type' => 'application/x-www-form-urlencoded']);
                    log_message("## ZB_API： " . json_encode(['url' => $zb_url . "/v1/api/game/pack/notice", 'body' => $data, 'result' => $zb_res]), 'log', LOG_PATH . 'admin/httplog/');
                }
            } else {
                $control_type = 1;
                $resultUpdate = Db::name('nw_game_package_upload')->insert($data);
            }

            if (!$resultUpdate) {
                throw new Exception("数据更新失败");
            }
            $cont = [];
            $cont['game_id'] = $postParam['game_id'];
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['control_name'] = session('USERNAME');
            $cont['control_type'] = $control_type;
            $result = Db::name('game_upload_log')->insert($cont);

            $sign_ver = input('sign_ver', 0);
            Db::name('cy_game')->where(['id' => $postParam['game_id']])->update(['channel_version' => $postParam['bag_version'], 'sign_ver' => $sign_ver]);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error("上传失败 " . $e->getMessage());
        }
        $this->success('上传成功');
    }

    /**
     * 开始上传
     *
     * @return [type] [description]
     */
    public function UploadBigFile()
    {
        $pinyin = new Pinyin();
        $getParam = input('get.');
        $postParam = input('post.');
        $game_id = $postParam['game_id'];
        if (!$game_id) {
            $this->error('上传失败,游戏ID不存在');
        }

        //放入sygame/temp 临时文件夹内
        $name = $pinyin->permalink(substr($postParam['name'], 0, strrpos($postParam['name'], ".")), '');//转中文
        $act = $getParam['act'];
        $tpm_ups = Env::get('MUBAO_PATH') . 'sygame/temp/' . $name . '/';
        $ext_suffix = substr($postParam['name'], strripos($postParam['name'], ".") + 1);//后缀
        $flag = true;
        !in_array($ext_suffix, ['ipa', 'apk']) && $flag = false;//判断后缀名是否符合

        if (!$flag) {
            $this->error('请选择ipa或者apk文件!');
        }
        $game_ext = db::name('cy_gameinfo')->where(['game_id' => $game_id])->value('platform');
        if ($game_ext == 1) {
            $game_ext = 'ipa';
        } else {
            $game_ext = 'apk';
        }
        if ($game_ext != $ext_suffix) {
            $this->error('文件类型选择错误');
        }
        if ($act == 'upload') {
            $index = $postParam['index'];
            $filename = $tpm_ups . "$index" . $name . '.' . $ext_suffix;
            if (!file_exists($tpm_ups))//如果文件夹不存在
            {
                mkdir($tpm_ups, 0777, 1);
            }
            //断点上传已经存在的就跳过
            if (file_exists($filename)) {
                $this->success('上传成功');
            } else {
                $result = move_uploaded_file($_FILES['data']['tmp_name'], iconv('UTF-8', 'GBK', $filename));
            }
            if ($result) {
                $this->success('上传成功');
            } else {
                $this->success('上传失败');
            }
        } elseif ($_GET['act'] == 'join') {
            $fileUpload = new FileUpload();
            $total = intval($postParam['total']);
            $filename = $tpm_ups . $name . '.' . $ext_suffix;
            $request = Request::instance();
            $domain = $request->domain();
            $game_package_name = db::name('cy_game')->where(['id' => $postParam['game_id']])->value('pinyin');
            if (empty($game_package_name)) {
                $this->error('游戏参数错误 请先刷新后重试');
            }
            $ups = Env::get('MUBAO_PATH') . 'sygame/' . $game_package_name . '/';
            if (!file_exists($ups))//如果文件夹不存在
            {
                mkdir($ups, 0777, 1);
            }
            //合并时候判断 存在之前上传过的文件就删除掉在合并
            @unlink(iconv('UTF-8', 'GBK', $ups . $game_package_name . '.' . $ext_suffix));
            for ($i = 1; $i <= $total; $i++) {
                file_put_contents($ups . iconv('UTF-8', 'GBK', $game_package_name . '.' . $ext_suffix), file_get_contents(iconv('UTF-8', 'GBK', $tpm_ups . "$i" . $name . '.' . $ext_suffix)), FILE_APPEND);
                @unlink(iconv('UTF-8', 'GBK', $tpm_ups . "$i" . $name . '.' . $ext_suffix));
            }

            //合并后记录数据库
            Db::startTrans();
            try {
                $data = [];
                if (Db::name('nw_game_package_upload')->where(['game_id' => $game_id])->value('id')) {//存在记录的情况更新
                    $control_type = 2;
                    $data['bag_size'] = $postParam['size'];
                    $data['control_name'] = session('USERNAME');
                    $data['update_time'] = NOW_TIMESTAMP;
                    $data['bag_name'] = $postParam['bag_name'];
                    $data['bag_version'] = $postParam['bag_version'];
                    $data['remark'] = $postParam['remark'];
                    $data['path'] = $domain . '/sygame/' . $game_package_name . '/' . $game_package_name . '.' . $ext_suffix;
                    $resultUpdate = Db::name('nw_game_package_upload')->where(['game_id' => $game_id])->update($data);

                    if ($resultUpdate) {
                        $zb_config = Config::get('zb_str');

                        $data = [
                            "gameid" => $game_id,
                            "app_key" => 'qm_sdk',
                            "timestamp" => time(),
                        ];
                        $data['sign'] = $this->getZbSign($data, $zb_config['sign_key']);
                        if (APP_STATUS == 'stable') {
                            $zb_url = $zb_config['pro_url'];
                        } else {
                            $zb_url = $zb_config['dev_url'];
                        }

                        // 通知直播平台更新游戏包
                        $zb_res = curlHeader($zb_url . "/v1/api/game/pack/notice", $data, ['Content-Type' => 'application/x-www-form-urlencoded']);
                        trace("## ZB_API： " . json_encode(['url' => $zb_url . "/v1/api/game/pack/notice", 'body' => $data, 'result' => $zb_res]), 'info');
                    }
                } else {
                    $game_name = db::name('cy_game')->where(['id' => $postParam['game_id']])->value('name');
                    $control_type = 1;
                    $data['game_name'] = $game_name;
                    $data['bag_name'] = $postParam['bag_name'];
                    $data['bag_size'] = $postParam['size'];
                    $data['bag_version'] = $postParam['bag_version'];
                    $data['platform_type'] = $ext_suffix == 'apk' ? 1 : 2;
                    $data['create_time'] = NOW_TIMESTAMP;
                    $data['update_time'] = NOW_TIMESTAMP;
                    $data['control_name'] = session('USERNAME');
                    $data['game_id'] = $game_id;
                    $data['remark'] = $postParam['remark'];
                    $data['path'] = $domain . '/sygame/' . $game_package_name . '/' . $game_package_name . '.' . $ext_suffix;
                    $resultUpdate = Db::name('nw_game_package_upload')->insert($data);
                }

                if (!$resultUpdate) {
                    throw new Exception("数据更新失败");
                }
                $cont = [];
                $cont['game_id'] = $game_id;
                $cont['create_time'] = NOW_TIMESTAMP;
                $cont['control_name'] = session('USERNAME');
                $cont['control_type'] = $control_type;
                $result = Db::name('game_upload_log')->insert($cont);

                Db::name('cy_game')->where(['id' => $game_id])->update(['channel_version' => $postParam['bag_version']]);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error("上传失败 " . $e->getMessage());
            }
            $this->success('上传成功');


            //       $datas = [];
            //       $datas['game_name'] = $postParam['name'];
            //       $datas['bag_name'] = $name.'.'.$ext_suffix;
            //       $datas['bag_size'] = $postParam['size'];
            //       $datas['platform_type'] = $ext_suffix == 'apk'?1:2;
            //       $datas['create_time'] = NOW_TIMESTAMP;
            //       $datas['control_type'] = 1;
            //       $datas['control_name'] = session('USERNAME');
            //       $datas['path'] = $domain.'/sygame/'.$name.'/'.$name.'.'.$ext_suffix;
            //       // if (Db::name('nw_game_package_upload')->where(['bag_name'=>$datas['bag_name']])->value('id')) {
            // //           $resultUpdate = Db::name('nw_game_package_upload')->where(['bag_name'=>$datas['bag_name']])->update($datas);
            // //       }else{
            //           $resultUpdate = Db::name('nw_game_package_upload')->insert($datas);
            //       // }
            //       $this->success('上传成功');
        }
    }

    // 直播接口交互签名
    private function getZbSign($data, $sign_key)
    {
        // TODO：注意&times会被转成×的问题，浏览器展示会处理，实际接口请求不影响
        // 1. 构建待拼接字符串
        $pre_sign_str = md5($sign_key);
        $sign_str = md5(sprintf('app_key=qm_sdk&timestamp=%d', $data['timestamp']));

        // 2. 进行两次 md5 计算
        $sign = md5($pre_sign_str . $sign_str);

        // 3. 返回签名
        return $sign;
    }

    public function testPackage(){
        if(request()->isPost()){
            $list = Db::table('nw_game_package_test')->select();
            $this->result(['list' => $list, 'upload_url' => Env::get('mubao.upload_url')], 'success', 200, 'json');
            return;
        }

        $gamne_ids = Db::table('nw_game_package_test')->column('game_id');
        $game_lsit = Db::table('cy_game')->where('id','not in', $gamne_ids)->field('id,name,pinyin')->order('id desc')->select();
        $this->assign('gameLsit', $game_lsit);
        return $this->fetch();
    }
    public function testPackageSave()
    {
        if(request()->isPost()){
            $param = input('post.');
            if (isset($param['id'])) {
                $id = $param['id'];
                unset($param['id']);
                $info = Db::table('nw_game_package_test')->where(['id' => $id])->update($param);
            }else{
                $info = Db::table('nw_game_package_test')->insert($param);
            }
            return [];
        }

        $gamne_ids = Db::table('nw_game_package_test')->column('game_id');
        $game_lsit = Db::table('cy_game')->whereIn('type', [1,2])->whereNotIn('id', $gamne_ids)->field('id,name,pinyin')->order('id desc')->select();

        $id = input('id', '');
        $info = Db::table('nw_game_package_test')->where(['id' => $id])->find();
        $this->assign('info', $info);
        $this->assign('gameLsit', $game_lsit);
        $this->assign('upload_url', Env::get('mubao.upload_url'));

        return $this->fetch();
    }

}
