<?php
/**
 * Created by PhpStorm.
 * User: edison
 * Date: 2018/3/26
 * Time: 上午11:50
 */

namespace app\admin\controller;

use think\Db;
use think\Exception;
use app\common\logic\SubPackage as SubChannel;

class ChannelFrozen extends Admin
{
    public function _initialize()
    {
        parent::_initialize();

        $this->channelFrozenModel = $this->nwModel('channel_frozen');
        $this->sdkgamelistModel = $this->cyModel('sdkgamelist');
        $this->gameList  = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;
    }

    public function index()
    {
        $channelId = input('channel_id');
        $gameId = input('game_id');

        $where = [];
        if ($channelId) {
            $where['channel_id'] = $channelId;
        }

        if ($gameId) {
            $where['game_id'] = $gameId;
        }

        $where['channel_type'] = 0;

        $channleFrozen = $this->channelFrozenModel
            ->where($where)
            ->order("id DESC")
            ->paginate(10, false, ['query' => $where]);
        // 获取分页显示
        $page = $channleFrozen->render();

        // $gameList = $this->cyModel('game')->field('id,name')->select();
        $this->assign('game_list', $this->selfGameList);
        $this->assign('channel_id', $channelId);
        $this->assign('game_id', $gameId);
        $this->assign("page", $page);
        $this->assign("channle_frozen", $channleFrozen);
        return $this->fetch();
    }


    /**
     * 添加冻结渠道
     *
     * @return mixed
     */
    public function add()
    {
        $gameList = $this->cyModel('game')->field('id,name')->select();
        $channelList = model('Common/Channel')->getAllByCondition('id,name',['flag'=>3,'level'=>3]);
        $yyb_channel = model('Channel')->where(['name'=>'运营部'])->field('id,name')->find();
        $channelList[$yyb_channel['id']] = ['id'=>$yyb_channel['id'] , 'name'=>$yyb_channel['name']];
        $this->assign('channel_list', $channelList);
        $this->assign('game_list', $this->selfGameList);

        return $this->fetch();
    }

    public function addPost()
    {
        $result = $this->validate($this->request->param(), 'ChannelFrozen');

        if ($result !== true) {
            $this->error($result);
        } else {
            // 启动事务
            Db::startTrans();

            $result = $this->channelFrozenModel
                ->field('id')
                ->where(['game_id' => $_POST['game_id'], 'channel_id' => $_POST['channel_id']])
                ->find();
            if (!empty($result)) {
                $this->error("数据已存在！id: " . $result['id']);
            }

            $_POST['create_time'] = time();
            $result = $this->channelFrozenModel->insertGetId($_POST);

            if ($result !== false) {
                // 写日志
                $channel_name    = get_channel_name($_POST['channel_id']);
                $game_name       = get_game_nickname($_POST['game_id']);
                $subpackageStr   = '未禁止';
                $grant_moneyStr  = '未禁止';
                $consumeStr      = '未禁止';
                $member_loginStr = '未禁止';
                $registerStr     = '未禁止';

                if ($_POST['subpackage'] ==1){
                    $subpackageStr = "已禁止";
                }
                if ($_POST['grant_money'] ==1){
                    $grant_moneyStr = "已禁止";
                }
                if ($_POST['consume'] ==1){
                    $consumeStr = "已禁止";
                }
                if ($_POST['member_login'] ==1){
                    $member_loginStr = "已禁止";
                }
                if ($_POST['register'] ==1){
                    $registerStr = "已禁止";
                }

                $this->insertLog($this->current_node, "冻结渠道：".$channel_name."，游戏：".$game_name."，分包：".$subpackageStr."，发币：{$grant_moneyStr}，消费：{$consumeStr}，登录：{$member_loginStr}，新增：{$registerStr}",43);

                // 新增渠道 如果禁止分包，则进行删包操作
                if ($_POST['subpackage'] ==1){
                    $game_id = $_POST['game_id'];
                    $channel_id = $_POST['channel_id'];
                    $subChannel = new SubChannel;

                    // 获取当前渠道及其子渠道
                    $channelList = model('Channel')->getChildIds($channel_id);
                    array_unshift($channelList,$channel_id);
                    foreach ($channelList as $v){
                        $res[] = $subChannel->deletePackage($v, $game_id,0);  //普通删包
                        $res[] = $subChannel->deletePackage($v, $game_id,1);  //光环删包
                    }
                }else{
                    $res = [true,true];
                }

                if (!in_array(false,$res)) {
                    // 提交事务
                    Db::commit();
                    $this->success("添加成功！", url("channel_frozen/index"));
                }else{
                    // 回滚事务
                    Db::rollback();
                    $this->error("添加失败！");
                }

            } else {
                // 回滚事务
                Db::rollback();
                $this->error("添加失败！");
            }
        }
    }


    /**
     * 添加已分渠道冻结
     *
     * @return mixed
     */
    public function sdkgamelistAdd()
    {
        $gameList = $this->cyModel('game')->field('id,name')->select();
        $this->assign('game_list', $this->selfGameList);

        return $this->fetch('sdkgamelistadd');
    }

    /**
     * 添加已分渠道冻结处理
     *
     * @return mixed
     */
    public function sdkgamelistAddPost()
    {
        $result = $this->validate($this->request->param(), 'ChannelFrozen.sdkgamelist');

        if ($result !== true) {
            $this->error($result);
        } else {
            $gameid     = input('post.game_id',0,'intval');
            $sdkstatus  = $_POST['sdkstatus'];              //删包状态
            $subpackage = $_POST['subpackage'];             //分包状态

            if(empty($gameid)){
                $this->error('游戏ID不能为空');
            }

            if (empty($sdkstatus) && intval($sdkstatus ) !== 0) {
                $this->error('是否删包不能为空!');
            }

            //分包记录中的渠道ID
            $channelIds         = $this->sdkgamelistModel->where(['gameid' => $gameid])->column('channel_id');
            //渠道冻结中的渠道ID,不关联控制聚合渠道的游戏，仅控制weilong子渠道内的游戏。
            $channelFrozenIds   = $this->channelFrozenModel->where(['game_id' => $gameid,'channel_type'=>0])->column('channel_id');
            $channelIds = array_unique(array_merge($channelIds,$channelFrozenIds));

            if (count($channelIds) <= 0) {
                $this->error('该游戏无渠道信息! ');
            }

            $subChannel     = new SubChannel;

            // 启动事务
            Db::startTrans();

            try{
                //------- 游戏冻结处理  start------------//
                $gameFrozenData['subpackage']   = $_POST['subpackage'];
                $gameFrozenData['grant_money']  = $_POST['grant_money'];
                $gameFrozenData['consume']      = $_POST['consume'];
                $gameFrozenData['member_login'] = $_POST['member_login'];
                $gameFrozenData['register']     = $_POST['register'];
                $gameFrozenData['update_time']  = NOW_TIMESTAMP;

                //更新
                if(Db::table('nw_game_frozen')->where(['game_id'=>$gameid])->find()){
                    Db::table('nw_game_frozen')->where(['game_id'=>$gameid])->update($gameFrozenData);
                }
                //新增
                else{
                    $gameFrozenData['game_id']      = $gameid;
                    $gameFrozenData['create_time']  = NOW_TIMESTAMP;

                    if (!Db::table('nw_game_frozen')->insert($gameFrozenData)) {
                        throw new \Exception('添加游戏冻结数据失败！');
                    }
                }
                //------- 游戏冻结处理  end------------//


                //------- 渠道冻结处理  start------------//
                // 写日志
                $subpackageStr   = '未禁止';
                $grant_moneyStr  = '未禁止';
                $consumeStr      = '未禁止';
                $member_loginStr = '未禁止';
                $registerStr     = '未禁止';
                $sdkstatusStr    = '否';

                if ($_POST['subpackage'] ==1){
                    $subpackageStr = "已禁止";
                }
                if ($_POST['grant_money'] ==1){
                    $grant_moneyStr = "已禁止";
                }
                if ($_POST['consume'] ==1){
                    $consumeStr = "已禁止";
                }
                if ($_POST['member_login'] ==1){
                    $member_loginStr = "已禁止";
                }
                if ($_POST['register'] ==1){
                    $registerStr = "已禁止";
                }
                if ($_POST['sdkstatus'] ==1){
                    $sdkstatusStr = "是";
                }
 				$game_name       = get_game_nickname($gameid);

                foreach ($channelIds as $channelId) {

                    //要删包时  禁止分包时，也进行删包
                    if($sdkstatus || $subpackage){
                        $subChannel->deletePackage($channelId, $gameid,0);  //普通删包
                        $subChannel->deletePackage($channelId, $gameid,1);  //光环删包
                    }

                    //2. 渠道冻结数据插入
                    $result = $this->channelFrozenModel
                                    ->field('id')
                                    ->where(['game_id' => $gameid, 'channel_id' => $channelId])
                                    ->find();

                    if (empty($result)) {
                        $data['channel_id']   = $channelId;
                        $data['game_id']      = $gameid;
                        $data['subpackage']   = $_POST['subpackage'];
                        $data['grant_money']  = $_POST['grant_money'];
                        $data['consume']      = $_POST['consume'];
                        $data['member_login'] = $_POST['member_login'];
                        $data['register']     = $_POST['register'];
                        $data['create_time']  = NOW_TIMESTAMP;
                        $data['update_time']  = NOW_TIMESTAMP;
                        $insertResult = $this->channelFrozenModel->insertGetId($data);

                        if (!$insertResult) {
                            throw new \Exception('添加冻结数据失败！');
                        }
                    }
                    else{
                        $updateData['subpackage']   = $_POST['subpackage'];
                        $updateData['grant_money']  = $_POST['grant_money'];
                        $updateData['consume']      = $_POST['consume'];
                        $updateData['member_login'] = $_POST['member_login'];
                        $updateData['register']     = $_POST['register'];
                        $updateData['update_time']  = NOW_TIMESTAMP;
                        $this->channelFrozenModel->where(['game_id' => $gameid, 'channel_id' => $channelId])->update($updateData);

                    }
                }
                //------- 渠道冻结处理  end------------//

                // 提交事务
                Db::commit();
				$this->insertLog($this->current_node, "游戏：".$game_name."，分包：".$subpackageStr."，发币：{$grant_moneyStr}，消费：{$consumeStr}，登录：{$member_loginStr}，新增：{$registerStr}，删包：{$sdkstatusStr}",47);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();

                $this->error('操作失败, ' . $e->getMessage());
            }

            $this->success('操作成功！', url('index'));
        }
    }


    /**
     * 操作禁止
     */
    public function ban()
    {
        $id = $this->request->param('id', 0, 'intval');
        $action = input('action');

        if (!empty($id) && !empty($action)) {
            $subChannel = new SubChannel;

            $FrozenInfo = $this->channelFrozenModel->where(["id" => $id])->find();
			$channel_name    = get_channel_name($FrozenInfo['channel_id']);
			$game_name       = get_game_nickname($FrozenInfo['game_id']);

            $subpackageStr   = '未禁止';
            $grant_moneyStr  = '未禁止';
            $consumeStr      = '未禁止';
            $member_loginStr = '未禁止';
            $registerStr     = '未禁止';
            if ($FrozenInfo['subpackage'] ==1){
                $subpackageStr = "已禁止";
            }
            if ($FrozenInfo['grant_money'] ==1){
                $grant_moneyStr = "已禁止";
            }
            if ($FrozenInfo['consume'] ==1){
                $consumeStr = "已禁止";
            }
            if ($FrozenInfo['member_login'] ==1){
                $member_loginStr = "已禁止";
            }
            if ($FrozenInfo['register'] ==1){
                $registerStr = "已禁止";
            }
			$logStr = "冻结渠道：".$channel_name."，游戏：".$game_name;
			if($action == 'grant_money'){
				$grant_moneyStr = "已禁止";
			}
			else if($action == 'consume'){
				$consumeStr = "已禁止";
			}
			else if($action == 'member_login'){
				$member_loginStr = "已禁止";
			}
			else if($action == 'register'){
				$registerStr = "已禁止";
			}
			else if($action == 'subpackage'){
				$subpackageStr = "已禁止";
			}
			$logStr .= "，分包：{$subpackageStr} ，发币：{$grant_moneyStr}，消费：{$consumeStr}，登录：{$member_loginStr}，新增：{$registerStr}";

            // 启动事务
            Db::startTrans();
            $res = [];
            $result = $this->channelFrozenModel->where(["id" => $id])->setField($action, '1');

            // 禁止分包采取进行删包
            if ($action == 'subpackage'){
                // 获取当前渠道及其子渠道
                $currentChannel = $this->channelFrozenModel->where(["id" => $id])->field('channel_id,game_id')->find();
                $channelList = model('Channel')->getChildIds($currentChannel['channel_id']);
                array_unshift($channelList,$currentChannel['channel_id']);
                foreach ($channelList as $v){
                    $res[] = $subChannel->deletePackage($v, $currentChannel['game_id'],0);  //普通删包
                    $res[] = $subChannel->deletePackage($v, $currentChannel['game_id'],1);  //光环删包
                }
            }else{
                $res = [true,true];
            }

            if ($result !== false && !in_array(false,$res)) {
                // 提交事务
                Db::commit();
//                dump('禁止成功');
				$this->insertLog($this->current_node, $logStr,48);
                $this->success("禁止成功！");
            } else {
                // 回滚事务
                Db::rollback();
//                dump('禁止失败');
                $this->error("禁止失败！");
            }
        } else {
            $this->error('数据传入失败！');
        }
    }


    /**
     * 操作开启
     */
    public function cancelBan()
    {
        $id = $this->request->param('id', 0, 'intval');
        $action = input('action');
        if (!empty($id) && !empty($action)) {
			$FrozenInfo = $this->channelFrozenModel->where(["id" => $id])->find();
			$channel_name    = get_channel_name($FrozenInfo['channel_id']);
			$game_name       = get_game_nickname($FrozenInfo['game_id']);
			$logStr = "冻结渠道：".$channel_name."，游戏：".$game_name;

            if ($action !== 'all') {
				//日志
				$subpackageStr   = '未禁止';
				$grant_moneyStr  = '未禁止';
				$consumeStr      = '未禁止';
				$member_loginStr = '未禁止';
				$registerStr     = '未禁止';
				if ($FrozenInfo['subpackage'] ==1){
					$subpackageStr = "已禁止";
				}
				if ($FrozenInfo['grant_money'] ==1){
					$grant_moneyStr = "已禁止";
				}
				if ($FrozenInfo['consume'] ==1){
					$consumeStr = "已禁止";
				}
				if ($FrozenInfo['member_login'] ==1){
					$member_loginStr = "已禁止";
				}
				if ($FrozenInfo['register'] ==1){
					$registerStr = "已禁止";
				}
				$logStr = "冻结渠道：".$channel_name."，游戏：".$game_name;
				if($action == 'grant_money'){
					$grant_moneyStr = "未禁止";
				}
				else if($action == 'consume'){
					$consumeStr = "未禁止";
				}
				else if($action == 'member_login'){
					$member_loginStr = "未禁止";
				}
				else if($action == 'register'){
					$registerStr = "未禁止";
				}
				else if($action == 'subpackage'){
					$subpackageStr = "未禁止";
				}
				$logStr .= "，分包：{$subpackageStr} ，发币：{$grant_moneyStr}，消费：{$consumeStr}，登录：{$member_loginStr}，新增：{$registerStr}";

                $result = $this->channelFrozenModel->where(["id" => $id])->setField($action, '0');
            } else {
				//日志
				$logStr .= "，分包：未禁止，发币：未禁止，消费：未禁止，登录：未禁止，新增：未禁止";

                $result = $this->channelFrozenModel->where(["id" => $id])->update([
                    'subpackage' => 0,
                    'grant_money' => 0,
                    'consume' => 0,
                    'member_login' => 0,
                    'register' => 0,
                ]);
            }

            if ($result !== false) {
				$this->insertLog($this->current_node, $logStr,48);
                $this->success("开启成功！");
            } else {
                $this->error("开启失败！");
            }
        } else {
            $this->error('数据传入失败！');
        }
    }


    /**
     * 删包操作
     */
    public function delete()
    {
        $game_id = $this->request->param('game_id', 0, 'intval');
        $channel_id = $this->request->param('channel_id', 0, 'intval');

        $msg = '删包成功!';
        $code = 200;
        //删包功能
        if(empty($game_id) || empty($channel_id)){
            $this->error('游戏和渠道ID不能为空');
        }

        $subChannel = new SubChannel;
        $subChannel->deletePackage($channel_id, $game_id,0);  //普通删包
        $subChannel->deletePackage($channel_id, $game_id,1);  //光环删包

        $ret = $this->sdkgamelistModel
            ->where(['gameid' => $game_id, 'channel_id' => $channel_id])
            ->update(
                [
                    'upload_status' => 0,
                    'filename' => ''
                ]
            );

        if ($ret !== false) {
			$msg = '删包成功!';
			$channel_name    = get_channel_name($channel_id);
			$game_name       = get_game_nickname($game_id);
			$logStr = "渠道：".$channel_name."，游戏：".$game_name;
			$this->insertLog($this->current_node, $logStr,49);
        } else {
			$msg = '删包失败！!';
        }

        $data = [
            'msg' => $msg,
            'code'=> $code
        ];

        return json($data);
    }
}