<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\admin\controller;
use app\common\library\MakeReport;
use app\common\library\MakeReportGo;
use app\common\model\Members as MembersModel;

class MemberCoinInfo extends Admin
{
    protected $membersModel;


    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->membersModel = new MembersModel;
    }

    /**
     * 账户明细列表
     */
    public function getList()
    {

        $start_time       = input('request.start_time', '', 'trim');
        $end_time         = input('request.end_time', '', 'trim');
        $username         = input('request.username', '', 'trim');
        $userid			  = input('request.userid', 0, 'intval');
        $type         = input('type', '', 'trim');

        $where = [];
        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['m.create_time'] = getTimeCondition($start_time,$end_time,false);
        }
        if (!empty($type)) {
            $where['m.type'] = $type;
        }
        //用户ID
        if ($userid) {
            $where['m.userid'] = $userid;
        }
        //用户名
        if ($username != '') {
            $where['cm.username'] = $username;
        }
        if (request()->isAjax() && input('download')) {
//            $makeReport = new MakeReportGo();
            $sql = model("MemberCoinInfo")->alias('m')
                ->field("m.id,m.userid,cm.username,m.amount,m.start_amount,m.result_amount,m.orderid,m.create_time,m.type,m.remarks")
                ->join('cy_members cm','cm.id=m.userid','left')
                ->where($where)
                ->order('m.create_time desc')
                ->fetchSql(true)->select();
            if((new MakeReportGo())->addTask('admin.memberCoinInfoList',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }
        $list = model("MemberCoinInfo")->alias('m')
            ->field("m.id,m.userid,cm.username,m.amount,m.start_amount,m.result_amount,m.orderid,m.create_time,m.type,m.remarks")
            ->join('cy_members cm','cm.id=m.userid','left')
            ->where($where)
            ->order('m.create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];


        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('start_time',$start_time);
        $this->assign('end_time', $end_time);


        return $this->fetch('get_list');
    }

    /**
     * 充值记录管理页-查看玩家账号
     */
    public function showUsername()
    {
        $userid = input('userid');


        if(empty($userid)){

            $this->error('玩家ID不能为空');
        }

        $username= model('Common/Members')->where(['id'=>$userid])->value('username');

        if(!empty($username)){

            //充值记录管理页
            $this->insertLog($this->current_node,'查看账号：'.$username,82);

            $this->result($username,1);
        }
        else{
            $this->error('玩家信息不存在');
        }
    }

}
