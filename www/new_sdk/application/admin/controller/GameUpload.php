<?php

/**
 * 游戏上传
 */

namespace app\admin\controller;
use Overtrue\Pinyin\Pinyin;
use think\Env;

class GameUpload extends Admin
{
    public function index() {
        return $this->fetch();
    }
    /**
     * 文件上传
     * @param string $value [description]
     */
    public function UploadBigFile()
    {
    	$pinyin = new Pinyin();

		$getParam   = input('get.');
		$postParam  = input('post.');
		$name       = $pinyin->permalink(substr($postParam['name'],0,strrpos($postParam['name'] ,".")), '');//转中文
		$act        = $getParam['act'];
		$ups        = Env::get('MUBAO_PATH').'sygame/'.$name.'/';
		$ext_suffix = substr($postParam['name'],strripos($postParam['name'],".")+1);//后缀
    	$flag = true;
	    !in_array($ext_suffix, ['ipa','apk']) && $flag = false;//判断后缀名是否符合
	    if (!$flag) {
	    	$this->error('选择文件错误');
	    }
    	if ($act == 'upload') {
		    $index = $postParam['index'];
		    $filename = $ups."$index".$name.'.'.$ext_suffix;
		    if (!file_exists($ups))//如果文件夹不存在
			{
				mkdir($ups, 0777);
			}
		    //断点上传已经存在的就跳过
		    if (file_exists($filename)) {
		    	$this->success('上传成功');
		    }else{
		    	$result = move_uploaded_file($_FILES['data']['tmp_name'], iconv('UTF-8', 'GBK', $filename));
		    }
		    if ($result) {
		    	$this->success('上传成功');
		    } else {
		    	$this->success('上传失败');
		    }
		}
		elseif ($_GET['act'] == 'join') {
		    $total = intval($postParam['total']);
		    $filename = $ups.$name.'.'.$ext_suffix;
		    //合并时候判断 存在之前上传过的文件就删除掉在合并
		    @unlink(iconv('UTF-8', 'GBK', $filename));
		    for($i = 1; $i<=$total; $i++){
		        file_put_contents($ups.iconv('UTF-8', 'GBK', $name.'.'.$ext_suffix), file_get_contents(iconv('UTF-8', 'GBK', $ups."$i".$name.'.'.$ext_suffix)), FILE_APPEND);
		        @unlink(iconv('UTF-8', 'GBK', $ups."$i".$name.'.'.$ext_suffix));
		    }
		    $this->success('上传成功');
		}
    }

}
