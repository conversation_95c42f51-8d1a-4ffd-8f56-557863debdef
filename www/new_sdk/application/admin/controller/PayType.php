<?php
/**
 * 支付方式管理控制器
 */

namespace app\admin\controller;

use app\common\model\PayType as PayTypeModel;
use app\common\model\AndroidSdk;

class PayType extends Admin
{
    private $payTypeModel;

    protected function _initialize()
    {
        parent::_initialize();
        
        $this->payTypeModel = new PayTypeModel;
    }

    /**
     * 列表
     * @return mixed|string
     */
    public function index()
    {
        $sdkModel = new AndroidSdk;
        $list = $this->payTypeModel->order('status', 'desc')->paginate(30);
        $sdk_list = $sdkModel->field('id,version')->column('version','id');
        
        $this->assign('list', $list);
        $this->assign('sdk_list', $sdk_list);
        $this->assign('page', $list->render());
        
        return $this->fetch();
    }
    
    /**
     * 新增
     */
    public function create()
    {
        if (request()->isPost()) {
            
            $data = [
                'paytype' 	=> input('post.paytype'),
                'payname'   => input('post.payname'),
                'status'    => input('post.status'),
                'ext'    => input('post.ext'),
            ];
            
            $result = $this->validate($data,
                [
                    ['paytype', 'require|max:20', '请填写支付方式|支付方式最大不能超过20个字符'],
                    ['payname', 'require|max:20', '请填写名称|名称最大不能超过20个字符'],
                    ['status', 'require|integer', '请选择状态|状态值必须为整型'],
                ]);
            
            if (true !== $result) {
                $this->error($result);
            }

            try{
                $this->payTypeModel->insert($data);
            }
            catch(\Exception $e){
                
                $this->error('支付方式新增失败');
            }
            
            $this->success('支付方式新增成功','PayType/index');
        }
        
        return $this->fetch('new');
    }
    
    /**
     * 编辑
     */
    public function edit()
    {
        $id = input('id');
        
        if(empty($id)){
            
            $this->error('支付方式ID不能为空');
        }
        
        if (request()->isPost()) {
            
            $data = [
                'payname'    => input('post.payname'),
                'status'    => input('post.status'),
                'ext'    => input('post.ext'),
                'used_sdk_id'=> input('post.used_sdk_id/a')
            ];
            
            $result = $this->validate($data,
                [
                    ['status', 'require|integer', '请选择状态|状态值必须为整型'],
                    ['used_sdk_id', 'require', '请选择SDK版本'],
                ]);
            
            if (true !== $result) {
                $this->error($result);
            }
            
            $data['used_sdk_id'] = ','.implode(',',$data['used_sdk_id']).',';

            if($this->payTypeModel->where(['id'=>$id])->update($data)){
                
                $this->success('支付方式编辑成功','PayType/index');
            }
            else{
                $this->error('支付方式编辑失败');
            }
        }
        
        $sdkModel = new AndroidSdk;
        
        $info       = $this->payTypeModel->get($id);
        $sdk_list   = $sdkModel->field('id,version')->select();
        
        if(empty($info)){
            $this->error('支付方式不存在');
        }
        
        $this->assign('info',$info);
        $this->assign('arr_used_sdk_id',explode(',',substr($info['used_sdk_id'],1,-1)));
        $this->assign('sdk_list',$sdk_list);
        
        return $this->fetch('edit');
    }
    
}