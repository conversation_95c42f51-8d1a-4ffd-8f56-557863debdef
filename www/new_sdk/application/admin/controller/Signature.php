<?php

/**
 * 后台的公共控制器类
 *
 */

namespace app\admin\controller;

use app\common\controller\Base;
use app\common\model\Admin as AdminModel;
use think\Db;

class Signature extends Base
{
    public function __construct()
    {
        parent::__construct();
        $tmpSelfGameList = model('Common/Game')->alias('g')->field('g.id,g.name')
            ->join('cy_gameinfo cgi', 'g.id=cgi.game_id')
            ->where(['platform' => 1])->order('id desc')->select();

        $selfGameList = array();
        foreach ($tmpSelfGameList as $game) {
            $selfGameList[$game['id']] = $game;
        }
        $game_list = array(array('id' => 0, 'name' => ''));
        $game_list = array_merge($game_list, $selfGameList);
        $this->assign('game_list', $game_list);
    }

    public function signatureList()
    {
        $game_id = input('request.game_id', 0, 'intval');
        $start_time = input('request.start', '', 'trim');
        $end_time = input('request.end', '', 'trim');

        $where = [];
        //用户ID
        if ($game_id) {
            $where['game_id'] = $game_id;
        }
        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)) {
            $where['end_date'] = getDateTimeCondition($start_time, $end_time, false);
        }


        $list = model("Signature")->alias('m')
            ->field("m.id,m.game_id,m.start_date,m.end_date,m.contact,m.remarks,m.update_time,cg.name")
            ->join('cy_game cg', 'cg.id=m.game_id', 'left')
            ->where($where)
            ->where('end_date','>',date('Y-m-d',strtotime("-30 day")))
            ->order('m.end_date asc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];

        foreach ($data as $k => $v) {
            $day = (int)((strtotime($v['end_date']) - strtotime(date('Y-m-d')))/86400);

            if($day>0){
                $data[$k]['day'] = '<span style="color:green">'.$day.'天后到期</span>';
            }else{
                $data[$k]['day'] =  '<span style="color:red">已到期</span>';
            }
        }

        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('start_time', $start_time);
        $this->assign('end_time', $end_time);
        return $this->fetch();
    }

    /**
     * 编辑
     */
    public function signatureEdit()
    {
        $id = input('id', 0, 'intval');
        $signatureModel = new \app\common\model\Signature();

        if ($id) {
            $info = $signatureModel->where(['id' => $id])->find();
        } else {
            $info = [];
        }


        if (request()->isPost()) {

            $data = [
                'game_id' => input('post.game_id', '', 'intval'),
                'start_date' => input('post.start_date'),
                'end_date' => input('post.end_date'),
                'contact' => input('post.contact'),
                'remarks' => input('post.remarks', ''),
            ];

            $result = $this->validate($data, [
                ['game_id', 'require|integer|gt:0', '请选择游戏|游戏ID必须为整型|请选择游戏'],
                ['start_date', 'require|date', '开始时间必填|时间格式错误'],
                ['end_date', 'require|date', '结束时间必填|时间格式错误'],
                ['contact', 'require', '联系方式必填'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }

            //检查是否已存在记录
            if ($id > 0) {
                if ($signatureModel->where(['id' => ['neq', $id], 'game_id' => $data['game_id']])->find()) {
                    $this->error('记录已存在');
                }
            }else{
                if ($signatureModel->where([ 'game_id' => $data['game_id']])->find()) {
                    $this->error('记录已存在');
                }
            }


            $data['update_time'] = time();
            if ($info) {
                if ($signatureModel->where(['id' => $id])->update($data)) {
                    $this->success('编辑成功', 'signature/signatureList');
                } else {
                    $this->error('编辑失败');
                }
            } else {
                if ($signatureModel->insert($data)) {
                    $this->success('编辑成功', 'signature/signatureList');
                } else {
                    $this->error('编辑失败');
                }
            }
        }

        $this->assign('data', $info);

        return $this->fetch('signature_edit');
    }
}
