<?php
/**
 * 友情链接管理控制器
 */

namespace app\admin\controller;

use app\common\library\MakeReport;
use app\common\model\Links as LinksModel;
use Overtrue\Pinyin\Pinyin;
use think\Db;
use think\Exception;

class Links extends Admin
{
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 友情链接列表
     */
    public function index()
    {
		$order = trim(input('request.order'));
		if(!$order) $order = 'create_time';

        $where = [];

        //游戏名称
        if (input('request.name') != '') {
            $where['name'] = ['like', '%' .trim(input('request.name')).'%'];
        }

        //标题
        if (input('request.url') != '') {
            $where['url'] = ['like', '%' .trim(input('request.url')).'%'];
        }
		
		if($order=='create_time'){
			$orderby = 'create_time desc,id asc';
		}
		else{
			$orderby = 'sort asc,id asc';
		}

        //查询参数
        $param = input('get.');

        $links = model('Links')->field('id,name,url,sort,create_time')
                              ->where($where)->order($orderby)
                              ->paginate(10, false, array('query' => $param));

	//	echo model('Links')->getLastSql()."----getLastSql------<Br>";
        $this->assign('links', $links);
        $this->assign('page', $links->render());
        $this->assign('order', $order);

        return $this->fetch('index');
    }

    /**
     * 删除友情链接
     *
     */
    public function delete()
    {
        $id = input('id', 0, 'intval');

        if (empty($id)) $this->error('删除记录的ID不能为空');

        if (model('Links')->where(['id' => $id])->delete()) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 新增友情链接
     */
    public function add()
    {
        if (request()->isPost()) {
            $data = [
                'name'     => input('post.name', '', 'trim'),
                'url' => input('post.url','','trim'),    
                'sort'      => input('post.sort','','trim'),
            ];

            $result = $this->validate($data, [
                ['name', 'require', '请输入链接名'],
                ['url', 'require', '请输入链接地址'],
                ['sort', 'require|integer', '请选择排序号|排序号必须为整型'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }

			//检查是否已存在记录
			$condis = array();
			$condis['name'] = $data['name'];
			$condis['url'] = $data['url'];
            $linkInfo = model('Links')->whereOr($condis)->find();
		//	echo model('Links')->getLastSql()."---lastsql-----<br>";
			if(!empty($linkInfo)){
				$this->error('已存在该链接名或链接地址');
			}
            $data['create_time']    = time();

            if (model('Links')->insert($data)) $this->success('新增成功', 'links/index'); 
			else $this->error('新增失败');
        }

        return $this->fetch('add');
    }

    /**
     * 编辑友情链接
     */
    public function edit()
    {
        $id = input('id', 0, 'intval');
        if (empty($id)) $this->error('友情链接的ID不能为空');

        $linkInfo = model('Links')->where(['id' => $id])->find();

        if (empty($linkInfo)) $this->error('记录不存在');

        if (request()->isPost()) {
            $data = [
                'name'     => input('post.name', '', 'trim'),
                'url' => input('post.url','','trim'),    
                'sort'      => input('post.sort','','trim'),
            ];

            $result = $this->validate($data, [
                ['name', 'require', '请输入链接名'],
                ['url', 'require', '请输入链接地址'],
                ['sort', 'require|integer', '请输入排序号|排序号必须为整型'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }


			//检查是否已存在记录
			$condis = array();
			$condis['name'] = $data['name'];
			$condis['id'] = array('neq',$id);
            $linkInfo = model('Links')->where($condis)->find();
			if(!empty($linkInfo)){
				$this->error('已存在该链接名或链接地址');
			}
			$condis = array();
			$condis['url'] = $data['url'];
			$condis['id'] = array('neq',$id);
            $linkInfo = model('Links')->where($condis)->find();
			if(!empty($linkInfo)){
				$this->error('已存在该链接名或链接地址');
			}

            if (model('Links')->update($data, ['id' => $id])) {
                $this->success('编辑成功', 'links/index');
            } else
                $this->error('编辑失败');
        }

        $this->assign('data', $linkInfo);

        return $this->fetch('edit');
    }
}
