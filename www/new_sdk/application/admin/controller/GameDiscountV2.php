<?php
/**
 * 游戏充值折扣比例
 */

namespace app\admin\controller;

use app\common\model\Game;
use app\common\model\GamePayDiscount;
use think\Db;
use think\Loader;

class GameDiscountV2 extends Admin
{
    protected $gamePayDiscount;

    protected function _initialize()
    {
        parent::_initialize();
        $this->gamePayDiscount = new GamePayDiscount;
    }

    // 查
    public function index(){

        $list = $this->gamePayDiscount->alias('gpd')
            ->join("cy_game ng","gpd.game_id=ng.id", "left")
            ->field('gpd.*, ng.name,cooperation_status')
            ->order('id desc')
            ->paginate(10, false, ['query' => input('get.')]);


        $gameList  = Db::table('cy_game')->field('name,id')->select();
        $this->assign('game_list', $gameList);

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }
    // 增
    public function add(){
        if ($this->request->isPost()) {
            $data = $this->request->post();
            $data['proportion']	= trim($data['proportion']);
            if (true !== ($res = $this->validate($data, 'GameDiscountV2'))) {
                $this->error($res);
            }
            $appModel = model('Common/GamePayDiscount');
            $data['create_time'] = time();
            $data['update_time'] = time();
            if ($appModel->save($data)) {
                $gameName = Game::where('id', $data['game_id'])->value('name');
                $this->insertLog($this->current_node, "新增-游戏名：{$gameName}，折扣比例：{$data['proportion']}", 15);

                $this->success('添加成功', url('index'));
            }
            $this->error($appModel->getError() ?: '添加失败');
        }

        $gameList  = Db::table('cy_game')->order('id desc')->field('name,id')->select();
        $this->assign('game_list', $gameList);
        return $this->fetch();
    }
    // 删
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = model('Common/GamePayDiscount');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        $logInfo = GamePayDiscount::alias('gpd')->join('cy_game cg', 'gpd.game_id=cg.id', 'left')->field('gpd.id,cg.name, gpd.proportion')->where('gpd.id', $id)->find();
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->insertLog($this->current_node, "删除-游戏名：{$logInfo['name']}，折扣比例：{$logInfo['proportion']}, 改为：{$logInfo['proportion']}", 15);
            $this->success('删除成功', url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }
    // 改
    public function edit()
    {
        $id       = $this->request->param('id', '', 'intval');
        $appModel = model('Common/GamePayDiscount');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $logInfo = GamePayDiscount::alias('gpd')->join('cy_game cg', 'gpd.game_id=cg.id', 'left')->field('gpd.id,cg.name, gpd.proportion')->where('gpd.id', $id)->find();

        if ($this->request->isPost()) {
            $data           = $this->request->post();
            $data['proportion']	= trim($data['proportion']);
            $data['update_time'] = time();
            $appKeyValidate = Loader::validate('GameDiscountV2');
            if (!$res = $appKeyValidate->check($data)) {
                $this->error($appKeyValidate->getError());
            }
            if ($appModel->update($data, ['id' => $id]) !== false) {
                $this->insertLog($this->current_node, "编辑-游戏名：{$logInfo['name']}，折扣比例：{$logInfo['proportion']}, 改为：{$data['proportion']}", 15);
                $this->success('修改成功', url('index'));
            }
            $this->error($appModel->getError() ?: '修改失败');
        }

        $gameList  = Db::table('cy_game')->order('id desc')->field('name,id')->select();
        $this->assign('game_list', $gameList);

        $this->assign('data', $data);
        return $this->fetch();
    }

}
