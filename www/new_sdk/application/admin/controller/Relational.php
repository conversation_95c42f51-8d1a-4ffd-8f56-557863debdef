<?php
/**
 * 游戏包关联查询
 * Created by PhpStorm.
 * User: hhhhh
 * Date: 2019/1/8
 * Time: 18:02
 */
namespace app\admin\controller;

use app\common\model\PromotionShortLink;

class Relational extends Admin
{
    protected function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->packageModel = model('SdkGameList');
    }
    
    /**
                  *关联查询
     */
    public function index(){
        $condition = [];
        $url_type = input('url_type',0,'intval');
        $filename = input('filename');
        
        $result = $this->validate(['filename'=>$filename], [
            ['filename', 'url', '链接地址格式不正确']
        ]);
        
        if (true !== $result) {
            
            $this->error($result);
        }
        
        if (!empty($filename)){
            
            //推广页链接
            if($url_type==1){
                
                $shortLinkModel = new PromotionShortLink;
                
                $short_link = pathinfo($filename)['filename'];
                
                $list = $shortLinkModel->field('game_id as gameid,package_type,channel_id,create_time as update_time')->where(['short_link'=>$short_link])->order('id desc')->paginate(10, false, ['query' => input('get.')]);
            }
            //游戏包链接
            else{
                if (!empty($filename)){
                    if (preg_match('/^(http|https|ftp):\/\/[\w.]+[\w\/]*[\w.]*\??[\w=&\+\%]*/is', $filename)) {
                        $filename =explode('/',$filename);
                        $filename = end($filename);
                        $condition['filename'] = $filename;
                    } else {
                        $this->error('请输入正确的网址');
                    }
                }
                
                $list = $this->packageModel
                            ->where($condition)
                            ->order('id desc')
                            ->paginate(10, false, ['query' => input('get.')]);
            }
            
            $total  = $list->total();
            $page   = $list->render();
        }
        else{
            
            $list   = [];
            $total  = 0;
            $page   = 0;
        }
            
        $this->assign('list', $list);
        $this->assign('total', $total);
        $this->assign('page',$page);
        
        return $this->fetch('index');
    }
}