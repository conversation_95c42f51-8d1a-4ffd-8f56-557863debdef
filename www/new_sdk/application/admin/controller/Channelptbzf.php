<?php

namespace app\admin\controller;


use app\common\library\OneFileUpload;
use app\common\logic\Member as MemberService;
use think\Loader;
use think\Db;

class Channelptbzf extends Admin
{
    protected $gameList;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }

    /**
     *玩家福利列表
     */
    public function index()
    {
        $where = [];
        $order = 'create_time desc';

        $list = Db::table('mw_channel_ptb')->alias('info')
            ->join('nw_channel_admin channel', 'channel.id = info.channel_id')
            ->field(['channel.username as dep_name','info.id','info.je','info.status','info.out_trade_no','info.create_time'])
            ->where($where)
            ->order($order)
            ->paginate();
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }


    public function edit()
    {
        $id       = $this->request->param('id', '', 'intval');
        $appModel = Db::name('mw_channel_ptb');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data           = $this->request->post();
            $aaa = Db::name('mw_channel_ptb')->where(['id'=>$id])->find();
            Db::name('mw_channel_ptb')->where(['id'=>$id])->update($data);
            $channel_id = $aaa['channel_id'];
            $pyb = Db::name('nw_channel_admin')->where(['id'=>$channel_id])->find();
            $data_info['pyb'] = $pyb['pyb'] + $aaa['je'];
            Db::name('nw_channel_admin')->where(['id'=>$channel_id])->update($data_info);
            $this->success('修改成功',url('index'));
        }
        $this->assign('data', $data);
        return $this->fetch();
    }

}