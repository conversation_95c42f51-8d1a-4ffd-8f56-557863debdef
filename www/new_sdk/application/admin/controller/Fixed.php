<?php

namespace app\admin\controller;

class Fixed
{
    // 待支付订单，交易取消释放平台币
    public function recharge()
    {
        // set_time_limit(300);
        $pay = model('common/Pay')->field('userid,status,orderid,paytype,real_ptb,real_coin,coupon_amount,coupon_member_id')->where('create_time', '>', strtotime('-1 day'))
            ->where('create_time', '<', time() - 60 * 60)->where('status', '=', 0)->select();

        foreach ($pay as $k => $v) {
            model('MemberCoinInfo')->releaseCoin($v['userid'], $v['orderid']);
        }
    }
}
