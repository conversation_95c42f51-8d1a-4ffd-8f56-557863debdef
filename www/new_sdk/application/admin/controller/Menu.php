<?php
/**
 * Created by PhpStorm.
 * User: edison
 * Date: 2018/3/26
 * Time: 上午11:50
 */

namespace app\admin\controller;

use think\Config;
use think\Db;
use tree\Tree;

class Menu extends Admin
{
    protected $noCheckAuth = ['addPost','editPost'];
    
    public function _initialize() {
        parent::_initialize();

        $this->adminAuthRuleModel = $this->nwModel('admin_auth_rule');
    }

    public function index()
    {
        // 排序
        $this->listOrders($this->adminAuthRuleModel);

        $result     = $this->adminAuthRuleModel->order(["list_order" => "ASC"])->select();
        $tree       = new Tree();
        $tree->icon = ['&nbsp;&nbsp;&nbsp;│ ', '&nbsp;&nbsp;&nbsp;├─ ', '&nbsp;&nbsp;&nbsp;└─ '];
        $tree->nbsp = '&nbsp;&nbsp;&nbsp;';

        $newMenus = [];
        foreach ($result as $m) {
            $newMenus[$m['id']] = $m;
        }
        foreach ($result as $key => $value) {
            $result[$key]['parent_id_node'] = ($value['parent_id']) ? ' class="child-of-node-' . $value['parent_id'] . '"' : '';
            $result[$key]['style']          = empty($value['parent_id']) ? '' : 'display:none;';

            $str_manage = '';
            if (authUI('admin/menu/add')) {
                $str_manage .= '<a href="' . url("Menu/add", ["parent_id" => $value['id'], "menu_id" => $this->request->param("menu_id")])
                . '">添加子菜单</a> ';
            }

            if (authUI('admin/Menu/edit')) {
                $str_manage .=  '<a href="' . url("Menu/edit", ["id" => $value['id'], "menu_id" => $this->request->param("menu_id")])
                    . '">编辑</a>  ';
            }

            if (authUI('admin/Menu/delete')) {
                $str_manage .=  '<a href="javascript:;" onclick="del_info(this,\'' . url("Menu/delete", ["id" => $value['id'], "menu_id" => $this->request->param("menu_id")]) . '\')"> 删除 </a> ';
            }

            $result[$key]['status']         = $value['status'] ? '显示' : '隐藏';
            $result[$key]['str_manage']     = $str_manage;

            if (Config::get('app_debug')) {
                $result[$key]['app'] = $value['auth_name'];
            }
        }

        $tree->init($result);
        $str      = "<tr id='node-\$id' \$parent_id_node style='\$style'>
                        <td style='padding-left:20px;'><input name='list_orders[\$id]' type='text' size='3' value='\$list_order'  class=\'layui-input\'></td>
                        <td>\$id</td>
                        <td>\$spacer\$name</td>
                        <td>\$auth_name</td>
                        <td>\$status</td>
                        <td>\$str_manage</td>
                    </tr>";
        $category = $tree->getTree(0, $str);
        $this->assign("category", $category);
        return $this->fetch();
    }

    public function add()
    {
        $tree     = new Tree();
        $parentId = $this->request->param("parent_id", 0, 'intval');
        $result   = $this->adminAuthRuleModel->select();
        $array    = [];
        foreach ($result as $r) {
            $r['selected'] = $r['id'] == $parentId ? 'selected' : '';
            $array[]       = $r;
        }
        $str = "<option value='\$id' \$selected>\$spacer \$name</option>";
        $tree->init($array);
        $selectCategory = $tree->getTree(0, $str);
        $this->assign("select_category", $selectCategory);
        return $this->fetch();
    }

    public function addPost()
    {
        if ($this->request->isPost()) {
            $result = $this->validate($this->request->param(), 'AdminMenu');
            if ($result !== true) {
                $this->error($result);
            } else {
                $icon         = $this->request->param("icon");
                $status       = $this->request->param("status");
                $parent_id    = $this->request->param("parent_id");
                $app          = $this->request->param("app");
                $controller   = $this->request->param("controller");
                $action       = $this->request->param("action");
                $param        = $this->request->param("param");
                $authRuleName = "$app/$controller/$action";
                $menuName     = $this->request->param("name");
                
                //一级菜单时
                if($parent_id==0){
                    
                    $tab_type = $this->request->param("tab_type");
                }
                else{
                    $tab_type = 0;
                }
                

                $findAuthRuleCount = $this->adminAuthRuleModel->where([
                    'auth_name' => $authRuleName,
                ])->count();
                if (empty($findAuthRuleCount)) {
                    $this->adminAuthRuleModel->insert([
                        "name"  => $menuName,
                        "auth_name"   => $authRuleName,
                        'param' => $param,
                        'icon' => $icon,
                        'status' => $status,
                        'parent_id' => $parent_id,
                        'tab_type'  =>$tab_type
                    ]);
                }

                $this->success("添加成功！", url('Menu/index'));
            }
        }
    }
    /**
     * 菜单编辑页
     * @return mixed|string
     */
    public function edit()
    {
        $tree   = new Tree();
        $id     = $this->request->param("id", 0, 'intval');
        $rs     = $this->adminAuthRuleModel->where(["id" => $id])->find();
        list($app, $controller, $action) = explode('/', $rs['auth_name']);
        $rs['app'] = $app;
        $rs['controller'] = $controller;
        $rs['action'] = $action;

        $result = $this->adminAuthRuleModel->select();
        $array  = [];
        foreach ($result as $r) {
            $r['selected'] = $r['id'] == $rs['parent_id'] ? 'selected' : '';
            $array[]       = $r;
        }
        $str = "<option value='\$id' \$selected>\$spacer \$name</option>";
        $tree->init($array);
        $selectCategory = $tree->getTree(0, $str);
        
        $this->assign("data", $rs);
        $this->assign("select_category", $selectCategory);
        
        return $this->fetch();
    }

    /**
     * 菜单编辑处理
     * @return mixed|string
     */
    public function editPost()
    {
        if ($this->request->isPost()) {
            $id      = $this->request->param('id', 0, 'intval');
            $result = $this->validate($this->request->param(), 'AdminMenu.edit');

            if ($result !== true) {
                $this->error($result);
            }
            elseif(!$this->adminAuthRuleModel->where(['id' => $id])->find())
            {
                $this->error('菜单不存在');
            }
            else {
                $icon         = $this->request->param("icon");
                $status       = $this->request->param("status");
                $parent_id    = $this->request->param("parent_id");
                $app          = $this->request->param("app");
                $controller   = $this->request->param("controller");
                $action       = $this->request->param("action");
                $param        = $this->request->param("param");
                $authRuleName = "$app/$controller/$action";
                $menuName     = $this->request->param("name");
                
                //一级菜单时
                if($parent_id==0){
                    
                    $tab_type = $this->request->param("tab_type");
                }
                else{
                    $tab_type = 0;
                }

         
                $this->adminAuthRuleModel->where(['id' => $id])
                        ->update([
                            "name"      => $menuName,
                            "auth_name" => $authRuleName,
                            'param'     => $param,
                            'icon'      => $icon,
                            'status'    => $status,
                            'parent_id' => $parent_id,
                            'tab_type'  => $tab_type
                        ]);
                

                $this->success("保存成功！");
            }
        }
    }

    public function delete()
    {
        $id    = $this->request->param("id", 0, 'intval');
        $count = $this->adminAuthRuleModel->where(["parent_id" => $id])->count();
        if ($count > 0) {
            $this->error("该菜单下还有子菜单，无法删除！");
        }
        if ($this->adminAuthRuleModel->delete($id) !== false) {
            $this->success("删除菜单成功！");
        } else {
            $this->error("删除失败！");
        }
    }
}