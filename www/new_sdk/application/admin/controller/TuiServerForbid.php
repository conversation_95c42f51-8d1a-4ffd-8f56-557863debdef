<?php


namespace app\admin\controller;


use think\Db;
use think\Request;

class TuiServerForbid extends Admin
{
    protected $channel_list;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->assign("channel_list", $this->getChannelList());
    }


    public function index(Request $request)
    {
        $query = Db::table("nw_channel_view_limit")->alias("a");
        if (!empty($request->get("channel_id"))) {
            $query->where("a.channel_id", $request->get("channel_id"));
        }
        $list = $query->join("nw_channel b", "b.id = a.channel_id")
            ->field("a.id,a.channel_id,b.name as channel_name,a.create_time")
            ->order("create_time desc")
            ->paginate(20);
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign("channel_list", $this->getSearchChannelList());
        return $this->fetch();
    }

    // 获取推广员渠道id
    public function getChannelList()
    {
        return Db::table("nw_channel")->where(['status' => 1, "level" => ["in",[2,3]]])->field("id,name")->select();
    }


    // 获取推广员渠道id
    public function getSearchChannelList()
    {
        $list = Db::table("nw_channel")->where(['status' => 1, "level" => ["in",[2,3]]])->field("id,name")->select();
        $new_list = [];
        foreach ($list as $key => $item) {
            $new_list[$key] = $item;
        }
        return $new_list;
    }


    public function add(Request $request)
    {
        if ($request->isPost()) {
            $channel_id = $request->post("channel_id", null);
            if (empty($channel_id)) {
                $this->error("参数错误");
            }
            if (Db::table("nw_channel_view_limit")->where("channel_id", $channel_id)->value("id")) {
                $this->error("记录已经存在");
            }
            $data = [
                "channel_id" => $channel_id,
                "create_time" => time(),
                "update_time" => time(),
            ];
            $id = Db::table("nw_channel_view_limit")->insertGetId($data);
            if ($id) {
                $this->success("操作成功", url("index"));
            } else {
                $this->error("操作失败");
            }
        }

        return $this->fetch();
    }

    public function delete($id=0)
    {
        $id = Db::table("nw_channel_view_limit")->where("id",$id)->delete();
        if ($id) {
            $this->success("删除成功", url("index"));
        } else {
            $this->error("删除失败");
        }
    }
}
