<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\admin\controller;
use app\common\library\MakeReportGo;
use app\common\model\Members as MembersModel;

class MemberCoinPay extends Admin
{
    protected $membersModel;
    protected $where;
    protected $start_time;
    protected $end_time;
    protected $pay_haiwai = ['airwallex','airwallexh5'];

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->membersModel = new MembersModel;
        $this->where        = [];
    }

    /**
     * 账户明细列表
     */
    public function getList()
    {
        $where = $this->_getDetListCondition();
//        var_dump($where);

        if (request()->isAjax() && input('download')) {

            $sql = model("MemberCoinPay")->alias('m')
                ->field("m.id,m.userid,cm.username,m.amount,m.real_amount,m.orderid,m.create_time,m.pay_time,m.paytype,m.status,m.callback_order")
                ->join('cy_members cm','cm.id=m.userid','left')
                ->where($where)
                ->whereNotIn('paytype',$this->pay_haiwai)
                ->order('m.create_time desc')
                ->fetchSql(true)->select();
            //	echo $sql;
            if((new MakeReportGo())->addTask('admin.memberCoinGetList',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }

        $list = model("MemberCoinPay")->alias('m')
            ->field("m.id,m.userid,cm.username,m.amount,m.real_amount,m.orderid,m.create_time,m.paytype,m.status")
            ->join('cy_members cm','cm.id=m.userid','left')
            ->where($where)
            ->whereNotIn('paytype',$this->pay_haiwai)
            ->order('m.create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];
        $totalAmount = $todayTotalAmount = $lastDayTotalAmount = 0;
        if ($list->total()>0) {
            //支付成功 或者支付查询条件未选择时
            if(input('status')==1 || input('status')==''){
                //充值完成总金额
                $totalAmount = model("MemberCoinPay")->alias('m')
                    ->join('cy_members cm','cm.id=m.userid','left')
                    ->field('sum(m.amount) as total_amount')->where($where)
                    ->whereNotIn('paytype',$this->pay_haiwai)
                    ->where('status=1')->find()['total_amount'];
            }
        }

        //今日充值
        $where = array();
        $where['m.create_time'] = [
            ['>=', strtotime(date('Y-m-d'))],
            ['<=', strtotime(date('Y-m-d') . ' 23:59:59')],
        ];
        $where['status'] = 1;


        $todayTotalAmount = floatval(model("MemberCoinPay")->alias('m')
            ->join('cy_members cm','cm.id=m.userid','left')
            ->field('sum(m.amount) as total_amount')->where($where)
            ->whereNotIn('paytype',$this->pay_haiwai)
            ->where('status=1')->find()['total_amount']);


        $where = array();
        $where['m.create_time'] = [
            ['>=', strtotime(date("Y-m-d",strtotime("-1 day")))],
            ['<', strtotime(date('Y-m-d'))],
        ];
        $where['status'] = 1;
        $lastDayTotalAmount = floatval(model("MemberCoinPay")->alias('m')
            ->join('cy_members cm','cm.id=m.userid','left')
            ->field('sum(m.amount) as total_amount')->where($where)
            ->whereNotIn('paytype',$this->pay_haiwai)
            ->where('status=1')->find()['total_amount']);


        $this->assign('paytype', config('paytype'));
        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('start_time', input('request.start_time', '', 'trim'));
        $this->assign('end_time', input('request.end_time', '', 'trim'));
        $this->assign('totalAmount', $totalAmount);
        $this->assign('todayTotalAmount', $todayTotalAmount);
        $this->assign('lastDayTotalAmount', $lastDayTotalAmount);

        return $this->fetch('get_list');
    }

    /**
     * 账户变动明细  条件查询
     * @return array
     */
    protected function _getDetListCondition()
    {

        $start_time       = input('request.start_time', '', 'trim');
        $end_time         = input('request.end_time', '', 'trim');
        $username         = input('request.username', '', 'trim');
        $userid			  = input('request.userid', 0, 'intval');
        $status         = input('status', '', 'trim');
        $orderid        = input('orderid', '', 'trim');
        $paytype        = input('paytype', '', 'trim');

        $where = array();
        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['m.create_time'] = getTimeCondition($start_time,$end_time,false);
        }
        //用户ID
        if ($userid) {
            $where['m.userid'] = $userid;
        }
        //用户名
        if ($username != '') {
            $where['cm.username'] = $username;
        }
        if (!empty($orderid)) {
            $where['m.orderid'] = $orderid;
        }
        if (!empty($paytype)) {
            $where['m.paytype'] = $paytype;
        }

        if (is_numeric($status)) {
            $where['m.status'] = (int)$status;
        }
        return $where;
    }
    /**
     * 充值记录管理页-查看玩家账号
     */
    public function showUsername()
    {
        $userid = input('userid');


        if(empty($userid)){

            $this->error('玩家ID不能为空');
        }

        $username= model('Common/Members')->where(['id'=>$userid])->value('username');

        if(!empty($username)){

            //充值记录管理页
            $this->insertLog($this->current_node,'查看账号：'.$username,82);

            $this->result($username,1);
        }
        else{
            $this->error('玩家信息不存在');
        }
    }

    /**
     * 账户明细列表
     */
    public function getLists()
    {
        $where = $this->_getDetListCondition();
//        var_dump($where);

        if (request()->isAjax() && input('download')) {

            $sql = model("MemberCoinPay")->alias('m')
                ->field("m.id,m.userid,cm.username,m.amount,m.real_amount,m.orderid,m.create_time,m.pay_time,m.paytype,m.status,m.callback_order")
                ->join('cy_members cm','cm.id=m.userid','left')
                ->where($where)
                ->whereIn('paytype',$this->pay_haiwai)
                ->order('m.create_time desc')
                ->fetchSql(true)->select();
            //	echo $sql;
            if((new MakeReportGo())->addTask('admin.memberCoinGetList',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }

        $list = model("MemberCoinPay")->alias('m')
            ->field("m.id,m.userid,cm.username,m.amount,m.real_amount,m.orderid,m.create_time,m.paytype,m.status")
            ->join('cy_members cm','cm.id=m.userid','left')
            ->where($where)
            ->whereIn('paytype',$this->pay_haiwai)
            ->order('m.create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];
        $totalAmount = $todayTotalAmount = $lastDayTotalAmount = 0;
        if ($list->total()>0) {
            //支付成功 或者支付查询条件未选择时
            if(input('status')==1 || input('status')==''){
                //充值完成总金额
                $totalAmount = model("MemberCoinPay")->alias('m')
                    ->join('cy_members cm','cm.id=m.userid','left')
                    ->field('sum(m.amount) as total_amount')->where($where)
                    ->whereIn('paytype',$this->pay_haiwai)
                    ->where('status=1')->find()['total_amount'];
            }
        }

        //今日充值
        $where = array();
        $where['m.create_time'] = [
            ['>=', strtotime(date('Y-m-d'))],
            ['<=', strtotime(date('Y-m-d') . ' 23:59:59')],
        ];
        $where['status'] = 1;


        $todayTotalAmount = floatval(model("MemberCoinPay")->alias('m')
            ->join('cy_members cm','cm.id=m.userid','left')
            ->field('sum(m.amount) as total_amount')->where($where)
            ->whereIn('paytype',$this->pay_haiwai)
            ->where('status=1')->find()['total_amount']);


        $where = array();
        $where['m.create_time'] = [
            ['>=', strtotime(date("Y-m-d",strtotime("-1 day")))],
            ['<', strtotime(date('Y-m-d'))],
        ];
        $where['status'] = 1;
        $lastDayTotalAmount = floatval(model("MemberCoinPay")->alias('m')
            ->join('cy_members cm','cm.id=m.userid','left')
            ->field('sum(m.amount) as total_amount')->where($where)
            ->whereIn('paytype',$this->pay_haiwai)
            ->where('status=1')->find()['total_amount']);


        $this->assign('paytype', config('paytype'));
        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('start_time', input('request.start_time', '', 'trim'));
        $this->assign('end_time', input('request.end_time', '', 'trim'));
        $this->assign('totalAmount', $totalAmount);
        $this->assign('todayTotalAmount', $todayTotalAmount);
        $this->assign('lastDayTotalAmount', $lastDayTotalAmount);

        return $this->fetch('get_lists');
    }
}
