<?php

namespace app\admin\controller;

use think\Config;
use think\Db;


class Autoreply extends Admin
{

    const DEFAULTMSG = 'defaultmsg'; // 收到消息未匹配关键词时回复
    const SUBSCRIBE  = 'subscribe';  // 关注回复

    protected function _initialize()
    {

        $this->model = model('WxAutoreplyMsg');

        parent::_initialize();
    }

    public function index()
    {

        $defaultmsg = $this->model->where('reply_type',self::DEFAULTMSG)->value('content');
        $subscribe = $this->model->where('reply_type',self::SUBSCRIBE)->value('content');
        $this->assign('defaultmsg',$defaultmsg);
        $this->assign('subscribe',$subscribe);

        return $this->fetch();
    }


    public function add()
    {
        if ( request()->isPost()) {
            $post = input('post.');

            // 判断是否新增
            $info = $this->model->where('reply_type',$post['reply_type'])->find();

            $data = [
                'content' => $post[$post['reply_type']]
            ];

            if ( empty($info)) {
                $data['reply_type'] = $post['reply_type'];
                $result = $this->model->insert($data);
            }else{
                $result = $this->model->where('reply_type',$post['reply_type'])->update($data);
            }

            if ( $result !== false) {
                $this->success('更新成功');
            }else{
                $this->error('更新失败');
            }

        }

    }






}