<?php
/**
 * 公众号管理
 * 礼包 控制器
 */

namespace app\admin\controller;

use think\Config;
use app\common\library\MakeReport;
use app\common\model\Links as LinksModel;
use Overtrue\Pinyin\Pinyin;
use think\Db;
use think\Exception;

class Libao extends Admin
{
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 分享礼包-列表
     */
    public function index()
    {
        $wechatDb = Config::get('db_config_wechat.database'); // 角色库

        $where['a.is_del'] = 0;
        $where['b.is_del'] = 0;

        // 标题
        if (input('request.title') != '') {
            $where['b.title'] = ['like', '%' . input('request.title') .'%'];
        }
        if (input('request.name') != '') {
            $where['a.name'] = ['like', '%' . input('request.name') .'%'];
        }
        if (input('request.usetype') != '') {
            $where['a.usetype'] = input('request.usetype');
        }

        $start          = $this->request->param('cr_start');
        $end            = $this->request->param('cr_end');
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $where['a.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $where['a.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $where['a.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        }

        $infoList = Db::table($wechatDb . '.nw_wx_sharelibao')->alias('a')
            ->join($wechatDb.'.nw_wx_article b','a.article_id = b.id')
            ->field('a.*,b.title')
            ->where($where)
            ->order('a.create_time', 'desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $this->assign('list', $infoList);
        $this->assign('page', $infoList->render());

        return $this->fetch('index');
    }

    /**
     * 分享礼包 - 删除
     */
    public function del()
    {
        $id = input('id', 0, 'intval');
        if(empty($id)) $this->error('记录ID不能为空');

        $wechatDb = Config::get('db_config_wechat.database'); // 微信公众号数据库

        $ret =  Db::table($wechatDb . '.nw_wx_sharelibao')->where(['id' => $id])->setField('is_del', 1);

        if (!$ret) {
            $this->error('删除错误！');
        }

        $this->success('删除成功', url('wxArticleList'));
    }


    /**
     * 分享礼包 - 添加
     */
    public function add()
    {
        if (request()->isPost()){
            $data = [
                'name'        => input('post.name') ,
                'article_id'  => input('post.article_id' , 0 , 'intval') ,
                'usetype'     => input('post.usetype' , 0 , 'intval') ,
                'response'    => input('post.response') ,
                'remark'      => input('post.remark') ,
                'create_time' => NOW_TIMESTAMP
            ];

            if (true !== ($res = $this->validate($data, 'Wxarticle'))) {
                $this->error($res);
            }

            $codeStr = input('post.code');
            $codeArray     = explode("\r\n", $codeStr);
            $codeArray     = array_map('filterAndTrimInput', $codeArray);
            $codeArray     = array_filter($codeArray);

            if (empty($codeArray)){
                $this->error('礼包码不能为空');
            }

            $GiftModel = model('Common/WxShareLibao');
            // 判断礼包名是否重复
            $isExit = $GiftModel->where('name',$data['name'])->find();
            if (!empty($isExit)){
                $this->error('礼包名不能重复');
            }
            // 判断文章是否重复
            $isExit_1 = $GiftModel->where(['article_id'=>$data['article_id'],'is_del'=>0])->find();
            if (!empty($isExit_1)){
                $this->error('文章不能重复');
            }

            if ($GiftModel->add($data,$codeArray)) {
                $this->success('添加成功',url('index'));
            }
            $this->error($GiftModel->getError() ?: '添加失败');

        }

        $articleList = model('WxArticle')->getList();
        $this->assign('arList',$articleList);
        return $this->fetch();
    }


    /**
     * 分享礼包 - 编辑
     */
    public function edit()
    {
        $id = $this->request->param('id',0,'intval');
        if (!$id){
            $this->error('ID不能为空');
        }
        $GiftModel = model('Common/WxShareLibao');
        $info = $GiftModel->find($id);

        if (request()->isPost()){
            $usetype = $this->request->param('usetype',0,'intval');
            if (!$usetype){
                $this->error('参数错误');
            }

            $data = [
                'name'        => input('post.name') ,
                'article_id'  => input('post.article_id' , 0 , 'intval') ,
                'response'    => input('post.response') ,
                'remark'      => input('post.remark') ,
                'update_time' => NOW_TIMESTAMP,
                'update_id'   => session('ADMIN_ID'),
            ];

            if (true !== ($res = $this->validate($data, 'Wxarticle.edit'))) {
                $this->error($res);
            }

            // 判断礼包名是否重复
            $isExit = $GiftModel->where('name',$data['name'])->find();
            if (!empty($isExit) && $id != $isExit['id']){
                $this->error('礼包名不能重复');
            }
            // 判断文章是否重复
            if ($data['article_id'] != $info['article_id']){
                $isExit_1 = $GiftModel->where(['article_id'=>$data['article_id'],'is_del'=>0])->find();
                if (!empty($isExit_1) ) $this->error('文章不能重复');
            }

            // 判断是否是无限次礼包，无限次礼包可以修改礼包码
            $code = $this->request->param('code');
            if ($usetype == 2){
                empty($code) ? $this->error('礼包码不能为空') : true;
            }

            Db::startTrans();
            try{

                if ($usetype == 2){
                    model('WxShareLibaoCode')->save(['code'=>$code],['giftid'=>$id]);
                }elseif ($usetype == 1 && !empty($code)){
                    $res = $this->addLibaoCode($code,$id);
                    if (!$res){
                        Db::rollback();
                        $this->error('礼包码添加失败');
                    }
                }

                model('WxShareLibao')->save($data,['id'=>$id]);
                // 提交事务
                Db::commit();
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                $this->error('修改失败');
            }
            $this->success('添加成功',url('index'));
        }


        $code = $info->wxShareLibaoCode()->where(['status'=>['in',[0,2]]])->column('code', 'id');
        $this->assign('code', implode("\r\n", $code));

        $articleList = model('WxArticle')->getList();
        $this->assign('arList',$articleList);
        $this->assign('info',$info);
        return $this->fetch();
    }

    /**
     * 分享礼包 - 添加礼包码
     */
    public function addLibaoCode($codeStr,$giftid)
    {
        /*$id = $this->request->param('id',0,'intval');
        if (!$id){
            $this->error('ID不能为空');
        }
        $codeStr = $this->request->param('code');*/
        if (!$codeStr){
            $this->error('礼包码不能为空');
        }

        $codeArray     = explode("\r\n", $codeStr);
        $codeArray     = array_map('filterAndTrimInput', $codeArray);
        $codeArray     = array_filter($codeArray);

        $codes = [];
        foreach ($codeArray as $v) {
            $codes[] = ['code' => $v,'giftid'=>$giftid];
        }

        $res = model('WxShareLibaoCode')->saveAll($codes);

        return $res;
    }

}
