<?php

/**
 * SDK密钥管理
 * Date: 2018/1/22
 * Time: 10:18
 */

namespace app\admin\controller;


use think\Loader;
use think\Db;
class Appkey extends Admin
{
    protected $gameList;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');

        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;
    }

    /**
     *SDK密钥列表
     */
    public function index()
    {
        $condition = [];
        $gameId    = $this->request->param('gameid', '', 'intval');
        if (!empty($gameId)) {
            $condition ['gameid'] = $gameId;

        }
        $appList = model('Common/App')->alias('a')->join('cy_game g', 'a.gameid=g.id', 'left')->where($condition)->where("g.is_default = 0")->field('a.id,a.appid,a.app_package,a.appkey,a.client_appkey,a.create_time,a.beizhu,a.gameid,g.is_default')->order('id desc')->paginate(20);
        $data     = $appList->toArray();
        $gameid   = array_column($data['data'], 'gameid');
        $gameid   = array_unique($gameid);
        $gameName = model('game')->whereIn('id', $gameid)->column('id,name');

        $this->assign('game_list', $this->selfGameList);
        $this->assign('game_name', $gameName);
        $this->assign('appList', $appList);
        $this->assign('page', $appList->render());
        return $this->fetch();
    }


    /**
     *增
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (true !== ($res = $this->validate($data, 'appkey'))) {
                $this->error($res);
            }

            $data['appkey']         = md5($data['appid'] . $data['gameid'] . time());
            $data['client_appkey']  = md5(random(10).$data['appid']);       //客户端appkey
//
//            if (!$gameInfo = model("game")->field('initial')->where(['id' => $data['gameid']])->find()) {
//                $this->error('游戏信息不存在');
//            }
//            $data['appid'] = $gameInfo['initial'];
            $appModel = model('Common/App');
            if ($appModel->save($data)) {
                $this->success('添加成功',url('index'));
            }
            $this->error($appModel->getError() ?: '添加失败');
        }
        $this->assign('game_list', $this->selfGameList);
        return $this->fetch();
    }


    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = model('Common/App');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功',url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }


    /**
     *编辑SDK密钥
     */
    public function edit()
    {
        $id       = $this->request->param('id', '', 'intval');
        $appModel = model('Common/App');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data           = $this->request->post();
            $appKeyValidate = Loader::validate('appkey');
            if (!$res = $appKeyValidate->check($data)) {
                $this->error($appKeyValidate->getError());
            }
            if ($appModel->save($data, ['id' => $id]) !== false) {
                $this->success('修改成功',url('index'));
            }
            $this->error($appModel->getError() ?: '修改失败');
        }
        $this->assign('game_list', $this->selfGameList);
        $this->assign('data', $data);
        return $this->fetch();
    }

}