<?php

namespace app\admin\controller;

use app\common\library\MakeReport;
use think\Config;
use think\Db;
use think\Exception;
use app\common\logic\SubPackage as SubChannel;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use app\common\library\FileUpload;
use think\Env;

class ChannelShare extends Admin
{
    public function _initialize()
    {
        parent::_initialize();

        $this->adminModel = Db::table('nw_channel_admin');
        $this->channelModel = Db::table('nw_channel');
        $this->divideModel = Db::table('nw_game_channel_divide');

        $tmpGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$gameList = array();
		foreach ($tmpGameList as $game) {
              $gameList[ $game['id']] = $game;
        }
        $this->gameList  = $gameList;

        $temGameAgglist = db::name('nw_extra_game')->field('id,extra_game_name as name')->select();

		$gameAggList = array();
		foreach ($temGameAgglist as $game) {
            $gameAggList[ $game['id']] = $game;
        }
        $this->gameAggList  = $gameAggList;
        $tmpChannelList = Db::table('nw_channel')->field('id,name')->where(['level'=>1])->select();
		$channeList = array();
		foreach ($tmpChannelList as $game) {
              $channeList[ $game['id']] = $game;
        }
        $this->channeList  = $channeList;
    }
    //首页渲染
    public function index()
    {
        $channelId = input('channel_id');
        $gameId = input('game_id');

        $where = [];
        if ($channelId) {
            $where['channel_id'] = $channelId;
        }

        if ($gameId) {
            $where['game_id'] = $gameId;
        }
        $list = $this->divideModel->alias('divide')
            ->field('divide.id,divide.channel_id,divide.game_id,from_unixtime(divide.create_time) as create_time,divide.ratio,game.name as game_name,divide.channel_name')
            ->where($where)
            ->order("id desc")
            ->join('cy_game game', 'divide.game_id = game.id','LEFT')
            ->paginate(10, false, ['query' => $where]);
        //获取分页显示
        $page = $list->render();
        $this->assign('game_list', $this->gameList);
        $this->assign('channel_id', $channelId);
        $this->assign('channel_list', $this->channeList);
        $this->assign('game_id', $gameId);
        $this->assign("page", $page);
        $this->assign("list", $list);
        return $this->fetch();
    }
    //添加分成比例
    public function add()
    {
        $this->assign('channel_list', $this->channeList);
        $this->assign('game_list', $this->gameList);
        return $this->fetch();
    }
    //添加分成比例
    public function addPost()
    {
        $postParam = $this->request->param();
        $ratio = intval($postParam['ratio']);
        if(is_int($ratio) == false){
          $this->error("分成比例必须是正整数");
        }
        if($ratio < 0 || $ratio >= 100){
          $this->error("分成比例范围必须在0-99内");
        }

		$gameInfo = model("Game")->field('id,name')->where('id',$postParam['game_id'])->find();
		if(!$gameInfo){
			$this->error("您选择的游戏不存在");
		}
		$channelInfo = model("Channel")->field('id,name')->where('id',$postParam['channel_id'])->find();
		if(!$channelInfo){
			$this->error("您选择的公会不存在");
		}
		else{
			$channel_name = $channelInfo['name'];
		}
        Db::startTrans();
        try {
            if (Db::table('nw_game_channel_divide_log')->where(['channel_id'=>$postParam['channel_id'],'game_id'=>$postParam['game_id']])->value('id')) {
                throw new Exception("已经添加过分成比例,请不要重复添加");
            }
            $res = $this->divideModel->insertGetId([
                'channel_id'   => $postParam['channel_id'],
                'channel_name' => $channel_name,
                'game_id'      => $postParam['game_id'],
                'ratio'        => $postParam['ratio'],
                'create_time'  => NOW_TIMESTAMP,
            ]);
            if ($res === false) {
                throw new Exception("添加分成比例失败");
            }
            $cont['divide_id']   = $res;
            $cont['channel_id']  = $postParam['channel_id'];
            $cont['game_id']     = $postParam['game_id'];
            $cont['ratio']       = $postParam['ratio'];
            $cont['type']        = 1;
            $cont['create_time'] = NOW_TIMESTAMP;
            Db::table('nw_game_channel_divide_log')->insertGetId($cont);
            Db::commit();

			$template = '公会结算分成比例新增：管理员"'.session('USERNAME').'" 新增了公会结算分成比例，游戏名：'.$gameInfo['name'].'，公会账号：'.$channel_name.'，分成比例：'.$postParam['ratio'].'% 。操作时间：'.date('Y-m-d H:i:s');
			$this->insertLog($this->current_node,$template, 102);
			$ddurl = Env::get('operat_url');
			//curlDD($template, $ddurl,true);

        } catch (Exception $e) {
            Db::rollback();
            $this->error("添加失败： " . $e->getMessage());
        }
        $this->success("添加成功" , url("ChannelShare/index"));
    }

    //修改分成比例
    public function edit()
    {
        $postParam = $this->request->param();
        $ratio = intval($postParam['value']);

        if(is_int($ratio) == false){
           $this->error("分成比例必须是正整数");
        }
        if($ratio < 0 || $ratio >= 100){
           $this->error("分成比例范围必须在0-99内");
        }

		if(!intval($postParam['gameid'])){
			$this->error("请选择要修改的游戏");
		}

		$divideInfo =  $this->divideModel->where('id',$postParam['id'])->find();
		if(!$divideInfo){
			$this->error("您选择的游戏不存在");
		}
		else if(floatval($divideInfo['ratio']) == floatval($ratio)){
			$this->success("分成比例未做任何更改");
		}
		$gameInfo = model("Game")->field('id,name')->where('id',$postParam['gameid'])->find();
		if(!$gameInfo){
			$this->error("您选择的游戏不存在");
		}
		$channelInfo = model("Channel")->field('id,name')->where('id',$postParam['channelid'])->find();
		if(!$channelInfo){
			$this->error("您选择的公会不存在");
		}

        Db::startTrans();
        try {
            $res = model("GameChannelDivide")->where('id',$postParam['id'])->update(['update_time' => NOW_TIMESTAMP,'ratio' => $ratio]);
            if (!$res) {
                $this->error("更新失败");
            }
            $cont['divide_id']   = $postParam['id'];
            $cont['channel_id']  = $postParam['channelid'];
            $cont['game_id']     = $postParam['gameid'];
            $cont['ratio']       = $postParam['value'];
            $cont['type']        = 2;
            $cont['create_time'] = NOW_TIMESTAMP;
            $insertId = Db::table('nw_game_channel_divide_log')->insertGetId($cont);
            Db::commit();

			$template = '公会结算分成比例更新：管理员"'.session('USERNAME').'" 更改了公会结算分成比例，游戏名：'.$gameInfo['name'].'，公会账号：'.$channelInfo['name'].'，分成比例：'.$postParam['value'].'% 。操作时间：'.date('Y-m-d H:i:s');
			$this->insertLog($this->current_node,$template, 103);
			$ddurl = Env::get('operat_url');
			//curlDD($template, $ddurl,true);

        } catch (Exception $e) {
            Db::rollback();
            $this->error("添加失败： " . $e->getMessage());
        }
        $this->success("更新成功");

    }
    /**
     * 补点设置页面
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-07
     * @return void
     */
    public function edithtml()
    {
        $id = input('id');
        $name = input('name');
        if(empty($id)||empty($name)){
            $this->error("参数错误");
        }
        $data = db::name('nw_game_extra_divide')->where(['game_id'=>$id])->value('divide');
        if ($data) {
            $data = json_decode($data,1);
        }
        $this->assign('data',$data);
        $this->assign('id',$id);
        $this->assign('name',$name);
        return $this->fetch('edit');
    }
    //添加分成比例
    public function checklog()
    {
        $postParam = $this->request->param();
        if (empty($postParam['gameid']) || empty($postParam['channelid'])) {
            $this->error("参数错误");
        }
        $res = Db::table('nw_game_channel_divide_log')->where(['game_id'=>$postParam['gameid'],'channel_id'=>$postParam['channelid']])->order("id desc")->select();
        foreach ($res as $key => $value) {
            $res[$key]['create_time'] = date('Y-m-d H:i:s',$value['create_time']);
            $res[$key]['type'] = $value['type'] == 1?'增加':'修改';
        }
        if ($res) {
            $this->success('添加成功',null,$res);
        }else{
            $this->error("暂无数据");
        }
    }

    //游戏默认公会分成比例
    public function gamesplit()
    {
        $gameId = input('game_id');

        $where = [];
		$where['game.is_default'] = 0;
        if ($gameId) {
            $where['game.id'] = $gameId;
        }
        //查询参数
        $param = input('get.');

        $list = model('Game')->alias('game')
            ->join('cy_gameinfo info', 'game.id = info.game_id', 'left')
            ->field('game.id,game.name as game_name,game.origin_name,game.cooperation_status,game.create_time,game.game_kind,game.channel_split_ratio')
            ->where($where)
            ->order('game.id desc')
            ->paginate(10, false, array('query' => $param));
        $this->assign('list', $list);
        $this->assign('page', $list->render());

        $tmpGameList = $this->cyModel('game')->field('id,name')->select();
		$gameList = array();
		foreach ($tmpGameList as $game) {
              $gameList[ $game['id']] = $game;
        }
        $this->assign('game_list',$gameList);
        $this->assign('game_id',$gameId);
        return $this->fetch();
    }

    //修改游戏默认公会分成比例
    public function editGameSplit()
    {
        $postParam = $this->request->param();
        $ratio = intval($postParam['value']);

        if(is_int($ratio) == false){
           $this->error("分成比例必须是正整数");
        }
        if($ratio < 0 || $ratio >= 100){
           $this->error("分成比例范围必须在0-99内");
        }

		if(!intval($postParam['gameid'])){
			$this->error("请选择要修改的游戏");
		}
		$where = array();
		$where['id'] = intval($postParam['gameid']);
		$gameInfo = model("Game")->field('id,name,channel_split_ratio')->where($where)->find();
		if(!$gameInfo){
			$this->error("您选择的游戏不存在");
		}
		else if(floatval($gameInfo['channel_split_ratio']) == floatval($ratio)){
			$this->success("分成比例未做任何更改");
		}

        Db::startTrans();
        try {
            $res = model("Game")->where($where)->update(['channel_split_ratio' => $ratio]);
            if ($res != 1) {
				throw new Exception("分成比例未做任何更改");
            }
            $cont['divide_id']   = 0;
            $cont['channel_id']  = 0;
            $cont['game_id']     = $postParam['gameid'];
            $cont['ratio']       = $ratio;
            $cont['type']        = 2;
            $cont['create_time'] = NOW_TIMESTAMP;
            Db::table('nw_game_channel_divide_log')->insertGetId($cont);
            Db::commit();

			$template = '游戏默认公会分成比例更改：管理员"'.session('USERNAME').'" 更改了游戏公会默认分成比例，游戏名：'.$gameInfo['name'].'，分成比例：'.$ratio.'% 。操作时间：'.date('Y-m-d H:i:s');
			$this->insertLog($this->current_node,$template, 101);
			$ddurl = Env::get('operat_url');
//			curlDD($template, $ddurl,true);
            //$this->success("更新成功");
        } catch (Exception $e) {
            Db::rollback();
            $this->error("添加失败： " . $e->getMessage());
        }
        $this->success("更新成功");

    }
    //获取游戏默认公会分成比例日志
    public function gameSplitLog()
    {
        $postParam = $this->request->param();
        if (empty($postParam['gameid'])) {
            $this->error("参数错误");
        }
        $res = Db::table('nw_game_channel_divide_log')->where(['game_id'=>$postParam['gameid'],'channel_id'=>0])->order("id desc")->select();
        foreach ($res as $key => $value) {
            $res[$key]['create_time'] = date('Y-m-d H:i:s',$value['create_time']);
            $res[$key]['type'] = $value['type'] == 1?'增加':'修改';
        }
        if ($res) {
            $this->success('获取成功',null,$res);
        }else{
            $this->error("暂无数据");
        }
    }
    /**
     * 获取补点记录
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-08
     * @return void
     */
    public function divideLog()
    {
        $postParam = $this->request->param();
        if (empty($postParam['gameid'])) {
            $this->error("参数错误");
        }
        $res = Db::table('nw_game_extra_divide_log')->where(['extra_game_id'=>$postParam['gameid']])->order("id desc")->select();

        foreach ($res as $key => $value) {
            $res[$key]['create_time'] = date('Y-m-d H:i:s',$value['create_time']);
            switch ($value['type']) {
            case '1':
                    $res[$key]['type'] = '增加';
                break;
            case '2':
                    $res[$key]['type'] = '修改';
                break;
            case '3':
                    $res[$key]['type'] = '删除';
                break;
                default:
                $res[$key]['type'] = '增加';
                    break;
            }
        }
        if ($res) {
            $this->success('获取成功',null,$res);
        }else{
            $this->error("暂无数据");
        }
    }

    /**
     * 游戏补点设置
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-08
     * @param Type $var
     * @return void
     */
    public function gameDivideEdit()
    {
        $postParam = $this->request->param();
        $where = array();
        $data = [];
        $where['extra_game_id'] = intval($postParam['game_agg_id']);;
        if (empty($postParam['game_agg_id']) || empty($postParam['data'])) {
            $this->error("参数错误,请刷新后重试!");
        }
        $gameInfo = db::name('nw_extra_game')->field('id,extra_game_name as name')->where(['id'=>$postParam['game_agg_id']])->find();
        $data['divide'] = json_encode($postParam['data'],1);
        $data['remark'] = $postParam['remark'];
        $type = 1;
        Db::startTrans();
        try {
            if (db::name('nw_game_extra_divide')->where($where)->value('id')) { //存在记录
                if (db::name('nw_game_extra_divide')->where($where)->value('divide') == $data['divide']) {
                    throw new Exception("阶梯分成比例未做任何更改");
                }
                $type = 2;
                $data['update_time'] = NOW_TIMESTAMP;
                $data['update_admin_name'] = session('USERNAME');
                $res = db::name('nw_game_extra_divide')->where($where)->update($data);
            }else{
                $type = 1;
                $data['extra_game_id'] = intval($postParam['game_agg_id']);
                $data['create_time'] = NOW_TIMESTAMP;
                $data['add_admin_name'] = session('USERNAME');
                $res = db::name('nw_game_extra_divide')->insert($data);

            }
            if ($res != 1) {
				throw new Exception("阶梯分成比例未做任何更改");
            }
            $cont['extra_game_id']   = $postParam['game_agg_id'];
            $cont['divide']    = json_encode($postParam['data'],1);
            $cont['remark']    = $postParam['remark'];
            $cont['type']      = $type;
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['admin_name'] = session('USERNAME');
            Db::table('nw_game_extra_divide_log')->insertGetId($cont);
            Db::commit();

			$template = '游戏集默认补点比例更改：管理员"'.session('USERNAME').'" 更改了游戏集补点比例，游戏集名：'.$gameInfo['name'].'。操作时间：'.date('Y-m-d H:i:s');
			$this->insertLog($this->current_node,$template, 101);
			$ddurl = Env::get('operat_url');
			//curlDD($template, $ddurl,true);
        } catch (Exception $e) {
            Db::rollback();
            $this->error("添加失败： " . $e->getMessage());
        }
        $this->success("更新成功");
    }
    /**
     * Undocumented function
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-09
     * @param Type $var
     * @return void
     */
    public function checkdel()
    {
        $postParam = $this->request->param();
        $where = array();
        $data = [];
        $where['game_id'] = intval($postParam['game_id']);
        if (empty($postParam['game_id'])) {
            $this->error("参数错误,请刷新后重试!");
        }
        $gameInfo = model("Game")->field('id,name')->where(['id'=>$postParam['game_id']])->find();
		if(!$gameInfo){
			$this->error("您选择的游戏不存在");
        }
        Db::startTrans();
        try {
            if (db::name('nw_game_extra_divide')->where($where)->value('id')) { //存在记录
                $type = 3;
                $data['divide'] = '';
                $data['update_time'] = NOW_TIMESTAMP;
                $data['update_admin_name'] = session('USERNAME');
                $res = db::name('nw_game_extra_divide')->where($where)->update($data);
            }else{
                throw new Exception("该游戏补点不存在");

            }
            if ($res != 1) {
				throw new Exception("阶梯分成比例未做任何更改");
            }
            $cont['game_id']   = $postParam['game_id'];
            $cont['divide']    = '';
            $cont['remark']    = '';
            $cont['type']      = 3;
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['admin_name'] = session('USERNAME');
            Db::table('nw_game_extra_divide_log')->insertGetId($cont);
            Db::commit();

			$template = '游戏默认游戏补点比例更改：管理员"'.session('USERNAME').'" 删除了游戏补点比例，游戏名：'.$gameInfo['name'].'。操作时间：'.date('Y-m-d H:i:s');
			$this->insertLog($this->current_node,$template, 101);
			$ddurl = Env::get('operat_url');
			//curlDD($template, $ddurl,true);
        } catch (Exception $e) {
            Db::rollback();
            $this->error("操作失败： " . $e->getMessage());
        }
        $this->success("操作成功");
    }
    /**
     * 公会补点页面
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-09
     * @param Type $var
     * @return void
     */
    public function dividehtml()
    {
        $channelId = input('channel_id');
        $gameId = input('game_id');
        $where = [];
        $where['channel_type'] = 1;
        $channelId && $where['channel_id'] = $channelId;
        $gameId && $where['extra_game_id'] = $gameId;
        //内部公会账号
        $tmpChannelList = Db::table('nw_channel')->alias('channel')
        ->field('channel.id,channel.name,parent.channel_tg_type')
        ->join('nw_channel parent','channel.parent_id = parent.id','left')
        ->where(['channel.level'=>1,'channel.channel_tg_type'=>0,'parent.channel_tg_type'=>0])
        ->select();
        $list = db::name('nw_game_channel_extra_divide')->alias('divide')
            ->field('divide.id,divide.channel_id,divide.extra_game_id,from_unixtime(divide.create_time) as create_time,divide.divide,game.extra_game_name,channel.name as c_name')
            ->where($where)
            ->order("id desc")
            ->join('nw_extra_game game', 'divide.extra_game_id = game.id','LEFT')
            ->join('nw_channel channel', 'divide.channel_id = channel.id','LEFT')
            ->paginate(10, false, ['query' => $where]);
        //获取分页显示
        $page = $list->render();
        $this->assign('game_list', $this->gameAggList);
        $this->assign('channel_id', $channelId);
        $this->assign('channel_list', $tmpChannelList);
        $this->assign('game_id', $gameId);
        $this->assign("page", $page);
        $this->assign("list", $list);
        return $this->fetch();
    }
    /**
     * 新增公会补点设置页面
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-10
     * @return void
     */
    public function adddividehtml()
    {
        $postParam = $this->request->param();

        if (isset($postParam['game_agg_id'])&&isset($postParam['channel_id'])&&isset($postParam['divide'])) {
            $this->assign('game_agg_id', $postParam['game_agg_id']);
            $this->assign('channel_id', $postParam['channel_id']);
            $this->assign('data', json_decode($postParam['divide']));
            $this->assign('type', 2);
        }else{
            $this->assign('type', 1);
        }
        if (isset($postParam['type']) && $postParam['type'] == 'uniom') {
            $tmpChannelList = Db::table('nw_channel')->alias('channel')
            ->field('channel.id,channel.name')
            ->where(['channel.level'=>0,'channel.channel_tg_type'=>['neq',0]])
            ->select();
            $this->assign('typename', '选择联盟');
            $this->assign('channel_type', '2');
        }else{
            $tmpChannelList = Db::table('nw_channel')->alias('channel')
            ->field('channel.id,channel.name,parent.channel_tg_type')
            ->join('nw_channel parent','channel.parent_id = parent.id','left')
            ->where(['channel.level'=>1,'channel.channel_tg_type'=>0,'parent.channel_tg_type'=>0])
            ->select();
            $temarray = Db::table('nw_channel')->alias('channel')
            ->join('nw_channel parent','channel.parent_id = parent.id','left')
            ->where(['channel.level'=>1,'channel.channel_tg_type'=>0,'parent.channel_tg_type'=>0])
            ->column('channel.id');
            if (isset($postParam['channel_id']) && !in_array($postParam['channel_id'],$temarray)) {
                $this->error("该公会的商务已经更改为外放,无法编辑");
            }
            $this->assign('typename', '选择公会');
            $this->assign('channel_type', '1');
        }
        $list = db::name('nw_extra_game')->column('id,extra_game_name as game');
        $this->assign('channel_list', $tmpChannelList);
        $this->assign('game_agg_list', $list);
        return $this->fetch();
    }
    /**
     * 新增公会补点设置功能
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-10
     * @param Type $var
     * @return void
     */
    public function gameChannelDivideEdit()
    {
        $postParam = $this->request->param();
        $type = $postParam['type'];
        $where = array();
        $data = [];
        $where['extra_game_id'] = intval($postParam['game_agg_id']);
        $where['channel_id'] = intval($postParam['channel_id']);
        if (empty($postParam['game_agg_id']) || empty($postParam['channel_id'])) {
            $this->error("参数错误,请刷新后重试!");
        }
        $gameInfo = db::name('nw_extra_game')->field('id,extra_game_name as name')->where(['id'=>$postParam['game_agg_id']])->find();
		if(!$gameInfo){
			$this->error("您选择的游戏集不存在");
        }
        $channelInfo = model("Channel")->field('id,name,channel_tg_type,level,parent_id')->where('id',$postParam['channel_id'])->find();
		if(!$channelInfo){
			$this->error("您选择的公会不存在");
		}
		else{
			$channel_name = $channelInfo['name'];
        }
        if ($channelInfo['level'] == 1) {//会长情况:对应商务必须是内部
            $flag = model("Channel")->where('id',$channelInfo['parent_id'])->value('channel_tg_type');
            if ($flag) {
                $this->error("该会长渠道为外放商务下的渠道,无法编辑");
            }

            //查看之前是否有商务补点(考虑更改过外放);
            $temp_array = db::name('nw_game_channel_extra_divide')->where('channel_id',$channelInfo['parent_id'])->value('id');
            if ($temp_array) {
                $this->error("创建失败:该公会的商务之前已经创建过外放商务补点请先删除后再创建");
            }
        }else{//商务情况:必须外放

            $flag = model("Channel")->where('id',$postParam['channel_id'])->value('channel_tg_type');
            if (!$flag) {
                $this->error("该商务渠道非外放");
            }

            //判断之前是否有公会补点(考虑更改过外放类型)
            $temp_array = db::name('nw_game_channel_extra_divide')->alias('divide')
            ->join('nw_channel channel', 'channel.id = divide.channel_id','LEFT')
            ->where(['channel.level'=>1,'channel.id_path'=>['LIKE', '%,' . $postParam['channel_id'] . ',%']])
            ->column('divide.id');
            if ($temp_array) {
                $this->error("创建失败:该外放商务之前已经创建过子公会补点请先删除后再创建");
            }
        }
        $data['divide'] = json_encode($postParam['data'],1);
        $data['remark'] = $postParam['remark'];
        $data['channel_type'] = $postParam['channel_type'];
        Db::startTrans();
        try {
            switch ($type) {
                case '1'://新增
                    if (db::name('nw_game_channel_extra_divide')->where($where)->value('id')) {
                        throw new Exception("该公会下的该游戏补点已经设置");
                    }
                    $data['channel_id'] = $postParam['channel_id'];
                    $data['extra_game_id'] = intval($postParam['game_agg_id']);
                    $data['create_time'] = NOW_TIMESTAMP;
                    $data['add_admin_name'] = session('USERNAME');
                    $res = db::name('nw_game_channel_extra_divide')->insertGetId($data);
                    $cont['divide_id']   = $res;

                break;
                case '2'://编辑
                    if (db::name('nw_game_channel_extra_divide')->where($where)->value('divide') == $data['divide']) {
                        throw new Exception("补点信息未做任何更改");
                    }
                    $cont['divide_id'] = db::name('nw_game_channel_extra_divide')->where($where)->value('id');
                    $data['update_time'] = NOW_TIMESTAMP;
                    $data['update_admin_name'] = session('USERNAME');
                    $res = db::name('nw_game_channel_extra_divide')->where($where)->update($data);
                break;
            }
            if (empty($res)) {
				throw new Exception("补点信息添加失败");
            }
            $cont['channel_id']   = $postParam['channel_id'];
            $cont['extra_game_id']   = $postParam['game_agg_id'];
            $cont['divide']    = json_encode($postParam['data'],1);
            $cont['remark']    = $postParam['remark'];
            $cont['type']      = $type;
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['admin_name'] = session('USERNAME');
            Db::table('nw_game_channel_extra_divide_log')->insertGetId($cont);
            Db::commit();

			$template = '公会补点比例新增：管理员"'.session('USERNAME').'" 新增了公会补点比例，游戏名：'.$gameInfo['name'].'，公会账号：'.$channel_name.'。操作时间：'.date('Y-m-d H:i:s');
			$this->insertLog($this->current_node,$template, 101);
			$ddurl = Env::get('operat_url');
			//curlDD($template, $ddurl,true);
        } catch (Exception $e) {
            Db::rollback();
            $this->error("添加失败： " . $e->getMessage());
        }
        $this->success("更新成功");
    }
    /**
     * 删除游戏补点信息
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-10
     * @param Type $var
     * @return void
     */
    public function deldividepost()
    {
        $postParam = $this->request->param();
        if(empty($postParam['id'])){
            $this->error("参数错误");
        }
        $where = array();
        $data = [];
        $where['id'] = intval($postParam['id']);
        $info = db::name('nw_game_channel_extra_divide')->where(['id'=>$postParam['id']])->find();
		if(!$info){
			$this->error("参数错误");
        }
        Db::startTrans();
        try {

            $res = db::name('nw_game_channel_extra_divide')->where(['id'=>$postParam['id']])->delete();//删除
            if (empty($res)) {
				throw new Exception("补点信息删除失败");
            }
            $cont['channel_id']   = $info['channel_id'];
            $cont['extra_game_id']   = $info['extra_game_id'];
            $cont['divide']    = 0;
            $cont['remark']    = '';
            $cont['type']      = 3;
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['admin_name'] = session('USERNAME');
            Db::table('nw_game_channel_extra_divide_log')->insertGetId($cont);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error("操作失败： " . $e->getMessage());
        }
        $this->success("操作成功");
    }

    /**
     * 删除游戏补点信息
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-10
     * @param Type $var
     * @return void
     */
    public function delgamedividepost()
    {
        $postParam = $this->request->param();
        if(empty($postParam['id'])){
            $this->error("参数错误");
        }
        $where = array();
        $data = [];
        $where['id'] = intval($postParam['id']);
        $info = db::name('nw_game_extra_divide')->where(['id'=>$postParam['id']])->find();
		if(!$info){
			$this->error("参数错误");
        }
        Db::startTrans();
        try {

            $res = db::name('nw_game_extra_divide')->where(['id'=>$postParam['id']])->delete();//删除
            if (empty($res)) {
				throw new Exception("补点信息删除失败");
            }
            $cont['extra_game_id']   = $info['extra_game_id'];
            $cont['divide']    = 0;
            $cont['remark']    = '';
            $cont['type']      = 3;
            $cont['create_time'] = NOW_TIMESTAMP;
            $cont['admin_name'] = session('USERNAME');
            Db::table('nw_game_extra_divide_log')->insertGetId($cont);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error("操作失败： " . $e->getMessage());
        }
        $this->success("操作成功");
    }

    /**
     * 查看公会补点修改记录
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-11
     * @return void
     */
    public function checkdividelog()
    {
        $postParam = $this->request->param();
        $where = array();
        $where['extra_game_id'] = intval($postParam['game_id']);
        $where['channel_id'] = intval($postParam['channel_id']);
        $res = Db::table('nw_game_channel_extra_divide_log')->where($where)->order("id desc")->select();
        foreach ($res as $key => $value) {
            $res[$key]['create_time'] = date('Y-m-d H:i:s',$value['create_time']);
            switch ($value['type']) {
            case '1':
                    $res[$key]['type'] = '增加';
                break;
            case '2':
                    $res[$key]['type'] = '修改';
                break;
            case '3':
                    $res[$key]['type'] = '删除';
                break;
            default:
            $res[$key]['type'] = '增加';
                break;
            }
        }
        if ($res) {
            $this->success('获取成功',null,$res);
        }else{
            $this->error("暂无数据");
        }
    }
    /**
     * 外放商务补点
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-11
     * @return void
     */
    public function uniomdividehtml()
    {
        $channelId = input('channel_id');
        $extra_game_id = input('extra_game_id');
        $where = [];
        $where['channel_type'] = 2;
        $channelId && $where['channel_id'] = $channelId;
        $extra_game_id && $where['extra_game_id'] = $extra_game_id;
        //内部公会账号
        $tmpChannelList = Db::table('nw_channel')->alias('channel')
        ->field('channel.id,channel.name')
        ->where(['channel.level'=>0,'channel.channel_tg_type'=>1])
        ->select();
        $list = db::name('nw_game_channel_extra_divide')->alias('divide')
            ->field('divide.id,divide.channel_id,divide.extra_game_id,from_unixtime(divide.create_time) as create_time,divide.divide,game.extra_game_name,channel.name as c_name')
            ->where($where)
            ->order("id desc")
            ->join('nw_extra_game game', 'divide.extra_game_id = game.id','LEFT')
            ->join('nw_channel channel', 'divide.channel_id = channel.id','LEFT')
            ->paginate(10, false, ['query' => $where]);
        //获取分页显示
        $page = $list->render();
        $this->assign('game_list', $this->gameAggList);
        $this->assign('type', "uniom");
        $this->assign('channel_id', $channelId);
        $this->assign('channel_list', $tmpChannelList);
        $this->assign('game_id', $extra_game_id);
        $this->assign("page", $page);
        $this->assign("list", $list);
        return $this->fetch();
    }
    /**
     * 游戏补点设置
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-18
     * @param Type $var
     * @return void
     */
    public function gamedevide()
    {
        $id = input('game_id');

        $where = [];
        if ($id) {
            $where['g.id'] = $id;
        }
        //查询参数
        $param = input('get.');

        $list = Db::table('nw_game_extra_divide')->alias('d')
            ->join('nw_extra_game g', 'g.id = d.extra_game_id', 'left')
            ->field('d.id, from_unixtime(d.create_time) as create_time,d.divide,g.extra_game_name,d.extra_game_id')
            ->where($where)
            ->order('d.id desc')
            ->paginate(10, false, array('query' => $param));
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('game_list',$this->gameAggList);
        $this->assign('game_id',$id);
        return $this->fetch();
    }
    /**
     * 创建游戏集页面
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-21
     * @return void
     */
    public function gamesAggDivide()
    {
        $postParam = $this->request->param();
        if (isset($postParam['game_agg_id'])&&isset($postParam['channel_id'])&&isset($postParam['divide'])) {
            $this->assign('game_agg_id', $postParam['game_agg_id']);
            $this->assign('channel_id', $postParam['channel_id']);
            $this->assign('data', json_decode($postParam['divide']));
            $this->assign('type', 2);
        }else{
            $this->assign('type', 1);
        }
        $list = db::name('nw_extra_game')->column('id,extra_game_name as game');
        $this->assign('channel_list', $this->channeList);
        $this->assign('game_agg_list',$list);
        return $this->fetch();
    }
    /**
     * 添加游戏合集
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-22
     * @return void
     */
    public function addGameAgg()
    {
        $postParam = $this->request->param();
        if (isset($postParam['title'])&&isset($postParam['id'])) {
            $res = db::name('nw_extra_subgame')->where(['extra_game_id'=>$postParam['id']])->column('game_id');
            $this->assign('title', $postParam['title']);
            $this->assign('id', $postParam['id']);
            $this->assign('selected', $res);
            $this->assign('type', 2);
        }else{
            $this->assign('selected', []);
            $this->assign('id', '');
            $this->assign('type', 1);
        }
        $this->assign('game_list',$this->gameList );

        return $this->fetch();
    }

    public function addaggpost()
    {
        $postParam = $this->request->param();
        $data = $postParam['game'];
        $type = $postParam['type'];
        if (empty($postParam['title'])) {
            $this->error("请输入集合名称");
        }
        if (!isset($data[0]) || empty($data[0])) {
            $this->error("请选择至少一款游戏");
        }
        if ($type == 2) {
            if (empty($postParam['id'])) {
                $this->error("参数错误");
            }
        }
        $data = explode(',',$data[0]);
        $cont = [];
        $cont['extra_game_name'] = $postParam['title'];
        $cont['create_time'] = NOW_TIMESTAMP;
        $cont['add_admin_name'] = session('USERNAME');
        $backIds = [];
        $back_msg = '';
        Db::startTrans();
        try {
            switch ($type) {
                case '1'://新增
                    $res = db::name('nw_extra_game')->insertGetId($cont);//创建游戏集
                    if (empty($res)) {
                        throw new Exception("游戏集创建失败");
                    }
                    foreach ($data as $key => $value) {//循环插入数据库
                        if (db::name('nw_extra_subgame')->where(['game_id'=>$value])->value('id')) {
                            $backIds[] = $value;
                        }else{
                            $any = [];
                            $any['extra_game_id'] = $res;
                            $any['game_id'] = $value;
                            $any['create_time'] = NOW_TIMESTAMP;
                            $any['add_admin_name'] = session('USERNAME');
                            db::name('nw_extra_subgame')->insert($any);
                        }
                    }
                    if ($backIds == $data){
                        throw new Exception("所有游戏已存在在其他游戏集里面无法添加");
                    }
                break;
                case '2'://编辑
                    if (db::name('nw_extra_subgame')->where(['extra_game_id'=>$postParam['id']])->column('game_id') == $data) {
                        throw new Exception("更新失败,数据未发生更改");
                    }
                    db::name('nw_extra_subgame')->where(['extra_game_id'=>$postParam['id']])->delete();
                    foreach ($data as $key => $value) {//循环插入数据库
                        if (db::name('nw_extra_subgame')->where(['game_id'=>$value])->value('id')) {
                            $edit_backIds[] = $value;
                        }else{
                            $any = [];
                            $any['extra_game_id'] = $postParam['id'];
                            $any['game_id'] = $value;
                            $any['create_time'] = NOW_TIMESTAMP;
                            $any['add_admin_name'] = session('USERNAME');
                            db::name('nw_extra_subgame')->insert($any);
                        }

                    }
                    if (isset($edit_backIds)&&!empty($edit_backIds)) {
                        $res = db::name('cy_game')->where(['id'=>['in',$edit_backIds]])->column('name');
                        $back_msg = $res;
                    }
                break;
            }

            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error("操作失败： " . $e->getMessage());
        }
        if ($back_msg) {
            $str = implode(',',$back_msg);
            $this->success("操作成功,部分游戏(".$str.")已存在其他游戏集未添加成功");
        }
        $this->success("操作成功");
    }
    /**
     * 游戏集页面
     *
     * @<NAME_EMAIL>
     * @DateTime 2020-12-22
     * @param Type $var
     * @return void
     */
    public function gameAggSet()
    {
        $id = input('game_id');

        $where = [];
        if ($id) {
            $where['id'] = $id;
        }
        //查询参数
        $param = input('get.');

        $list = Db::table('nw_extra_game')
            ->field('id,from_unixtime(create_time) as create_time,extra_game_name,add_admin_name')
            ->where($where)
            ->order('id desc')
            ->paginate(10, false, array('query' => $param));
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('game_list',$this->gameAggList);
        $this->assign('game_id',$id);
        return $this->fetch();
    }
}
