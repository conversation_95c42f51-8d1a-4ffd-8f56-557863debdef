<?php

namespace app\admin\controller;


use app\common\library\OneFileUpload;
use app\common\logic\Member as MemberService;
use think\Loader;
use think\Db;

class WelfareVoluntarily extends Admin
{

    private $type = [
        1 => '订单回调',
        2 => '定时每1分钟',
        3 => '定时每5分钟',
        4 => '定时每10分钟',
        5 => '定时每30分钟',
        6 => '定时每1小时',
        7 => '定时每日凌晨1点',
        8 => '定时每日凌晨5点'
    ];

    /**
     *玩家福利类型列表
     */
    public function index()
    {
        $where = [];
        $order = 'id desc';
        $tmpGameList = model('Common/Game')->getAllByCondition('id,name', ['is_welfare' => 2]);
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }
        //查询参数
        $game_id = $this->request->get('game_id', '', 'trim');
        $welfare = model('Welfare')
            ->order('id desc')->column('name', 'id');
        $welfareModel = model('common/WelfareVoluntarily');
        $list = $welfareModel->where(function ($query) use ($game_id) {
            if (isset($game_id) && !empty($game_id)) {
                $query->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $game_id));
            }
        })
            ->order($order)
            ->paginate(10, false, ['query' => input('get.')])->each(function ($item, $key) use ($gameList) {
                $game_ids = explode(',', $item['game_id']);
                $tmp = [];
                foreach ($game_ids as $k => $v) {
                    $tmp[] = $gameList[$v]['name'];
                }
                $item['game_name'] = implode('<br>', $tmp);

                return $item;
            });
        $this->assign('welfare', $welfare);
        $this->assign('grant_type_ids', model('Welfare')->grant_type_id);
        $this->assign('list', $list);
        $this->assign('game_list', $tmpGameList);
        $this->assign('page', $list->render());
        $this->assign('type', $this->type);

        return $this->fetch();
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data['game_id']) {
                $this->error('游戏不能为空');
            }
            if (!$data['grant_type_id'] && $data['grant_type_id'] < 1) {
                $this->error('福利类型不能为空');
            }
            if (!isset($data['welfare_id']) && $data['welfare_id'] < 1) {
                $this->error('福利不能为空');
            }
//            if (!$data['condition']) {
//                $this->error('条件不能为空');
//            }
            unset($data['select']);
            $data['create_time'] = time();
            if (model('common/WelfareVoluntarily')->insertGetId($data)) {
                $this->success('添加成功', url('index'));
            }
            $this->error('添加失败');
        }
        $gameList = model('Common/Game')->getAllByCondition('id,name', ['is_welfare' => 2]);
        $this->assign('game_list', $gameList);
        $this->assign('type', $this->type);
        $this->assign('grant_type_ids', model('Welfare')->grant_type_id);
        return $this->fetch();
    }

    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = model('common/WelfareVoluntarily');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }
}
