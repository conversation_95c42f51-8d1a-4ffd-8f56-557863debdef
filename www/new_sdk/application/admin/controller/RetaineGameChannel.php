<?php
/**
 * 留存统计 - 游戏渠道
 */

namespace app\admin\controller;


use app\service\RetaineService;
use think\Controller;
use think\Db;
use think\Exception;
use think\Model;

class RetaineGameChannel extends Controller
{
    /**
     * 留存表-游戏-渠道-新增玩家
     * @return string
     */
    public function reg_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();
        //当天注册账号
        $mebmers = $retaineService->getMembers($day);
        $list = $retaineService->regNumNew($day, $mebmers, 0, 1);
        $retaineService->insertOrUpdate($list, 2, $day, 'reg_num');
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-新增角色
     * @return string
     */
    public function role_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //当天新增角色
        $list = $retaineService->getFirstRoleMember($day, 0, 1);

        $retaineService->insertOrUpdate($list, 2, $day, 'role_num');

        return 'ok';

    }

    /**
     * 留存表-游戏-渠道-活跃玩家
     * @return string
     */
    public function act_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //当天活跃玩家
        $list = $retaineService->activeMember($day, 'act_num', 0, 1);

        $retaineService->insertOrUpdate($list, 2, $day, 'act_num');

        return 'ok';

    }

    /**
     * 留存表-游戏-渠道-充值金额-付费人数
     * @return string
     */
    public function recharge_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //充值金额
        $list = $retaineService->rechargeMoney($day, 0, 1);

        $retaineService->insertOrUpdate($list, 2, $day, 'recharge_num');

        $retaineService->insertOrUpdate($list, 2, $day, 'pay_num');

        return 'ok';

    }

    /**
     * 留存表-游戏-渠道-次留
     * @return string
     */
    public function one_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 1));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'one_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 1), 'one_stay');
        }
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-三留
     * @return string
     */
    public function three_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 2));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'three_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 2), 'three_stay');
        }
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-四留
     * @return string
     */
    public function four_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 3));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'four_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 3), 'four_stay');
        }
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-五留
     * @return string
     */
    public function five_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 4));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'five_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 4), 'five_stay');
        }
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-六留
     * @return string
     */
    public function six_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 5));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'six_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 5), 'six_stay');
        }
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-七留
     * @return string
     */
    public function seven_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 6));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'seven_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 6), 'seven_stay');
        }
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-十五留
     * @return string
     */
    public function fifteen_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 14));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'fifteen_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 14), 'fifteen_stay');
        }
        return 'ok';
    }

    /**
     * 留存表-游戏-渠道-三十留
     * @return string
     */
    public function thirty_stay()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //那天创角用户数
        $members = $retaineService->getFirstRoleMemberList($this->getTime($day, 29));
        if ($members) {
            //当天活跃玩家
            $list = $retaineService->activeMember($day, 'thirty_stay', 0, 1, $members);

            $retaineService->insertOrUpdate($list, 2, $this->getTime($day, 29), 'thirty_stay');
        }
        return 'ok';
    }


    private function getTime($day, $num)
    {
        return date('Y-m-d', strtotime($day) - 86400 * $num);
    }
    
    /**
     * 玩家总数
     *
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function reg_total()
    {
        
        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }
        
        $retaineService = new RetaineService();
        $list = $retaineService->regNumNew($day,0,1,0, 0, 'reg_total');
        $retaineService->insertOrUpdate($list, 2, $day, 'reg_total');
        return 'ok';
    }
}
