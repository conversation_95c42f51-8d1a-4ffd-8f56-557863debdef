<?php
/**
 * 运营管理控制器
 */

namespace app\admin\controller;

use app\common\model\Game as GameModel;
use app\common\model\SdkGameList;
use app\common\logic\SubPackage as SubChannel;
use app\common\library\FileUpload;
use think\Db;
use think\Exception;

class OperationManagement extends Admin
{
    protected $gameModel;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->gameModel = new GameModel;
    }

    /**
     * 手游排行
     */
    public function gameRankList()
    {
        $where = [];

        //游戏名称
        if (input('request.name') != '') {
            $where['name'] = ['like', '%' . input('request.name', '', 'trim') . '%'];
        }

        //查询参数
        $param = input('get.');

        $list = $this->gameModel->field('id,name,power,power_stat,power_plus')
                                        ->where(['cooperation_status' => ['in', '1,2'],'is_show'=>1,'isdelete'=>0])
                                       ->where($where)
                                       ->order('power desc')
                                       ->paginate(10, false, array('query' => $param));

        $this->assign('list', $list);
        $this->assign('page', $list->render());

        return $this->fetch('game_rank_list');
    }

    /**
     * 手游排行编辑
     */
    public function gameRankEdit($id)
    {
        if(empty($id)){

            $this->error('游戏ID不能为空');
        }

        $info = $this->gameModel->field('id,name,power,power_stat,power_plus')
                    ->where(['id' => $id])
                    ->find();

        if(empty($info)){
            $this->error('游戏不存在');
        }

        if (request()->isPost()) {

            $data['power_plus'] = input('post.power_plus');

            $result = $this->validate($data, [
                ['power_plus', 'require|integer', '请输入修改热度|修改热度必须为整数']
            ]);

            if (true !== $result) {

                $this->error($result);
            }

            $data['power'] = Db::raw("power_stat+".$data['power_plus']);

            $update = $this->gameModel->save($data,['id'=>$id]);

            $this->success('手游排行编辑成功', 'gameRankList');
        }

        $this->assign('info', $info);

        return $this->fetch('game_rank_edit');
    }

    /**
     * 热搜列表
     */
    public function keywordsList()
    {
        $where = [];

        //游戏名称
        if (input('request.title') != '') {
            $where['title'] = ['like', '%' . input('request.title', '', 'trim') . '%'];
        }

        //查询参数
        $param = input('get.');

        $keywordsModel = Db::name('cy_keywords');

        $list = $keywordsModel->where($where)
                    ->order('create_time desc')
                    ->paginate(10, false, array('query' => $param))
                    ->each(function ($item, $key){
                        if ($item['type'] == 2){
                            $item['title'] = Db::name('cy_game')->where('id',$item['title'])->value('name');
                        }
                        return $item;
                    });

        $this->assign('list', $list);
        $this->assign('page', $list->render());

        return $this->fetch('keywords_list');
    }

    /**
     * 热搜词新增
     */
    public function keywordsCreate()
    {
        $keywordsModel = Db::name('cy_keywords');

        if (request()->isPost()) {

            $data['title'] = input('post.title');
            $data['type']  = input('post.type');
            $gameid        = input('post.gameid');

            if ($data['type'] == 2){
                $data['title'] = $gameid;
            }

            $result = $this->validate($data, [
                ['title', 'require|max:50', '请输入热搜词名称|热搜词名称不能超过50个字符'],
                ['type', 'require|integer', '请输入热搜词类型|热搜词类型必须为整数']
            ]);

            if (true !== $result) {

                $this->error($result);
            }

            if ($data['type'] == 2){
                $res = $keywordsModel->where(['title'=>$data['title']])->find();
                if ($res) $this->error('已添加过该游戏');
            }

            $data['create_time'] = NOW_TIMESTAMP;

            $keywordsModel->insert($data);

            $this->success('热搜词新增成功', 'keywordsList');
        }

        $game_list = Db::name('cy_game')->field('id,name')->where(['is_show'=>1,'cooperation_status'=>['in',[1,2]]])->order('name asc')->select();
        $this->assign('game_list', $game_list);
        return $this->fetch('keywords_new');
    }


    /**
     * 热搜词编辑
     */
    public function keywordsEdit($id)
    {
        if(empty($id)){

            $this->error('热搜词ID不能为空');
        }

        $keywordsModel = Db::name('cy_keywords');

        $info = $keywordsModel->where(['id' => $id])->find();

        if(empty($info)){
            $this->error('热搜词不存在');
        }

        if (request()->isPost()) {

            $data['title']= input('post.title');
            $data['type'] = input('post.type');
            $gameid = input('post.gameid');
            if ($data['type'] == 2){
                $data['title'] = $gameid;
            }

            $result = $this->validate($data, [
                ['title', 'require|max:50', '请输入热搜词名称|热搜词名称不能超过50个字符'],
                ['type', 'require|integer', '请输入热搜词类型|热搜词类型必须为整数']
            ]);

            if (true !== $result) {

                $this->error($result);
            }

            if ($data['type'] == 2){
                $res = $keywordsModel->where(['title'=>$data['title'],'id'=>['neq',$id]])->find();
                if ($res) $this->error('已添加过该游戏');
            }

            $update = $keywordsModel->where(['id'=>$id])->update($data);

            $this->success('热搜词编辑成功', 'keywordsList');
        }

        $this->assign('info', $info);
        $game_list = Db::name('cy_game')->field('id,name')->where(['is_show'=>1,'cooperation_status'=>['in',[1,2]]])->order('name asc')->select();
        $this->assign('game_list', $game_list);
        return $this->fetch('keywords_edit');
    }

    /**
     * 热搜词删除
     */
    public function keywordsDel($id)
    {
        if(empty($id)){

            $this->error('热搜词ID不能为空');
        }

        $keywordsModel = Db::name('cy_keywords');

        if($keywordsModel->delete(['id'=>$id])){

            $this->success('热搜词删除成功', 'keywordsList');
        }
        else{
            $this->error('热搜词删除失败');
        }
    }

    /**
     * 广告列表
     */
    public function adList()
    {
        $where = [];

        $order = input('order','create_time desc','trim');

        if($order=='sort_desc'){

            $order = 'sort desc';
        }
        elseif($order=='sort_asc'){

            $order = 'sort asc';
        }
        else{
            $order = 'create_time desc';
        }

        //广告标题
        if (input('request.title') != '') {
            $where['title'] = ['like', '%' . input('request.title', '', 'trim') . '%'];
        }

        //投放位置
        if (input('request.type') != '') {
            $where['type'] = input('request.type');
        }

        //查询参数
        $param = input('get.');

        $adModel = Db::name('cy_ad');

        $list = $adModel->where($where)
                    ->order($order)
                    ->paginate(10, false, array('query' => $param));

        $this->assign('list', $list);
        $this->assign('page', $list->render());

        return $this->fetch('ad_list');
    }

    /**
     * 广告新增
     */
    public function adCreate()
    {
        $adModel = Db::name('cy_ad');

        if (request()->isPost()) {

            $data = ['title'=>input('post.title','','trim'),
                    'type'  =>input('post.type'),
                    'url'   =>input('post.url'),
                    'sort'  =>input('post.sort')
            ];

            $result = $this->validate($data, [
                ['title', 'require|max:50', '请输入广告标题|广告标题不能超过50个字符'],
                ['type', 'require|integer', '请选择投放位置|投放位置必须为整数'],
                ['url', 'require|max:160', '请输入跳转地址|跳转地址不能超过160个字符'],
                ['sort', 'integer', '排序必须为整数']
            ]);

            if (true !== $result) {

                $this->error($result);
            }
            elseif (!request()->file('image')) {
                $this->error('广告图片不能为空! ');
            }

            $fileUpload = new FileUpload();

            // 1.校验
            $file_path = $fileUpload->set('allowExt', 'jpg,png')->set('maxsize', 5242880)->set('dir','image/aoyou/')->upload(request()->file('image'));
            if ($file_path) {
                $data['image'] = $file_path;
            } else {
                $this->error('广告图片' . $fileUpload->getError());
            }

            $data['create_time'] = NOW_TIMESTAMP;

            if($adModel->insert($data)){

                $this->success('广告新增成功', 'adList');
            }
            else{
                $this->error('广告新增失败');
            }
        }

        return $this->fetch('ad_new');
    }


    /**
     * 广告编辑
     */
    public function adEdit($id)
    {
        if(empty($id)){

            $this->error('广告ID不能为空');
        }

        $adModel = Db::name('cy_ad');

        $info = $adModel->where(['id' => $id])->find();

        if(empty($info)){
            $this->error('广告不存在');
        }

        if (request()->isPost()) {

            $data = ['title'=>input('post.title','','trim'),
                    'type'  =>input('post.type'),
                    'url'   =>input('post.url'),
                    'sort'  =>input('post.sort')
            ];

            $result = $this->validate($data, [
                ['title', 'require|max:50', '请输入广告标题|广告标题不能超过50个字符'],
                ['type', 'require|integer', '请选择投放位置|投放位置必须为整数'],
                ['url', 'require|max:160', '请输入跳转地址|跳转地址不能超过160个字符'],
                ['sort', 'integer', '排序必须为整数']
            ]);

            if (true !== $result) {

                $this->error($result);
            }

            $fileUpload = new FileUpload();

            $file_path = $fileUpload->set('allowExt', 'jpg,png')->set('maxsize', 5242880)->set('dir','image/aoyou/')->upload(request()->file('image'));

            //广告图片上传出错时
            if(!empty(request()->file('image')) && empty($file_path))
            {
                $this->error('广告图片'.$fileUpload->getError());
            }

            if(!empty($file_path)){
                $data['image'] = $file_path;
            }

            $adModel->where(['id'=>$id])->update($data);

            $this->success('广告编辑成功', 'adList');
        }

        $this->assign('info', $info);

        return $this->fetch('ad_edit');
    }

    /**
     * 热搜词删除
     */
    public function adDel($id)
    {
        if(empty($id)){

            $this->error('广告ID不能为空');
        }

        $adModel = Db::name('cy_ad');

        if($adModel->delete(['id'=>$id])){

            $this->success('广告删除成功', 'adList');
        }
        else{
            $this->error('广告删除失败');
        }
    }

    /**
     * 光环助手游戏列表
     */
    public function gameAidList()
    {
        $where = [];

        //游戏名称
        if (input('request.gameid')) {
            $where['a.gameid'] = input('request.gameid');
        }

        //游戏类型
        if (input('request.type')) {
            $where['a.type'] = input('request.type');
        }

        //查询参数
        $param = input('get.');

        $gameaidModel = Db::name('cy_gameaid');

        $list = $gameaidModel->table('cy_gameaid a,cy_game g')
                    ->field('g.name,a.id,a.type,a.url,a.create_time')
                    ->where('a.gameid=g.id')->where($where)
                    ->order('a.create_time desc')
                    ->paginate(10, false, array('query' => $param));

        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);

        return $this->fetch('gameaid_list');
    }

    /**
     * 光环助手游戏新增
     */
    public function gameAidCreate()
    {
        $gameaidModel = Db::name('cy_gameaid');

        if (request()->isPost()) {

            $data = ['gameid'   =>input('post.gameid'),
                    'type'      =>input('post.type'),
                    'url'       =>input('post.url','','trim'),
                    'content'   =>input('post.content')
            ];

            $result = $this->validate($data, [
                ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['type', 'require|integer', '请选择类型|类型必须为整数'],
                ['url', 'url|max:160', '请输入跳转地址|跳转地址格式不正确|跳转地址不能超过250个字符'],
            ]);

            if (true !== $result) {

                $this->error($result);
            }
            elseif($gameaidModel->where(['gameid'=>$data['gameid']])->find())
            {
                $this->error('该游戏已经存在不能重复添加');
            }

            $data['create_time'] = NOW_TIMESTAMP;

            if($gameaidModel->insert($data)){

                $this->success('新增成功', 'gameAidList');
            }
            else{
                $this->error('新增失败');
            }
        }


        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');

        $this->assign('game_list', $gameList);

        return $this->fetch('gameaid_new');
    }

    /**
     * 光环助手游戏编辑
     */
    public function gameAidEdit($id)
    {
        if(empty($id)){

            $this->error('ID不能为空');
        }

        $gameaidModel = Db::name('cy_gameaid');

        $info = $gameaidModel->where(['id' => $id])->find();

        if(empty($info)){
            $this->error('光环助手游戏不存在');
        }

        if (request()->isPost()) {

            $data = ['gameid'   =>input('post.gameid'),
                    'type'      =>input('post.type'),
                    'url'       =>input('post.url','','trim'),
                    'content'   =>input('post.content')
            ];

            $result = $this->validate($data, [
                ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['type', 'require|integer', '请选择类型|类型必须为整数'],
                ['url', 'url|max:160', '跳转地址格式不正确|跳转地址不能超过250个字符'],
            ]);

            if (true !== $result) {

                $this->error($result);
            }
            elseif($gameaidModel->where(['gameid'=>$data['gameid'],'id'=>['<>',$id]])->find())
            {
                $this->error('该游戏已经存在不能重复');
            }

            $gameaidModel->where(['id'=>$id])->update($data);

            $this->success('编辑成功', 'gameAidList');
        }

        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');

        $this->assign('game_list', $gameList);
        $this->assign('info', $info);

        return $this->fetch('gameaid_edit');
    }

    /**
     * 光环助手游戏删除
     */
    public function gameAidDel($id)
    {
        if(empty($id)){

            $this->error('ID不能为空');
        }

        $gameaidModel       = Db::name('cy_gameaid');
        $sdkGameListModel   = new SdkGameList;
        $subChannel         = new SubChannel;

        $gameaidInfo = $gameaidModel->where(['id'=>$id])->find();

        if(!$gameaidInfo){

            $this->error('光环游戏助手记录不存在');
        }

        //分包记录
        $sdkGameList = $sdkGameListModel->field('gameid,filename,id')->where(['gameid'=>$gameaidInfo['gameid'],'package_type'=>1])->select();

        //删除光环记录
        $gameaidModel->delete(['id'=>$id]);

        if(!empty($sdkGameList)){

            foreach($sdkGameList as $value){

                //删除包体文件
                $subChannel->removePackageFile($gameaidInfo['gameid'], $value['filename']);

                //删除分包记录
                $sdkGameListModel->where(['id'=>$value['id'],'gameid'=>$gameaidInfo['gameid']])->delete();
            }
        }

        $this->success('删除成功', 'gameAidList');
    }


    /**
     * 开服开测订阅管理
     */
    public function subscribeList()
    {
        $where = [];

        $start_time = input('request.start_time');

        //游戏名称
        if (input('request.username')) {
            $where['sub.username'] = input('request.username');
        }

        //游戏名称
        if (input('request.gameid')) {
            $where['ser.gameid'] = input('request.gameid');
        }

        //通知类型
        if (input('request.type')) {
            $where['sub.type'] = input('request.type');
        }

        //发送状态
        if (input('request.is_send')!='') {
            $where['sub.is_send'] = input('request.is_send');
        }

        //短信状态
        if (input('request.is_del')!='') {
            $where['sub.is_del'] = input('request.is_del');
        }

        //开始时间和结束时间不为空时
        if ($start_time != '' && input('request.end_time') != '') {
            $where['sub.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime(input('request.end_time').' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time!= '') {
            $where['sub.create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif (input('request.end_time') != '') {
            $where['sub.create_time'] = ['<=', strtotime(input('request.end_time').' 23:59:59')];
        }

   //     $where['sub.is_del'] = 0;

        //查询参数
        $param = input('get.');

        $list = Db::table('cy_subscribelog sub,cy_serverinfo ser,cy_game g')
                    ->field('g.name,sub.id,sub.userid,sub.username,sub.mobile,sub.type,sub.is_del,sub.is_send,sub.send_time,sub.create_time,ser.sername,ser.sertime,ser.serstatus,ser.gameid')
                    ->where('sub.related_id=ser.id and ser.gameid=g.id')->where($where)
                    ->order('sub.create_time desc')
                    ->paginate(10, false, array('query' => $param));

        $tmpGameList = model('Common/Game')->getAllByCondition('id,name');
      	$gameList = array();
		foreach ($tmpGameList as $game) {
              $gameList[ $game['id']] = $game;
        }

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('game_list', $gameList);

        return $this->fetch('subscribe_list');
    }

    /**
     * 开服开测订阅管理--查看玩家账号
     */
    public function showSubscribeName()
    {
        $id     = input('id');
        $gameid = input('gameid');

        if(empty($id)){

            $this->error('ID不能为空');
        }

        $username= Db::table('cy_subscribelog')->where(['id'=>$id])->value('username');

        if(!empty($username)){

            $game_name = model('Common/Game')->where(['id'=>$gameid])->value('name');
            $this->insertLog($this->current_node,'查看账号：'.$username.'，关联游戏：'.$game_name,141);

            $this->result($username,1);
        }
        else{
            $this->error('订阅信息不存在');
        }
    }

    /**
     * 开服开测订阅管理--查看玩家手机号
     */
    public function showSubscribeMobile()
    {
        $id     = input('id');
        $gameid = input('gameid');

        if(empty($id)){

            $this->error('ID不能为空');
        }

        $username= Db::table('cy_subscribelog')->where(['id'=>$id])->value('mobile');

        if(!empty($username)){

            $game_name = model('Common/Game')->where(['id'=>$gameid])->value('name');
            $this->insertLog($this->current_node,'查看手机号：'.$username.'，关联游戏：'.$game_name,142);

            $this->result($username,1);
        }
        else{
            $this->error('订阅信息不存在');
        }
    }
}
