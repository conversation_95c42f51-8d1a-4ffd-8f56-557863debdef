<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Exception;
use app\common\model\FkRosterLists as FkRosterListsModel;

class FkRosterLists extends Controller
{
    /**
     * 名单列表页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $page = input('get.page', 1);
            $limit = input('get.limit', 20);
            $where = [];
            
            // 搜索条件
            $listType = input('get.list_type', '');
            if ($listType) {
                $where['list_type'] = $listType;
            }
            
            $objectType = input('get.object_type', '');
            if ($objectType) {
                $where['object_type'] = $objectType;
            }
            
            $objectValue = input('get.object_value', '');
            if ($objectValue) {
                $where['object_value'] = ['like', "%{$objectValue}%"];
            }
            
            $count = Db::name('fk_roster_lists')->where($where)->count();
            $data = Db::name('fk_roster_lists')
                ->where($where)
                ->page($page, $limit)
                ->order('roster_lists_id desc')
                ->select();
                
            return json([
                'code' => 200,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 添加名单
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = [
                'list_type' => input('post.list_type'),
                'object_type' => input('post.object_type'),
                'object_value' => input('post.object_value'),
                'remark' => input('post.remark', '')
            ];
            
            // 验证
            if (empty($data['list_type']) || empty($data['object_type']) || empty($data['object_value'])) {
                return json(['code' => 1, 'msg' => '必填项不能为空']);
            }
            
            // 检查是否已存在
            $info = FkRosterListsModel::get(['list_type' => $data['list_type'], 'object_type' => $data['object_type'], 'object_value' => $data['object_value']]);
            if ($info) {
                return json(['code' => -100, 'msg' => '该名单记录已存在']);
            }
            
            try {
                $model = new FkRosterListsModel($data);
                $model->save();

                return json(['code' => 200, 'msg' => '添加成功']);
            } catch (\Exception $e) {
                return json(['code' => -100, 'msg' => '添加失败：' . $e->getMessage()]);
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 删除名单
     */
    public function delete()
    {
        $id = input('post.id');
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        try {
            $info = FkRosterListsModel::get(['roster_lists_id' => $id]);
            if($info){
                $info->delete();
                return json(['code' => 200, 'msg' => '删除成功']);
            }
            return json(['code' => -100, 'msg' => '删除失败：数据有误！']);

        } catch (\Exception $e) {
            return json(['code' => -110, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量导入
     */
    public function batchImport()
    {
        if ($this->request->isPost()) {
            $list_type = $this->request->param('list_type');
            $object_type = $this->request->param('object_type');
            $reason = $this->request->param('reason', '批量导入');
            $object_values = $this->request->param('object_values/a', []);
            
            if (empty($list_type) || empty($object_type)) {
                return json(['code' => -100, 'msg' => '名单类型和对象类型不能为空']);
            }
            
            if (empty($object_values)) {
                return json(['code' => -100, 'msg' => '对象值列表不能为空']);
            }
            
            try {
                $insertData = [];
                
                foreach ($object_values as $value) {
                    $value = trim($value);
                    if (empty($value)) {
                        continue;
                    }
                    
                    // 检查是否已存在
                    $exists = Db::name('fk_roster_lists')->where([
                        'list_type' => $list_type,
                        'object_type' => $object_type,
                        'object_value' => $value
                    ])->find();
                    
                    if (!$exists) {
                        $insertData[] = [
                            'list_type' => $list_type,
                            'object_type' => $object_type,
                            'object_value' => $value,
                            'remark' => $reason
                        ];
                    }
                }
                
                if (!empty($insertData)) {
                    Db::name('fk_roster_lists')->insertAll($insertData);
                    $count = count($insertData);
                    return json(['code' => 200, 'msg' => "成功导入{$count}条记录"]);
                } else {
                    return json(['code' => 200, 'msg' => '所有记录已存在，无需重复导入']);
                }
            } catch (\Exception $e) {
                return json(['code' => -100, 'msg' => '导入失败：' . $e->getMessage()]);
            }
        }
    }

    /**
     * 改为白名单
     * 1. 异常对象删除，后续不再监控此对象。
     */
    public function checkWhite()
    {
        $id = input('post.id');
        if (!$id) {
            return json(['code' => -100, 'msg' => '参数错误']);
        }

        // 开启事务
        Db::startTrans();
        try {

            // 查询当前记录
            $info = FkRosterListsModel::get(['roster_lists_id' => $id]);
            if (!$info) {
                throw new Exception(-100, '记录不存在');
            }

            // 检查是否已经是白名单
            if ($info['list_type'] == 'white') {
                throw new Exception(-100, '该记录已经是白名单');
            }

            // 1. 修改当前数据的黑名单为白名单
            $info->isUpdate(true)->save(['list_type' => 'white', 'remark' => '由黑名单改为白名单', 'update_at' => date("Y-m-d H:i:s", time())]);

            // 2. 删除关联的异常对象数据
            Db::name('fk_exception_objects')
                ->where('exception_type', $info['object_type'])
                ->where('exception_value', $info['object_value'])
                ->delete();

            // 提交事务
            Db::commit();

            return json(['code' => 200, 'msg' => '改为白名单成功']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['code' => -100, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }
}
