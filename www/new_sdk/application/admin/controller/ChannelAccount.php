<?php
/**
 * 前台注册用户管理控制器
 */

namespace app\admin\controller;

use app\common\library\MakeReportGo;
use think\Db;
use app\common\logic\Member as MemberService;
//use app\common\library\MakeReport;
use app\common\library\FileUpload;
use think\Exception;

class ChannelAccount extends Admin
{
    protected $where;
    protected $start_time;
    protected $end_time;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->where        = [];
    }

    /**
     * 公会账户余额
     */
    public function index()
    {
        $this->indexWhere();

		if (request()->isAjax() && input('download')) {


			$sql = model("Channel")->field('id,name,level,status,amount_coin,amount,js_amount,cps_settle_period,mcps_settle_period')
					->where($this->where)
					->order('id desc')
					->fetchSql(true)->select();
		//	echo $sql;
            if((new MakeReportGo())->addTask('admin.channelAccountIndex',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
		}

        $param = input('get.');
        $list  = model("Channel")->field('id,name,level,status,amount,amount_coin,js_amount,cps_settle_period,mcps_settle_period')
					->where($this->where)
					->order('id desc')
					->paginate(10, false, array('query' => $param));

        $data     = $list->toArray()['data'];

        $this->assign('list', $data);
        $this->assign('total', $list->total());     //总条数
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        $this->assign('page', $list->render());

        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        $channelWhere = [];
        if( $business != -1){
            if($business && $business != -2){
                $channelWhere['id'] = ['in',$business];
            }else{
                $channelWhere['id'] = -1;
            }
        }

        $uniom = model('Channel')->where(['level'=>0])->where($channelWhere)->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表

        return $this->fetch();
    }

    // 用户信息汇总查询条件
    public function indexWhere()
    {
        $where = array();
        $userid           = input('request.userid',0,'intval');
        $username         = input('request.username');
        $status           = input('request.status');
		$bplus_channel_id = input('request.bplus_channel_id',0,'intval');

		$where['level']   = 1;
        //用户ID
        if ($userid) {
            $where['id'] = $userid;
        }
        //用户名
        if ($username != '') {
            $where['name'] = $username;
        }
        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        if( $business != -1){
            if($bplus_channel_id){
                if(!in_array($bplus_channel_id,$business)){
                    $where['parent_id'] = -1;
                }
            }else{
                if($business && $business != -2){
                    $where['parent_id']  = implode(',',$business);
                }else{
                    $where['parent_id']  = -1;
                }
            }
        }else{
            //商务账号
            if ($bplus_channel_id) {
                $where['parent_id'] = $bplus_channel_id;
            }
        }



        //账户状态
        if ($status != '') {
            $where['status'] = $status;
        }

        $this->where      = $where;

    }

    /**
     * 渠道账户明细列表
     */
    public function detList()
    {
        $where = $this->_getDetListCondition();
		//var_dump($where);

		if (request()->isAjax() && input('download')) {


			$sql = model("ChannelAccountDet")->alias('d')
					->join('nw_channel channel', 'd.channel_id = channel.id','left')
					->field("d.id,d.channel_id,d.channel_name,d.change_amount,d.change_amount,d.account_type,d.type,d.out_orderid,d.create_time,channel.level,channel.status,channel.amount,channel.js_amount")
					->where($where)
					->order('d.create_time desc')
					->fetchSql(true)->select();
		//	echo $sql;

            if((new MakeReportGo())->addTask('admin.channelAccountDetList',$sql,session_id())){
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
		}

        $list = model("ChannelAccountDet")->alias('d')
			->join('nw_channel channel', 'd.channel_id = channel.id','left')
            ->field("d.id,d.channel_id,d.channel_name,d.change_amount,d.change_amount,d.account_type,d.type,d.out_orderid,d.create_time,channel.level,channel.status,channel.amount,channel.js_amount")
            ->where($where)
            ->order('d.create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);

        $data = $list->toArray()['data'];
		$this->assign('list', $data);

		$showTotal = $incomeAmt = $payoutAmt = 0;
		if((isset($where['d.channel_id'])&&$where['d.channel_id'] || isset($where['d.channel_name'])&&$where['d.channel_name']) && (isset($where['d.account_type'])&&$where['d.account_type'])){
			$showTotal = 1;
			$incomeAmt = model("ChannelAccountDet")->alias('d')->where($where)->where(['change_amount'=>['egt',0]])->sum('change_amount');
			$payoutAmt = model("ChannelAccountDet")->alias('d')->where($where)->where(['change_amount'=>['lt',0]])->sum('change_amount');
		}
        $this->assign('showTotal',$showTotal);
        $this->assign('incomeAmt',$incomeAmt);
        $this->assign('payoutAmt',$payoutAmt);
        $this->assign('total', $list->total());     //总条数
        $this->assign('page', $list->render());
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);

        return $this->fetch('det_list');
    }

    /**
     * 账户变动明细  条件查询
     * @return array
     */
    protected function _getDetListCondition()
    {
        $start_time       = input('request.start_time', '', 'trim');
        $end_time         = input('request.end_time', '', 'trim');
        $channel_name     = input('request.channel_name', '', 'trim');
		$channel_id		  = input('request.channel_id', 0, 'intval');
		$type			  = input('request.type', '', 'trim');
		$account_type	  = input('request.account_type', '', 'trim');
		$out_orderid	  = input('request.out_orderid', '', 'trim');

		$where = array();
        // 获取查询日期
        if (!empty($start_time) || !empty($end_time)){
            $where['d.create_time'] = $this->getTimeCondition($start_time,$end_time,false);
        }
        //渠道ID
        if ($channel_id) {
            $where['d.channel_id'] = $channel_id;
        }
        //渠道名称
        if ($channel_name != '') {
            $where['d.channel_name'] = $channel_name;
        }
        //账户类型
        if ($account_type != '') {
            $where['d.account_type'] =  $account_type;
        }
        //类型
        if ($type != '') {
            $where['d.type'] =  $type;
        }
	    //游戏ID
        if ($out_orderid != '') {
            $where['d.out_orderid'] = $out_orderid;
        }
        return $where;
    }

    /**
     * 获取日期查询条件
     * @param  int  $start_time  开始时间
     * @param  int  $end_time    结束时间
     * @param  bool  $isdefault    是否默认今天日期
     * @return
     */
    private function getTimeCondition($start_time,$end_time,$isdefault = true)
    {
        $this->start_time = $start_time;
        $this->end_time   = $end_time;
        $time = [];
        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $time = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $time = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $time = ['<=', strtotime($end_time . ' 23:59:59')];
        } else {

            if($isdefault){
                $this->start_time = $this->end_time = date('Y-m-d', time());

                $time = [
                    ['>=', strtotime($this->start_time)],
                    ['<=', strtotime($this->end_time . ' 23:59:59')],
                ];
            }
        }

        return $time;
    }
}
