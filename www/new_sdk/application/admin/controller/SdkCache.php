<?php
namespace app\admin\controller;

use think\Cache;
/**
 * 后台index控制器
 * <AUTHOR>
 *
 */
class SdkCache extends Admin
{
    /**
     * 初始化操作
     */
    protected function _initialize()
    {
        parent::_initialize();
    }
    
    /**
     * 清楚redis缓存
     */
    public function clearRedisCache()
    {
        $redis = Cache::store('default');
        
        // 获取缓存对象句柄
        $handler = $redis->handler();
        
        $del_cache_key = '*clearable_*';
 
        $keys = $handler->keys($del_cache_key);
        
        if(!empty($keys)){
            $result = $handler->del($keys);
            
            if (empty($result)){
                $this->error('缓存删除失败');
            }
        }
        
        $this->success('缓存删除成功');
    }
}
