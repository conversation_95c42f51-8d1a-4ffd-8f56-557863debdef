<?php

namespace app\admin\controller;

use app\common\model\ComplexChannelModel;
use app\common\model\ComplexRoleModel;
use app\common\model\ComplexServer;
use think\Db;
use think\Request;

class Complex extends Admin
{
    protected $where = [];
    public function getWhere()
    {
        $where = [];
        $start_time = input('request.start_time');
        $end_time = input('request.end_time');
        $complex_id = input('request.complex_id');
        $game_id = input('request.game_id');
        $server_name = input('request.server_name', '');
        $role_name = input('request.role_name', '');
        $mg_username = input('request.mg_username', '');
        
        //开始时间和结束时间不为空时
        if ($start_time != '' && $end_time != '') {
            $where['create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start_time != '') {
            $where['create_time'] = ['>=', strtotime($start_time)];
        } //结束时间不为空时
        elseif ($end_time != '') {
            $where['create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }
        
        // 渠道
        if ($complex_id != '') {
            $where['complex_id'] = $complex_id;
        }
        
        // 游戏
        if ($game_id != '') {
            $where['game_id'] = $game_id;
        }
        
        // 角色名
        if ($role_name != '') {
            $where['role_name'] = ['like', "%".$role_name."%"];
        }

        // ## 特殊处理 - 不同方法参数名区分
        $action = Request::instance()->action();
        if($action == 'rolelist'){
            $where = addPrefixToKeys($where, 'ncr.');
            // 区服名
            if ($server_name != '') {
                $where['server_id'] = $server_name;
            }
        }else{
            // 区服名
            if ($server_name != '') {
                $where['id'] = $server_name;
            }
        }
        
        // 账号
        if ($mg_username != '') {
            $where['ncm.mg_username'] = ['like', "%".$mg_username."%"];
        }
        
        $this->where      = $where;
        $this->start_time = $start_time;
        $this->end_time   = $end_time;
    }
    
    // 区服管理
    public function serverList(){
        $this->getWhere();
        $param = input('get.');
        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');
        $channelList = (new ComplexChannelModel())->getAllByCondition('id,name',['flag'=>4],'name asc');
        $serverList = Db::table('nw_complex_server')->field('id,server_name as name')->select();
        $gameByIdList = array_column($gameList, 'name', 'id');
        $channelByIdList = array_column($channelList, 'name', 'id');
        $serverByIdList = array_column($serverList, 'name', 'id');

        $list = ComplexServer::where($this->where)->order('id desc')
            ->paginate(20, false, array('query' => $param));
        
        $this->assign('list', $list);
        $this->assign('total', $list->total());
        $this->assign('page', $list->render());
        
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        
        $this->assign('game_list', $gameList);
        $this->assign('channel_list', $channelList);
        $this->assign('serverList', $serverList);
        
        $this->assign('game_by_id_list', $gameByIdList);
        $this->assign('channel_by_id_list', $channelByIdList);
        $this->assign('serverByIdList', $serverByIdList);
        return $this->fetch();
    }
    
    // 角色管理
    public function roleList(){
        $this->getWhere();
        $gameList = model('Common/Game')->getAllByCondition('id,name',[],'','self');
        $complexList = (new ComplexChannelModel())->getAllByCondition('id,name',['flag'=>4],'name asc');
        $serverList = Db::table('nw_complex_server')->field('id,server_name as name')->select();
        $gameByIdList = array_column($gameList, 'name', 'id');
        $complexByIdList = array_column($complexList, 'name', 'id');
        $serverByIdList = array_column($serverList, 'name', 'id');
        
        $param = input('get.');
        $list = ComplexRoleModel::alias('ncr')->field('ncr.*, ncm.mg_username')
            ->join('nw_complex_members ncm','ncr.member_id=ncm.id', 'left')
            ->where($this->where)->order('ncr.id desc')
            ->paginate(20, false, array('query' => $param));
        
        $roleList = $list->toArray()['data'];
        foreach ($roleList as $k => &$v) {
            $v['server_name'] = $serverByIdList[$v['server_id']] ?? '';
            $v['complex_name'] = $complexByIdList[$v['complex_id']] ?? '';
        }
        
        $this->assign('list', $roleList);
        $this->assign('total', $list->total());
        $this->assign('page', $list->render());
        
        $this->assign('start_time', $this->start_time);
        $this->assign('end_time', $this->end_time);
        
        // ## 数据列表
        $this->assign('gameList', $gameList);
        $this->assign('complexList', $complexList);
        $this->assign('serverList', $serverList);
        
        // ## 格式化数据列表
        $this->assign('gameByIdList', $gameByIdList);
        $this->assign('complexByIdList', $complexByIdList);
        $this->assign('serverByIdList', $serverByIdList);
        return $this->fetch();
    }
}