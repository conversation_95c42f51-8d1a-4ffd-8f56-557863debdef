<?php
/**
 * Created by PhpStorm.
 * User: edison
 * Date: 2018/3/26
 * Time: 上午11:50
 */

namespace app\admin\controller;

use think\Config;
use think\Db;
use tree\Tree;

class User extends Admin
{

    public function _initialize() {
        parent::_initialize();

        $this->adminModel = Db::name('cy_admin');
        $this->adminRoleModel = Db::name('nw_admin_role');
        $this->adminRoleUserModel = Db::name('nw_admin_role_user');
    }

    public function index()
    {

        $role_id = 0;

        $data = $this->adminRoleModel->where('status',1)->field('id,parent_id,name')->order('id')->select();

        $tree = new Tree();

        $tree->init($data);

        $where = [];

        //非超级管理员时
        if(!in_array(session('ADMIN_ID') ,[1,19])){
            $role_id = $this->adminRoleUserModel->where(['user_id'=>session('ADMIN_ID')])->value('role_id');
            $childrenIds = $tree->getChildrenIds($role_id,false);
            $where['ru.role_id'] = ['in',$childrenIds];
        }

        $childrenlist = $tree->getTreeList($tree->getTreeArrayO($role_id));

        /**搜索条件**/
        $username = input('username');
        $mobile = input('mobile');
        $status = input('status');
        $role_id = input('role_id');
        $start_time = input('start_time');
        $end_time = input('end_time');

        if ($username) {
            $where['a.username'] = ['like', "%$username%"];
        }

        if ($mobile) {
            $where['a.mobile'] = $mobile;
        }

        if ($role_id) {
            $where['ru.role_id'] = $role_id;
        }
        if ($status != '') {
            $where['a.status'] = $status;
        }
        if ($start_time &&$end_time ) {
            $where['a.create_time'] = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        }elseif($start_time){
            $where['a.create_time'] = ['>=', strtotime($start_time)];
        }elseif($end_time) {
            $where['a.create_time'] = ['<=', strtotime($end_time . ' 23:59:59')];
        }


        $where['a.type']= config('ADMIN');
        $where['a.id']  = ['<>', 1];

        $param = input('request.');		//分页带条件

        $users = $this->adminModel->alias('a')
                    ->join('nw_admin_role_user ru','a.id=ru.user_id','left')
                    ->join('nw_admin_role ro','ru.role_id=ro.id','left')
                    ->field('a.id,a.username,a.mobile,a.status,ro.name,a.create_time')
                    ->where($where)
                    ->order("a.id DESC")
                    ->paginate(10,false,array('query' => $param));

        // 获取分页显示
        $page = $users->render();

        $this->assign("page", $page);
        $this->assign("roles", $childrenlist);
        $this->assign("users", $users);
        return $this->fetch();
    }

    public function add()
    {
        $role_id = 0;

        //非超级管理员时
        if(session('ADMIN_ID') != 1){
            $role_id = $this->adminRoleUserModel->where(['user_id'=>session('ADMIN_ID')])->value('role_id');
        }

        $data = $this->adminRoleModel->where('status',1)->field('id,parent_id,name')->order('id')->select();

        $tree = new Tree();

        $tree->init($data);

        $childrenlist = $tree->getTreeList($tree->getTreeArrayO($role_id));


        $this->assign("roles", $childrenlist);

        return $this->fetch();
    }

    public function addPost()
    {
        if ($this->request->isPost()) {
            if (!empty($_POST['role_id'])) {
                $role_id = $_POST['role_id'];
                unset($_POST['role_id']);
                $result = $this->validate($this->request->param(), 'User');
                if ($result !== true) {
                    $this->error($result);
                } else {
                    $_POST['password']              = mg_password($_POST['password']);
                    $_POST['status']                = 1;
                    $_POST['type']                  = config('ADMIN');
                    $_POST['create_time']			= time();
                    $_POST['password_update_time']  = time();
                    $_POST['username']              = trim($_POST['username']);

                    $userInfo = $this->adminModel->field('id')->where(['username' => $_POST['username']])->find();
                    if ($userInfo) {
                        $this->error("用户名已存在! ");
                    }

                    $channelInfo = model('channel')->where(['name' => $_POST['username']])->find();
                    if ( $channelInfo ) {
                        $this->error("跟渠道用户重名! ");
                    }

                    $result = $this->adminModel->insertGetId($_POST);
                    if ($result !== false) {
                        if (mg_get_current_admin_id() != 1 && $role_id == 1) {
                            $this->error("为了网站的安全，非网站创建者不可创建超级管理员！");
                        }

                        $this->adminRoleUserModel->insert(["role_id" => $role_id, "user_id" => $result]);
                        // 记录日志
                        $LogStr = "新增账号：" .$_POST['username'];
                        $this->insertLog($this->current_node,$LogStr,161);

                        $this->success("添加成功！", url("user/index"));
                    } else {
                        $this->error("添加失败！");
                    }
                }
            } else {
                $this->error("请为此用户指定角色！");
            }

        }
    }

    public function edit()
    {
        $id    = input('id', 0, 'intval');

        $role_id = 0;

        //非超级管理员时
        if(session('ADMIN_ID') != 1){
            $role_id = $this->adminRoleUserModel->where(['user_id'=>session('ADMIN_ID')])->value('role_id');
        }

        $data = $this->adminRoleModel->where('status',1)->field('id,parent_id,name')->order('id')->select();

        $tree = new Tree();

        $tree->init($data);

        $childrenlist = $tree->getTreeList($tree->getTreeArrayO($role_id));

        $this->assign("roles", $childrenlist);


        $role_ids = $this->adminRoleUserModel->where(["user_id" => $id])->value("role_id");
        $this->assign("role_ids", $role_ids);

        $user = $this->adminModel->where(["id" => $id])->find();
        $this->assign('user', $user);
        return $this->fetch();
    }

    public function editPost()
    {
        if ($this->request->isPost()) {
            if (!empty($_POST['role_id'])) {
                if (empty($_POST['password'])) {
                    unset($_POST['password']);
                } else {
                    $result = $this->validate(['password' => $_POST['password']],
                        [
                            ['password', 'require|length:6,30|passwordStrength:2', '请输入新密码|新密码长度为 6 - 30|新密码强度不足,至少要包含字母、数字、符号中的两种'],
                        ]);

                    if (true !== $result) {
                        $this->error($result);
                    }

                    $_POST['password']              = mg_password($_POST['password']);
                    $_POST['password_update_time']  = NOW_TIMESTAMP;
                }
                $role_id = $this->request->param('role_id');
                unset($_POST['role_id']);
                $result = $this->validate($this->request->param(), 'User.edit');

                if ($result !== true) {
                    // 验证失败 输出错误信息
                    $this->error($result);
                } else {
                    $result = $this->adminModel->update($_POST);
                    if ($result !== false) {
                        $uid = $this->request->param('id', 0, 'intval');
                        $this->adminRoleUserModel->where(["user_id" => $uid])->delete();

                        if (mg_get_current_admin_id() != 1 && $role_id == 1) {
                            $this->error("为了网站的安全，非网站创建者不可创建超级管理员！");
                        }
                        $this->adminRoleUserModel->insert(["role_id" => $role_id, "user_id" => $uid]);

                        $roleName = $this->adminRoleModel->where('id',$role_id)->value('name');
                        $username = $this->adminModel->where('id',$uid)->value('username');

                        // 记录日志
                        $LogStr = "编辑账号：".$username."，手机号：".$_POST['mobile']."，角色：" .$roleName;
                        $this->insertLog($this->current_node,$LogStr,161);

                        $this->success("保存成功！",url("user/index"));
                    } else {
                        $this->error("保存失败！");
                    }
                }
            } else {
                $this->error("请为此用户指定角色！");
            }

        }
    }

    public function delete()
    {
        $id = $this->request->param('id', 0, 'intval');
        if ($id == 1) {
            $this->error("最高管理员不能删除！");
        }

        $username = $this->adminModel->where('id',$id)->value('username');

        if ($this->adminModel->delete($id) !== false) {
            $this->adminRoleUserModel->where(["user_id" => $id])->delete();

            // 记录日志
            $LogStr = "删除账号：" . $username;
            $this->insertLog($this->current_node,$LogStr,161);

            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    public function ban()
    {
        $id = $this->request->param('id', 0, 'intval');
        if (!empty($id)) {
            $result = $this->adminModel->where(["id" => $id])->setField('status', '0');
            if ($result !== false) {

                $username = $this->adminModel->where('id',$id)->value('username');
                // 记录日志
                $LogStr = "冻结账号：" . $username;
                $this->insertLog($this->current_node,$LogStr,161);

                $this->success("管理员停用成功！", url("user/index"));
            } else {
                $this->error('管理员停用失败！');
            }
        } else {
            $this->error('数据传入失败！');
        }
    }

    public function cancelBan()
    {
        $id = $this->request->param('id', 0, 'intval');
        if (!empty($id)) {
            $result = $this->adminModel->where(["id" => $id])->setField('status', '1');
            if ($result !== false) {

                $username = $this->adminModel->where('id',$id)->value('username');
                // 记录日志
                $LogStr = "启用账号：" . $username;
                $this->insertLog($this->current_node,$LogStr,161);

                $this->success("管理员启用成功！", url("user/index"));
            } else {
                $this->error('管理员启用失败！');
            }
        } else {
            $this->error('数据传入失败！');
        }
    }

    // 修改密码页
    public function modifyPassword()
    {
        return $this->fetch('modify_password');
    }

    // 修改密码处理
    public function updatePwd()
    {
        $old_password           = input('old_password');
        $new_password           = input('new_password');
        $confirm_new_password   = input('confirm_new_password');

        $data = [
            'new_password'  => $new_password,
            'old_password'  => $old_password
        ];


        $result = $this->validate($data, [
            ['new_password', 'require|length:6,50', '请输入新密码|新密码长度错误'],
            ['old_password', 'require|length:6,50', '请输入旧密码|旧密码长度错误'],
        ]);


        if (true !== $result) {
            $this->error($result);
        }

        $adminInfo = $this->adminModel->where([
                        'id'        => session('ADMIN_ID'),
                        'type'      => 1,
                        'password'  => mg_password($old_password)
                    ])->find();

        if (empty($adminInfo)) {
            $this->error('账号或密码错误!');
        }
        elseif ($new_password != $confirm_new_password) {
            $this->error('两次密码不一致');
        }

        $ret = $this->adminModel->where(["id" => session('ADMIN_ID'),'type'=> 1])->update(['password_update_time'=>NOW_TIMESTAMP,'password'=>mg_password($new_password)]);

        if ($ret) {
            $this->success('密码修改成功!','index/index');
        } else {
            $this->error('密码修改失败!');
        }
    }
}
