<?php
/**
 * 游戏管理控制器
 */

namespace app\admin\controller;

use app\common\library\FileUpload;
use app\common\library\MakeReportGo;
use app\common\model\GameBand;
use app\common\model\Game as GameModel;
use app\common\model\SdkGameList;
use app\common\model\GameInfo;
use Overtrue\Pinyin\Pinyin;
use app\common\model\PromotionShortLink;
use think\Db;
use think\Exception;

class ChannelRatioCoin extends Admin
{

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $tmpChannelList = Db::table('nw_channel')->field('id,name')->where(['level'=>1])->select();
        $channeList = array();
        foreach ($tmpChannelList as $game) {
            $channeList[ $game['id']] = $game;
        }
        $this->channeList  = $channeList;
    }


    /**
     * 审核列表
     * @return [type] [description]
     */
    public function applyCheck()
    {
        $start = $this->request->param('start');
        $end = $this->request->param('end');
        $channelId = input('channel_id');
        $orderid = input('orderid');
        $recharge_type = input('recharge_type');
        $status = input('status');
        $where = ['c.level'=>1];
        if ($channelId) {
            $where['c.id'] = $channelId;
        }
        if ($orderid) {
            $where['r.orderid'] = $orderid;
        }
        if ($recharge_type) {
            $where['r.recharge_type'] = $recharge_type;
        }
        if ($status || $status == "0") {
            $where['r.status'] = $status;
        }
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $where['r.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $where['r.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $where['r.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        }

        if (request()->isAjax()) {
            $sql = model('ChannelRechargeCoin')->alias('r')
                ->join('nw_channel c', 'r.channel_id = c.id','inner')
                ->field('r.*')
                ->order("r.id desc")
                ->where($where)->fetchSql(true)->select();
            $makeReportGo = new MakeReportGo();
            if ($makeReportGo->addTask('channelRatioCoin', $sql, session_id(), [])) {
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            } else {
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }
        $list = model('ChannelRechargeCoin')->alias('r')
            ->join('nw_channel c', 'r.channel_id = c.id','inner')
            ->field('r.*')
            ->order("r.id desc")
            ->where($where)
            ->paginate(10, false, ['query' => input('get.')]);
        if (!empty($list)) {
            foreach ($list as $key => $value) {
                $list[$key]['ratio'] = @round($value['real_amount']/$value['amount']*100,2);
                switch ($value['status']) {
                    case '0':
                        if($value['recharge_type'] == 1){
                            $list[$key]['status'] = '审核中';
                        }else{
                            $list[$key]['status'] = '待支付';
                        }
                        break;
                    case '1':
                        if($value['recharge_type'] == 1){
                            $list[$key]['status'] = '审核通过';
                        }else {
                            $list[$key]['status'] = '充值成功';
                        }
                        break;
                    case '2':
                        if($value['recharge_type'] == 1){
                            $list[$key]['status'] = '审核不通过';
                        }else {
                            $list[$key]['status'] = '充值失败';
                        }
                        break;
                }
            }
        }
        $page = $list->render();
        $this->assign('channel_id', $channelId);
        $this->assign('channel_list', $this->channeList);
        $this->assign("page", $page);
        $this->assign("list", $list);
        $this->assign("start", $start);
        $this->assign("end", $end);
        return $this->fetch();
    }
    /**
     * 审核页面
     * @param  string $value [description]
     * @return [type]        [description]
     */
    public function examine()
    {
        $postParam = $this->request->param();
        if (empty($postParam['id'])) {
            $this->error("参数错误");
        }
        $list = db::name('nw_channel_recharge_coin')
            ->where(['id'=>$postParam['id']])->find();
        if (!empty($list)) {
            $list['ratio'] = @round($list['real_amount']/$list['amount']*100,2);
            $list['create_time'] = date('Y-m-d H:i:s',$list['create_time']);
            switch ($list['status']) {
                case '0':
                    $list['status_type'] = '未审核';
                    break;
                case '1':
                    $list['status_type'] = '审核成功';
                    break;
                case '2':
                    $list['status_type'] = '审核失败';
                    break;
            }

        }
        $this->assign("list", $list);
        return $this->fetch();
    }
    /**
     * 审核
     * @return [type] [description]
     */
    public function doAudit()
    {
        $postParam = $this->request->param();
        $id        = $postParam['id'];	//渠道ID
        $status    = $postParam['status'];	//审核状态:1(审核通过),2(审核不通过)
        $remark    = $postParam['check_remark'];	//审核备注
        if(!$id){
            $this->error('请选择要审核的记录!');
        }
        if($status<>1 && $status<>2){
            $this->error('非法审核状态');
        }

        $auditData = array();
        $auditData['id'] = $id;
        $auditData['status'] = $status;
        $auditData['finish_time'] = NOW_TIMESTAMP;
        $auditData['check_remark'] = $remark;
        $auditData['check_admin_type'] = 1;
        $auditData['check_admin_name'] = SESSION('USERNAME');
        Db::startTrans();
        try {
            //审核通过
            if($status==1){
                $list = db::name('nw_channel_recharge_coin')->where(['id'=>$postParam['id'],'status'=>array('in',[0])])->find();
                if (!$list) {
                    throw new Exception("获取系统数据异常");
                }

                $detData = array();
                $detData['channel_id']      = $list['channel_id'];
                $detData['channel_name']    = $list['channel_name'];
                $detData['change_amount']	= +$list['amount'];
                $detData['account_type']	= 3;   //通用账户
                $detData['type']			= 4;   //充值收入
                $detData['out_orderid']		= $list['orderid'];
                $detData['create_time']     = NOW_TIMESTAMP;
                $insertDetId = model('ChannelAccountDet')->insertGetId($detData);
                if ( !$insertDetId) {
                    throw new Exception("添加账户变动明细失败");
                }
                $updData = array();
                $updData['amount_coin'] = Db::raw("amount_coin+".$list['amount']);
                $updData['update_time'] = time();
                $updFromResult = model('Channel')->where(['id'=>$list['channel_id']])->update($updData);

                if (!$updFromResult) {
                    throw new Exception("账户金额变动失败");
                }
                $auditData['finish_status'] = 1;
                $result = model('ChannelRechargeCoin')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
                if (!$result) {
                    throw new Exception("充值审核失败");
                }
            }
            else if($status==2){  //审核不通过
                $result = model('ChannelRechargeCoin')->where(['id'=>$id,'status'=>array('in',[0])])->update($auditData);
                if (!$result) {
                    throw new Exception("充值审核失败");
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('', 0, '充值审核失败'.$e->getMessage());

        }
        $this->success('审核成功',url('applyCheck'));
    }
}
