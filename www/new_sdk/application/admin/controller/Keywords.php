<?php

namespace app\admin\controller;

use think\Config;
use think\Request;
use think\Db;


class Keywords extends Admin
{
    
    protected $_wechatDb = null;

    protected $model = null;

    protected function _initialize()
    {
        parent::_initialize();

        $this->_wechatDb = Config::get('db_config_wechat.database'); // 微信公众号

        $this->model = model('WxKeywordRule');
    }


    public function index()
    {
    	// 查询条件
    	$where = [];
    	$where['is_del'] = 0;

        // 规则名
        if (input('request.rule_name') != '') {
            $where['rule_name'] =['like', '%' . input('request.rule_name') .'%'];
        }

        if (input('request.type') != '') {
            $where['type'] = input('request.type') == 2 ? 0 : 1 ;
        }

        if (input('request.keywords') != '') {
        	$ids = model('WxKeywords')->getRuleID(input('request.keywords'));
            $where['id'] = ['in',$ids];
        }

        $start          = $this->request->param('cr_start');
        $end            = $this->request->param('cr_end');
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $where['create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $where['create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $where['create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        }

    	// 
    	$infoList  = $this->model
            ->where($where)
            ->order('create_time', 'desc')
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function($item, $key){
	            $item['keywords'] = model('WxKeywords')->getKeywords($item['id']);
	            $item['replays'] = model('WxReplays')->getReplays($item['id']);
	            return $item;
	        });
 

        $this->assign('list', $infoList);
        $this->assign('page', $infoList->render());

    	return $this->fetch();
    }

    public function add()
    {
    	if (request()->isPost()){
    		$post = input('post.');
    		$keywords = $post['keyword'];
    		$is_alls = $post['is_all'];
    		$replays = $post['replays'];

    		$type = $post['type'];

    		$data = [
    			'rule_name'   => input('post.rule_name') ,
                'type'        => input('post.type' , 0 , 'intval') ,
                'status'      => $type ? $post['gift_status'] : $post['status'],
                'remark'      => input('post.remark') ,
                'create_time' => NOW_TIMESTAMP,
                'update_time' => NOW_TIMESTAMP

    		];

    		// 字段验证
    		$verify = $this->verify($post);

    		if ( !$verify['code']) {
    			$this->error( $verify['msg']);
    		}

    		Db::startTrans();

    		try {

    			$ruleId = Db::table($this->_wechatDb . '.nw_wx_keyword_rule')->insertGetId($data);

    			$keywordData = [];

    			if ( $type) {
    				if( model('WxKeywords')->existsKeyword($post['gift_keyword'])){
	    				throw new \Exception('关键词不能重复：' . $post['gift_keyword']);
	    			}
	    			$keywordData[] = [
	    				'keyword' => $post['gift_keyword'],
	    				'rule_id' => $ruleId,
	    				'is_all'  => 1
	    			];
    				
    			}else{
    				// 判断关键词是否重复
		    		foreach ($keywords as $key => $value) {
		    			if ( empty($value)) {
		    				throw new \Exception('关键词不能为空');
		    			}

		    			if( model('WxKeywords')->existsKeyword($value)){
		    				throw new \Exception('关键词不能重复：' . $value);
		    			}

		    			$keywordData[] = [
		    				'keyword' => $value,
		    				'rule_id' => $ruleId,
		    				'is_all'  => $is_alls[$key]
		    			];
		    		}
    			}

	    		$result = Db::table($this->_wechatDb . '.nw_wx_keywords')->insertAll($keywordData);

	    		// 回复
	    		$replaysData = [];

	    		if ( $type) {
	    			$replaysData[] = [
	    				'content' => $post['gift_replays'],
	    				'rule_id' => $ruleId,
	    			];

	    		}else{

		    		foreach ($replays as $k => $v) {
		    			$replaysData[] = [
		    				'content' => $v,
		    				'rule_id' => $ruleId,
		    			];
		    		}
		    	}
	    		$result2 = Db::table($this->_wechatDb . '.nw_wx_replays')->insertAll($replaysData);

	    		// 礼包
	    		if ( $type) {

	    			$codeStr       = $post['gift_code_' . $post['gift_status']];
		            $codeArray     = explode("\n", $codeStr);
		            $codeArray     = array_map('filterAndTrimInput', $codeArray);
		            $codeArray     = array_filter($codeArray);

	    			$libaocodeData = [];

	    			foreach ($codeArray as $key => $value) {
	    				$libaocodeData[] = [
	    					'rule_id' => $ruleId,
	    					'code'    => $value,
	    					'status'  => $post['gift_status'] ? 0 : 2,
	    				];
	    			}

	    			$result3 = Db::table($this->_wechatDb . '.nw_wx_keyword_libaocode')->insertAll($libaocodeData);
	    		}

	    		Db::commit();

    		} catch (\Exception $e) {
                Db::rollback();
                $this->error("添加失败： " . $e->getMessage());
            }

            $this->success('操作成功！', url('index'));



        }

        return $this->fetch();
    }

    public function edit()
    {
    	$id = $this->request->param('id',0,'intval');
        if (!$id){
            $this->error('ID不能为空');
        }

        if (request()->isPost()){
        	$post = input('post.');

    		$type = $post['type'];

    		$data = [
    			'id'          => $id,
    			'rule_name'   => input('post.rule_name') ,
                'status'      => $type ? $post['gift_status'] : $post['status'],
                'remark'      => input('post.remark') ,
                'update_time' => NOW_TIMESTAMP

    		];

    		// 字段验证
    		$verify = $this->verify($post,$id);

    		if ( !$verify['code']) {
    			$this->error( $verify['msg']);
    		}


    		Db::startTrans();

    		try {

    			$result = Db::table($this->_wechatDb . '.nw_wx_keyword_rule')->update($data);

    			$keywordData = [];

    			if ( $type) {
    				if( model('WxKeywords')->existsKeyword($post['gift_keyword'],$id)){
	    				throw new \Exception('关键词不能重复：' . $post['gift_keyword']);
	    			}

	    			$keywordRes = Db::table($this->_wechatDb . '.nw_wx_keywords')
	    				->where('rule_id',$id)
	    				->update(['keyword' => $post['gift_keyword']]);
    				
    			}else{

    				$keywords = $post['keyword'];
    				$is_alls = $post['is_all'];
    		
    				// 判断关键词是否重复
		    		foreach ($keywords as $key => $value) {
		    			if ( empty($value)) {
		    				throw new \Exception('关键词不能为空');
		    			}

		    			if( model('WxKeywords')->existsKeyword($value,$id)){
		    				throw new \Exception('关键词不能重复：' . $value);
		    			}

		    			$keywordData[] = [
		    				'keyword' => $value,
		    				'rule_id' => $id,
		    				'is_all'  => $is_alls[$key]
		    			];
		    		}
		    		$delRes = Db::table($this->_wechatDb . '.nw_wx_keywords')
		    			->where('rule_id',$id)
		    			->delete();
		    		$keywordRes = Db::table($this->_wechatDb . '.nw_wx_keywords')->insertAll($keywordData);

    			}

	    		// 回复
	    		$replaysData = [];

	    		if ( $type) {
	    			$replaysRes = Db::table($this->_wechatDb . '.nw_wx_replays')
	    				->where('rule_id',$id)
	    				->update(['content' => $post['gift_replays']]);
	
	    		}else{

	    			$replays = $post['replays'];

		    		foreach ($replays as $k => $v) {
		    			$replaysData[] = [
		    				'content' => $v,
		    				'rule_id' => $id,
		    			];
		    		}
		    		$delRes = Db::table($this->_wechatDb . '.nw_wx_replays')
		    			->where('rule_id',$id)
		    			->delete();
		    		$replaysRes = Db::table($this->_wechatDb . '.nw_wx_replays')->insertAll($replaysData);
		    	}

	    		// 礼包
	    		if ( $type && $post['gift_status'] == 0) {

	    			$codeRes = Db::table($this->_wechatDb . '.nw_wx_keyword_libaocode')
	    				->where(['rule_id' => $id,'status' => 2])
	    				->update(['code' => $post['gift_code_0']]);
		            
	    		}

	    		if ( $type && $post['gift_status'] == 1) {
	    			$codeStr       = $post['gift_code_add'];
		            $codeArray     = explode("\n", $codeStr);
                    $codeArray     = array_map('filterAndTrimInput', $codeArray);
                    $codeArray     = array_filter($codeArray);

	    			$libaocodeData = [];

	    			foreach ($codeArray as $key => $value) {
	    				$libaocodeData[] = [
	    					'rule_id' => $id,
	    					'code'    => $value,
	    				];
	    			}

	    			$codeRes = Db::table($this->_wechatDb . '.nw_wx_keyword_libaocode')->insertAll($libaocodeData);
	    		}

	    		Db::commit();

    		} catch (\Exception $e) {
                Db::rollback();
                $this->error("编辑失败： " . $e->getMessage());
            }

            $this->success('操作成功！', url('index'));

        }

        $info = $this->model->find($id);

        $keywords = model('WxKeywords')->getKeywordsData($id);

        $replays = model('WxReplays')->getReplaysData($id);

        if ( $info['type'] == 1) {
        	$code = model('WxKeywordLibaocode')->where(['rule_id'=>$id,'status'=> ['in',[0,2]]])->column('code', 'id');
        	$this->assign('code', implode("\r\n", $code));
        }

        $this->assign('keywords',$keywords);
        $this->assign('replays',$replays);
        $this->assign('info',$info);

    	return $this->fetch();
    }

    // 删除
    public function del()
    {
    	$id = $this->request->param('id',0,'intval');
        if (!$id){
            $this->error('ID不能为空');
        }

        $info = Db::table($this->_wechatDb . '.nw_wx_keyword_rule')->find($id);

        if ( empty($info)) {
        	$this->error('ID不存在');
        }

        Db::startTrans();

    	try {
    		// 伪删除
    		Db::table($this->_wechatDb . '.nw_wx_keyword_rule')->where('id',$id)->update(['is_del' => 1]);

    		Db::table($this->_wechatDb . '.nw_wx_keywords')->where('rule_id',$id)->delete();

    		Db::table($this->_wechatDb . '.nw_wx_replays')->where('rule_id',$id)->delete();

    		Db::commit();

    	} catch (\Exception $e) {
            Db::rollback();
            $this->error("删除失败： " . $e->getMessage());
        }

        $this->success('删除成功！', url('index'));


    }

    // 数据验证
    public function verify($data,$id = 0)
    {
    	$result = [
    		'code' => 0,
    		'msg'  => ''
    	];

    	if ( empty($data['rule_name'])) {
    		$result['msg'] = '规则名不能为空';
    		return $result;
    	}

    	if ( $this->model->existsRuleName($data['rule_name'],$id)) {
    		$result['msg'] = '规则名不能重复';
    		return $result;
    	}

    	if ( $data['type'] == 1) {

    		if ( empty($data['gift_keyword'])) {
	    		$result['msg'] = '关键词不能为空';
	    		return $result;
	    	}

	    	if ( empty($data['gift_replays'])) {
	    		$result['msg'] = '回复内容不能为空';
	    		return $result;
	    	}

	    	if ( empty($data['gift_code_' . $data['gift_status']])) {
	    		$result['msg'] = '礼包序列号不能为空';
	    		return $result;
	    	}
    		
    	}else{
    		foreach ($data['replays'] as $k => $v){
    			if ( empty($v)) {
    				$result['msg'] = '回复内容不能为空';
	    			return $result;
    			}

    		}
    	}

    	$result['code'] = 1;

    	return $result;


    }

}