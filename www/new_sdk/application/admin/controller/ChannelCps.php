<?php
/**
 * Created by PhpStorm.
 * User: edison
 * Date: 2018/3/26
 * Time: 上午11:50
 */

namespace app\admin\controller;

use app\common\library\FileUpload;
use app\common\model\CpsTemplate;
use app\common\model\CpsChannel;
use app\common\model\Agent;
use app\common\model\CpsChannelGame;
use think\Db;

class ChannelCps extends Admin
{
    public function _initialize()
    {
        parent::_initialize();
        $this->CPSTemplate = $this->cyModel('cps_template');
        $this->CPSChannel = $this->cyModel('cps_channel');
    }

    /**
     * 加载 模板列表 页面
     */
    public function index()
    {
        $where =[];
        $list = $this->CPSTemplate
            ->where($where)
            ->order("id DESC")
            ->paginate(10, false, ['query' => $where]);

        // 获取分页显示
        $page = $list->render();
        $this->assign("page", $page);
        $this->assign('list', $list);
        return $this->fetch();
    }

    /**
     * 加载模板添加页
     */
    public function templateAdd(){
        if (request()->isPost()) {
            $data = [
                'name'      => input('post.name'),
                'html_code' => input('post.html_code'),
            ];

            $result = $this->validate($data, [
                ['name', 'require', '模板名不能为空'],
                ['html_code', 'require', 'HTML代码不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            } else {
                $where['name']        = $data['name'];
                $where['html_code']   = $data['html_code'];
                $where['create_time'] = time();

                $file = request()->file('img');

                if ($file) {
                    $fl = new FileUpload();
                    // 1.校验
                    $fl->set('allowExt', 'jpg,png');
                    // 2.上传
                    $path = $fl->upload($file);

                    if ($path) {
                        $where['icon_url'] = $path;
                    } else {
                        $this->error('上传失败! ' . $fl->getError());
                    };
                }

                $id = $this->CPSTemplate->insertGetId($where);

                if (empty($id)) {
                    $this->error('添加失败!');
                }
                $this->success('CPS渠道模板添加成功!', 'index');
            }

        }
        return $this->fetch('template_add');
    }

    /**
     * 加载模板编辑页
     */
    public function templateEdit(){

        $id = $this->request->param('id');
        if (empty($id)){
            $this->error("参数错误");
        }
        $list = $this->CPSTemplate->where('id',$id)->find();
        $this->assign('list', $list);
        return $this->fetch('template_edit');
    }

    /**
     * 更新 CPS渠道模板内容
     */
    public function templateEditPost(){
        $data = [
            'id'      => input('post.id'),
            'name'      => input('post.name'),
            'html_code' => input('post.html_code'),
        ];

        $result = $this->validate($data, [
            ['name', 'require', '模板名不能为空'],
            ['html_code', 'require', 'HTML代码不能为空'],
        ]);

        if (true !== $result) {
            $this->error($result);
        } else {
            $where['name']        = $data['name'];
            $where['html_code']   = $data['html_code'];
            $where['update_time'] = time();

            $file = request()->file('img');

            if ($file) {
                $fl = new FileUpload();
                // 1.校验
                $fl->set('allowExt' , 'jpg,png');
                // 2.上传
                $path = $fl->upload($file);

                if ($path) {
                    $where['icon_url'] = $path;
                } else {
                    $this->error('上传失败! ' . $fl->getError());
                };
            }

            $this->CPSTemplate->where(['id' => $data['id']])->update($where);

            $this->success('CPS渠道模板更新成功!' , 'index');
        }
    }

    /**
     * 加载 渠道列表 页面
     */
    public function channelList()
    {
        $where =[];
        $list = $this->CPSChannel
            ->alias("a")
            ->join("nw_channel b", 'a.channel_id = b.id', 'left')
            ->field("a.*, b.name as channel_name")
            ->where($where)
            ->order("a.id asc")
            ->paginate(10, false, ['query' => $where])->each(function($item, $key){
                // 分别统计每个渠道加了几款游戏
                $item['hot_game'] = model('CpsChannelGame')->where(['channel_id' => $item['channel_id'], 'game_type' => '2'])->count();
                $item['new_game'] = model('CpsChannelGame')->where(['channel_id' => $item['channel_id'], 'game_type' => '1'])->count();
                return $item;
            });

        $page = $list->render();
        $this->assign("page", $page);
        $this->assign('list', $list);
        return $this->fetch('channel_list');
    }

    /**
     * 加载 渠道列表添加渠道 页面
     */
    public function channelAdd(){
        $channel_list = model('Department')
            ->where(['flag' => 3])
            ->field('id,name')
            ->select();
        $this->assign('channel_list', $channel_list);
        return $this->fetch('channel_add');
    }

    /**
     * 添加 渠道
     */
    public function channelAddPost(){
        $channel_id = input('post.channel_id');
        if (empty($channel_id)){
            $this->error('请选择渠道');
        }
        $user           = new CpsChannel;

        $res_is = $user->where('channel_id', $channel_id)->find();
        if(!empty($res_is)){
            $this->error('渠道已存在，请勿重复添加');
        }

        $user->channel_id     = $channel_id;
        $user->create_time     = time();
        $user->save();
        if($user->id) {
            $this->success('添加渠道成功', 'channelList');
        }
    }

    /**
     * 删除CPS渠道
     */
    public function channelDelete(){
        $id = $this->request->param('id');

        empty($id) && $this->error('参数错误');

        // 获取该条记录的渠道信息
        $channel_id = $this->CPSChannel->where('id',$id)->column('channel_id');
        $channel_id = $channel_id[0];

        // 判断一下，该渠道是否已经添加了游戏
        $count = model('CpsChannelGame')->where(['channel_id' => $channel_id])->count();

        if($count) {
            $this->error('该渠道已添加游戏，请先删除');
        }

        $res = $this->CPSChannel->delete($id);

        if($res) {
            $this->success('删除成功');
        }else{
            $this->error('删除失败');
        }
    }

    /**
     * 渠道游戏表
     */
    public function channelGameList(){
        $channel_id = $this->request->param('channel_id');
        empty($channel_id) && $this->error('参数错误');

        $model = model('CpsChannelGame');

        $map = ['a.channel_id' => $channel_id];
        $game_type = $this->request->param('game_type');
        !empty($game_type) && $map['a.game_type'] = $game_type;

        $list   = $model
            ->alias("a")
            ->join("nw_channel b", 'a.channel_id = b.id', 'left')
            ->join("cy_game c", 'a.game_id = c.id', 'left')
            ->field("a.*, b.name as channel_name, c.name as ganme_name")
            ->where($map)
            ->paginate(10, false, ['query' => []]);
        $this->assign('list', $list);
        $this->assign('channel_id', $channel_id);
        $this->assign('game_type', $game_type);
        $this->assign('page', $list->render());
        return $this->fetch('channel_game_list');
    }

    /**
     * 渠道游戏添加
     */
    public function channelGameAdd(){
        if(request()->isGet()) {
            $game_list = model('Game')->field('id,name')->select();
            $this->assign('game_list', $game_list);
            $channel_id = input('channel_id');
            $this->assign('channel_id', $channel_id);
            return $this->fetch('channel_game_add');
        }

        if(request()->isPost()) {
            $model = model('CpsChannelGame');
            $runnable = true;
            $msg      = '';
            // 需要同步添加分包表记录
            $data = [
                'channel_id'    => input('post.channel_id') ,
                'game_id'       => input('post.game_id') ,
                'game_type'     => input('post.game_type') ,
                'sort'          => input('post.sort') ,
                'create_time'   => time()
            ];
            // channel_id 和 game_id 是联合主键，不能重复
            $uniquename = $model->get(['channel_id'=>$data['channel_id'], 'game_id'=>$data['game_id']]);
            if(sizeof($uniquename)){
                $runnable  = false;
                $this->error('该游戏已经在该CPS渠道里了，请重新选择~~~');
            }

            $model->data($data);
            $result = $model->save();

            if(empty($result)) {
                $runnable  = false;
                $msg       = '添加游戏失败，游戏已存在';
            }

            if($runnable) {
                // 先判断一下cy_agent是否已经存在游戏了
                $agent_model = model('Agent');
                $has_record = $agent_model
                    ->where(['gameid' => $data['game_id'], 'departmentid' => $data['channel_id']])
                    ->count('id');
                if(1) { // 不存在记录，创建一条
                    $agent_data = [];
                    $agent_data['gameid']       = $data['game_id'];
                    $agent_data['departmentid'] = $data['channel_id'];
                    $agent_data['create_time']  = time();
                    $agent_data['agent']  = date('YmdHis').rand(10, 99);

                    // channel_id 和 game_id 是联合主键，不能重复
                    $uniqueKey = $agent_model->get(['departmentid'=>$data['channel_id'], 'gameid'=>$data['game_id']]);
                    if(sizeof($uniqueKey)){
                        $runnable  = false;
                        $this->error('创建分包记录失败');
                    }

                    $agent_model->data($agent_data);
                    $agent_result = $agent_model->save($agent_data);
                    if(empty($agent_result)) {
                        $runnable  = false;
                        $msg       = '创建分包记录失败';
                    }
                }
            }

            if($runnable) {
                $this->success('添加游戏成功', url('channelGameList', ['channel_id' => input('post.channel_id')]));
            }else{
                $this->error($msg);
            }
        }
    }

    /**
     * 渠道游戏编辑
     */
    public function channelGameEdit(){
        if(request()->isGet()) {
            $id = input('id', 0, 'intval');
            empty($id) && $this->error('参数错误');
            $data = model('CpsChannelGame')->find($id);
            $game_name = model('Game')->getName($data['game_id']);
            $this->assign('data', $data);
            $this->assign('game_name', $game_name);
            /*$channel_id = input('channel_id');
            $this->assign('channel_id', $channel_id);*/
            return $this->fetch('channel_game_edit');
        }

        if(request()->isPost()) {
            $model               = model('CpsChannelGame');
            $data                = [];
            $data['game_type']   = input('post.game_type' , 1 , 'intval');
            $data['sort']        = input('post.sort' , 0 , 'intval');
            $data['update_time'] = NOW_TIMESTAMP;
            $id                  = input('post.id' , 0 , 'intval');

            $res = $model->where('id', $id)->update($data);
            if($res === false) {
                $this->error('游戏修改失败');
            }else{
                $this->success('游戏修改成功', url('channelGameList', ['channel_id' => input('post.channel_id')]));
            }
        }
    }

    /**
     * 渠道游戏记录删除
     */
    public function channelGameDelete(){
        $id = input('id', 0, 'intval');
        empty($id) && $this->error('参数错误');

        $model = CpsChannelGame::get($id);
        $res = $model->delete();

        if($res === false) {
            $this->error('游戏删除失败');
        }else{
            $this->success('游戏删除成功');
        }
    }

    /**
     * 加载 渠道推广列表 页面
     */
    public function channelListTg()
    {
        $where =[];
        $list = $this->CPSChannel
            ->alias("a")
            ->join("nw_channel b", 'a.channel_id = b.id', 'left')
            ->field("a.*, b.name as channel_name")
            ->where($where)
            ->order("a.id asc")
            ->paginate(10, false, ['query' => $where])->each(function($item, $key){
                // 分别统计每个渠道加了几款游戏
                $item['hot_game'] = model('CpsChannelGame')->where(['channel_id' => $item['channel_id'], 'game_type' => '2'])->count();
                $item['new_game'] = model('CpsChannelGame')->where(['channel_id' => $item['channel_id'], 'game_type' => '1'])->count();
                return $item;
            });

        $page = $list->render();
        $this->assign("page", $page);
        $this->assign('list', $list);
        return $this->fetch('channel_list_tg');
    }

    /**
     * CPS模板选择页面
     */
    public function CPSTemplateSelect(){
        if(request()->isGet()) {
            $id = input('id', 0, 'intval');
            empty($id) && $this->error('参数错误');

            $info = model('CpsChannel')->find($id);
            $template_list = model('CpsTemplate')->select();
            $this->assign('info', $info);
            $this->assign('template_list', $template_list);
            return $this->fetch('CPSTemplateSelect');
        }

        if(request()->isPost()) {
            $id           = input('post.id', 0, 'intval');
            $template_id  = input('post.template_id', 0, 'intval');

//            $res = model('CpsChannel')->save(['id' => $id, 'template_id' => $template_id]);
            $res = model('CpsChannel')->save([
                            'template_id'  => $template_id,
                            'update_time' => time()
                        ],['id' => $id]);
            if($res === false) {
                $this->error('模板选择失败');
            }else{
                $this->success('模板选择成功', url('channelListTg'));
            }
        }
    }

    /**
     * CPS游戏列表页面
     */
    public function CPSGameList(){
        $channel_id = input('channel_id', 0, 'intval');
        empty($channel_id) && $this->error('参数错误');

        $game_type = input('game_type', 0, 'intval');
        $map = [];
        $map['a.channel_id'] = $channel_id;
        !empty($game_type) && $map['a.game_type'] = $game_type;

        $model = model('CpsChannelGame');

        $game_list = $model
            ->alias("a")
            ->join("cy_game b", "a.game_id = b.id", 'left')
            ->field("a.*, b.name as game_name")
            ->where($map)
            ->order('a.sort DESC, a.id DESC')
            ->paginate(10, false, ['query' => $map]);

        $page = $game_list->render();
        $this->assign('page', $page);
        $this->assign('list', $game_list);
        $this->assign('game_type', $game_type);
        $this->assign('channel_id', $channel_id);
        return $this->fetch('CPSGameList');
    }


}