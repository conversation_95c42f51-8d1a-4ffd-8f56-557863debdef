<?php
/**
* 游戏管理控制器
*/

namespace app\admin\controller;

use app\common\library\FileUpload;
use app\common\library\MakeReport;
use app\common\library\MakeReportGo;
use app\common\model\AndroidSdk;
use app\common\model\GameBand;
use app\common\model\Game as GameModel;
use app\common\logic\SubPackage as SubChannel;
use app\common\model\GameBelong;
use app\common\model\GameBindDouyinOpen;
use app\common\model\SdkGameList;
use app\common\model\GameInfo;
use app\common\model\GameNotify;
use app\common\model\GameBanned;
use app\common\model\GameExtraPointRatio;
use Overtrue\Pinyin\Pinyin;
use app\common\model\PromotionShortLink;
use think\Db;
use think\Env;
use think\Exception;

class Game extends Admin
{
    protected $gameModel;
    
    // 游戏等级
    protected $gameLevel = ['S', 'A1', 'A2', 'B1', 'B2', 'B3', 'B4', 'C', 'D'];
    
    /**
     * 游戏列表
     */
    public function index()
    {
        $where = $this->_indexParam();
        $gameBandModel = new GameBand();
    
        //查询参数
        $param = input('get.');
        $list = $this->gameModel->alias('game')
            ->join('cy_gameinfo info', 'game.id = info.game_id', 'left')
            ->join('nw_android_sdk_config sdk', 'game.android_sdk_id = sdk.id', 'left')
            ->field('game.id,game.type,game.name,game.origin_name,game.pinyin,game.cooperation_status,game.channel_version,game.order_recheck,game.create_time,game.remarks,game.is_show,game.logout_btn,game.game_kind,info.platform,sdk.version as sdk_version,game.is_default,game.is_login,game.is_register')
            ->where($where)
            ->order('game.id desc')
            ->paginate(10, false, array('query' => $param))
            ->each(function($item, $key) use($gameBandModel) {
                // 绑定信息
                $bandWhere = [];
                if($item['type'] == 1){
                    $bandWhere['ios_game_id'] = $item['id'];
                } else if ($item['type'] == 2) {
                    $bandWhere['android_game_id'] = $item['id'];
                } else if ($item['type'] == 3) {
                    $bandWhere['h5_game_id'] = $item['id'];
                }
                $gameBandInfo = $gameBandModel->where($bandWhere)->field('id,android_game_id,ios_game_id,h5_game_id')->find();
                $item['band_info'] = $gameBandInfo;
            });
        $this->assign('version', (new AndroidSdk())->getAllVersion());
        $this->assign('list', $list);
        $this->assign('page', $list->render());
    
        $tmpGameList = $this->cyModel('game')->field('id,name')->select();
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }
        $this->assign('gameList', $gameList);
        return $this->fetch();
    }
    
    // 游戏列表 查询条件  列表页面和报表下载共用
    public function _indexParam()
    {
        $where = [];
    
        //游戏ID
        if (input('request.id') != '') {
            $where['game.id'] = input('request.id');
        }
    
        //游戏类型
        if (input('request.game_kind') != '') {
            $where['game.game_kind'] = input('request.game_kind');
        }
    
        //游戏名称
        if (input('request.game_name') != '') {
            $where['game.name'] = ['like', '%' . input('request.game_name') . '%'];
        }
    
        //对接状态
        if (input('request.cooperation_status_str') != '') {
            $cooperation_status = explode(',', input('request.cooperation_status_str'));
            $where['game.cooperation_status'] = ['in', $cooperation_status];
        }
    
        //是否显示
        if (input('request.is_show') != '') {
            $where['game.is_show'] = input('request.is_show');
        }
    
        //SDK版本号
        if (input('request.android_sdk_id') != '') {
            $where['game.android_sdk_id'] = input('request.android_sdk_id');
        }
    
        // 游戏名(模糊搜)
        if (input('request.game_name_link') != '') {
            $where['game.name'] = ['like', '%' . input('request.game_name_link') . '%'];
        }
        return $where;
    }
    
    /**
     * 新增游戏
     */
    public function create()
    {
        if (request()->isPost()) {
            $this->assign('is_default', 1);
            $data = [
                'name' => input('post.name'),
                'package_name' => input('post.package_name'),
                'origin_name' => input('post.name'),
                'order_recheck' => input('post.order_recheck'),
                'cooperation_status' => input('post.cooperation_status'),
                'is_show' => input('post.is_show'),
                'channel_version' => input('post.channel_version'),
                // 'channel_split_ratio' => input('post.channel_split_ratio'),
                'type' => input('post.type'),
                'remarks' => input('post.remarks'),
                'android_game_id' => input('android_game_id', ''),
                'ios_game_id' => input('ios_game_id', ''),
                'h5_game_id' => input('h5_game_id', ''),
                'logout_btn' => input('post.logout_btn'),
                'is_default' => input('post.is_default'),
                'game_kind' => input('post.game_kind'),
                'download_url' => input('post.download_url'),
                'free_pay' => input('post.free_pay'),
                'is_pay' => input('post.is_pay'),
                'is_welfare' => input('post.is_welfare'),
                'is_login' => input('post.is_login'),
                'is_register' => input('post.is_register'),
            ];
    
            $ver_id = input('post.version_id');
            if (empty($ver_id)) {
                $this->error('请选择SDK版本号');
            }
            $data['android_sdk_id'] = $ver_id;
            $result = $this->validate($data, [
                ['name', 'require|max:30|unique:cy_game', '请输入游戏名称|游戏名称不能超过30个字符|游戏名称已存在'],
                ['order_recheck', 'integer', '游客登录必须为整型'],
                ['cooperation_status', 'integer|in:0,1,2,3', '对接状态必须为整型|对接状态参数错误'],
                ['is_show', 'integer|in:0,1', '是否显示必须为整型|是否显示参数错误'],
                ['channel_version', 'integer', '强更版本必须为整型'],
                //   ['channel_split_ratio', 'integer|egt:0|elt:99', '渠道分成比例必须为整型|渠道分成比例不能低于1|渠道分成比例不能超过99'],
                ['type', 'integer|in:1,2,3', '平台必须为整型|平台参数错误'],
                ['game_kind', 'integer|in:1,2', '类型必须为整型|类型参数错误'],
                ['remarks', 'max:800', '备注不能超过800字'],
                ['android_game_id', 'integer', '绑定游戏必须为整型'],
                ['ios_game_id', 'integer', '绑定游戏必须为整型'],
                ['h5_game_id', 'integer', '绑定游戏必须为整型'],
                ['android_sdk_id', 'require|integer', '请选择SDK版本号|SDK版本号必须为整型'],
                ['logout_btn', 'require|integer|in:0,1', '请选择注销状态|注销状态必须为整型|注销状态参数错误'],
                ['is_default', 'integer|in:0,1', '是否内部游戏必须为整型|是否内部游戏参数错误'],
                ['download_url', 'url', '下载地址结构错误'],
                ['free_pay', 'require|integer|in:1,2', '请选择自由支付状态|自由支付状态必须为整型|自由支付状态参数错误'],
                ['is_pay', 'require|integer|in:1,2', '请选择停止充值状态|停止充值状态必须为整型|停止充值状态参数错误'],
                ['is_welfare', 'require|integer|in:1,2', '请选择福状态|福状态必须为整型|福状态参数错误'],
                ['is_login', 'require|integer|in:0,1', '请选封禁状态|封禁状态必须为整型|封禁状态参数错误'],
                ['is_register', 'require|integer|in:0,1', '请选封禁状态|封禁状态必须为整型|封禁状态参数错误'],
            ]);
            //绑定的游戏判断
            // , 'cooperation_status' => ['<>', 3]
            // if (!empty(input('post.band_game_id')) && !$this->gameModel->field('id')->where(['id' => input('post.band_game_id')])->find()) {
            //     $this->error('绑定游戏不存在或者已经下架');
            // }
    
            if (true !== $result) {
                $this->error($result);
            } else {
                $pinyin = new Pinyin();
                $pinyinStr = $pinyin->permalink($data['name'], '');
    
                //游戏的拼音是否存在
                $isExitPinyin = $this->gameModel->field('id')->where(['pinyin' => $pinyinStr])->find();
    
                if ($isExitPinyin) {
                    $this->error('游戏拼音已经存在，请在游戏名称上加入标识字符');
                } else {
                    $data['pinyin'] = $pinyinStr;
                    $data['initial'] = $pinyin->abbr($data['name']);
                    $gameInfoModel = new GameInfo;
                    $data['create_time'] = NOW_TIMESTAMP;
                    
                    Db::startTrans();
                    try {
                        //insert触发不了事件
                        if(!$this->gameModel->allowField(true)->save($data)){
                            $this->error('添加失败，请重试！');
                        }
                        $gameInfoModel->insert(['game_id' => $this->gameModel->id, 'platform' => input('post.type')]);
                        
                        $CooperationStatusString = "";
                        if ($data['cooperation_status'] == 0) {
                            $CooperationStatusString = "对接中";
                        } else if ($data['cooperation_status'] == 1) {
                            $CooperationStatusString = "上线";
                        } else if ($data['cooperation_status'] == 2) {
                            $CooperationStatusString = "白名单限制";
                        } else if ($data['cooperation_status'] == 3) {
                            $CooperationStatusString = "下架";
                        }
                        //    $message = "添加游戏：" . $this->gameModel->name ." ,分成比例为：" .$data['channel_split_ratio']." ,对接状态为：" .$CooperationStatusString;
                        $message = "添加游戏：" . $this->gameModel->name . " ,对接状态为：" . $CooperationStatusString . '，登陆封禁：' . $data['is_login'] == 0 ? '关' : '开' . '，注册封禁：' . $data['is_register'] == 0 ? '关' : '开';
                        $this->insertLog($this->current_node, $message, 11);
                        
                        // 游戏绑定
                        $gameBandModel = new GameBand();
                        $bandWhere = [];
                        $bandData = [];
                        if($data['type'] == 1){
                            $bandData = [
                                'android_game_id' => $data['android_game_id']??'',
                                'h5_game_id' => $data['h5_game_id']??'',
                                'ios_game_id' => $this->gameModel->id,
                            ];
                            
                            if($bandData['android_game_id']){
                                $bandWhere['android_game_id'] = $bandData['android_game_id'];
                            }
                            if ($bandData['h5_game_id']) {
                                $bandWhere['h5_game_id'] = $bandData['h5_game_id'];
                            }
                        } else if ($data['type'] == 2) {
                            $bandData = [
                                'ios_game_id' => $data['ios_game_id']??'',
                                'h5_game_id' => $data['h5_game_id']??'',
                                'android_game_id' => $this->gameModel->id
                            ];
                            
                            if ($bandData['ios_game_id']) {
                                $bandWhere['ios_game_id'] = $bandData['ios_game_id'];
                            }
                            if($bandData['h5_game_id']){
                                $bandWhere['h5_game_id'] = $bandData['h5_game_id'];
                            }
                        } else if ($data['type'] == 3) {
                            $bandData = [
                                'android_game_id' => $data['android_game_id']??'',
                                'ios_game_id' => $data['ios_game_id']??'',
                                'h5_game_id' => $this->gameModel->id,
                            ];
                            
                            if($bandData['android_game_id']){
                                $bandWhere['android_game_id'] = $bandData['android_game_id'];
                            }
                            if ($bandData['ios_game_id']) {
                                $bandWhere['ios_game_id'] = $bandData['ios_game_id'];
                            }
                        }
                        $gameBandInfo = $gameBandModel->whereOr($bandWhere)->find();
                        if (!empty($gameBandInfo) && !empty($bandWhere)) {
                            $gameBandModel->save($bandData, $bandWhere);
                        } else {
                            $bandData['create_time'] = NOW_TIMESTAMP;
                            $gameBandModel->save($bandData);
                        }
                        Db::commit();
                    } catch (Exception $e) {
                        // 回滚事务
                        Db::rollback();
                        
                        $this->error('游戏添加失败： ' . $e->getMessage());
                    }
                    
                    $this->success('游戏新增成功', 'game/index');
                }
            }
        }
        
        $this->assign('version', (new AndroidSdk())->getAllVersion());
        $this->assign('band_ios_game', $this->getBandGame(1));
        $this->assign('band_az_game', $this->getBandGame(2));
        $this->assign('band_h5_game', $this->getBandGame(3));
        return $this->fetch('new');
    }
    
    /**
     * 获得绑定游戏信息
     *
     * @param $type int 游戏类型：1=ios、2=安卓、3=h5
     * @param $all boolean 是否展示所有数据（默认不展示已下架游戏的）
     *
     * @return array 游戏列表
     */
    private function getBandGame($type, $all = true)
    {
        $where = [
            'type' => $type
        ];
        // if($type == 1){
        //     $where['type'] = ['in', [2, 3]];
        // } else if ($type == 2) {
        //     $where['type'] = ['in', [1, 3]];
        // } else if ($type == 3) {
        //     $where['type'] = ['in', [1, 2]];
        // }
        
        // 去除已绑定的游戏
        // $androidIds = GameBand::whereOr(['ios_game_id' => ['neq', 0]])->whereOr(['h5_game_id' => ['neq', 0]])->column('android_game_id AS game_id');
        // $iosIds = GameBand::whereOr(['android_game_id' => ['neq', 0]])->whereOr(['h5_game_id' => ['neq', 0]])->column('ios_game_id AS game_id');
        // $h5Ids = GameBand::whereOr(['ios_game_id' => ['neq', 0]])->whereOr(['android_game_id' => ['neq', 0]])->column('h5_game_id AS game_id');
        // // 合并去重
        // $bandIds = array_unique(array_merge($androidIds, $iosIds, $h5Ids));
        // if($bandIds){
        //     $where['id'] = ['not in', $bandIds];
        // }
        if($all){
            $where['cooperation_status'] = ['<>', 3];
        }
        return $this->gameModel->field('id,name,package_name')->where($where)->order('id desc')->select();
    }
    
    /**
     * 查询出游戏接入需要的参数
     */
    public function queryGameParam()
    {
        $game_id = $this->request->post('id');
        if (empty($game_id)) {
            $this->error('参数错误');
        }
        $game_data = model('Game')->where(['id' => $game_id])->field('id')->find();
        if (empty($game_data)) {
            $this->error('参数错误,不存在数据');
        }
        $appkey = model('App')->where(['gameid' => $game_id])->field('id,appkey,client_appkey')->find();
    
        $data['id'] = isset($game_data['id']) ? $game_data['id'] : '';
        $data['appkey'] = isset($appkey['appkey']) ? $appkey['appkey'] : '';
        $data['appid'] = isset($appkey['id']) ? $appkey['id'] : '';
        $data['gameid'] = isset($game_data['id']) ? $game_data['id'] : '';
        $data['client_appkey'] = isset($appkey['client_appkey']) ? $appkey['client_appkey'] : '';
    
        $this->success('成功', '', $data);
    }
    
    /**
     * 编辑
     */
    public function update($id)
    {
        $id = (int)$id;
        if (empty($id)) {
            $this->error('游戏ID不能为空');
        }
    
        // $gameInfo = $this->gameModel->where(['id' => $id])->find();
        $gameInfo = $this->gameModel->alias('g')->join('cy_gameinfo info', 'g.id=info.game_id', 'left')
            ->field('g.*,info.id as info_id,info.platform')->where(['g.id' => $id])->find();
        if (empty($gameInfo)) $this->error('游戏不存在');
        
        $gameBandModel = new GameBand();
        $bandWhere = [];
        if($gameInfo['type'] == 1){
            $bandWhere['ios_game_id'] = $gameInfo['id'];
        } else if ($gameInfo['type'] == 2) {
            $bandWhere['android_game_id'] = $gameInfo['id'];
        } else if ($gameInfo['type'] == 3) {
            $bandWhere['h5_game_id'] = $gameInfo['id'];
        }
        $gameBandInfo = $gameBandModel->where($bandWhere)->find();
        
        if (request()->isPost()) {
            $data = [
                'name' => input('post.name'),
                'type' => input('post.type'),
                'package_name' => input('post.package_name'),
                'order_recheck' => input('post.order_recheck'),
                'cooperation_status' => input('post.cooperation_status'),
                'is_show' => input('post.is_show'),
                'channel_version' => input('post.channel_version'),
                // 'channel_split_ratio' => input('post.channel_split_ratio'),
                'remarks' => input('post.remarks'),
                'android_game_id' => input('android_game_id', ''),
                'ios_game_id' => input('ios_game_id', ''),
                'h5_game_id' => input('h5_game_id', ''),
                'logout_btn' => input('post.logout_btn'),
                'android_sdk_id' => input('post.version_id'),
                // 'is_default'         => input('post.is_default'),
                // 'download_url'       => input('post.download_url'),
                'game_auth_type' => input('post.game_auth_type'),
                'game_bizid' => input('post.game_bizid'),
                'free_pay' => input('post.free_pay'),
                'is_pay' => input('post.is_pay'),
                'is_welfare' => input('post.is_welfare'),
                'is_login' => input('post.is_login'),
                'is_register' => input('post.is_register'),
            ];
            $result = $this->validate($data, [
                ['name', 'require|max:30', '请输入游戏名称|游戏名称不能超过30个字符'],
                ['order_recheck', 'integer', '游客登录必须为整型'],
                ['cooperation_status', 'integer|in:0,1,2,3', '对接状态必须为整型|对接状态参数错误'],
                ['is_show', 'integer|in:0,1', '是否显示必须为整型|是否显示参数错误'],
                ['channel_version', 'integer', '强更版本必须为整型'],
                ['type', 'integer|in:1,2,3', '平台必须为整型|平台参数错误'],
                // ['channel_split_ratio', 'integer|egt:0|elt:99', '渠道分成比例必须为整型|渠道分成比例不能低于0|渠道分成比例不能超过99'],
                ['remarks', 'max:800', '备注不能超过800字'],
                ['android_game_id', 'integer', '绑定游戏必须为整型'],
                ['ios_game_id', 'integer', '绑定游戏必须为整型'],
                ['h5_game_id', 'integer', '绑定游戏必须为整型'],
                ['android_sdk_id', 'require|integer', '请选择SDK版本号|SDK版本号必须为整型'],
                ['logout_btn', 'require|integer|in:0,1', '请选择注销状态|注销状态必须为整型|注销状态参数错误'],
                ['game_auth_type', 'integer|in:0,1,2', '实名认证方式必须为整型|实名认证方式参数错误'],
                // ['download_url', 'url', '下载地址结构错误'],
                ['free_pay', 'require|integer|in:1,2', '请选择自由支付状态|自由支付状态必须为整型|自由支付状态参数错误'],
                ['is_pay', 'require|integer|in:1,2', '请选择停止充值状态|停止充值状态必须为整型|停止充值状态参数错误'],
                ['is_welfare', 'require|integer|in:1,2', '请选择福状态|福状态必须为整型|福状态参数错误'],
                ['is_login', 'require|integer|in:0,1', '请选封禁状态|封禁状态必须为整型|封禁状态参数错误'],
                ['is_register', 'require|integer|in:0,1', '请选封禁状态|封禁状态必须为整型|封禁状态参数错误'],
            ]);
            
            // 修改游戏名称 验证是否唯一
            if ($gameInfo['name'] != $data['name']) {
                if ($this->gameModel->where(['name' => $data['name']])->find()) {
                    $this->error('游戏名称已经存在');
                }
    
                // 去除编辑时候修改游戏名，不然之前的分包就不能用了。
                // $pinyin = new Pinyin();
                // $data['pinyin'] = $pinyin->permalink($data['name'], '');
                // $data['initial'] = $pinyin->abbr($data['name']);
            }
    
            if ($data['game_auth_type'] == 1 && !trim($data['game_bizid'])) {
                $this->error('实名认证方式为“中宣部认证”时,游戏备案识别码(bizId)必须填写');
            }
            
            if (true !== $result) {
                $this->error($result);
            } else {
                // 启动事务
                Db::startTrans();
                try {
                    // 游戏表
                    $this->gameModel->allowField(true)->save($data, ['id' => $id]);
                    $gameInfoModel = new GameInfo;
    
                    // 游戏扩展表
                    if (!empty($gameInfo['info_id'])) {
                        $gameInfoModel->update(['platform' => input('post.type')], [
                            'id' => $gameInfo['info_id'],
                            'game_id' => $id,
                        ]);
                    } else {
                        $gameInfoModel->insert(['game_id' => $id, 'platform' => input('post.type')]);
                    }
                    
                    // 游戏绑定
                    $bandData = [];
                    if($data['type'] == 1){
                        $bandData = [
                            'android_game_id' => $data['android_game_id']??'',
                            'h5_game_id' => $data['h5_game_id']??'',
                            'ios_game_id' => $id,
                        ];
                        $bandWhere['ios_game_id'] = $id;
                    } else if ($data['type'] == 2) {
                        $bandData = [
                            'ios_game_id' => $data['ios_game_id']??'',
                            'h5_game_id' => $data['h5_game_id']??'',
                            'android_game_id' => $id,
                        ];
                        $bandWhere['android_game_id'] = $id;
                    } else if ($data['type'] == 3) {
                        $bandData = [
                            'android_game_id' => $data['android_game_id']??'',
                            'ios_game_id' => $data['ios_game_id']??'',
                            'h5_game_id' => $id,
                        ];
                        $bandWhere['h5_game_id'] = $id;
                    }
                    
                    // 登录白名单
                    $white_username = input('post.white_username', '');
                    if(input('post.is_login', '') == 1){
                        Db::name('cy_game_user_white')->where('game_id', $id)->delete();
                    }
                    if($white_username && input('post.is_login', '') == 1){
                        // 适配多系统
                        $normalized = str_replace(["\r\n", "\r"], "\n", $white_username);
                        $lines = explode("\n", $normalized);
                        $lines = array_map('trim', $lines);
                        $white_list = array_filter($lines);
                        
                        $addWhiteData = [];
                        for($i=0; $i<count($white_list); $i++){
                            if(!empty($white_list[$i])){
                                $addWhiteData[] = ['game_id' => $id, 'username' => $white_list[$i]];
                            }
                        }
                        if($addWhiteData){
                            if(!Db::name('cy_game_user_white')->insertAll($addWhiteData)){
                                throw new \Exception('白名单保存失败,确认是否有重复数据');
                            }
                        }
                    }
                    if (!empty($gameBandInfo)) {
                        $gameBandModel->save($bandData, $bandWhere);
                    } else {
                        $bandData['create_time'] = NOW_TIMESTAMP;
                        $gameBandModel->save($bandData);
                    }
    
                    // 写入日志
                    $gameDJStatus = '';
                    if ($data['cooperation_status'] == 0) {
                        $gameDJStatus = '对接中';
                    } elseif ($data['cooperation_status'] == 1) {
                        $gameDJStatus = '上线';
                    } elseif ($data['cooperation_status'] == 2) {
                        $gameDJStatus = '白名单限制';
                    } elseif ($data['cooperation_status'] == 3) {
                        $gameDJStatus = '下架';
                    }
    
                    if ($gameInfo['name'] != $data['name']) {
                        //	$this->insertLog($this->current_node,'游戏名：'.$gameInfo['name'].' -> '.$data['name'].'，修改后的强更版号：'.input('post.channel_version').'，对接状态：'.$gameDJStatus.'，分成比例：'.$data['channel_split_ratio'], 11);
                        $this->insertLog($this->current_node, '游戏名：' . $gameInfo['name'] . ' -> ' . $data['name'] . '，修改后的强更版号：' . input('post.channel_version') . '，对接状态：' . $gameDJStatus . '，登陆封禁：' . $data['is_login'] == 0 ? '关' : '开' . '，注册封禁：' . $data['is_register'] == 0 ? '关' : '开', 11);
                    } else {
                        //	$this->insertLog($this->current_node,'游戏名：'.$gameInfo['name'].'，修改后的强更版号：'.input('post.channel_version').'，对接状态：'.$gameDJStatus.'，分成比例：'.$data['channel_split_ratio'], 11);
                        $this->insertLog($this->current_node, '游戏名：' . $gameInfo['name'] . '，修改后的强更版号：' . input('post.channel_version') . '，对接状态：' . $gameDJStatus . '，登陆封禁：' . $data['is_login'] == 0 ? '关' : '开' . '，注册封禁：' . $data['is_register'] == 0 ? '关' : '开', 11);
                    }

                    // 提交事务
                    Db::commit();
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
    
                    $this->error('游戏信息编辑失败： ' . $e->getMessage().' - '. $e->getFile().":".$e->getLine());
                }
    
                $this->success('游戏信息编辑成功', 'game/index');
            }
        }
        
        // 登录白名单
        $whiteListNew = "";
        $whiteList = Db::name('cy_game_user_white')->whereIn('game_id', $id)->column('username');
        if($whiteList){
            $whiteListNew = implode("\n", $whiteList);
        }
        $this->assign('whiteList', $whiteListNew);

        // 抖音游戏平台绑定
        $dyConfig = Db::name('cy_game_bind_douyin_open')->where('game_id', $id)->find();
        $this->assign('dyConfig', $dyConfig);

        $this->assign('gameInfo', $gameInfo);
        $this->assign('gameBandInfo', $gameBandInfo);
        $this->assign('version', (new AndroidSdk())->getAllVersion());
        
        $this->assign('band_ios_game', $this->getBandGame(1));
        $this->assign('band_az_game', $this->getBandGame(2));
        $this->assign('band_h5_game', $this->getBandGame(3));
    
        return $this->fetch('edit');
    }
    public function dyConfigSave(){
        $input = input();
        $dy_id = input('dy_id', '');

        $data = [
            'game_id' => $input['game_id'],
            'app_id' => $input['app_id'],
            'app_secret' => $input['app_secret'],
        ];
        if($dy_id){
            $data['id'] = $dy_id;
            $res = GameBindDouyinOpen::update($data);
        }else{
            $res = GameBindDouyinOpen::insert($data);
        }

        if($res){
            $gameName = Db::name('cy_game')->where('id', $input['game_id'])->value('name');
            $this->insertLog($this->current_node, "游戏：".$gameName." 绑定 抖音游戏 app_id：".$input['app_id'],41);
            $this->result([], 100, "操作成功！");
        }
        $this->result([], -100, '操作失败！');
    }
    /**
     * 通过ajax获得绑定游戏信息
     *
     */
    public function ajaxGetBandGame()
    {
        if (request()->isAjax()) {
    
            $platform = input('post.platform', 0, 'intval');
    
            $this->result($this->getBandGame($platform), 1);
        } else {
            $this->error('非法请求');
        }
    }
    /**
     * 通过ajax获得绑定游戏信息
     *
     */
    public function packageApply()
    {
        $page_size = input('page_size', 10);
    
        $where = [];
        //白名单
        //$where['g.cooperation_status'] = 2;
        //游戏名称;
        if (input('request.game_name') != '') {
            $where['g.name'] = [['eq', input('request.game_name', '', 'trim')], ['exp', Db::raw('is null')], 'or'];
        }
        //审核状态
        if (input('request.apply_status') != '') {
            $where['apply.apply_status'] = ['eq', input('request.apply_status')];
        }
        $channelList = model('common/Channel')->where(['level' => 1])->column('name', 'id');
        $param = input('get.');
        $list = Db::name('nw_game_package_apply')->alias('apply')
            ->field('apply.id,apply.channel_id,apply.game_id,FROM_UNIXTIME( apply.create_time ) AS create_time,apply.apply_status,g.name game_name,g.game_kind,channel.name as channel_name,frozen.subpackage,bchild.name as bchild_name,bchild.parent_id as bcparent_id,channel.parent_id as cparent_id') //,bchild.name as bchild_name,b.name as b_name
            ->join('cy_game g', 'g.id=apply.game_id', 'left')
            ->join('nw_channel channel', 'channel.id=apply.channel_id', 'left')
            ->join('nw_channel bchild', 'bchild.id=channel.parent_id AND bchild.level = 2', 'left')
            //        ->join('nw_channel b', '(b.id=bchild.parent_id or b.id = channel.parent_id) AND b.level = 1', 'left')
            ->join('nw_channel_frozen frozen', 'apply.channel_id = frozen.channel_id and apply.game_id = frozen.game_id', 'LEFT')
            ->where($where)->order('apply.apply_status asc,apply.create_time desc')
            ->paginate($page_size, false, array('query' => $param))->each(function ($item, $key) use ($channelList) {
    
                if ($item['bcparent_id']) {
                    $item['b_name'] = $channelList[$item['bcparent_id']];
                } else {
                    $item['b_name'] = $channelList[$item['cparent_id']];
                }
                return $item;
            });
    
        $tmpGameList = $this->cyModel('game')->field('id,name')->select();
    
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }
        $this->assign('gameList', $gameList);
    
        $this->assign('notify_list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }
    
    /**
     * 游戏公告列表
     */
    public function notifyList()
    {
        $notify = new GameNotify;
    
        $where = [];
    
        //游戏名称
        if (input('request.game_name') != '') {
            $where['g.name'] = [['eq', input('request.game_name', '', 'trim')], ['exp', Db::raw('is null')], 'or'];
        }
    
        //标题
        if (input('request.title') != '') {
            $where['n.title'] = ['like', '%' . input('request.title', '', 'trim') . '%'];
        }
    
        //推送类型
        if (input('request.status') != '') {
            $where['n.status'] = input('request.status', 0, 'intval');
        }
    
        //限制类型
        if (input('request.deny_type') != '') {
            $where['n.deny_type'] = input('request.deny_type', 0, 'intval');
        }
    
        //查询参数
        $param = input('get.');
    
        $notify_list = $notify->alias('n')->field('n.*,g.name game_name')->join('cy_game g', 'g.id=n.game_id', 'left')
            ->where($where)->order('n.create_time desc')
            ->paginate(10, false, array('query' => $param));
    
        $this->assign('notify_list', $notify_list);
        $this->assign('page', $notify_list->render());
    
        $this->assign('gameList', $this->selfGameList);
    
        return $this->fetch('notify_list');
    }
    /**
     * 获得限制渠道信息
     */
    public function notifychannel()
    {
        $id = input('id', 0, 'intval');
    
        if (empty($id)) $this->error('删除记录的ID不能为空');
    
        $notify = new GameNotify;
    
        if (!$info = $notify->where(['id' => $id])->find()) {
            $this->error('参数错误');
        }
        
        if($info['data_type'] == 2){
            $uids = Db::table('cy_game_notify_user')->where(['pid' => $id])->column('uid');
            $userListResult = model('Members')->field('id as value,username as name,"true" as selected ')->whereIn('id', $uids)->select();
            foreach ($userListResult as $k=>$v){
                echo "ID: ".$v['value']." - Username: ".$v['name']."<br />";
            }
        }else{
            $denied_channel = ltrim($info['denied_channel'], ',');
            $denied_channel_arr = model('channel')->field('id,name')->whereIn('id', $denied_channel)->select();
            if ($denied_channel_arr) {
                foreach ($denied_channel_arr as $item) {
                    echo "ID: ".$item['id']." - Name: ".$item['name']."<br />";
                }
            }
        }
        exit;
    }
    /**
     * 新增游戏公告
     */
    public function notifyCreate()
    {
        $channelModel = new \app\common\model\Channel();
    
        if (request()->isPost()) {
    
            $fileUpload = new FileUpload();
            $notify = new GameNotify;
    
            $imgFile = request()->file('img');                           //图片
            $img_url = $fileUpload->upload($imgFile);                    //图片URL
    
            $data = [
                'game_id' => input('post.gameid', '', 'intval'),
                'notify_type' => input('post.notify_type'),        // 内容类型：1=公告，2=广告、3=游戏强更
                'deny_type' => input('post.deny_type'),            // 限制类型:0=白名单，1=黑名单
                'data_type' => input('post.data_type'),            // 数据类型区分: 1=渠道、2=用户
                'status' => input('post.status')??'',
                'title' => input('post.title'),
                'content' => input('post.content'),
                'begin_time' => strtotime(input('post.begin_time')),
                'end_time' => strtotime(input('post.end_time')),
            ];
            
            $result = $this->validate($data, [
                ['game_id', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['notify_type', 'integer', '上架类型必须为整型'],
                ['deny_type', 'integer', '限制方式必须为整型'],
                ['data_type', 'integer', '限制类型必须为整型'],
                ['status', 'require|integer', '是否推送不能为空|是否推送必须为整型'],
                ['title', 'require|max:50', '通知标题不能为空|通知标题不能超过50个字符'],
            ]);
    
            if (true !== $result) {
                $this->error($result);
            } //有上传图片时
            elseif (!empty($imgFile) && empty($img_url)) {
                $this->error($fileUpload->getError());
            }
    
            $data['img_url'] = $img_url??'';
            $data['create_time'] = time();
            if(implode(',', input('denied_channel/a', [])) && $data['data_type'] == 1){
                $data['denied_channel'] = ','.implode(',', input('denied_channel/a', [])) . ',';
            }else{
                $data['denied_channel'] = ',';
            }
            
            if ($id = $notify->insertGetId($data)){
                if($data['data_type'] == 2){
                    $select_user = input('post.select_user', '');
                    if($select_user != ''){
                        $select_user_ids = explode(',', $select_user);
                        $newData = [];
                        foreach ($select_user_ids as $uid) {
                            $newData[] = [
                                'pid' => $id, // 假设 $id 是外部定义的变量
                                'uid' => $uid,
                                'update_time' => time(),
                            ];
                        }
                        Db::table('cy_game_notify_user')->insertAll($newData);
                    }
                }
                
                $this->success('新增成功', 'game/notifyList');
            }else{
                $this->error('新增失败');
            }
        }
        $game_list = $this->gameModel->field('id,name')->order('name asc')->select();
    
        //渠道列表
        $this->assign('game_list', $game_list);
        return $this->fetch('notify_new');
    }
    /**
     * 编辑游戏公告
     */
    public function notifyEdit()
    {
        $id = input('id', 0, 'intval');
    
        $notifyModel = new GameNotify;
        if (empty($id)) $this->error('游戏公告的ID不能为空');
        $notifyInfo = $notifyModel->where(['id' => $id])->find();
        if (empty($notifyInfo)) $this->error('记录不存在');
    
        if (request()->isPost()) {
    
            $fileUpload = new FileUpload();
            $notify = new GameNotify;
    
            $imgFile = request()->file('img');                            //图片
            $img_url = $fileUpload->upload($imgFile);                     //图片URL
    
            $data = [
                'game_id' => input('post.gameid', '', 'intval'),
                'notify_type' => input('post.notify_type'),
                'deny_type' => input('post.deny_type'),
                'data_type' => input('post.data_type'),
                'status' => input('post.status'),
                'title' => input('post.title'),
                'content' => input('post.content'),
                'begin_time' => strtotime(input('post.begin_time')),
                'end_time' => strtotime(input('post.end_time')),
            ];
            $result = $this->validate($data, [
                ['game_id', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['notify_type', 'integer', '上架类型必须为整型'],
                ['deny_type', 'integer', '限制方式必须为整型'],
                ['data_type', 'integer', '限制类型必须为整型'],
                ['status', 'require|integer', '是否推送不能为空|是否推送必须为整型'],
                ['title', 'require|max:50', '通知标题不能为空|通知标题不能超过50个字符'],
            ]);
    
            if (true !== $result) {
                $this->error($result);
            } //有上传图片时
            elseif (!empty($imgFile) && empty($img_url)) {
                $this->error($fileUpload->getError());
            }
    
            //如果图片有变更
            if (!empty($img_url)) {
                //删除旧文件
                $fileUpload->deleteFile($notifyInfo['img_url']);
                $data['img_url'] = $img_url;
            }
            
            if(implode(',', input('denied_channel/a', []))){
                $data['denied_channel'] = ','.implode(',', input('denied_channel/a', [])) . ',';
            }else{
                $data['denied_channel'] = ',';
            }
            
            if ($notifyModel->update($data, ['id' => $id])) {
                if($data['data_type'] == 2){
                    Db::table('cy_game_notify_user')->where(['pid' => $id])->delete();
                    $select_user = input('post.select_user', '');
                    if($select_user != ''){
                        $select_user_ids = explode(',', $select_user);
                        $newData = [];
                        foreach ($select_user_ids as $uid) {
                            $newData[] = [
                                'pid' => $id, // 假设 $id 是外部定义的变量
                                'uid' => $uid,
                                'update_time' => time(),
                            ];
                        }
                        Db::table('cy_game_notify_user')->insertAll($newData);
                    }
                }
                
                $this->success('编辑成功', 'game/notifyList');
            } else
                $this->error('编辑失败');
        }
    
        $game_list = $this->gameModel->field('id,name')->order('name asc')->select();
    
        //渠道列表
        $this->assign('denied_channel', ltrim($notifyInfo['denied_channel'], ','));
        $this->assign('game_list', $game_list);
        $this->assign('data', $notifyInfo);
        
        
        $userListResult = [];
        if($notifyInfo['data_type'] == 2){
            $uids = Db::table('cy_game_notify_user')->where(['pid' => $id])->column('uid');
            $userListResult = model('Members')->field('id as value,username as name,"true" as selected ')->whereIn('id', $uids)->select();
        }
        
        $this->assign('userList', json_encode($userListResult));
        return $this->fetch('notify_edit');
    }
    /**
     * 删除游戏公告
     *
     */
    public function notifyDelete()
    {
        $id = input('id', 0, 'intval');
        
        if (empty($id)) $this->error('删除记录的ID不能为空');
        
        $notify = new GameNotify;
        
        if ($notify->where(['id' => $id])->delete()) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    // 获取匹配的用户名相关账户列表
    public function getUserList(){
        $username =  input('username', '');
        if(!$username){
            $this->jsonResult('',0,'查询参数有误！');
        }
        
        $list = model('Members')->field('id as value,username as name')->whereLike('username', "%".$username."%")->limit(1000)->select();
        return $this->jsonResult($list, 1);
    }
    
    
    /**
     *是否显示
     */
    public function show()
    {
        $id = $this->request->param('id', '', 'intval');
        $status = $this->request->param('is_show', '', 'intval');
        $data['is_show'] = $status;
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $result = $this->validate($data, [
            ['is_show', 'in:0,1', '状态参数错误'],
        ]);
        if (true !== $result) {
            $this->error($result);
        }
        $gameModel = model("Game");
        if (!$gameModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($gameModel->update($data, ['id' => $id])) {
            $msg = $status ? '显示' : '隐藏';
            $this->insertLog($this->current_node, $msg . '游戏：' . get_game_name($id), 11);
            $this->success('修改成功');
        }
        $error = $gameModel->getError();
        $this->error($error ?: '修改失败');
    }
    
    /**
     * 游戏点位
     *
     * @return mixed|string
     */
    public function gamePointList()
    {
        $where['g.cooperation_status'] = ['not in', '3'];      //下架的游戏不显示
        //游戏名称
        if (input('request.game_id') != '') {
            $where['g.id'] = ['=', input('request.game_id')];
            $game = get_game_name(input('request.game_id'));
            $message = "查询游戏：{$game}";
            $this->insertLog($this->current_node, $message, 12);
        }
    
        $list = Db::table('cy_game')->alias('g')->join('cy_game_point p', 'g.id = p.game_id', 'left')
            ->join("nw_game_channel_pointset b", "b.game_id=g.id", "left")
            ->field("p.*,g.id as game_id,g.name,b.isset_first,b.restricted_list_1,b.isset_second,b.restricted_list_2")->order('g.id desc')->where($where)->where("g.is_default = 0")
            ->paginate(10, false, ['query' => input('get.')])
            ->each(function ($item, $key) {
                $item['restricted_list_1'] = ltrim($item['restricted_list_1'], ',');
                $item['restricted_list_2'] = ltrim($item['restricted_list_2'], ',');
                // 判断首充渠道限制名单是否为空
                $item['channel_name_1'] = '';
                $item['channel_name_2'] = '';
                if (!empty($item['restricted_list_1'])) {
                    $channelArr = Db::table('cy_department')
                        ->field("name")
                        ->where('id', 'IN', $item['restricted_list_1'])
                        ->select();
                    $nameStr = '';
                    foreach ($channelArr as $v) {
                        $nameStr .= $v['name'] . '，';
                    }
                    $nameStr = substr($nameStr, 0, strlen($nameStr) - 3);
                    $item['channel_name_1'] = $nameStr; //给数据集追加字段num并赋值
                }
                // 判断续充渠道限制名单是否为空
                if (!empty($item['restricted_list_2'])) {
                    $channelArr = Db::table('cy_department')
                        ->field("name")
                        ->where('id', 'IN', $item['restricted_list_2'])
                        ->select();
                    $nameStr = '';
                    foreach ($channelArr as $v) {
                        $nameStr .= $v['name'] . '，';
                    }
                    $nameStr = substr($nameStr, 0, strlen($nameStr) - 3);
                    $item['channel_name_2'] = $nameStr; //给数据集追加字段num并赋值
                }
                return $item;
            });
    
        // 获取配置表 首充默认最大天数  FIRST_MAX_DAY
        $FIRST_MAX_DAY = model('Setting')->where(['name' => 'FIRST_MAX_DAY'])->field('value')->find()['value'];
        $this->assign('game_list', $this->selfGameList);
        $this->assign('game_point_list', $list);
        $this->assign('FIRST_MAX_DAY', $FIRST_MAX_DAY);
        $this->assign('page', $list->render());
        return $this->fetch('game_point_list');
    }
    
    public function gamePointDownload()
    {
        if (request()->isAjax()) {
            $where['g.cooperation_status'] = ['not in', '0,3'];      //对接中，下架的游戏不显示
    
            //游戏名称
            if (input('request.game_id') != '') {
                $where['g.id'] = ['=', input('request.game_id', '0', 'intval')];
            }
    
            $num = Db::table('cy_game')->alias('g')
                ->join('cy_game_point p', 'g.id = p.game_id', 'left')
                ->field("g.id as game_id,g.name,p.game_level,p.inner_point,p.min_percent,p.third_party_pay_point,
            p.remark,p.gp_create_time,p.gp_update_time,p.first_max_days")
                ->where($where)
                ->order('g.id desc')
                ->count('*');
            //2023-2-7
            $this->success('查无数据，不支持下载');
    
            if (!$num) {
                $this->success('查无数据，不支持下载');
            }
    
            $sql = Db::table('cy_game')->alias('g')
                ->join('cy_game_point p', 'g.id = p.game_id', 'left')
                ->field("g.id as game_id,g.name,p.game_level,p.inner_first_point,p.inner_point,p.min_percent,p.third_party_pay_point,
            p.remark,p.gp_create_time,p.gp_update_time,p.first_max_days")
                ->where($where)
                ->order('g.id desc')
                ->fetchSql(true)
                ->select();
    
            if ((new MakeReportGo())->addTask('gamePointList', $sql, session_id())) {
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            } else {
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
    
        } else {
            $this->error('非法请求');
        }
    }
    
    // 游戏点位报表
    public function editPoint()
    {
        $data = $this->request->param();
        if (isset($data['type'])) {
            if ($data['type'] == 'editMultiLevel') {
                $this->editMultiLevel();
            }
            if ($data['type'] == 'editLevel') {
                $this->editLevel();
            }
            if ($data['type'] == 'editPercent') {
                $this->editPercent();
            }
        }
    
        $type = ['third_party_pay_point', 'inner_point', 'remark', 'inner_first_point', 'first_max_days'];
        if (!in_array($data['name'], $type)) {
            $this->error('选择参数错误');
        }
    
        //返点
        $gamePointModel = model("GamePoint");
        $oldPoint = $gamePointModel->where("game_id = '{$data['pk']}'")->field('gp_id,inner_point,inner_first_point,third_party_pay_point')->find();
        $nameZK = '';
        if ($data['name'] == 'third_party_pay_point') {
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|点数', 'number|between:0,100'],
                ['name', 'require', '点位参数错误'],
            ]);
        } else if ($data['name'] == 'inner_first_point') {
            // 首充
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|首充折扣', 'number|between:0,10'],
                ['name', 'require', '点位参数错误'],
            ]);
            $nameZK = '首充折扣';
            $point_fla = true;
            if ($data['value'] <= 0 || $data['value'] > 10 || !is_numeric($data['value'])) {
                $result = '折扣点位只能0＜折扣≤10';
                $point_fla = false;
            } elseif ((float)$oldPoint['inner_point'] > 0 && $data['value'] > $oldPoint['inner_point'] && $point_fla) {
                $result = '首充折扣要小等于续充折扣';
            }
    
        } else if ($data['name'] == 'inner_point') {
            // 续充
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|续充折扣', 'number|between:0,10'],
                ['name', 'require', '点位参数错误'],
            ]);
            $nameZK = '续充折扣';
            $point_fla = true;
            if ($data['value'] <= 0 || $data['value'] > 10 || !is_numeric($data['value'])) {
                $result = '折扣点位只能0＜折扣≤10';
                $point_fla = false;
            } elseif ((float)$oldPoint['inner_first_point'] > 0 && $data['value'] < $oldPoint['inner_first_point'] && $point_fla) {
                $result = '续充折扣要大等于首充折扣';
            }
    
        } else if ($data['name'] == 'first_max_days') {
            $result = true;
            if ($data['value'] != '') {
                if ($data['value'] <= 0 || !is_numeric($data['value']) || floor($data['value']) != $data['value']) {
                    $result = '首充最大天数只能是大于0的整数';
                }
            } else {
                $data['value'] = null;
            }
        } else {
            // 备注
            $result = $this->validate($data, [
                ['pk|ID', 'require', '参数错误'],
                ['value|备注', 'require|max:500'],
                ['name', 'require', '点位参数错误'],
            ]);
        }
    
        if (true !== $result) {
            $this->error($result);
        }
    
        // 开启事务
        Db::startTrans();
    
        $insert[$data['name']] = $data['value'];
        $result = $gamePointModel->updateData($insert, ['game_id' => $data['pk']]);
    
        if (!isset($oldPoint['gp_id'])) {
            $oldPoint['gp_id'] = $gamePointModel->where("game_id = '{$data['pk']}'")->field('gp_id')->find()['gp_id'];
        }
    
        $logArr = [
            "gp_id" => $oldPoint['gp_id'],
            $data['name'] => $data['value'],
            'operate_id' => $admin_id = session('ADMIN_ID') ?: 1
        ];
        $res = true;
        if (($data['name'] == 'inner_point' || $data['name'] == 'inner_first_point' || $data['name'] == 'third_party_pay_point')) {
            $res = model('GamePointLog')->add($logArr);
        }
    
        if ($result && $res) {
            // 提交事务
            Db::commit();
            $game = get_game_name($data['pk']);
            if ($data['name'] == 'inner_point') {
                $field = '续充点位';
            } else if ($data['name'] == 'third_party_pay_point') {
                $field = '其它支付返点';
            } else if ($data['name'] == 'inner_first_point') {
                $field = '首充点位';
            }
    
            if (isset($field)) {
                $message = "更新游戏：{$game} 的{$field}：{$data['value']}";
                $this->insertLog($this->current_node, $message, 12);
            }
            $this->success('修改成功');
        } else {
            // 回滚事务
            Db::rollback();
            $this->error('修改失败');
        }
    }
    
    public function editMultiLevel()
    {
        $pks = $this->request->param('pks');
        $level = $this->request->param('value');
    
        $result = $this->validate(['value' => $level], [
            ['value', 'require|in:' . implode(',', $this->gameLevel), '游戏等级不能为空|游戏等级不存在']
        ]);
    
        if (true !== $result) {
            $this->error($result);
        }
    
        $pksArr = explode(',', $pks);
        if (empty($pksArr)) {
            $this->error('请选择游戏');
        }
    
        $result = model("GamePoint")->updateData(['game_level' => $level], ['game_id' => ['IN', $pksArr]]);
    
        if ($result) {
            if (isset($field)) {
                $message = "更新游戏：批量更新游戏ID为[{$pks}]游戏等级：{$level}";
                $this->insertLog($this->current_node, $message, 12);
            }
    
            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }
    
    public function editLevel()
    {
        $data = [
            'pk' => $this->request->param('pk'),
            'value' => $this->request->param('value')
        ];
        $result = $this->validate($data, [
            ['pk', 'require', '参数错误'],
            ['value', 'require|in:' . implode(',', $this->gameLevel), '游戏等级不能为空|游戏等级不存在']
        ]);
    
        if (true !== $result) {
            $this->error($result);
        }
    
        $result = model("GamePoint")->updateData(['game_level' => $data['value']], ['game_id' => $data['pk']]);
    
        if ($result) {
            $game = get_game_name($data['pk']);
            if (isset($field)) {
                $message = "更新游戏：{$game} 的游戏等级：{$data['value']}";
                $this->insertLog($this->current_node, $message, 12);
            }
    
            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }
    
    public function editPercent()
    {
        $data = [
            'pk' => $this->request->param('pk'),
            'value' => $this->request->param('value')
        ];
        $result = $this->validate($data, [
            ['pk|ID', 'require', '参数错误'],
            ['value|折扣', 'number|between:0,10', '必须是数字|折扣在0到10之间']
        ]);
    
        if (true !== $result) {
            $this->error($result);
        }
    
        $result = model("GamePoint")->updateData(['min_percent' => $data['value']], ['game_id' => $data['pk']]);
    
        if ($result) {
            $game = get_game_name($data['pk']);
            if (isset($field)) {
                $message = "更新游戏：{$game} 的最低折扣：{$data['value']}";
                $this->insertLog($this->current_node, $message, 12);
            }
    
            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }
    
    // 最低折扣
    public function editPointStatus()
    {
        $id = $this->request->param('gp_id', '', 'intval');
        $status = $this->request->param('gp_status', '', 'intval');
        $data['gp_status'] = $status;
        if (empty($id)) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置!');
        }
        $result = $this->validate($data, [
            ['gp_status', 'in:0,1', '状态参数错误'],
        ]);
        if (true !== $result) {
            $this->error($result);
        }
        $gamePointModel = model("GamePoint");
        if (!$info = $gamePointModel->where(['gp_id' => $id])->field('gp_id')->find()) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置');
        }
        if ($gamePointModel->update(['gp_status' => $status], ['gp_id' => $info['gp_id']])) {
            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }
    
    // 游戏等级
    public function editPointDisplay()
    {
        $id = $this->request->param('gp_id', '', 'intval');
        $status = $this->request->param('gp_isdisplay', '', 'intval');
        $data['gp_isdisplay'] = $status;
        if (empty($id)) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置!');
        }
        $result = $this->validate($data, [
            ['gp_isdisplay', 'in:0,1', '状态参数错误'],
        ]);
        if (true !== $result) {
            $this->error($result);
        }
        $gamePointModel = model("GamePoint");
        if (!$info = $gamePointModel->where(['gp_id' => $id])->field('gp_id')->find()) {
            $this->error('参数错误,如果未设置游戏点位数据，请先设置');
        }
        if ($gamePointModel->update(['gp_isdisplay' => $status], ['gp_id' => $info['gp_id']])) {
            $this->success('修改成功');
        } else {
            $this->error('修改失败');
        }
    }
    
    // 批量修改等级
    
    /**
     *  游戏内容编辑列表
     *
     */
    public function contentList()
    {
        $where = [];
    
        $order = input('order', 'id desc', 'trim');
    
        if ($order == 'xulie_desc') {
    
            $order = 'g.xulie desc';
        } elseif ($order == 'xulie_asc') {
    
            $order = 'g.xulie asc';
        } else {
            $order = 'id desc';
        }
    
        //显示样式
        if (input('request.show_style') != '') {
            $where['info.show_style'] = ['like', '%' . input('request.show_style') . ',%'];
        }
        //游戏名称
        if (input('request.game_name') != '') {
            $where['g.name'] = ['like', '%' . input('request.game_name') . '%'];
        }
    
        //对接状态
        if (input('request.cooperation_status') != '') {
            $where['g.cooperation_status'] = input('request.cooperation_status');
        }
        // 游戏状态
        if (input('request.lanmu') != '') {
            $where['g.lanmu'] = input('request.lanmu');
        }
    
        //查询参数
        $param = input('get.');
    
        $list = $this->gameModel
            ->alias('g')
            ->join('cy_gameinfo info', 'g.id=info.game_id', 'left')
            ->join('cy_gamesubject s', 'g.subject=s.id', 'left')
            ->join('cy_gametype t', 'g.type=t.id', 'left')
            ->field('g.*, t.name as type, s.name as subject,info.mobileicon,info.show_style')
            ->where($where)->order($order)->paginate(10, false, array('query' => $param));
    
        $this->assign('list', $list);
        $this->assign('page', $list->render());
    
        $tmpGameList = $this->gameList;
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }
        $this->assign('game_list', $gameList);
    
        return $this->fetch('content_list');
    }
    
    /**
     * 游戏内容编辑页
     */
    public function contentUpdate($id)
    {
        set_time_limit(300);
        $id = (int)$id;     //游戏ID
    
        if (empty($id)) {
            $this->error('游戏ID不能为空');
        }
    
        $gameInfo = $this->gameModel->alias('g')->join('cy_gameinfo info', 'g.id=info.game_id', 'left')
            ->field('g.*,info.id as info_id,info.platform,info.description,info.drawingImg,info.video,info.mobileicon,info.bigimage,info.developer,info.video_image,info.updateinfo,info.show_style,info.landing_img,info.title_img')
            ->where(['g.id' => $id])->find();

        if (empty($gameInfo)) $this->error('游戏不存在');
    
        if (request()->isPost()) {
            $this->contentUpdateProcess($id);
        }
        if ($gameInfo['bigimage']) {
            $bigimage_list = explode(';', $gameInfo['bigimage']);
        } else {
            $bigimage_list = [];
        }
        $gameType = Db::name('cy_gametype')->select();
        $gameSubject = Db::name('cy_gamesubject')->select();
    
        $gameInfo['show_style'] = explode(",", $gameInfo['show_style']);
        $this->assign('game_type', $gameType);
        $this->assign('gameid', $id);
        $this->assign('game_subject', $gameSubject);
        $this->assign('gameInfo', $gameInfo);
        $this->assign('bigimage_list', $bigimage_list);
        $this->assign('drawingimage_list', json_decode($gameInfo['drawingImg'], 1));
        return $this->fetch('content_edit');
    }
    
    /**
     * 游戏内容编辑页保存处理
     *
     */
    private function contentUpdateProcess($id)
    {
        $fileUpload = new FileUpload();
        $gameInfoModel = new GameInfo;    //游戏扩展表
    
        //游戏图标
        $mobileicon_url = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000)->set('dir', 'image/game/icons')->upload(request()->file('mobileicon'));
        $mobileicon_error = $fileUpload->getError();

        //视频封面
        $videoImg_url = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1572864)->set('dir', 'image/game/videoImg')->upload(request()->file('video_image'));
        $videoImg_error = $fileUpload->getError();
        //视频
        $video_url = $fileUpload->set('allowExt', 'mp4')->set('maxsize', 52428800)->set('dir', 'video/game/icons')->upload(request()->file('video'));
        $video_error = $fileUpload->getError();
        //落地页背景
        $landing_img_img_url = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 10485760)->set('dir', 'image/game/landing')->upload(request()->file('landing_img'));
        $landing_img_url_error = $fileUpload->getError();

        //游戏宣传语
        $publicity = input('post.publicity', '', 'trim');
    
    
        //头部图片
        $title_img_url = $fileUpload->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000)->set('dir', 'image/game/icons')->upload(request()->file('title_img'));
        $title_img_error = $fileUpload->getError();

        $data = [
            'nickname' => input('post.nickname'),
            'xulie' => input('post.xulie'),
            'type' => input('post.type'),
            'subject' => input('post.subject'),
            'package_name' => input('post.package_name'),
            'developer' => input('post.developer'),
            'publicity' => $publicity,
        ];
    
        $result = $this->validate($data, [
            ['nickname', 'require|max:32', '游戏昵称不能为空|游戏昵称不能超过32个字符'],
            ['xulie', 'integer', '游戏序列号必须为整型'],
            ['type', 'require', '游戏类型不能为空'],
            ['subject', 'require', '游戏题材不能为空'],
            ['package_name', 'max:100', '游戏包名不能超过100个字符'],
            ['developer', 'max:30', '开发商不能超过30个字符'],
            ['publicity', 'max:50', '游戏宣传语不能超过50个字符'],
        ]);
    
        if (true !== $result) {
            $this->error($result);
        } elseif (!empty(request()->file('mobileicon')) && empty($mobileicon_url)) { // 游戏图标上传出错时
            $this->error('游戏图标：' . $mobileicon_error);
        } elseif (!empty(request()->file('video_image')) && empty($videoImg_url)) {
            $this->error('视频封面：' . $videoImg_error);
        } elseif (!empty(request()->file('video')) && empty($video_url)) { //视频上传失败
            $this->error('视频上传：' . $video_error);
        } elseif (!empty(request()->file('landing_img')) && empty($landing_img_img_url)) {
            $this->error('落地页背景：' . $landing_img_url_error);
        } elseif (!empty(request()->file('title_img_url')) && empty($title_img_url)) {
            $this->error('落地页背景：' . $title_img_error);
        } else {
            // 启动事务
            Db::startTrans();
    
            try {
                $gameData['nickname'] = $data['nickname'];
                $gameData['lanmu'] = input('post.lanmu');      //游戏状态
                $gameData['xulie'] = $data['xulie'];           //游戏序列号
                // $gameData['type'] = $data['type'];
                $gameData['subject'] = $data['subject'];
                $gameData['package_name'] = $data['package_name'];
                $gameData['publicity'] = $publicity;
    
                //cy_game表更新
                $this->gameModel->save($gameData, ['id' => $id]);
    
                $arrImage = [];
    
                //游戏截图
                for ($i = 1; $i <= 5; $i++) {
    
                    $file = new FileUpload();
    
                    $bigimage_url = $file->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000)->set('dir', 'image/game/screenshots')->upload(request()->file('bigimage' . $i));
    
                    if (!empty(request()->file('bigimage' . $i)) && $bigimage_url == false) {
    
                        throw new Exception('游戏截图:' . $file->getError());
                    }
    
                    if (!empty($bigimage_url))
                        $arrImage[$i] = $bigimage_url;
                }
                //立绘截图
                for ($i = 1; $i <= 5; $i++) {
    
                    $file = new FileUpload();
    
                    $drawing_url = $file->set('allowExt', 'jpg,jpeg,png')->set('maxsize', 1024000)->set('dir', 'image/game/screenshots')->upload(request()->file('drawingimage' . $i));
    
                    if (!empty(request()->file('drawingimage' . $i)) && $drawing_url == false) {
    
                        throw new Exception('立绘截图:' . $file->getError());
                    }
    
                    if (!empty($drawing_url))
                        $aryImage[$i - 1] = $drawing_url;
                }
                //有上传游戏截图时
                if (!empty($arrImage)) {
                    $imgRet = $gameInfoModel->where(['game_id' => $id])->value('bigimage');
                    if ($imgRet) {
                        $imgRet = explode(';', $imgRet);
                        foreach ($arrImage as $key => $value) {
                            $imgRet[$key - 1] = $value;
                        }
                        $gameInfoData['bigimage'] = implode(";", $imgRet);
                    } else {
                        $gameInfoData['bigimage'] = implode(";", $arrImage);
                    }
                }
                if (!empty($aryImage)) {
                    $imgRet = $gameInfoModel->where(['game_id' => $id])->value('drawingImg');
                    if ($imgRet) {
                        $imgRet = json_decode($imgRet, 1);
                        foreach ($aryImage as $key => $value) {
                            $imgRet[$key] = $value;
                        }
                        $gameInfoData['drawingImg'] = json_encode($imgRet);
                    } else {
                        $gameInfoData['drawingImg'] = json_encode($aryImage);
                    }
                }
                //有上传游戏截图时
                if (!empty($mobileicon_url))
                    $gameInfoData['mobileicon'] = $mobileicon_url;
    
                //落地页背景图
                if (!empty($landing_img_img_url)) {
                    $gameInfoData['landing_img'] = $landing_img_img_url;
                    $gameInfoData['new_back_img'] = STATIC_DOMAIN . $landing_img_img_url;
                }

                if (!empty($videoImg_url))
                    $gameInfoData['video_image'] = $videoImg_url;
    
                if (!empty($title_img_url))
                    $gameInfoData['title_img'] = STATIC_DOMAIN .'/'. $title_img_url;
    
                //有视频的时候
                if (!empty($video_url))
                    $gameInfoData['video'] = $video_url;
    
                $gameInfoData['description'] = input('post.description');
                $gameInfoData['developer'] = input('post.developer');
                $gameInfoData['updateinfo'] = input('post.updateinfo');     //更新说明
    
                //单机游戏或者为未选择时
                if ($gameData['lanmu'] == 5 || empty($gameData['lanmu'])) {
    
                    $gameInfoData['show_style'] = '';     //更新说明
                } else {
                    $gameInfoData['show_style'] = !empty($_POST['show_style']) ? implode(",", input('post.show_style/a')) . ',' : '';     //更新说明
                }

                //更新游戏扩展信息
                $gameInfoModel->where(['game_id' => $id])->update($gameInfoData);
    
                Db::commit();
    
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
    
                $this->error('游戏信息编辑失败:' . $e->getMessage());
            }
    
            $this->success('游戏信息编辑成功', url('game/contentList'));
        }
    }
    
    /**
     * delimg图片删除
     *
     * @param string $value [description]
     */
    public function delimg()
    {
        $type = input('type');
        $index = input('index');
        $id = input('gameid');
        $gameInfoModel = new GameInfo;
        if (empty($type) || empty($index) || empty($id))
            $this->error('参数错误,请刷新后重试');
        if ($type == 'bigimage') {
            $imgRet = $gameInfoModel->where(['game_id' => $id])->value('bigimage');
            if ($imgRet) {
                $imgRet = explode(';', $imgRet);
                if (isset($imgRet[$index - 1])) {
                    unset($imgRet[$index - 1]);
                    $updateData['bigimage'] = implode(";", $imgRet);
                } else {
                    $this->error('没有图片哦,请刷新后重试');
                }
    
            } else {
                $this->error('没有图片哦,请刷新后重试');
            }
        }
        if ($type == 'drawingimage') {
            $imgRet = $gameInfoModel->where(['game_id' => $id])->value('drawingImg');
            if ($imgRet) {
                $imgRet = json_decode($imgRet, 1);
                if (isset($imgRet[$index - 1])) {
                    unset($imgRet[$index - 1]);
                    $updateData['drawingImg'] = json_encode($imgRet);
                } else {
                    $this->error('没有图片哦,请刷新后重试');
                }
            } else {
                $this->error('没有图片哦,请刷新后重试');
            }
        }
    
        $res = $gameInfoModel->where(['game_id' => $id])->update($updateData);
        if ($res) {
            $this->success('删除图片成功');
        } else {
            $this->error('删除失败,请刷新后重试');
        }
    }
    
    /**
     * 玩家是否在线查询
     *
     * @return mixed|string
     */
    public function online()
    {
        $info = [];
    
        if ($this->request->isPost()) {
            $gameid = input('gameid');
            $username = input('username', '', 'trim');
    
            $where = [];
            if (empty($gameid)) {
                $this->error('游戏不能为空!');
            }
    
            if (empty($username)) {
                $this->error('玩家名称不能为空!');
            }
    
            $where['m.username'] = $username;
            $where['s.game_id'] = $gameid;
    
            $membersModel = Db::name('cy_members');
            $info = $membersModel->table('cy_members m,nw_subaccount s')
                ->field('m.id,m.username,s.sub_username')
                ->where('m.id=s.member_id')
                ->where($where)
                ->select();
    
            foreach ($info as &$val) {
    
                $val['token'] = '';
    
                $redisTokenRandom = \think\Cache::store('default')->get('token|sdk|' . $val['id'] . '|' . $gameid);
    
                if ($redisTokenRandom) {
    
                    $token = auth_code($val['id'] . '|' . $val['username'] . '|' . $gameid . '|' . $val['sub_username'] . '|' . $redisTokenRandom . '|sdk', "ENCODE", Env::get('auth_key'));
                    $isLogin = \think\Cache::store('default')->get($token);
    
                    if ($isLogin) {
                        $val['token'] = $token;
                    }
                }
            }
            unset($val);
    
        }
    
        $this->assign('list', $info);
        $this->assign('game_list', $this->selfGameList);
        $this->assign('list_count', count($info));
        return $this->fetch();
    }
    
    /**
     * 玩家踢下线
     *
     */
    public function downline()
    {
        $token = input('token');
    
        if (empty($token)) {
            $this->error('token不能为空! ');
        }
    
        $isLogin = \think\Cache::store('default')->get($token);
    
        if (empty($isLogin)) {
            $this->error('token已失效! ');
        }
    
    
        list($userid, $username, $gameid, $sub_username, $token_random, $type) = explode('|', auth_code($token, "DECODE", Env::get('auth_key')));
    
        \think\Cache::store('default')->rm($token);
        \think\Cache::store('default')->rm('token|sdk|' . $userid . '|' . $gameid);
    
        $this->success('已强制下线!', url('online'));
    }
    
    /**
     * 获取改游戏历史点位修改记录
     */
    public function showPointHistory()
    {
        $gp_id = $this->request->param('gp_id', '', 'intval');
        $list = model("GamePointLog")->selectAll($gp_id);
        $code = 1;
        if (!count($list)) {
            $code = 0;
        } else {
            foreach ($list as &$v) {
                if (empty($v['username'])) {
                    $v['username'] = '';
                }
            }
        }
        echo json_encode(['code' => $code, 'data' => $list]);
    }
    
    public function setGameChannelPoint()
    {
        $data['game_id'] = input('post.id');
        $data['isset_first'] = input('post.isset_first');
        $data['isset_second'] = input('post.isset_second');
        $data['restricted_list_1'] = implode(',', input('denied_channel_1/a', [])) . ',';
        $data['restricted_list_2'] = implode(',', input('denied_channel_2/a', [])) . ',';
    
        $game_id = $data['game_id'];
        $isset_first = $data['isset_first'];
        $isset_second = $data['isset_second'];
        $restricted_list_1 = $data['restricted_list_1'];
        $restricted_list_2 = $data['restricted_list_2'];
    
        if (empty($data['game_id']) || $data['isset_first'] == null || $data['isset_second'] == null) {
            echo json_encode(['code' => 0, 'msg' => '参数错误！']);
            exit;
        }
    
        // 过滤没有选择限制渠道名单
        $firstStr = true;                   // 有首充限制名单
        $secondtStr = true;                 // 有续充限制名单
        if (strlen($data['restricted_list_1']) == 1) {
            //            unset($data['restricted_list_1']);
            $data['restricted_list_1'] = null;
            $firstStr = false;
        }
        if (strlen($data['restricted_list_2']) == 1) {
            //            unset($data['restricted_list_2']);
            $data['restricted_list_2'] = null;
            $secondtStr = false;
        }
    
        /*
         * 去 渠道游戏点位表（cy_channel_game_point） 进行数据操作
         *  1、获取 游戏id
         *  2、判断【首充】是否允许设置
         *      1）、首充允许配置：
         *          a、无黑名单，不进行操作；
         *          b、有黑名单， 对 黑名单里的 在 cy_channel_game_point 的首充配置 清 0
         *      2）、首充 【 不 】 允许配置：
         *          a、无白名单， 对 cy_channel_game_point 里关于 该游戏的 所有渠道首充清0  ；
         *          b、有白名单， 对 白名单外的 在 cy_channel_game_point 的首充配置 清 0
         *  3、判断【续充】是否允许：判断规则如上
         *
         * */
    
        // 判断【首充】是否允许设置
        $map1 = ['game_id' => $game_id];    // 查询条件
        $where1 = [];                       // 要修改的数据
        if ($isset_first) { // 允许
            // 判断有没有黑名单
            if ($firstStr) {
                $where1 = ['first_point' => 0];
                $map1['channel_id'] = ['IN', $restricted_list_1];
            }
        } else {
            $where1 = ['first_point' => 0];
            // 不允许设置首充，那么判断有没有 白名单
            if ($firstStr) {
                $map1['channel_id'] = ['NOT IN', $restricted_list_1];
            }
        }
    
        // 判断 【续充】是否允许设置
        $map2 = ['game_id' => $game_id];    // 查询条件
        $where2 = [];                       // 要修改的数据
        if ($isset_second) { // 允许
            // 判断有没有黑名单
            if ($secondtStr) {
                $where2 = ['point' => 0];
                $map2['channel_id'] = ['IN', $restricted_list_2];
            }
        } else {
            $where2 = ['point' => 0];
            // 不允许设置续充，那么判断有没有 白名单
            if ($secondtStr) {
                $map2['channel_id'] = ['NOT IN', $restricted_list_2];
            }
        }
    
        //        DB::startTrans();
    
        // 判断是否必要对 cy_channel_game_point 的首充 进行操作
        $res1 = true;
        if (count($where1)) {
            $res1 = model('ChannelGamePoint')->allowField(true)->save($where1, $map1);
        }
    
        $res = model("GameChannelPointset")->add($data);
    
        // 判断是否必要对 cy_channel_game_point 的 续充 进行操作
        $res2 = true;
        if (count($where2)) {
            //            $res2 = model('ChannelGamePoint')->allowField(true)->save($where2,$map2);
            $res2 = Db::table('cy_channel_game_point')->where($map2)->update($where2);
        }
    
        $resArr = ['code' => 1, 'msg' => '设置成功！'];
        //        if( $res1 && $res2){
        //            //DB::commit();
        //            $resArr = ['code'=>1,'msg'=>'设置成功！'];
        //        }else{
        //            //DB::rollback();
        //            $resArr = ['code'=>0,'msg'=>'设置失败，请检查数据！'];
        //        }
        echo json_encode($resArr);
    }
    
    /**
     * ajax获取一级渠道列表
     */
    public function getChannelByOne()
    {
        $parent_id = config('TOP_CHANNEL_ID');
        $channel_list = model('Common/Channel')->where('parent_id', "{$parent_id}")->select();
        $result = [];
        foreach ($channel_list as $v) {
            $result[$v['id']] = $v['name'];
        }
        echo json_encode($result);
    }
    
    /**
     * 加载游戏补点页面
     */
    public function fillPoint()
    {
        $model = model('GameExtraPoint');
        $list = $model->field('id,year_month,add_user,add_time,update_time')->order("year_month DESC")->where([])->paginate(10, false, ['query' => input('get.')]);
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch('fillPoint');
    }
    
    /**
     * 新增游戏补点月份
     */
    public function addFillPointMonth()
    {
        $date_c = input('post.date_c', '', 'filterAndTrimInput');
    
        if (empty($date_c)) {
            $this->error('请选择月份');
        }
    
        // 查找一下看是否已经添加了 这个月份的
        $res_1 = model("GameExtraPoint")->where(['year_month' => $date_c])->count('id');
        if ($res_1) {
            $this->error('每月只可添加一条记录，不可重复');
        }
        $res = model("GameExtraPoint")->add(['year_month' => $date_c, 'add_user' => session('USERNAME'), 'add_time' => time()]);
        if ($res) {
            $where['id'] = $res;
            $this->insertLog($this->current_node, '添加游戏补点月份：' . $date_c, 14);
            $this->success('设置完成');
        } else {
            $this->error('每月只可添加一条记录，不可重复');
        }
    }
    
    /**
     * 加载补点详情页
     */
    public function fillPointDetails()
    {
        $monthid = $this->request->param('monthid');
        $game_id = $this->request->param('game_id');
        $status = $this->request->param('status');
        $type = $this->request->param('type');
        $department_id = $this->request->param('channel_id');
        /*$ID            = $this->request->param('ID');*/
        $is_download = $this->request->param('down');
    
        //查询条件设置
        if (!empty($game_id)) {
            $condition['a.game_id'] = array('eq', $game_id);
        }
        if (!empty($type)) {
            $condition['a.type'] = array('eq', $type);
        }
        if ($status != '') {
            if ($status == 3) {
                $condition['a.status'] = array('in', '3,5');
            } else {
                $condition['a.status'] = array('eq', $status);
            }
        }
        if (!empty($department_id)) {
            $condition['a.channel_id'] = array('eq', $department_id);
        }
        /*if(!empty($ID)) {
            $condition['a.id'] = array('eq', $ID);
        }*/
        if (!empty($monthid)) {
            $condition['a.year_month'] = array('eq', $monthid);
            session('year_month', $monthid);
        } else {
            $monthid = session('year_month');
            $condition['a.year_month'] = array('eq', $monthid);
        }
    
        // 查询条件存session 留着下载报表的时候用
        session('fillPointDownCondition', $condition);
        //列表数据整理
        $list = model('GameExtraPointRatio')
            ->alias('a')
            ->join("nw_channel b", "b.id=a.channel_id", "left")
            ->join("cy_game c", "c.id=a.game_id", 'left')
            ->field('a.*, b.name channel_name, c.name game_name')
            ->order("a.create_time desc , a.id desc")
            ->where($condition)
            ->paginate(10, false, ['query' => input('get.')]);
    
        $this->assign('list', $list);
        $this->assign('status', $status);
        $this->assign('game_id', $game_id);
        $this->assign('channel_id', $department_id);
        /*$this->assign('ID', $ID);*/
        $this->assign('YearMonth', session('year_month'));
        $this->assign('page', $list->render());
        $gameList = $this->cyModel('game')->field('id,name')->select();
        $this->assign('game_list', $gameList);
    
        return $this->fetch('fillPointDetails');
    
    }
    
    /**
     * 下载补点详情页的报表
     */
    public function fillPointDetailsDown()
    {
    
        //1.从数据库中取出数据
        $condition = session('fillPointDownCondition');
        $list = model('GameExtraPointRatio')
            ->alias('a')
            ->join("nw_channel b", "b.id=a.channel_id", "left")
            ->join("cy_game c", "c.id=a.game_id", 'left')
            ->field('a.*, b.name channel_name, c.name game_name')
            ->order("a.add_time", " desc")
            ->where($condition)
            ->select();
    
        // 判断是否有数据
        if (count($list)) {
            Vendor('PHPExcel.PHPExcel');//调用类库,路径是基于vendor文件夹的
            Vendor('PHPExcel.PHPExcel.Worksheet.Drawing');
            Vendor('PHPExcel.PHPExcel.Writer.Excel2007');
            $objExcel = new \PHPExcel();
            //set document Property
            $objWriter = \PHPExcel_IOFactory::createWriter($objExcel, 'Excel2007');
    
            $objActSheet = $objExcel->getActiveSheet();
            $key = ord("A");
            $letter = explode(',', "A,B,C,D,E,F,G,H,I,J,K,L");
            $arrHeader = array('ID', '开始时间', '结束时间', '游戏', '渠道', '首充补点', '续充补点', '自然补点', '自然返利', '备注', '添加时间', '状态');;
            //填充表头信息
            $lenth = count($arrHeader);
            for ($i = 0; $i < $lenth; $i++) {
                $objActSheet->setCellValue("$letter[$i]1", "$arrHeader[$i]");
            };
            //填充表格信息
            foreach ($list as $k => $v) {
                $k += 2;
                $objActSheet->setCellValue('A' . $k, $v['id']);
                $objActSheet->setCellValue('B' . $k, date('Y-m-d', $v['begin_date']));
                // // 图片生成
                // $objDrawing[$k] = new \PHPExcel_Worksheet_Drawing();
                // $objDrawing[$k]->setPath('public/static/admin/images/profile_small.jpg');
                // // 设置宽度高度
                // $objDrawing[$k]->setHeight(40);//照片高度
                // $objDrawing[$k]->setWidth(40); //照片宽度
                // /*设置图片要插入的单元格*/
                // $objDrawing[$k]->setCoordinates('C'.$k);
                // // 图片偏移距离
                // $objDrawing[$k]->setOffsetX(30);
                // $objDrawing[$k]->setOffsetY(12);
                // $objDrawing[$k]->setWorksheet($objPHPExcel->getActiveSheet());
                // 表格内容
                $objActSheet->setCellValue('C' . $k, date('Y-m-d', $v['end_date']));
                $objActSheet->setCellValue('D' . $k, $v['game_name']);
                $objActSheet->setCellValue('E' . $k, $v['channel_name']);
                $objActSheet->setCellValue('F' . $k, $v['first_point'] . '%');
                $objActSheet->setCellValue('G' . $k, $v['common_point'] . '%');
                $objActSheet->setCellValue('H' . $k, $v['cash_point'] . '%');
                $objActSheet->setCellValue('I' . $k, $v['cash_profit_point'] . '%');
                $objActSheet->setCellValue('J' . $k, $v['remark']);
                $objActSheet->setCellValue('K' . $k, date('Y-m-d H:i:s', $v['add_time']));
                $statusStr = '已过期';
                if ($v['status']) {
                    $statusStr = '已审核';
                }
                $objActSheet->setCellValue('L' . $k, $statusStr);
                // 表格高度
                $objActSheet->getRowDimension($k)->setRowHeight(20);
            }
    
            $width = array(10, 20, 15, 10, 10, 30, 10, 15, 15, 20, 20);
            //设置表格的宽度
            $objActSheet->getColumnDimension('A')->setWidth($width[0]);
            $objActSheet->getColumnDimension('B')->setWidth($width[1]);
            $objActSheet->getColumnDimension('C')->setWidth($width[1]);
            $objActSheet->getColumnDimension('D')->setWidth($width[1]);
            $objActSheet->getColumnDimension('E')->setWidth($width[1]);
            $objActSheet->getColumnDimension('F')->setWidth($width[2]);
            $objActSheet->getColumnDimension('G')->setWidth($width[2]);
            $objActSheet->getColumnDimension('H')->setWidth($width[2]);
            $objActSheet->getColumnDimension('I')->setWidth($width[2]);
            $objActSheet->getColumnDimension('J')->setWidth($width[5]);
            $objActSheet->getColumnDimension('K')->setWidth($width[1]);
            $objActSheet->getColumnDimension('L')->setWidth($width[5]);
    
            $YM = str_replace('-', '_', session('year_month'));
            $outfile = '游戏补点详情_' . $YM . ".xls";
            ob_end_clean();
            header("Content-Type: application/force-download");
            header("Content-Type: application/octet-stream");
            header("Content-Type: application/download");
            header('Content-Disposition:inline;filename="' . $outfile . '"');
            header("Content-Transfer-Encoding: binary");
            header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
            header("Pragma: no-cache");
            $objWriter->save('php://output');
    
            $this->success('下载成功！');
        } else {
            $monthid = session('year_month');
            //            dump(url('fillPointDetails',['monthid'=>$monthid]));
            $this->error('查无数据，不支持下载', url('fillPointDetails', ['monthid' => $monthid]));
            //            $this->error('查无数据，不支持下载');
        }
    }
    
    /**
     * 补点详情--改变状态
     */
    public function changePointRatioStatus()
    {
        if (!request()->isAjax()) {
            return;
        }
        $runnable = true;
        $msg = '';
        $monthid = session('year_month');
        $status = $this->request->param('status');
        $id = $this->request->param('dataid');
        $departid = $this->request->param('departid');
        $game_id = $this->request->param('game_id');
    
        if (empty($id) || ($status != '0' && $status != '1') || empty($departid) || empty($game_id)) {
            $runnable = false;
            $code = 0;
            $msg = '参数错误';
        }
        if ($runnable) {
    
            // 补点记录改为已审核，和已审核的补点进行时间、渠道的判断，看有无冲突
            if ($status != '0') {
                $moder_PR = model('GameExtraPointRatio');
                $map_2 = [
                    'status' => ['eq', 1],
                    'game_id' => ['eq', $game_id],
                    'year_month' => ['eq', session('year_month')]
                ];
                $pdList = $moder_PR->where($map_2)->field('id,game_id,channel_id,begin_date,end_date')->select();
    
                // 查有该游戏，可以进行对比判断
                if (count($pdList)) {
                    // 1、判断时间有没有冲突
                    $thisGame = $moder_PR->where("id=" . $id)->field('id,game_id,channel_id,begin_date,end_date')->find();
                    $topDP = model('Department')->where('id=' . $departid)->field('id,id_path')->find();
                    $map_1 = [];
                    $map_1 = [
                        'id_path' => ['like', '%,' . $departid . ',%']
                    ];
                    $downDP_1 = model('Department')->where($map_1)->field('id')->select();
                    $downDP = '';
                    foreach ($downDP_1 as $key => $value) {
                        $downDP .= ',' . $value['id'] . ',';
                    }
    
    
                    foreach ($pdList as $v_pdlist) {
                        // 只要 $thisGame 的开始时间或结束时间 在对方的开始时间和结束时间之间，哪就发生了时间冲突
    
                        // 2、判断渠道有没有冲突
                        // 2.1判断上级渠道有没有冲突
                        if (strpos($topDP['id_path'], ',' . $v_pdlist['channel_id'] . ',') !== false) {
                            $runnable = false;
                            $status = false;
                            $msg = '上级渠道已经添加补点信息，冲突ID：' . $v_pdlist['id'];
                            break;
                        }
    
                        // 2.2判断下级渠道有没有冲突
                        $downDP = str_replace($departid, ",", $downDP);
    
                        if (strpos($downDP, ',' . $v_pdlist['channel_id'] . ',') !== false) {
                            $runnable = false;
                            $status = false;
                            $msg = '下级渠道已经添加补点信息，冲突ID：' . $v_pdlist['id'];
                            break;
                        }
    
                        if ((($thisGame['begin_date'] >= $v_pdlist['begin_date'] && $thisGame['begin_date'] < $v_pdlist['end_date']) || ($thisGame['end_date'] > $v_pdlist['begin_date'] && $thisGame['end_date'] <= $v_pdlist['end_date']) || ($thisGame['begin_date'] < $v_pdlist['begin_date'] && $thisGame['end_date'] > $v_pdlist['end_date'])) && $thisGame['channel_id'] == $v_pdlist['channel_id']) {
                            // 1.如果游戏相同，渠道相同，时间冲突，提示“补点时间有冲突，冲突ID：xxx”；
                            $runnable = false;
                            $status = false;
                            $msg = '补点时间有冲突，冲突ID：' . $v_pdlist['id'];
                            break;
                        }
    
                    }
    
                }
            }
    
            if ($runnable) {
                // 开启事物
                Db::startTrans();
                $result = model('GameExtraPointRatio')->save(['status' => $status, 'update_time' => time(), 'update_user' => session('ADMIN_ID')], ['id' => $id]);
    
                $map['year_month'] = $monthid;
                $res = model('GameExtraPoint')->save(['update_user' => session('ADMIN_ID'), 'update_time' => time()], $map);
    
                if (empty($result) || empty($res)) {
                    $runnable = false;
                    $msg = '操作失败';
                    Db::rollback();
                } else {
                    //$this->insertLog( session('username'), 3, MODULE_NAME, ACTION_NAME, NOW_TIMESTAMP, '更新了游戏补点月表一条记录，状态改为了：'. $status,session_id());
                    $statusStr = '已过期';
                    if ($status) {
                        $statusStr = '已审核';
                    }
                    // 获取游戏名和渠道名
                    $channel_obj = model('Department')->getCondition(['id' => $departid]);
                    $Game_name = model('Game')->where(['id' => $game_id])->field('name')->find();
    
                    $this->insertLog($this->current_node, '修改补点状态，ID：' . $id . '，游戏名：' . $Game_name['name'] . '，渠道名：' . $channel_obj['name'] . '，状态：' . $statusStr, 14);
                    $status = true;
                    $msg = '操作成功';
                    Db::commit();
                }
            }
        }
        $code = 1;
        if (!$status) {
            $code = 0;
        }
        $this->result([], $code, $msg);
    }
    
    /**
     * 加载添加游戏补点页面
     */
    public function fillPointAddPage()
    {
        $this->assign('gamelist', $this->cyModel('game')->field('id,name')->select());
        $channelList = model('Channel')->getAllByCondition('id,name', ['status' => 1, 'flag' => 3]);
        $this->assign('departmentlist', $channelList);
        $monthid = session('year_month');
        $this->assign('YearMonth', $monthid);
        return $this->fetch('fillPointAddPage');
    }
    
    /**
     * 添加游戏补点
     */
    public function batchAddPointList()
    {
        $data['game_id'] = $this->request->param('game_id', 0, 'intval');
        $data['channel_id'] = $this->request->param('channel_id', 0, 'intval');
        $data['type'] = $this->request->param('type', 0, 'intval');
        $datarange = $this->request->param('datarange', '', 'trim');
        $time_line = strtotime(date('Y-m-d', time()));
    
        if (empty($data['game_id'])) {
            $this->error('游戏不能为空！');
        }
        if (empty($data['channel_id'])) {
            $this->error('渠道不能为空！');
        }
        if (empty($datarange)) {
            $this->error('生效时间不能为空！');
        }
    
        $data['begin_time'] = strtotime($datarange);
        if ($time_line > $data['begin_time']) {
            $this->error('生效时间不得小于当天0点！');
        }
    
    
        // 按结算方式获取 参数
        if (empty($data['type'])) {
            $this->error('结算类型不能为空！');
        } elseif ($data['type'] == 1) {
            // 折扣结算
            $data['first_point'] = $this->request->param('first_point');
            $data['common_point'] = $this->request->param('common_point');
            $data['extra_point'] = $this->request->param('extra_point');
            $data['extra_profit_point'] = $this->request->param('extra_profit_point');
    
            $this->numPD($data['first_point'], '请输入正确的首充补点!', '0<=首充补点<100，保留两位小数');
            $this->numPD($data['common_point'], '请输入正确的续充补点！', '0<=续充补点<100，保留两位小数');
            $this->numPD($data['extra_profit_point'], '请输入正确的充值返利！', '0<=充值返利<100，保留两位小数');
            $this->numPD($data['extra_point'], '请输入正确的充值补点！', '0<=充值补点<100，保留两位小数');
    
        } elseif ($data['type'] == 2) {
            $data['extra_point'] = $this->request->param('extra_point');
            $data['extra_profit_point'] = $this->request->param('extra_profit_point');
    
            $this->numPD($data['extra_profit_point'], '请输入正确的充值总额返利！', '0<=充值总额返利<100，保留两位小数');
            $this->numPD($data['extra_point'], '请输入正确的充值总额补点！', '0<=充值总额补点<100，保留两位小数');
    
        } elseif ($data['type'] == 3) {
            $data['first_point'] = $this->request->param('first_point');
            $data['common_point'] = $this->request->param('common_point');
            $data['extra_point'] = $this->request->param('extra_point');
            $data['extra_profit_point'] = $this->request->param('extra_profit_point');
    
            $first_date = $this->request->param('sc_supply_date', '', 'trim');
    
            $this->numPD($data['first_point'], '请输入正确的首充补点!', '0<=首充补点<100，保留两位小数');
            $this->numPD($data['common_point'], '请输入正确的续充补点！', '0<=续充补点<100，保留两位小数');
            $this->numPD($data['extra_profit_point'], '请输入正确的充值返利！', '0<=充值返利<100，保留两位小数');
            $this->numPD($data['extra_point'], '请输入正确的充值补点！', '0<=充值补点<100，保留两位小数');
    
            if (empty($first_date)) {
                $this->error('请选择首充补点时间区间！');
            }
    
            /*$first_date = '2019-04-15 00:00:00 - 2019-05-15 00:00:00';
            $common_date = '2019-04-15 00:20:00 - 2019-05-15 00:00:00';*/
    
            $first_date_arr = explode(' - ', $first_date);
            $data['first_begin_time'] = strtotime($first_date_arr[0]);
            if ($time_line > $data['first_begin_time']) {
                $this->error('首充补点时间不得小于当天0点！');
            }
            $data['first_end_time'] = strtotime($first_date_arr[1]);
    
    
        } elseif ($data['type'] == 4) {
            $data['first_point'] = $this->request->param('first_point');
            $data['common_point'] = $this->request->param('common_point');
            $data['extra_profit_point'] = $this->request->param('extra_profit_point');
    
            $this->numPD($data['first_point'], '请输入正确的首充补点!', '0<=首充补点<100，保留两位小数');
            $this->numPD($data['common_point'], '请输入正确的续充补点！', '0<=续充补点<100，保留两位小数');
            $this->numPD($data['extra_profit_point'], '请输入正确的充值总额返利！', '0<=充值总额返利<100，保留两位小数');
    
            $first_date = $this->request->param('sc_supply_date', '', 'trim');
    
    
            /*$first_date = '2019-04-15 00:00:00 - 2019-05-15 00:00:00';
            $common_date = '2019-04-15 00:20:00 - 2019-05-15 00:00:00';
            $profit_date = '2019-04-15 00:20:00 - 2019-05-15 00:00:00';*/
    
            $first_date_arr = explode(' - ', $first_date);
            $data['first_begin_time'] = strtotime($first_date_arr[0]);
            if ($time_line > $data['first_begin_time']) {
                $this->error('首充补点时间不得小于当天0点！');
            }
            $data['first_end_time'] = strtotime($first_date_arr[1]);
    
        } else {
            $this->error('请选择正确的结算类型');
        }
    
        $data['add_user'] = session('ADMIN_ID');
        $data['year_month'] = session('year_month');
        $data['create_time'] = time();
    
        $GameExtraPointObj = model('GameExtraPoint')->where(['year_month' => session('year_month')])->field('id')->find();
        $data['game_extra_point_id'] = $GameExtraPointObj['id'];
    
        // 判断 同游戏 同月份 的是否有上下级渠道
        $numArr = model('GameExtraPointRatio')
            ->where(['game_id' => $data['game_id'], 'year_month' => $data['year_month'], 'channel_id' => ['neq', $data['channel_id']], 'game_extra_point_id' => $data['game_extra_point_id']])->column('channel_id');
    
        if (count($numArr) > 0) {
            $channelArr = array_unique($numArr);
    
            // 获取当前渠道下的所有子渠道
            $childids = model('Channel')->getChildIds($data['channel_id']);
            // 获取 父级部门ID路径 有当前渠道 的所有渠道
            $channel_aOrc = model('Channel')->where(['id' => $data['channel_id']])->column('id_path');
            $channel_aOrc_arr = [];
            foreach ($channel_aOrc as $v) {
                $arr = [];
                $arr = explode(',', $v);
                $channel_aOrc_arr = array_unique(array_merge($arr, $channel_aOrc_arr));
            }
    
            $channel_all = array_unique(array_merge($channel_aOrc_arr, $childids));
    
            // 判断查询出来的  $channelArr  有没有在 当前渠道的归属渠道里（上下级）
            foreach ($channelArr as $value) {
                if (in_array($value, $channel_all)) {
                    $gameName = model('Game')->getName($data['game_id']);
                    $channelName = model('Channel')->where(['id' => $value])->value('name');
                    $this->error('《' . $gameName . '》同归属渠道“' . $channelName . '”已配置补点信息，请核实确认');
                }
            }
            //$this->result(['当前渠道'=>$data['channel_id'],'列表渠道'=>$channelArr,'子渠道'=>$childids,'所有'=>$channel_all,'上级'=>$channel_aOrc_arr,'数量'=>count($channel_all)]);
        }
    
        // 存数据
        $res = model('GameExtraPointRatio')->add($data);
    
        if ($res) {
            $this->success('保存成功', 'fillPointDetails');
        }
    
        $this->error('保存失败');
    }
    
    /**
     * 判断数字是否在指定区间内，两位小数
     *
     * @param        $first_point
     * @param string $msg1
     * @param string $msg2
     * @param int    $mix
     * @param int    $max
     */
    public function numPD($num, $msg1 = '', $msg2 = '', $mix = 0, $max = 100)
    {
        if (is_numeric($num)) {
            $first_point_1 = (float)$num * 100;
            $res_num = $first_point_1 - (float)((int)$first_point_1);
    
            if ($first_point_1 < $mix || $first_point_1 >= $max * 100 || $res_num > 1e-7) {
                $this->result([], 0, $msg2);
            }
        } else {
            $this->result([], 0, $msg1);
        }
    }
    
    /**
     * 游戏冻结信息列表
     */
    public function bannedList()
    {
        $banned = new GameBanned;
    
        $where = [];
    
        //游戏名称
        if (input('request.game_id') != '') {
            $where['n.game_id'] = input('request.game_id', '', 'trim');
        }
    
        //标题
        if (input('request.banned_device') != '') {
            $where['n.banned_device'] = input('request.banned_device', '', 'trim');
        }
    
        //冻结类型
        if (input('request.banned_type') != '') {
            $where['n.banned_type'] = input('request.banned_type', '', 'trim');
        }
    
        $start = $this->request->param('start');
        $end = $this->request->param('end');
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $where['n.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $where['n.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $where['n.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
            /*
            $start = date('Y-m-d', time());
            $end   = date('Y-m-d', time());
    
            $where['n.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
            */
        }
    
        //查询参数
        $param = input('get.');
    
        $banned_list = $banned->alias('n')->field('n.*,g.name game_name')->join('cy_game g', 'g.id=n.game_id', 'left')
            ->where($where)->order('n.create_time desc')
            ->paginate(10, false, array('query' => $param));
    
        //	echo $banned->getLastSql()."----getLastSql------<Br>";
        $this->assign('banned_list', $banned_list);
        $this->assign('page', $banned_list->render());
    
        $gameList = $this->selfGameList;
        $gameList[0] = array('id' => 0, 'name' => '所有游戏');
        $this->assign('gameList', $gameList);
    
        return $this->fetch('banned_list');
    }
    
    /**
     * 删除游戏冻结信息
     *
     */
    public function bannedDelete()
    {
        $id = input('id', 0, 'intval');
    
        if (empty($id)) $this->error('删除记录的ID不能为空');
    
        $banned = new GameBanned;
    
        if ($banned->where(['id' => $id])->delete()) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 新增游戏冻结信息
     */
    public function bannedCreate()
    {
        if (request()->isPost()) {
            $banned = new GameBanned;
            $data = [
                'game_id' => input('post.game_id', '', 'trim'),
                'banned_type' => input('post.banned_type', '', 'trim'),
                'banned_device' => input('post.banned_device', '', 'trim'),
                'notes' => input('post.notes', ''),
            ];
    
            $result = $this->validate($data, [
                ['game_id', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['banned_type', 'require', '请选择冻结类型'],
                ['banned_device', 'require', '请输入IP或IMEI'],
            ]);
    
            if (true !== $result) {
                $this->error($result);
            }
    
            //检查是否已存在记录
            $condis = array();
            $condis['game_id'] = intval($data['game_id']);
            $condis['banned_type'] = $data['banned_type'];
            $condis['banned_device'] = $data['banned_device'];
            $bannedInfo = $banned->where($condis)->find();
            if (!empty($bannedInfo)) {
                $this->error('已添加过该冻结信息！');
            }
    
            $data['create_time'] = time();
            $data['update_time'] = time();
            if ($banned->insert($data)){
                $this->success('新增成功', 'game/bannedList');
            } else {
                $this->error('新增失败');
            }
        }
    
        $gameList = $this->selfGameList;
        $gameList[0] = array('id' => 0, 'name' => '所有游戏');
        $this->assign('game_list', $gameList);
    
        return $this->fetch('banned_new');
    }
    
    /**
     * 编辑游戏冻结信息
     */
    public function bannedEdit()
    {
        $id = input('id', 0, 'intval');
        $bannedModel = new GameBanned;
        if (empty($id)) $this->error('游戏冻结的ID不能为空');
    
        $bannedInfo = $bannedModel->where(['id' => $id])->find();
    
        if (empty($bannedInfo)) $this->error('记录不存在');
    
        if (request()->isPost()) {
            $banned = new GameBanned;
            $data = [
                'game_id' => input('post.game_id', '', 'intval'),
                'banned_type' => input('post.banned_type'),
                'banned_device' => input('post.banned_device'),
                'notes' => input('post.notes', ''),
            ];
    
            $result = $this->validate($data, [
                ['game_id', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['banned_type', 'require', '请选择冻结类型'],
                ['banned_device', 'require', '请输入IP或IMEI'],
            ]);
    
            if (true !== $result) {
                $this->error($result);
            }
    
            //检查是否已存在记录
            $condis = array();
            $condis['game_id'] = intval($data['game_id']);
            $condis['banned_type'] = $data['banned_type'];
            $condis['banned_device'] = $data['banned_device'];
            $condis['id'] = array('neq', $id);
            $bannedInfo = model('GameBanned')->where($condis)->find();
            if (!empty($bannedInfo)) {
                $this->error('已存在该冻结信息！');
            }
            if ($bannedModel->update($data, ['id' => $id])) {
                $this->success('编辑成功', 'game/bannedList');
            } else
                $this->error('编辑失败');
        }
    
        $game_list = array(array('id' => 0, 'name' => '所有游戏'));
        $game_list = array_merge($game_list, $this->selfGameList);
        $this->assign('game_list', $game_list);
        $this->assign('data', $bannedInfo);
    
        return $this->fetch('banned_edit');
    }
    
    /**
     * 点位提审
     */
    public function examinePoint()
    {
        $id = $this->request->param('id', 0, 'intval');
        $countType = [
            '1' => '折扣类结算',
            '2' => '自然类结算',
            '3' => '首充补点结算',
            '4' => '新用户补点结算',
        ];
        if (empty($id)) {
            $this->error('参数错误！');
        }
    
        $info = model('GameExtraPointRatio')->alias('a')
            ->join('cy_game b', 'b.id = a.game_id')
            ->join('nw_channel c', 'c.id = a.channel_id')
            ->field('a.*, c.name channel_name, b.name game_name')
            ->where('a.id=' . $id)->find();
        // 同游戏通渠道 至多只能有一条数据 在审核当中
        $numInfo = model('GameExtraPointRatio')
            ->where(['game_id' => $info['game_id'], 'year_month' => $info['year_month'], 'channel_id' => $info['channel_id'], 'game_extra_point_id' => $info['game_extra_point_id'], 'status' => 1])->select();
        if (count($numInfo) >= 1) {
            $this->error('《' . $info['game_name'] . '》《' . $info['channel_name'] . '》已有结算补点审核中，请联系上级确认。');
        }
        $today = strtotime(date('Y-m-d')); // 今日 0点0分 时间戳
        if ($today > $info['begin_time']) {
            $data = ['status' => 5, 'update_user' => session('ADMIN_ID'), 'update_time' => time()];
            model('GameExtraPointRatio')->save($data, ['id' => $id]);
            $this->success('《' . $info['game_name'] . '》《' . $info['channel_name'] . '》“' . $countType[$info['type']] . '”生效时间小于当日时间，无法通过。');
        }
    
        $data = ['status' => 1, 'update_user' => session('ADMIN_ID'), 'update_time' => time()];
        $res = model('GameExtraPointRatio')->save($data, ['id' => $id]);
    
        if ($res) {
            $this->success('操作成功！');
        }
        $this->error('操作失败');
    }
    
    /**
     * 点位审核
     */
    public function examineAction()
    {
        $id = $this->request->param('id', 0, 'intval');
        $type = $this->request->param('type', '', 'trim');
        $remark = $this->request->param('remark', '', 'trim');
        $countType = [
            '1' => '折扣类结算',
            '2' => '自然类结算',
            '3' => '首充补点结算',
            '4' => '新用户补点结算',
        ];
        if (empty($id) || empty($type)) {
            $this->error('参数错误！');
        }
    
        if (!in_array($type, ['refuse', 'pass'])) {
            $this->error('参数错误！');
        }
    
        // 拒绝
        if ($type == 'refuse') {
            $data = ['status' => 4, 'update_user' => session('ADMIN_ID'), 'update_time' => time(), 'remark' => $remark];
            model('GameExtraPointRatio')->save($data, ['id' => $id]);
            $this->success('操作成功！');
        }
    
    
        // 判断 同游戏 同月份 的是否有上下级渠道
        $info = model('GameExtraPointRatio')->where('id=' . $id)->find();
        $numArr = model('GameExtraPointRatio')
            ->where(['game_id' => $info['game_id'], 'year_month' => $info['year_month'], 'channel_id' => ['neq', $info['channel_id']], 'game_extra_point_id' => $info['game_extra_point_id']])->column('channel_id');
    
        if (count($numArr) > 0) {
            $channelArr = array_unique($numArr);
    
            // 获取当前渠道下的所有子渠道
            $childids = model('Channel')->getChildIds($info['channel_id']);
            // 获取 父级部门ID路径 有当前渠道 的所有渠道
            $channel_aOrc = model('Channel')->where(['id' => $info['channel_id']])->column('id_path');
            $channel_aOrc_arr = [];
            foreach ($channel_aOrc as $v) {
                $arr = [];
                $arr = explode(',', $v);
                $channel_aOrc_arr = array_unique(array_merge($arr, $channel_aOrc_arr));
            }
    
            $channel_all = array_unique(array_merge($channel_aOrc_arr, $childids));
    
            // 判断查询出来的  $channelArr  有没有在 当前渠道的归属渠道里（上下级）
            foreach ($channelArr as $value) {
                if (in_array($value, $channel_all)) {
                    $gameName = model('Game')->getName($info['game_id']);
                    $channelName = model('Channel')->where(['id' => $value])->value('name');
                    $this->error('《' . $gameName . '》同归属渠道“' . $channelName . '”已配置补点信息，请核实确认');
                }
            }
        }
    
        // 通过的判断----
        $info = model('GameExtraPointRatio')->alias('a')
            ->join('cy_game b', 'b.id = a.game_id')
            ->join('nw_channel c', 'c.id = a.channel_id')
            ->field('a.*, c.name channel_name, b.name game_name')
            ->where('a.id=' . $id)->find();
        // 通过审核 1、判断生效时间是否大于今天0点0分
        $today = strtotime(date('Y-m-d')); // 今日 0点0分 时间戳
        if ($today > $info['begin_time']) {
            $data = ['status' => 5, 'update_user' => session('ADMIN_ID'), 'update_time' => time(), 'remark' => $remark];
            model('GameExtraPointRatio')->save($data, ['id' => $id]);
            $this->success('《' . $info['game_name'] . '》《' . $info['channel_name'] . '》“' . $countType[$info['type']] . '”生效时间小于当日时间，无法通过。');
        }
        // 判断当前同游戏通渠道同结算模式生效的规则 的生效时间 和 审核的规则的生效时间 大小关系（ 若审核的 小于 生效的 ，则生效的规则状态变为5）（ $nowInfo 不为空表示有历史记录 ）
        $nowInfo = model('GameExtraPointRatio')->where(['game_id' => $info['game_id'], 'year_month' => $info['year_month'], 'channel_id' => $info['channel_id'], 'game_extra_point_id' => $info['game_extra_point_id'], 'status' => 2])->find();
    
        // 不存在覆盖，直接生效
        if (!$nowInfo) {
            $data = ['status' => 2, 'update_user' => session('ADMIN_ID'), 'update_time' => time(), 'remark' => $remark];
            model('GameExtraPointRatio')->save($data, ['id' => $id]);
            $this->success('操作成功！');
        }
    
        if ($nowInfo['begin_time'] < $info['begin_time']) {
            $data_1 = ['status' => 3, 'update_user' => session('ADMIN_ID'), 'update_time' => time(), 'end_time' => $info['begin_time'] - 1];
        } else {
            $data_1 = ['status' => 5, 'update_user' => session('ADMIN_ID'), 'update_time' => time()];
        }
    
        // session 暂时保存 一下 $data_1，用于前端确认覆盖后的SQL条件
        session('examineAction_data', ['data' => $data_1, 'id' => $nowInfo['id']]);
        // 提示  《游戏名》《渠道名》“结算类型”“生效时间”已存在是否覆盖该信息。
        $msg = '《' . $info['game_name'] . '》《' . $info['channel_name'] . '》“' . $countType[$info['type']] . '”已存在是否覆盖该信息？';
        $this->result([], 2, $msg);
    
        /*Db::startTrans();
        try{
            model('GameExtraPointRatio')->save($data_1,['id'=>$nowInfo['id']]);
            $data = ['status'=>2,'update_user'=>session('ADMIN_ID'),'update_time'=>time(),'remark'=>$remark];
            model('GameExtraPointRatio')->save($data,['id'=>$id]);
            // 提交事务
            Db::commit();
            $this->success('操作成功！');
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error('操作失败');
        }*/
    }
    
    /**
     * 二次确认后，游戏点位审核通过
     */
    public function examineActionDo()
    {
        $id = $this->request->param('id', 0, 'intval');
        $remark = $this->request->param('remark', '', 'trim');
    
        if (empty($id)) {
            $this->error('参数错误！');
        }
    
        $data_1 = session('examineAction_data')['data'];
        $nowId = session('examineAction_data')['id'];
    
        Db::startTrans();
        try {
            model('GameExtraPointRatio')->save($data_1, ['id' => $nowId]);
            $data = ['status' => 2, 'update_user' => session('ADMIN_ID'), 'update_time' => time(), 'remark' => $remark];
            model('GameExtraPointRatio')->save($data, ['id' => $id]);
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error('操作失败');
        }
        $this->success('操作成功！');
    }
    
    /**
     * 审核界面
     *
     * @param string $value [description]
     *
     * @return [type]        [description]
     */
    public function examine()
    {
        $applyid = request()->get('applyid');
        $data = Db::name('nw_game_package_apply')->where(['id' => $applyid])->find();
        $this->assign('applyid', $applyid);
        $this->assign('list', $data);
        return $this->fetch();
    }
    
    /**
     * 更改审核状态
     */
    public function doExamine()
    {
        $data = request()->post();
        $status = $data['apply_status'];
        $applyid = $data['applyid'];
        if (empty($status)) {
            $this->error("系统错误");
        }
        $msg = '';
        $res = Db::name('nw_game_package_apply')->where(['id' => $applyid])->find();
        $gameId = $res['game_id'];
        $channel_id = $res['channel_id'];
        $applicant_id = $res['applicant_id'];
    
        //同意审核状态下 分包
        if ($status == 1) {
    
    
            $subPackageInfo = $this->sdkgamelistModel->where(['gameid' => $gameId, 'channel_id' => $channel_id])->find();
            $game_info = GameModel::get($gameId);
            if ($subPackageInfo['upload_status'] == 1) {
                $msg = '分包情况:已经存在分包，请删包后再分包！';
                $code = 400;
            }
    
            if (!empty($subPackageInfo['filename'])) {
                $msg = '分包情况:文件名已生成无法分包,请等待后端分包完成！';
                $code = 400;
            }
    
            $gameInfoModel = new GameInfo;
    
            //游戏平台
            $platform = $gameInfoModel->where(['game_id' => $gameId])->value('platform');
            $platform = ($platform == false) ? 0 : $platform;
            $subPath = $this->subChannel->index($channel_id, $gameId, 'cps' . $applicant_id, $platform, 0);
            //如果有异常 分包任务给sdklist
            $result = Db::name('cy_sdkgamelist')->where(['gameid' => $gameId, 'channel_id' => $channel_id, 'package_type' => 0])->find();
            if (empty($result)) {
                Db::startTrans();
                try {
                    $cont = [
                        'gameid' => $gameId,
                        'channel_id' => $channel_id,
                        'update_time' => time(),
                        'create_time' => time(),
                        'channel_version' => $game_info['channel_version'],
                        'package_type' => 4,
                    ];
                    Db::name('cy_sdkgamelist')->insert($cont);
                    (new PromotionShortLink())->insertData($gameId, $channel_id, 0);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error('游戏信息编辑失败' . $e->getMessage());
                }
            }else{
                Db::name('cy_sdkgamelist')->where(['gameid' => $gameId, 'channel_id' => $channel_id])->update(['upload_status' => 4]);
            }
        }
    
    
        $data['apply_time'] = NOW_TIMESTAMP;
        unset($data['applyid']);
    
        $resultChannel = Db::name('nw_game_package_apply')->where(['id' => $applyid])->update($data);
        if ($resultChannel) {
            if ($status == 1) {
                $this->success("审核成功,已经提交分包任务");
            } else {
                Db::name('cy_sdkgamelist')->where(['gameid' => $gameId, 'channel_id' => $channel_id])->update(['upload_status' => 3]);
                $this->success("已审核完成");
            }
        }
    }
    
    /**
     * 游戏管理下载
     */
    public function gameManagerDown()
    {
        if (request()->isAjax()) {
            $where = $this->_indexParam();
            $num = $this->gameModel->alias('game')
                ->join('cy_gameinfo info', 'game.id = info.game_id', 'left')
                ->join('nw_android_sdk_config sdk', 'game.android_sdk_id = sdk.id', 'left')
                ->where($where)->count('game.id');
    
            if (!$num) {
                $this->error('查无数据，不支持下载');
            }
    
            $sql = $this->gameModel->alias('game')
                ->join('cy_gameinfo info', 'game.id = info.game_id', 'left')
                ->join('nw_android_sdk_config sdk', 'game.android_sdk_id = sdk.id', 'left')
                ->field('game.id,game.name,game.origin_name,game.pinyin,game.cooperation_status,game.create_time,game.remarks,game.is_show,info.platform,sdk.version as sdk_version')
                ->where($where)
                ->order('game.id desc')
                ->fetchSql(true)->select();
    
            if ((new MakeReportGo())->addTask('gameManagerDown', $sql, session_id())) {
                $this->jsonResult('', 1, '报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            } else {
                $this->jsonResult('', 0, '报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }
        $this->error('非法请求');
    }
    
    /**
     * 批量通过
     */
    public function approved()
    {
        $data = request()->post();
        $status = $data['apply_status'];
        $ids = explode(',', $data['ids']);
        unset($data['ids']);
        if (empty($status)) {
            $this->error("系统错误");
        }
        if (empty($ids)) {
            $this->error("请选择记录");
        }
        $msg = '';
    
        foreach ($ids as $k => $v) {
            $res = Db::name('nw_game_package_apply')->where(['id' => $v])->find();
            $gameId = $res['game_id'];
            $channel_id = $res['channel_id'];
            if ($status == 1 && $v > 1) {
                $applicant_id = $res['applicant_id'];
                $subPackageInfo = $this->sdkgamelistModel->where(['gameid' => $gameId, 'channel_id' => $channel_id])->find();
                $game_info = GameModel::get($gameId);
                if ($subPackageInfo['upload_status'] == 1) {
                    $msg = '分包情况:已经存在分包，请删包后再分包！';
                    $code = 400;
                }
    
                if (!empty($subPackageInfo['filename'])) {
                    $msg = '分包情况:文件名已生成无法分包,请等待后端分包完成！';
                    $code = 400;
                }
    
                $gameInfoModel = new GameInfo;
    
                //游戏平台
                $platform = $gameInfoModel->where(['game_id' => $gameId])->value('platform');
                $platform = ($platform == false) ? 0 : $platform;
                $subPath = $this->subChannel->index($channel_id, $gameId, 'cps' . $applicant_id, $platform, 0);
                //如果有异常 分包任务给sdklist
                $result = Db::name('cy_sdkgamelist')->where(['gameid' => $gameId, 'channel_id' => $channel_id, 'package_type' => 0])->find();
    
                if (empty($result)) {
                    Db::startTrans();
                    try {
                        $cont = [
                            'gameid' => $gameId,
                            'channel_id' => $channel_id,
                            'update_time' => time(),
                            'create_time' => time(),
                            'channel_version' => $game_info['channel_version'],
                            'package_type' => 0,
                            'upload_status' => 4
                        ];
                        Db::name('cy_sdkgamelist')->insert($cont);
                        (new PromotionShortLink())->insertData($gameId, $channel_id, 0);
                        Db::commit();
                    } catch (Exception $e) {
                        Db::rollback();
                        $this->error('游戏信息编辑失败' . $e->getMessage());
                    }
                }
            }else{
                $this->sdkgamelistModel->where(['gameid' => $gameId, 'channel_id' => $channel_id])->update(['upload_status' => 3]);
            }
    
            $data['apply_time'] = NOW_TIMESTAMP;
            unset($data['applyid']);
            $resultChannel = Db::name('nw_game_package_apply')->where(['id' => $v])->update($data);
    
    
        }
        if ($status == 1) {
            $this->success("审核成功,已经提交分包任务");
        } else {
            $this->success("已审核完成");
        }
    }
    
    /**
     * 设置游戏归属
     *
     * @return mixed
     * @throws \think\exception\DbException
     */
    public function gameBelong(){
        $param = input('get.');
        
        $list = $this->gameBelongModel->alias('gb')
            ->join('cy_game g', 'gb.game_id = g.id', 'left')
            // , FROM_UNIXTIME(create_time, "%Y-%m-%d %H:%i:%s") as create_time
            ->field('gb.*,g.name,g.cooperation_status')
            ->order('gb.id desc')
            ->paginate(25, false, array('query' => $param));
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        
        return $this->fetch();
    }
    public function gameBelongEdit(){
        
        $id = input('id', '');
        if(request()->isPost()){
            $param = input('post.');
            $result = $this->validate($param, [
                'game_id'  => 'require',
                'start_date' => 'require',
                'end_date' => 'require',
            ], [
                'game_id' => '请选择游戏',
                'start_date' => '开始时间参数有误',
                'end_date' => '结束时间参数有误',
            ]);
            if (true !== $result) {
                $this->error($result);
            }
            if(empty($param['status'])){
                $param['status'] = 0;
            }
            
            $data = [
                'game_id' => $param['game_id'],
                'status' => $param['status']==1?1:0,
                'start_date' => $param['start_date'],
                'end_date' => $param['end_date'],
                'remarks' => $param['remarks'],
            ];
            if($id){
                $res = $this->gameBelongModel->update($data, ['id' => $id]);
            }else{
                if($id && $this->gameBelongModel->where(['game_id' => $param['game_id']])->find()){
                    $this->error("当前游戏已存在！");
                }
                $res = $this->gameBelongModel->create($data);
            }
    
            if($res){
                $this->success("操作成功！", url('game/gameBelong'));
            }
            $this->error("操作失败！");
        }
    
        $info = $this->gameBelongModel->where(['id' => $id])->find();
        $this->assign('info', $info??"");
        
        $tmpGameList = $this->cyModel('game')->field('id,name')->select();
        $gameList = array_column($tmpGameList, 'name', 'id');
        $this->assign('gameList', $gameList);
        return $this->fetch();
    }
    public function gameBelongDelete(){
        
        $id = input('id', '');
        if(!$id){
            $this->error("操作有误！");
        }
        if(!$this->gameBelongModel->where(['id' => $id])->find()){
            $this->error("操作游戏不存在！");
        }
        
        $res = $this->gameBelongModel->where(['id' => $id])->delete();
        if($res){
            $this->success("操作成功！", url('game/gameBelong'));
        }
        
        $this->error("操作有误！");
    }
    
    
    //    /**
    //     * 批量添加游戏补点
    //     */
    //    public function batchAddPointList(){
    //
    //        $listNum           = $this->request->param('listNum');
    //        $listArr           = $_POST;
    //        $begin_date        = [];
    //        $end_date          = [];
    //        $first_point       = [];
    //        $common_point      = [];
    //        $cash_point        = [];
    //        $cash_profit_point = [];
    //        $remark            = [];
    //        $game_id           = [];
    //        $channel_id        = [];
    //        $runable           = true;
    //        $status            = '';
    //        $msg               = '';
    //
    //        for ($i=0; $i<$listNum; $i++){
    //            // 获取补点月 月初 和月末的时间戳
    //            $month_st = strtotime(session('year_month'));
    //
    //            // 时间戳转换 确保 选则的不是今年的时间 也能转为正确的时间戳
    //            $monthArr = explode('-',session('year_month'));
    //            $monthArr[1] = (int)$monthArr[1] +1;
    //            $monthArr[1] = (string)$monthArr[1];
    //            if (strlen($monthArr[1]) < 2 ){
    //                $monthArr[1] = '0'.$monthArr[1];
    //            }
    //            $monthStr = $monthArr[0].$monthArr[1]."01 00:00:00";
    //            $month_end = strtotime($monthStr) -1;
    //
    //            // 开始时间判断
    //            if (empty($listArr['begin_date'][$i])){
    //                $this->result(['i'=>$i],0,'请选择正确的开始时间');
    //                break;
    //            }else{
    //                $listArr['begin_date'][$i] = $listArr['begin_date'][$i].' 00:00:00';
    //                $catimeNew = strtotime($listArr['begin_date'][$i]);
    //
    //                if ($catimeNew < $month_st || $catimeNew > $month_end) {
    //                    $this->result(['i'=>$i],0,'请选择正确的开始时间');
    //                    break;
    //                }
    //                $listArr['begin_date'][$i] = $catimeNew;
    //            }
    //            // 结束时间判断
    //            if (empty($listArr['end_date'][$i])){
    //                $this->result(['i'=>$i],0,'请选择正确的结束时间');
    //                break;
    //            }else{
    //                $listArr['end_date'][$i] = $listArr['end_date'][$i].' 23:59:59';
    //                $entTime = strtotime($listArr['end_date'][$i]);
    //                $listArr['end_date'][$i] = $entTime;
    //
    //                if ($entTime < $month_st || $entTime > $month_end) {
    //                    $this->result(['i'=>$i],0,'请选择正确的结束时间');
    //                    break;
    //                }
    //
    //                if ($listArr['end_date'][$i] <= $listArr['begin_date'][$i]) {
    //                    $this->result(['i'=>$i],0,'开始时间不能大于结束时间');
    //                    break;
    //                }
    //
    //            }
    //            // 游戏id不能为空
    //            if (empty($listArr['game_id'][$i])){
    //                $this->result(['i'=>$i],0,'请选择游戏');
    //                break;
    //            }
    //            // 渠道不能为空
    //            if (empty($listArr['channel_id'][$i])){
    //                $this->result(['i'=>$i],0,'请选择渠道');
    //                break;
    //            }
    //            // 对几个补点进行验证
    //            if (is_numeric($listArr['first_point'][$i])) {
    //                $first_point_1 = (float)$listArr['first_point'][$i] * 100;
    //                $res_num = $first_point_1 - (float)((int)$first_point_1);
    //
    //                if ( $first_point_1 <0 || $first_point_1 > 10000 || $res_num > 1e-7 ) {
    //                    $this->result(['i'=>$i],0,'请输入正确的首充补点');
    //                    break;
    //                }
    //            }else{
    //                $this->result(['i'=>$i],0,'请输入正确的首充补点');
    //                break;
    //            }
    //
    //            if (is_numeric($listArr['common_point'][$i])) {
    //                $common_point_1 = (float)$listArr['common_point'][$i] * 100;
    //                $res_num = $common_point_1 - (float)((int)$common_point_1);
    //                if ( $common_point_1 <0 || $common_point_1 > 10000 || $res_num > 1e-7 ) {
    //                    $this->result(['i'=>$i],0,'请输入正确的续充补点');
    //                    break;
    //                }
    //            }else{
    //                $this->result(['i'=>$i],0,'请输入正确的续充补点');
    //                break;
    //            }
    //
    //            if (is_numeric($listArr['cash_point'][$i])) {
    //                $cash_point_1 = (float)$listArr['cash_point'][$i] * 100;
    //                $res_num = $cash_point_1 - (float)((int)$cash_point_1);
    //                if ( $cash_point_1 <0 || $cash_point_1 > 10000 || $res_num > 1e-7 ) {
    //                    $this->result(['i'=>$i],0,'请输入正确的自然补点');
    //                    break;
    //                }
    //            }else{
    //                $this->result(['i'=>$i],0,'请输入正确的自然补点');
    //                break;
    //            }
    //
    //            if (is_numeric($listArr['cash_profit_point'][$i])) {
    //                $cash_profit_point_1 = (float)$listArr['cash_profit_point'][$i] * 100;
    //                $res_num = $cash_profit_point_1 - (float)((int)$cash_profit_point_1);
    //                if ( $cash_profit_point_1 <0 || $cash_profit_point_1 > 10000 || $res_num > 1e-7 ) {
    //                    $this->result(['i'=>$i],0,'请输入正确的返利补点');
    //                    break;
    //                }
    //            }else{
    //                $this->result(['i'=>$i],0,'请输入正确的返利补点');
    //                break;
    //            }
    //
    //            $begin_date[]        = $listArr['begin_date'][$i];
    //            $end_date[]          = $listArr['end_date'][$i];
    //            $first_point[]       = $listArr['first_point'][$i];
    //            $common_point[]      = $listArr['common_point'][$i];
    //            $cash_point[]        = $listArr['cash_point'][$i];
    //            $cash_profit_point[] = $listArr['cash_profit_point'][$i];
    //            $remark[]            = $listArr['remark'][$i];
    //            $game_id[]           = $listArr['game_id'][$i];
    //            $channel_id[]        = $listArr['channel_id'][$i];
    //        }
    //
    //        // 开启事物
    //        Db::startTrans();
    //
    //        foreach ($begin_date as $key => $value) {
    //            $departid = $channel_id[$key];
    //            $game_id_1 = $game_id[$key];
    //
    //            $moder_PR = model('GameExtraPointRatio');
    //            $map_2 = [
    //                'status' => ['eq',1],
    //                'game_id' => ['eq',$game_id_1],
    //                'year_month' => ['eq',session('year_month')]
    //            ];
    //            $pdList = $moder_PR->where($map_2)->field('id,game_id,channel_id,begin_date,end_date')->select();
    //
    //            // 查有该游戏，可以进行对比判断
    //            $thisGame = ['year_month'=>session('year_month'),'game_id'=>$game_id_1,'channel_id'=>$departid,'begin_date'=>$begin_date[$key],'end_date'=>$end_date[$key],'first_point'=>$first_point[$key],'common_point'=>$common_point[$key],'cash_point'=>$cash_point[$key],'cash_profit_point'=>$cash_profit_point[$key],'remark'=>$remark[$key],'add_user'=>session('ADMIN_ID'),'add_time'=>time()];
    //            if (count($pdList)){
    //                $topDP = model('Department')->where(['id'=>$departid])->field('id,id_path')->find();
    //
    //                // 查出下级渠道
    //                $map_1 = [];
    //                $map_1 = [
    //                    'id_path'=>['like','%,'.$departid.',%']
    //                ];
    //                $downDP_1 = model('Department')->where($map_1)->field('id')->select();
    //                $downDP = '';
    //                foreach ($downDP_1 as $key => $value) {
    //                    $downDP .= ','.$value['id'].',';
    //                }
    //
    //                // 验证
    //                foreach ($pdList as $v_pdlist){
    //                    // 只要 $thisGame 的开始时间或结束时间 在对方的开始时间和结束时间之间，哪就发生了时间冲突
    //                    $v_pdlist['begin_date'] =(int)$v_pdlist['begin_date'];
    //                    $v_pdlist['end_date'] =(int)$v_pdlist['end_date'];
    //
    //                    // 2、判断渠道有没有冲突
    //                    // 2.1判断上级渠道有没有冲突----如果游戏相同，时间冲突，上级渠道已经添加补点，提示“上级渠道已经添加该补点信息，冲突ID：xxx”；
    //                    if ( strpos($topDP['id_path'],','.$v_pdlist['channel_id'].',')  !== false ){
    //                        $msg      = '上级渠道已经添加补点信息，冲突ID：' . $v_pdlist['id'];
    //                        $this->result(['i'=>$key],0,$msg);
    //                        break;
    //                    }
    //
    //                    // 2.2判断下级渠道有没有冲突
    //                    $downDP = str_replace($departid,",",$downDP);
    //                    if ( strpos($downDP,','.$v_pdlist['channel_id'].',')  !== false ){
    //                        $msg      = '下级渠道已经添加补点信息，冲突ID：' . $v_pdlist['id'];
    //                        $this->result(['i'=>$key],0,$msg);
    //                        break;
    //                    }
    //
    //                    /*var_dump($topDP['id_path']);
    //                    var_dump($downDP);*/
    //
    //                    // 时间发生冲突
    //                    if( (($thisGame['begin_date'] >= $v_pdlist['begin_date'] && $thisGame['begin_date'] < $v_pdlist['end_date']) || ($thisGame['end_date'] > $v_pdlist['begin_date'] && $thisGame['end_date'] <= $v_pdlist['end_date']) || ($thisGame['begin_date'] < $v_pdlist['begin_date'] && $thisGame['end_date'] > $v_pdlist['end_date'] )) && $thisGame['channel_id'] == $v_pdlist['channel_id']){
    //                        // 1.如果游戏相同，渠道相同，时间冲突，提示“补点时间有冲突，冲突ID：xxx”；
    //                        $msg      = '补点时间有冲突，冲突ID：' . $v_pdlist['id'];
    //                        $this->result(['i'=>$key],0,$msg);
    //                        break;
    //                    }
    //
    //                }
    //            }
    //            // 开始入库操作
    //            $GameExtraPointObj = model('GameExtraPoint')->where(['year_month'=>session('year_month')])->field('id')->find();
    //            $thisGame['game_extra_point_id'] = $GameExtraPointObj['id'];
    ////            $pointRatioM = model('GameExtraPointRatio');
    ////            $res = $pointRatioM->add($thisGame);
    //            $pointRatioM = new GameExtraPointRatio;
    //            $pointRatioM->data($thisGame);
    //            $res = $pointRatioM->save();
    //
    //            // 获取游戏名和渠道名
    //            $channel_obj = model('Department')->getCondition(['id'=>$departid]);
    //            $Game_name = model('Game')->where(['id'=>$game_id_1])->field('name')->find();
    //
    //            $res_log = $this->insertLog($this->current_node,'添加游戏补点，ID：'.$pointRatioM->id.'，游戏名：'.$Game_name['name'].'，渠道名：'.$channel_obj['name'], 14);
    //            // 判断是否写入库
    //            if(!$res || !$res_log){
    //                Db::rollback();
    //                $this->result([],0,'网络出现错误'.$res_log);
    //            }
    //        }
    //        Db::commit();
    //        $map['year_month'] = session('year_month');
    //        model('GameExtraPoint')->save(['update_user'=>session('ADMIN_ID'),'update_time'=>time()],$map);
    //
    //        $this->result([],1,'操作成功');
    //    }
    //    /**
    //     * 消费折扣点位列表
    //     *
    //     */
    //    public function payPointList()
    //    {
    //        $payPointModel = new PayPoint;
    //
    //        $where = [];
    //
    //        //游戏名称
    //        if (input('request.name') != '') {
    //            $where['g.name'] = ['like', '%' . input('request.name', '', 'trim') . '%'];
    //        }
    //
    //        //查询参数
    //        $param = input('get.');
    //
    //        $list = $this->gameModel->alias('g')
    //                                ->field('g.name as game_name,g.id as game_id,p.id,p.third_party_pay_point,p.create_time,p.update_time,p.status')
    //                                ->join('cy_pay_point p', 'g.id=p.game_id', 'left')
    //                                ->where(['g.cooperation_status' => ['<>', 0]])->where($where)->order('g.id desc')
    //                                ->paginate(10, false, array('query' => $param));
    //
    //        $this->assign('list', $list);
    //        $this->assign('page', $list->render());
    //
    //        return $this->fetch('pay_point_list');
    //    }
    //
    //    /**
    //     * 设置折扣点
    //     *
    //     */
    //    public function ajaxSetPayPoint()
    //    {
    //        if (request()->isAjax()) {
    //
    //            $gameId = input('post.pk', 0, 'intval');
    //            $field  = input('post.name', '', 'trim');
    //            $value  = input('post.value', '', 'trim');
    //
    //            if (empty($gameId)) {
    //                $this->result('', 0, '游戏ID不能为空');
    //            } elseif (empty($field)) {
    //                $this->result('', 0, '未指定要编辑的字段');
    //            } elseif (empty($value)) {
    //                $this->result('', 0, '更新的值不能为空');
    //            } elseif (!is_numeric($value)) {
    //                $this->result('', 0, '更新的值格式不正确');
    //            } else {
    //                $payPointModel = new PayPoint;
    //
    //                $payPointInfo = $payPointModel->where(['game_id' => $gameId])->find();
    //
    //                //编辑
    //                if ($payPointInfo) {
    //                    if ($payPointModel->where(['game_id' => $gameId])->update([
    //                        $field        => $value,
    //                        'update_time' => time(),
    //                    ])) {
    //                        $this->result('', 1, '设置成功');
    //                    } else {
    //                        $this->result('', 0, '设置失败');
    //                    }
    //                } //新增
    //                else {
    //                    if ($payPointModel->insert([
    //                        'game_id'     => $gameId,
    //                        $field        => $value,
    //                        'create_time' => time(),
    //                        'update_time' => time(),
    //                    ])) {
    //                        $this->result('', 1, '设置成功');
    //                    } else {
    //                        $this->result('', 0, '设置失败');
    //                    }
    //                }
    //            }
    //        } else {
    //            $this->error('非法操作');
    //        }
    //    }
    
    
    //    /**
    //     * 新增游戏自动上架
    //     */
    //    public function autoUpdateCreate()
    //    {
    //        if (request()->isPost()) {
    //
    //            $data = [
    //                'gameid'         => input('post.gameid'),
    //                'putaway_status' => input('post.putaway_status'),        //上架类型
    //                'putaway_time'   => input('post.putaway_time'),
    //            ];
    //
    //            $result = $this->validate($data, [
    //                ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
    //                ['putaway_status', 'integer', '上架类型必须为整型'],
    //                ['putaway_time', 'require|date|after:' . date('Y-m-d H:i:s'), '上架时间不能为空|上架时间的格式不正确|上架时间需大于当前时间'],
    //            ]);
    //
    //            if (true !== $result) {
    //
    //                $this->error($result);
    //            } //上架类型不是新增时
    //            elseif ($data['putaway_status'] != 0) {
    //                if (empty(input('post.target_filename')) || empty(input('post.source_filename'))) {
    //                    $this->error('请完整填写新旧文件名');
    //                }
    //            } elseif ($data['putaway_status'] == 1) {
    //                if (empty(input('post.version'))) {
    //                    $this->error('请输入强更版本号');
    //                }
    //            }
    //
    //            $data['target_filename']       = input('post.target_filename', '', 'trim');
    //            $data['source_filename']       = input('post.source_filename', '', 'trim');
    //            $data['version']               = input('post.version');
    //            $data['batch_subpackage_days'] = input('post.batch_subpackage_days');          //批量分包
    //            $data['create_time']           = time();
    //            $data['putaway_time']          = input('post.putaway_time', 0, 'strtotime');
    //            $data['token']                 = $this->createAutoUpdateGameToken();
    //
    //            if (Db::table('cy_game_auto_update')->insert($data)) $this->success('新增成功', 'game/autoUpdateList'); else
    //                $this->error('新增失败');
    //        }
    //
    //        $game_list = $this->gameModel->field('id,name')->order('name asc')->select();
    //
    //        $this->assign('game_list', $game_list);
    //
    //        return $this->fetch('auto_update_new');
    //    }
    //
    //    /**
    //     * 游戏推荐
    //     *
    //     */
    //    public function articleIndex()
    //    {
    //        $articleInfo = Db::table('cy_admin_article')->where(['status' => 1, 'open_time' => ['<', time()]])
    //                         ->order('open_time DESC')->find();
    //
    //        $this->assign('articleInfo', $articleInfo);
    //
    //        return $this->fetch('article_index');
    //    }
    //
    //    /**
    //     * 游戏推荐列表
    //     */
    //    public function articleList()
    //    {
    //        $where = [];
    //
    //        //游戏名称
    //        if (input('request.title') != '') {
    //            $where['title'] = ['like', '%' . input('request.title', '', 'trim') . '%'];
    //        }
    //
    //        //推送类型
    //        if (input('request.status') != '') {
    //            $where['status'] = input('request.status', 0, 'intval');
    //        }
    //
    //        //查询参数
    //        $param = input('get.');
    //
    //        $article_list = Db::table('cy_admin_article')->where($where)->order('id desc')
    //                          ->paginate(10, false, array('query' => $param));
    //
    //        $this->assign('article_list', $article_list);
    //        $this->assign('page', $article_list->render());
    //
    //        return $this->fetch('article_list');
    //    }
    //
    //    /**
    //     * 更改文章上架状态
    //     */
    //    public function changeArticleStatus()
    //    {
    //
    //        if (request()->isAjax()) {
    //
    //            $id     = input('post.id', 0, 'intval');
    //            $status = input('post.status', 0, 'intval');
    //
    //            if (empty($id)) {
    //
    //                $this->result('', 0, '文章ID不能为空');
    //            } else {
    //
    //                if (Db::table('cy_admin_article')->where(['id' => $id])->update(['status' => $status])) {
    //                    $this->result('', 1, '操作成功');
    //                } else {
    //                    $this->result('', 0, '操作失败');
    //                }
    //            }
    //        } else {
    //            $this->error('非法操作');
    //        }
    //    }
    //
    //    /**
    //     * 删除游戏文章
    //     *
    //     */
    //    public function articleDelete()
    //    {
    //        $id = input('id', 0, 'intval');
    //
    //        if (empty($id)) $this->error('删除记录的ID不能为空');
    //
    //        if (Db::table('cy_admin_article')->where(['id' => $id])->delete()) {
    //            $this->success('删除成功');
    //        } else {
    //            $this->error('删除失败');
    //        }
    //    }
    //
    //    /**
    //     * 新增文章
    //     */
    //    public function articleCreate()
    //    {
    //        if (request()->isPost()) {
    //
    //            $data = [
    //                'status'    => input('post.status'),
    //                'title'     => input('post.title'),
    //                'open_time' => input('post.open_time'),
    //                'sort'      => input('post.sort'),
    //                'content'   => input('post.content'),
    //            ];
    //
    //            $result = $this->validate($data, [
    //                ['status', 'require|integer', '状态不能为空|状态必须为整型'],
    //                ['title', 'require|max:32', '标题不能为空|标题不能超过32个字符'],
    //                ['open_time', 'require|date|after:' . date('Y-m-d H:i:s'), '上架时间不能为空|上架时间的格式不正确|上架时间需大于当前时间'],
    //                ['sort', 'require|integer', '排序不能为空|排序必须为整型'],
    //            ]);
    //
    //            if (true !== $result) {
    //                $this->error($result);
    //            }
    //
    //            $data['create_time'] = time();
    //            $data['open_time']   = strtotime($data['open_time']);
    //
    //            if (Db::table('cy_admin_article')->insert($data)) $this->success('新增成功', 'game/articleList'); else
    //                $this->error('新增失败');
    //        }
    //
    //        return $this->fetch('article_new');
    //    }
    //
    //    /**
    //     * 编辑文章
    //     */
    //    public function articleEdit()
    //    {
    //        $id = input('id', 0, 'intval');
    //
    //        if (empty($id)) $this->error('文章ID不能为空');
    //
    //        $articleInfo = Db::table('cy_admin_article')->where(['id' => $id])->find();
    //
    //        if (empty($articleInfo)) $this->error('记录不存在');
    //
    //        if (request()->isPost()) {
    //
    //            $data = [
    //                'status'    => input('post.status'),
    //                'title'     => input('post.title'),
    //                'open_time' => input('post.open_time'),
    //                'sort'      => input('post.sort'),
    //                'content'   => input('post.content'),
    //            ];
    //
    //            $result = $this->validate($data, [
    //                ['status', 'require|integer', '状态不能为空|状态必须为整型'],
    //                ['title', 'require|max:32', '标题不能为空|标题不能超过32个字符'],
    //                ['open_time', 'require|date|after:' . date('Y-m-d H:i:s'), '上架时间不能为空|上架时间的格式不正确|上架时间需大于当前时间'],
    //                ['sort', 'require|integer', '排序不能为空|排序必须为整型'],
    //            ]);
    //
    //            if (true !== $result) {
    //                $this->error($result);
    //            }
    //
    //            $data['open_time'] = strtotime($data['open_time']);
    //
    //            if (Db::table('cy_admin_article')->where(['id' => $id])
    //                  ->update($data)) $this->success('编辑成功', 'game/articleList'); else
    //                $this->error('编辑失败');
    //        }
    //
    //        $this->assign('articleInfo', $articleInfo);
    //
    //        return $this->fetch('article_edit');
    //    }
    //
    //    /**
    //     * 生成游戏自动更新token
    //     */
    //    private function createAutoUpdateGameToken()
    //    {
    //        return md5(time() . 'gameAutoUpdate');
    //    }
    //
    //    /**
    //     * 编辑游戏自动上架
    //     */
    //    public function autoUpdateEdit()
    //    {
    //        $id = input('id', 0, 'intval');
    //
    //        if (empty($id)) $this->error('游戏自动更新记录的ID不能为空');
    //
    //        $autoInfo = Db::table('cy_game_auto_update')->where(['id' => $id])->find();
    //
    //        if (empty($autoInfo)) $this->error('游戏自动更新记录不存在');
    //
    //        if (request()->isPost()) {
    //
    //            $data = [
    //                'gameid'         => input('post.gameid'),
    //                'putaway_status' => input('post.putaway_status'),        //上架类型
    //                'putaway_time'   => input('post.putaway_time'),
    //            ];
    //
    //            $result = $this->validate($data, [
    //                ['gameid', 'require|integer', '请选择游戏|游戏ID必须为整型'],
    //                ['putaway_status', 'integer', '上架类型必须为整型'],
    //                ['putaway_time', 'require|date|after:' . date('Y-m-d H:i:s'), '上架时间不能为空|上架时间的格式不正确|上架时间需大于当前时间'],
    //            ]);
    //
    //            if (true !== $result) {
    //
    //                $this->error($result);
    //            } //上架类型不是新增时
    //            elseif ($data['putaway_status'] != 0) {
    //                if (empty(input('post.target_filename')) || empty(input('post.source_filename'))) {
    //                    $this->error('请完整填写新旧文件名');
    //                }
    //            } elseif ($data['putaway_status'] == 1) {
    //                if (empty(input('post.version'))) {
    //                    $this->error('请输入强更版本号');
    //                }
    //            }
    //
    //            $data['target_filename']       = input('post.target_filename', '', 'trim');
    //            $data['source_filename']       = input('post.source_filename', '', 'trim');
    //            $data['version']               = input('post.version');
    //            $data['batch_subpackage_days'] = input('post.batch_subpackage_days');          //批量分包
    //            $data['putaway_time']          = input('post.putaway_time', 0, 'strtotime');
    //
    //            if (Db::table('cy_game_auto_update')->where(['id' => $id])
    //                  ->update($data)) $this->success('编辑成功', 'game/autoUpdateList'); else
    //                $this->error('编辑失败');
    //        }
    //
    //        $game_list = $this->gameModel->field('id,name')->order('name asc')->select();
    //
    //        $this->assign('game_list', $game_list);
    //        $this->assign('autoInfo', $autoInfo);
    //
    //        return $this->fetch('auto_update_edit');
    //    }
    //
    //    /**
    //     * 游戏自动更新列表
    //     */
    //    public function autoUpdateList()
    //    {
    //        $where = [];
    //
    //        //游戏名称
    //        if (input('request.name') != '') {
    //            $where['g.name'] = ['like', '%' . input('request.name') . '%'];
    //        }
    //
    //        //对接状态
    //        if (input('request.cooperation_status') != '') {
    //            $where['cooperation_status'] = input('request.cooperation_status');
    //        }
    //
    //        //查询参数
    //        $param = input('get.');
    //
    //        $list = $this->gameModel->table('cy_game g,cy_game_auto_update a')->field('g.name,a.*')->where('g.id=a.gameid')
    //                                ->where($where)->order('a.id desc')->paginate(10, false, array('query' => $param));
    //
    //        $this->assign('list', $list);
    //        $this->assign('page', $list->render());
    //
    //        return $this->fetch('auto_update_list');
    //    }
    //
    //
    //    /**
    //     * 游戏自动上架记录删除
    //     */
    //    public function autoUpdateDelete()
    //    {
    //
    //        $id = input('id', 0, 'intval');
    //
    //        if (empty($id)) $this->error('删除记录的ID不能为空');
    //
    //        if (Db::table('cy_game_auto_update')->where(['id' => $id])->delete()) {
    //            $this->success('删除成功');
    //        } else {
    //            $this->error('删除失败');
    //        }
    //    }

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
        $this->subChannel = new SubChannel;
        $this->gameModel = new GameModel;
        $this->gameBelongModel = new GameBelong;
        $this->sdkgamelistModel = $this->cyModel('sdkgamelist');
        $this->gameList = model('Common/Game')->getAllByCondition('id,name');
    
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [], '', 'self');
        $selfGameList = array();
        foreach ($tmpSelfGameList as $game) {
            $selfGameList[$game['id']] = $game;
        }
        $this->selfGameList = $selfGameList;
    }
}
