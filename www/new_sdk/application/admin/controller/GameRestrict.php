<?php
/**
 * 游戏管理控制器
 */

namespace app\admin\controller;

use app\common\library\FileUpload;
use app\common\library\MakeReport;
use app\common\model\Game as GameModel;
use app\common\model\GameInfo;
use app\common\model\GameRestrict as GameRestrictModel;
use Overtrue\Pinyin\Pinyin;
use think\Db;
use think\Exception;

class GameRestrict extends Admin
{
    protected $gameModel;

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        $this->gameModel = new GameModel;
        $this->gameList  = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
		$selfGameList = array();
		foreach ($tmpSelfGameList as $game) {
              $selfGameList[ $game['id']] = $game;
        }
		$this->selfGameList  = $selfGameList;
    }

    /**
     * 未成年限制列表
     */
    public function index()
    {
        $restrict = new GameRestrictModel;

        $where = [];

        //游戏名称
        if (input('request.game_id') != '') {
            $where['n.game_id'] = input('request.game_id', '', 'trim');
        }

        //标题
        if (input('request.restrict_type') != '') {
            $where['n.restrict_type'] = input('request.restrict_type', '', 'trim');
        }

        //冻结类型
		if(input('request.restrict_type') == 'realname'){
			if (input('request.realname_restrict_status') != '') {
				$where['n.restrict_status'] = input('request.realname_restrict_status', '', 'trim');
			}
		}
		else if(input('request.restrict_type') == 'preventhook'){
			if (input('request.preventhook_restrict_status') != '') {
				$where['n.restrict_status'] = input('request.preventhook_restrict_status', '', 'trim');
			}
		}
		else{
			if (input('request.restrict_status') != '') {
				$where['n.restrict_status'] = input('request.restrict_status', '', 'trim');
			}
		}

        $start          = $this->request->param('start');
        $end            = $this->request->param('end');
        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $where['n.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $where['n.create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $where['n.create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
			/*
            $start = date('Y-m-d', time());
            $end   = date('Y-m-d', time());

            $where['n.create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
			*/
        }

        //查询参数
        $param = input('get.');

        $restrict = $restrict->alias('n')->field('n.*,g.name game_name,adm.username add_user_name,adm2.username update_user_name')->join('cy_game g', 'g.id=n.game_id', 'left')->join('cy_admin adm', 'n.add_user_id=adm.id', 'left')->join('cy_admin adm2', 'n.update_user_id=adm2.id', 'left')
                              ->where($where)->order('n.create_time desc')
                              ->paginate(10, false, array('query' => $param));

	//	echo $restrict->getLastSql()."----getLastSql------<Br>";
        $this->assign('restrict', $restrict);
        $this->assign('page', $restrict->render());

		$gameList = $this->selfGameList;
		$gameList[0] = array('id'=>0,'name'=>'所有游戏');
        $this->assign('gameList',$gameList);

        return $this->fetch();
    }

    /**
     * 删除
     *
     */
    public function delete()
    {
        $id = input('id', 0, 'intval');

        if (empty($id)) $this->error('删除记录的ID不能为空');

        $restrict = new GameRestrictModel;

        if ($restrict->where(['id' => $id])->delete()) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 新增
     */
    public function add()
    {
        if (request()->isPost()) {
            $restrict     = new GameRestrictModel;
            $data = [
                'game_id'     => input('post.game_id', '', 'trim'),
                'restrict_type' => input('post.restrict_type','','trim'),
                'restrict_status'      => input('post.restrict_status','','intval'),
            ];
			if($data['restrict_type'] == 'realname'){
				$data['restrict_status'] = input('post.realname_restrict_status','', 'intval');
			}
			else{
				$data['restrict_status'] = input('post.preventhook_restrict_status','', 'intval');
			}

            $result = $this->validate($data, [
                ['game_id', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['restrict_type', 'require', '请选择限制类型'],
                ['restrict_status', 'require', '请选择限制状态'],
            ]);

            if (true !== $result) {
                $this->error($result);
            }

			//检查是否已存在记录
			$condis = array();
			$condis['game_id'] = intval($data['game_id']);
			$condis['restrict_type'] = $data['restrict_type'];
            $restrictInfo = $restrict->where($condis)->find();
			if(!empty($restrictInfo)){
				$this->error('已添加过该游戏限制类型！');
			}

            $data['create_time']    = time();
			$data['add_user_id']    = session('ADMIN_ID');
            $data['update_time']    = time();
			$data['update_user_id']    = session('ADMIN_ID');

            if ($restrict->insert($data)) $this->success('新增成功', 'GameRestrict/index'); else
                $this->error('新增失败');
        }

		$game_list = array(array('id'=>0,'name'=>'所有游戏'));
		$game_list = array_merge($game_list,$this->selfGameList);
        $this->assign('game_list', $game_list);

        return $this->fetch();
    }

    /**
     * 编辑
     */
    public function edit()
    {
        $id = input('id', 0, 'intval');
        $restrictModel = new GameRestrictModel;
        if (empty($id)) $this->error('游戏冻结的ID不能为空');

        $restrictInfo = $restrictModel->where(['id' => $id])->find();

        if (empty($restrictInfo)) $this->error('记录不存在');

        if (request()->isPost()) {
            $restrict     = new GameRestrictModel;
            $data = [
                'game_id'     => input('post.game_id', '', 'intval'),
                'restrict_type' => input('post.restrict_type'),
                'restrict_status'      => input('post.restrict_status','', 'intval'),
            ];
			if($data['restrict_type'] == 'realname'){
				$data['restrict_status'] = input('post.realname_restrict_status','', 'intval');
			}
			else{
				$data['restrict_status'] = input('post.preventhook_restrict_status','', 'intval');
			}

            $result = $this->validate($data, [
                ['game_id', 'require|integer', '请选择游戏|游戏ID必须为整型'],
                ['restrict_type', 'require', '请选择限制类型'],
                ['restrict_status', 'require', '请选择限制状态'],
            ]);

			$data['update_time']    = time();
			$data['update_user_id'] = session('ADMIN_ID');

            if (true !== $result) {
                $this->error($result);
            }

			//检查是否已存在记录
			$condis = array();
			$condis['game_id'] = intval($data['game_id']);
			$condis['restrict_type'] = $data['restrict_type'];
			$condis['id'] = array('neq',$id);
            $restrictInfo = model('GameRestrict')->where($condis)->find();
			if(!empty($restrictInfo)){
				$this->error('已存在该游戏限制类型！');
			}
            if ($restrictModel->update($data, ['id' => $id])) {
                $this->success('编辑成功', 'GameRestrict/index');
            } else
                $this->error('编辑失败');
        }

		$game_list = array(array('id'=>0,'name'=>'所有游戏'));
		$game_list = array_merge($game_list,$this->selfGameList);
        $this->assign('game_list', $game_list);
        $this->assign('data', $restrictInfo);

        return $this->fetch();
    }

    /**
     * 防沉迷规则说明
     */
    public function restrictExplain(){
        if($this->request->isGet()){
            $info = model('Setting')->where(['name'=>'GAME_RESTRICT_EXPLAIN'])->field('value')->find()['value'];
            $this->assign('info',$info);
            return $this->fetch('restrictExplain');
        }

        if ($this->request->isPost()){
            $content = input('post.content');

            // 写入配置表
            model('Setting')->save(['value'=>$content],['name'=>'GAME_RESTRICT_EXPLAIN']);
            $this->success('设置成功','restrictExplain');

        }
    }

}
