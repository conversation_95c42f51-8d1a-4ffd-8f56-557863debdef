<?php

namespace app\admin\controller;


use app\common\library\OneFileUpload;
use think\Loader;
use think\Db;

class <PERSON>iyuan extends Admin
{
    protected $gameList;

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }

    /**
     *玩家福利列表
     */
    public function index()
    {
        $order = 'create_time desc';
        $welfareModel = Db::name('cy_ziyuan');
        $list = $welfareModel
            ->field('*')
            ->order($order)
            ->paginate();
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }


    /**
     *增
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            $data['create_time'] = time();
            $m = Db::name('cy_ziyuan')->insertGetId($data);
            if (!empty($m)) {
                $this->success('添加成功',url('index'));
            }
            $this->error($appModel->getError() ?: '添加失败');
        }
        return $this->fetch();
    }


    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = model('Common/Welfare');
        if (!$appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功',url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }


    /**
     *编辑
     */
    public function edit()
    {
        $id       = $this->request->param('id', '', 'intval');

        $appModel = Db::name('cy_ziyuan');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data           = $this->request->post();
            Db::name('cy_ziyuan')->where(['id'=>$id])->update($data);
            if (!empty($id)) {
                $this->success('修改成功',url('index'));
            }
            $this->error($appModel->getError() ?: '修改失败');
        }

        $this->assign('data', $data);
        return $this->fetch();
    }


    /**
     *玩家福利列表
     */
    public function typelist()
    {
        $order = 'create_time desc';
        $welfareModel = Db::name('cy_ziyuan_type');
        $list = $welfareModel
            ->field('*')
            ->order($order)
            ->paginate();
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }


    /**
     *增
     */
    public function typeadd()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            $data['create_time'] = time();
            $m = Db::name('cy_ziyuan_type')->insertGetId($data);
            if (!empty($m)) {
                $this->success('添加成功',url('index'));
            }
            $this->error($appModel->getError() ?: '添加失败');
        }
        return $this->fetch();
    }


    /**
     *编辑
     */
    public function typeedit()
    {
        $id       = $this->request->param('id', '', 'intval');

        $appModel = Db::name('cy_ziyuan_type');
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data           = $this->request->post();
            Db::name('cy_ziyuan_type')->where(['id'=>$id])->update($data);
            if (!empty($id)) {
                $this->success('修改成功',url('index'));
            }
            $this->error($appModel->getError() ?: '修改失败');
        }

        $this->assign('data', $data);
        return $this->fetch();
    }


    /**
     * 资源审核记录
     * @return mixed
     * @throws \think\exception\DbException
     */
    public function ziyuanshlist()
    {
        $where = [];
        $sh_status     = $this->request->get('sh_status', '', 'trim');
        if(!empty($sh_status))
        {
            $where['record.sh_status'] = $sh_status;
        }

        $infoList = Db::table('cy_ziyuan_apply_record')->alias('record')
            ->join('cy_members member', 'member.id = record.member_id')
            ->join('cy_game game', 'game.id = record.gameid')
            ->join('nw_channel channel', 'channel.id = record.channel_id')
            ->field(['record.id','channel.name as dep_name','member.username','game.name','record.sh_status','record.rolename','record.servername','record.roleid_id','record.rolename','record.create_time','record.ziyuan_type','record.content','record.remark','record.sh_time'])
            ->where($where)
            ->order('record.sh_status', 'desc')
            ->paginate();
        $this->assign('list', $infoList);
        $this->assign('page', $infoList->render());
        return $this->fetch();
    }

    public function shstatus()
    {
        $id = $this->request->param('id', '', 'intval');
        $info = Db::table('cy_ziyuan_apply_record')->where(['id'=>$id])->find();
        $this->assign('info', $info);
        return $this->fetch();
    }

    /**
     * 资源审核保存
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function shziyuan()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            $info = Db::table('cy_ziyuan_apply_record')->where(['id'=>$data['id']])->find();
            if(!empty($info))
            {

                $info_list['sh_status'] = $data['sh_status'];
                $info_list['sh_time'] = time();
                Db::table('cy_ziyuan_apply_record')->where(['id'=>$data['id']])->update($info_list);

                $this->success('审核成功',url('index'));
            }
            $this->error('审核失败');
        }

    }

}