<?php
/**
 * 聚合sdk自动打包脚本获取客户端参数
 * User: Administrator
 * Date: 2020/4/3
 * Time: 17:32
 */

namespace app\admin\controller;

use think\Request;
use app\common\model\Polychannel;
use app\common\model\PolychannelGame;

class Autopack extends Admin{

    protected $_gameKey = 'ab6D4b2eMjfKfI5A76DFbSflYH9VxCEuQ6Q';
    protected $_debug   = false;

    public function _initialize()
    {

    }

    /**
     * 根据游戏拼音获取游戏信息
     */
    public function getGameInfo()
    {
        if (!$this->request->isPost()) $this->jsonResult('',0,'请求发生错误');;

        $param = Request::instance()->param();
        if ( !isset($param['pinyin']) || !isset($param['channel']) || !isset($param['sign'])) {
            $this->jsonResult('',0,'参数错误');
        }


        $sign = md5(strtolower($param['channel']. $param['pinyin'] . $this->_gameKey));
        if ($sign != $param['sign']) {
            $this->jsonResult('',0,'参数不合法');
        }

        if (!$this->_debug){
            $polychannelGameModel = new PolychannelGame;
            $polychannelModel     = new Polychannel;

            $info = $polychannelGameModel->table('cy_polychannel_game cg,nw_channel c,cy_game g')
                ->field('cg.*,g.name game_name,c.name channel_name')
                ->where('cg.channel_id=c.id and g.id=cg.game_id')
                ->where(['g.pinyin'=>$param['pinyin'],'c.mark'=>$param['channel']])
                ->find();

            if(empty($info)){
                $this->jsonResult('',0,'聚合渠道游戏信息不存在');
            }

            //参数字段
            $param_field_arr = $polychannelModel->field('field_client')->where(['channel_id'=>$info['channel_id']])->find();
            if (empty($param_field_arr)) {
                $this->jsonResult('',0,'游戏信息不存在');
            }


            $field_client = $param_field_arr['field_client'];
            $field_client = (!empty($field_client) ?  explode(';',$field_client) : $field_client);
            $arrParamClient = (!empty($info['param_client']) ?  unserialize($info['param_client']) : $info['param_client']);
        }else{
            $field_client = 'channel;gameid;sign';
            $field_client = (!empty($field_client) ?  explode(';',$field_client) : $field_client);
            $arrParamClient = [$param['channel'],$param['pinyin'],$param['sign']];
        }

        $data = [];
        foreach ($field_client as $k =>$v){
            $data[$v] = $arrParamClient[$k];
        }

        $this->jsonResult($data,1);
    }
}