<?php
/**
 * Created by PhpStorm.
 * User: edison
 * Date: 2018/3/26
 * Time: 上午11:50
 */

namespace app\admin\controller;

use app\common\library\MakeReport;
use think\Config;
use think\Db;
use think\Exception;
use app\common\logic\SubPackage as SubChannel;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use app\common\logic\Websocket;
use app\common\library\FileUpload;

class Workstation extends Admin
{
    public function _initialize()
    {
        parent::_initialize();
        $this->adminModel = Db::table('nw_channel_admin');
        $this->channelModel = Db::table('nw_channel');
    }
    /**
     * 首页渲染
     * @return [type] [description]
     */
    public function index()
    {
        $where = [];
        $channles = $this->adminModel->alias('a')
        ->join('nw_channel channel', 'a.channel_id = channel.id','left')
        ->where($where)
        ->where(['channel.level'=>1])
        ->field('a.*,channel.amount,channel.js_amount')
        ->order("a.id DESC")
        ->paginate(10,false, ['query' => input('get.')]);
        $info_unapply_count = db::name('nw_channel_info_apply a')->join('nw_channel channel', 'a.channel_id = channel.id','left')->where(['a.status'=>0,'level'=>['neq',0]])->count();//审核未处理
        $ratio_unapply_count =  Db::name('nw_channel_recharge')->where(['status'=>0,'recharge_type'=>1])->count();//充值待审核
        $js_unapply_count =  Db::name('nw_game_channel_divide_settle')->where(['second_audit_status'=>0,'first_audit_status'=>1])->count();//结算待审核
        $withdraw_unapply_count =  Db::name('nw_channel_withdraw')->where(['status'=>0])->count();//提现待审核
        $package_unapply_count =  Db::name('nw_game_package_apply apply')->join('cy_game game', 'game.id=apply.game_id','left')->where(['apply.apply_status'=>0,'game.cooperation_status'=>2])->count();//分包待审核
        $sinfo_unapply_count = db::name('nw_channel_info_apply a')->join('nw_channel channel', 'a.channel_id = channel.id','left')->where(['a.status'=>0,'level'=>['eq',0]])->count();//商务审核未处理
        $rebindapply_unapply_count =  Db::name('nw_player_rebind_apply')->where(['status'=>0])->count();//玩家换绑待审核
        $page = $channles->render();
        $this->assign("page", $page);
        $this->assign("total", $channles->total());     //总记录数
        $this->assign("list", $channles);
        $this->assign("info_unapply_count", $info_unapply_count>99?'99+':$info_unapply_count);
        $this->assign("sinfo_unapply_count", $sinfo_unapply_count>99?'99+':$sinfo_unapply_count);
        $this->assign("ratio_unapply_count", $ratio_unapply_count>99?'99+':$ratio_unapply_count);
        $this->assign("js_unapply_count", $js_unapply_count>99?'99+':$js_unapply_count);
        $this->assign("withdraw_unapply_count", $withdraw_unapply_count>99?'99+':$withdraw_unapply_count);
        $this->assign("package_unapply_count", $package_unapply_count>99?'99+':$package_unapply_count);
        $this->assign("rebindapply_unapply_count", $rebindapply_unapply_count>99?'99+':$rebindapply_unapply_count);
        return $this->fetch();
    }
}