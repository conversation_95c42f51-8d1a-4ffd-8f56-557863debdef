<?php
/**
 * 渠道通信密钥管理
 *
 * @category Controller
 * <AUTHOR>
 * @since    2018/9/26
 */

namespace app\admin\controller;

use app\common\model\ChannelCa as ChannelCaModel;
use think\Db;
use think\Validate;
use think\Exception;
use app\common\model\ChannelApiRequestAuth;

class ChannelCa extends Admin {

    private $_channelCaModel = null;

    public function _initialize() {
        parent::_initialize();

        $this->_channelCaModel = new ChannelCaModel;
    }

    /**
     * 显示已经配置的秘钥对列表
     */
    public function encryptionKeyList() {
        $where =[];

        $list = $this->cyModel('channel_ca')
            ->alias("a")
            ->join("nw_channel b", 'a.ca_channel_id = b.id', 'left')
            ->field("a.ca_id as id, a.ca_public_key, a.ca_encryption_key_type, a.ca_enable_status, a.ca_realname, a.ca_mobile, a.ca_mail, a.ca_create_time, a.ca_channel_id as channel_id, b.name as channel_name")
            ->where($where)
            ->order("a.ca_id desc")
            ->paginate(10, false, ['query' => $where])->each(function($item, $key){
                // 顶级渠道
                $item['top_channel_name'] = get_top_channel($item['channel_id'])['name'];
                return $item;
            });

        $page = $list->render();
        $this->assign('total', $list->total());
        $this->assign("page", $page);
        $this->assign('list', $list);

        return $this->fetch('encryptionKeyList');
    }

    /**
     * 密钥对编辑和添加公用的页面
     */
    public function encryptionKeyEdit() {
        if ( request()->isGet() ) {
            $id = input('id', 0, 'intval');

            $info = [];
            if ( ! empty($id) ) {
                $info = $this->_channelCaModel->where(['ca_id' => $id])->find();
            }

            $channelList= model('Channel')->field('id,name')->where(['status'=>1,'flag'=>3])->order('name asc')->select();

            $this->assign('channel_list', $channelList);

            $this->assign('info', $info);

            return $this->fetch('encryptionKeyEdit');
        }

        if ( request()->isPost() ) {
            $param = input('param.');

            $validate = new Validate([
                ['ca_private_key', 'isValidPrivatekey:1', '私钥信息错误'],
                ['ca_public_key', 'isValidPublickey:1', '公钥信息错误'],
                ['ca_realname', 'require', '请填写联系人姓名'],
                ['ca_mobile', 'require', '请填写联系人手机号'],
                ['ca_mail', 'email', '请正确填写联系人邮箱'],
            ]);

            $validate->extend([
                'isValidPrivatekey' => function ($value, $rule) {
                    if ( empty($value) ) {
                        return false;
                    }

                    $resource_id = openssl_pkey_get_private($value);
                    if ( empty($resource_id) ) {
                        return false;
                    }

                    return true;
                },
                'isValidPublickey' => function ($value, $rule) {
                    if ( empty($value) ) {
                        return false;
                    }

                    $resource_id = openssl_pkey_get_public($value);
                    if ( empty($resource_id) ) {
                        return false;
                    }

                    return true;
                }
            ]);

            $result = $validate->check($param);

            if ( true !== $result ) {
                $this->error($validate->getError());
            }

            $id = isset($param['id']) ? (int)$param['id'] : 0;
            unset($param['id']);
            if ( 0 < $id ) {
                $param['ca_update_time'] = NOW_TIMESTAMP;

                unset($param['ca_channel_id'],$param['ca_private_key'],$param['ca_public_key']);
                $result = $this->_channelCaModel->allowField(true)->save($param, ['ca_id' => $id]);
            } else {
                if ( ! $this->_channelCaModel->isValidEncryptionKey($param['ca_private_key'], $param['ca_public_key']) ) {
                    $this->error('公钥和私钥不匹配，请重新生成或填写');
                }

                if ( $this->_channelCaModel->where(['ca_channel_id' => $param['ca_channel_id']])->count() ) {
                    $this->error('已添加该渠道，请勿重复添加');
                }

                $param['ca_create_time'] = NOW_TIMESTAMP;

                $result = $this->_channelCaModel->allowField(true)->save($param);
            }

            if ( false === $result ) {
                $this->error('操作失败');
            } else {
                $this->success('操作成功','ChannelCa/encryptionKeyList');
            }
        }
    }

    /**
     * 秘钥对记录删除
     */
    public function encryptionKeyDelete() {
        $id = input('id', 0, 'intval');

        if ( empty($id) ) {
            $this->error('参数错误');
        }

        $info = $this->_channelCaModel->where(['ca_id' => $id])->find();

        if ( empty($info) ) {
            $this->error('记录不存在');
        }

        if ( $info['ca_enable_status'] != '0' ) {
            $this->error('请先禁用该密钥对');
        }

        $result = $this->_channelCaModel->where(['ca_id' => $id])->delete();

        if ( false === $result ) {
            $this->error('删除失败');
        } else {
            $content = '删除了渠道：'.model('Channel')->where(['id' => $info['ca_channel_id']])->value('name').'的密钥对';
            $this->insertLog($this->current_node, $content, 63);

            $this->success('删除成功','ChannelCa/encryptionKeyList');
        }
    }

    /**
     * @param $data
     * @return array
     */
    private function getChange($data){
        $limit = $this->_freezingLimit();           //限制的接口
        $limit = explode(',',$limit);   //转化成数组
        $limit_open = $data;     //可以访问的接口
        $limit_open = explode(',',$limit_open);  //转化成数组
        $diff = array_diff($limit,$limit_open);          //差集
        $intersect = array_intersect($limit,$limit_open);     //交集
        $limit_diff = array_flip($diff);
        foreach($limit_diff as $k => $v){
            $limit_diff[$k] = 0;
        }
        $limit_intersect = array_flip($intersect);
        foreach($limit_intersect as $k => $v){
            $limit_intersect[$k] = 1;
        }
        $data_limit = array_merge($limit_diff,$limit_intersect);
        return $data_limit;
    }

    /*
     * 外部渠道冻结管理
     */
    public function freezing()
    {
        $channel_name = $this->request->get('channel_name','','trim');
        $condition = [];
        if(!is_null($channel_name)){
            $condition['n.name'] = ['like','%'.$channel_name.'%'];
        }
        $freezingModel = new ChannelApiRequestAuth;
        $list = $freezingModel->alias('a')
            ->join('nw_channel n','a.channel_id = n.id','LEFT')
            ->field('a.*,n.name')
            ->where($condition)
            ->order('id desc')
            ->paginate(10, false, ['query' => input('get.')]);
        foreach ($list as &$item){

            $data_limit = $this->getChange($item['allow_action']);

            $item['game_list'] = $data_limit['game_list'];
            $item['member_recharge'] = $data_limit['member_recharge'];
            $item['query_discount'] = $data_limit['query_discount'];
            $item['login_list'] = $data_limit['login_list'];
            $item['register_list'] = $data_limit['register_list'];
            $item['pay_list'] = $data_limit['pay_list'];
            $item['sub_package'] = $data_limit['sub_package'];
            $item['detect_channel'] = $data_limit['detect_channel'];
            $item['detect_user'] = $data_limit['detect_user'];
            $item['subchannel_recharge'] = $data_limit['subchannel_recharge'];
            $item['create_channel'] = $data_limit['create_channel'];
            $item['check_balance'] = $data_limit['check_balance'];
            $item['channel_discount'] = $data_limit['channel_discount'];
            $item['operate_package'] = $data_limit['operate_package'];
            $item['game_point'] = $data_limit['game_point'];
            $item['sub_packageurl'] = $data_limit['sub_packageurl'];
        }
        $this->assign('list', $list);
        $this->assign('total', $list->total());
        $this->assign('page', $list->render());
        return $this->fetch('freezing');
    }

    /**
     * 获取聚合渠道列表
     * @return array
     */
    protected function _getChannelList() {
        $channelModel   = new \app\common\model\Channel();
        $channel_id = Db::table('cy_channel_ca')->column('ca_channel_id');
        $where = array(
            'id' => array('IN',$channel_id)
        );
        $list = $channelModel->field(['id', 'name'])->order('id desc')->where($where)->select();
        return $list ?: [];
    }

    /*
     *外部渠道冻结管理添加
     */
    public function addFreezing()
    {
        if($this->request->isPost()){
            $freezingModel = new ChannelApiRequestAuth;
            $data = [
                'channel_id' => input('post.channel_id'),
                'create_time' => time(),
                'per_hour_max_request_times' => '1000',
                'allow_action' => $this->getFreezing()
            ];
            $check = $freezingModel->where('channel_id',$data['channel_id'])->find();
            if ($check){
                $this->error('该渠道存在，请不要重复添加');
            }
            $result = $this->validate($data,[
                ['channel_id','require','渠道名称不能为空'],
            ]);
            if (true !== $result) {
                $this->error($result);
            }
            if ($freezingModel->allowField(true)->save($data)) {
                $content = '新增渠道冻结管理：'.model('Channel')->where(['id' => $data['channel_id']])->value('name');
                $this->insertLog($this->current_node, $content, 64);
                $this->success('添加成功',url('freezing'));
            }
            $this->error($freezingModel->getError() ?: '添加失败');
        }
        $this->assign('channel_list', $this->_getChannelList());
        return $this->fetch('addFreezing');
    }

    /*
     * 外部渠道冻结管理编辑
     */
    public function editFreezing()
    {
        $id = $this->request->param('id',0,'intval');
        $freezingModel = new ChannelApiRequestAuth;
        $list = $freezingModel->where('id',$id)->find();
        if ($this->request->isPost()){
            $id = input('id');
            $data = [
                'allow_action' => $this->getFreezing()
            ];
            $freezingModel = new ChannelApiRequestAuth;
            if ($freezingModel->allowField(true)->save($data, ['id' => $id]) !== false ) {
                $content = '编辑渠道冻结管理：'.model('Channel')->where(['id' => $list['channel_id']])->value('name');
                $this->insertLog($this->current_node, $content, 65);
                $this->success('编辑成功',url('freezing'));
            }
            $this->error($freezingModel->getError() ?: '编辑失败');
        }

        $data_limit = $this->getChange($list['allow_action']);

        $list['game_list'] = $data_limit['game_list'];
        $list['member_recharge'] = $data_limit['member_recharge'];
        $list['query_discount'] = $data_limit['query_discount'];
        $list['login_list'] = $data_limit['login_list'];
        $list['register_list'] = $data_limit['register_list'];
        $list['pay_list'] = $data_limit['pay_list'];
        $list['sub_package'] = $data_limit['sub_package'];
        $list['detect_channel'] = $data_limit['detect_channel'];
        $list['detect_user'] = $data_limit['detect_user'];
        $list['subchannel_recharge'] = $data_limit['subchannel_recharge'];
        $list['create_channel'] = $data_limit['create_channel'];
        $list['check_balance'] = $data_limit['check_balance'];
        $list['channel_discount'] = $data_limit['channel_discount'];
        $list['operate_package'] = $data_limit['operate_package'];
        $list['game_point'] = $data_limit['game_point'];
        $list['sub_packageurl'] = $data_limit['sub_packageurl'];

        $this->assign('list',$list);
        return $this->fetch('editFreezing');
    }

    /*
     *允许访问的接口拼接
     */
    private function getFreezing()
    {
        $allow = [
            'sync_time' => 'sync_time',
            'hello_world' => 'hello_world',
            'game_list' => input('post.game_list'),
            'member_recharge' => input('post.member_recharge'),
            'query_discount' => input('post.query_discount'),
            'login_list' => input('post.login_list'),
            'register_list' => input('post.register_list'),
            'pay_list' => input('post.pay_list'),
            'sub_package' => input('post.sub_package'),
            'detect_channel' => input('post.detect_channel'),
            'detect_user' => input('post.detect_user'),
            'subchannel_recharge' => input('post.subchannel_recharge'),
            'create_channel' => input('post.create_channel'),
            'check_balance' => input('post.check_balance'),
            'channel_discount' => input('post.channel_discount'),
            'operate_package' => input('post.operate_package'),
            'game_point' => input('post.game_point'),
            'sub_packageurl' => input('post.sub_packageurl'),
        ];
        $allow = array_filter($allow);    //去掉数组中的空值
        $allow = implode(',',$allow);     //将数组转化成字符串
        return $allow;
    }

    /*
     * 外部渠道冻结配置表
     */
    protected function _freezingLimit()
    {
        return 'sync_time,hello_world,game_list,member_recharge,query_discount,login_list,register_list,pay_list,sub_package,detect_channel,detect_user,subchannel_recharge,create_channel,check_balance,channel_discount,operate_package,game_point,sub_packageurl';
    }

}