<?php

namespace app\admin\controller;

use app\common\model\ComplexMembers as MembersModel;
use app\common\model\ComplexChannelModel;
use app\common\model\ComplexPay;
use GuzzleHttp\Exception\RequestException;
use think\Db;
use app\common\library\MakeReport;
use GuzzleHttp\Client;

class Coupon extends Admin
{
    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub

    }

    public function index()
    {
        $where = [];

        //查询参数
        $ganemid = $this->request->get('game_id', '', 'trim');
        $name = $this->request->get('name', '', 'trim');

        if (isset($name) && !empty($name)) {
            $where['name'] = ['like', "%$name%"];
        }
        $order = 'id desc';
        $tmpGameList = model('Common/Game')->getAllByCondition('id,name');
        $gameList = array();
        foreach ($tmpGameList as $game) {
            $gameList[$game['id']] = $game;
        }
        $couponModel = model('Coupon');
        $list = $couponModel
            ->where($where)
            ->where(function ($query) use ($ganemid) {
                if (isset($ganemid) && !empty($ganemid)) {
                    $query->whereRaw(sprintf('FIND_IN_SET(%s,game_id)', $ganemid));
                }
            })
            ->order($order)
            ->paginate(10, false, ['query' => input('get.')])->each(function ($item, $key) use ($gameList) {

                if ($item['game_id']) {
                    $game_ids = explode(',', $item['game_id']);
                    $tmp = [];
                    foreach ($game_ids as $k => $v) {
                        if (isset($gameList[$v])) {
                            $tmp[] = $gameList[$v]['name'];
                        }
                    }
                    $item['game_name'] = implode(', ', $tmp);
                } else {
                    $item['game_name'] = '全部';
                }
                return $item;
            });

        $this->assign('game_list', $tmpGameList);
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    /**
     *增
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            $checkResult = $this->validate($data, 'Coupon.add');
            if (true !== $checkResult) {
                $this->error($checkResult);
            }
            if ($data['select']) {
                $data['game_id'] = $data['select'];
            }

            unset($data['select']);

            $data['update_time'] = $data['create_time'] = time();
            if ($data['type_id'] == 1) {
                $data['start_time'] = strtotime($data['start_time']);
                $data['end_time'] = strtotime($data['end_time']);
                $data['day'] = 0;
            } else {
                $data['start_time'] = 0;
                $data['end_time'] = 0;
            }
            if (model('Coupon')->insertGetId($data)) {
                $this->success('添加成功', url('index'));
            }
            $this->error('添加失败');
        }
        $games = model('Common/Game')->getAllByCondition('id,name', ['cooperation_status' => ['neq', 3]], 'id desc', 'self');
        $this->assign('games', $games);
        return $this->fetch();
    }

    public function edit()
    {
        $id = $this->request->param('id', '', 'intval');
        $appModel = model('Coupon')->where(['id' => $id]);
        if (!$data = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if (empty($id)) {
            $this->error('参数错误!');
        }
        if ($this->request->isPost()) {
            $data = $this->request->post();

            $checkResult = $this->validate($data, 'Coupon.edit');
            if (true !== $checkResult) {
                $this->error($checkResult);
            }
            $data['update_time'] = $data['create_time'] = time();
            if ($data['type_id'] == 1) {
                $data['start_time'] = strtotime($data['start_time']);
                $data['end_time'] = strtotime($data['end_time']);
                $data['day'] = 0;
            } else {
                $data['start_time'] = 0;
                $data['end_time'] = 0;
            }
            $data['update_time'] = time();
            if (model('Coupon')->where(['id' => $id])->update($data)) {
                $this->success('修改成功', url('index'));
            }
            $this->error($appModel->getError() ?: '修改失败');
        }

        if ($data['type_id'] == 1) {
            $data['start_time'] = strtotime($data['start_time']);
            $data['end_time'] = strtotime($data['end_time']);
            $data['day'] = '';
        } else {
            $data['start_time'] = '';
            $data['end_time'] = '';
        }


        if ($data['game_id']) {
            $game = explode(',', $data['game_id']);
            $tmpGameList = model('Common/Game')->getAllByCondition('name', ['id' => ['in', $game]]);

            $this->assign('game_name', implode(',', array_column($tmpGameList, 'name')));
        } else {

            $this->assign('game_name', '全部');
        }



        $this->assign('data', $data);
        return $this->fetch();
    }

    /**
     *删
     */
    public function delete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $appModel = model('Common/Coupon');
        if (!$info = $appModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($info['grant_num'] > 0) {
            $this->error('已存在发放记录 不能删除');
        }
        if ($appModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('index'));
        }
        $error = $appModel->getError();
        $this->error($error ?: '删除失败');
    }

    public function addCoupon()
    {
        if (!$id = input('id')) {
            $this->error('记录不存在');
        }
        $coupon = model('Common/Coupon')->where('id', $id)->whereRaw(sprintf('(type_id=1 and end_time>=%s) or (type_id=2)',Db::quote(date('Y-m-d'))))->find();
        if (!$coupon) {
            $this->error('记录不存在');
        }
        $this->assign('id', $id);
        return $this->fetch();
    }

    public function addCouponSave()
    {
        if (!$id = input('id')) {
            $this->error('记录不存在');
        }
        $coupon = model('Common/Coupon')->where('id', $id)->whereRaw(sprintf('(type_id=1 and end_time>=%s) or (type_id=2)',Db::quote(date('Y-m-d'))))->find();
        if (!$coupon) {
            $this->error('记录不存在');
        }

        if (!$number = (int)input('number')) {
            $this->error('数量不存在');
        }
        if ($number <= 0) {
            $this->error('数量错误');
        }

        for ($i = 0; $i < $number; $i++) {
            $data[] = [
                'coupon_id' => $coupon['id'],
                'examine' => 2,
                'code' => makeUniqueid('Q'),
            ];
        }
        model('Common/Coupon')->startTrans();
        if(!model('Common/Coupon')->where('id', $id)->update(['grant_num'=>$coupon['grant_num']+$number])){
            model('Common/Coupon')->rollback();
            $this->jsonResult('', 0, '生成失败');
        }

        if (model('couponMember')->insertAll($data)) {
            model('Common/Coupon')->commit();
            $this->jsonResult('', 1, '生成成功');
        } else {
            model('Common/Coupon')->rollback();
            $this->jsonResult('', 0, '生成失败');
        }
    }

    public function coupon(){
        $where = [];

        //查询参数
        $username = input('username');
        $name = input('name');
        $id = input('id');
        $is_use = input('is_use');
        $state = input('state');

        if (isset($id) && !empty($id)) {
            $where['a.id'] = $id;
        }
        if (isset($name) && !empty($name)) {
            $where['a.name'] = ['like', "$name%"];
        }
        if (isset($username) && !empty($username)) {
            $where['b.username'] = $username;
        }

        if (isset($is_use) && !empty($is_use)) {
            $where['c.is_use'] = $is_use;
        }

        if (isset($state) && !empty($state)) {
            $where['c.state'] = $state;
        }

        $couponModel = model('Coupon');
        $list = $couponModel
            ->alias('a')
            ->join('cy_coupon_member c', 'a.id=c.coupon_id','left')
            ->join('cy_members b', 'c.member_id=b.id','left')
            ->where($where)
            ->field('c.*,b.username,a.name,a.money,a.min_money,a.start_time as coupon_start_time,a.end_time as coupon_end_time,a.type_id')
            ->order('id desc')
            ->paginate(10, false, ['query' => input()])->each(function ($item, $key) {

                if($item['use_time']) {
                    $item['use_time'] = date('Y-m-d H:i:s',$item['use_time']);
                }
                if($item['use_time'] == 0) {
                    $item['use_time'] = "";
                }

                $dayTime = strtotime(date('Y-m-d'));

                // 类型:1有效时间, 2领取后多少天内有效
                if ($item['type_id'] == 2) {
                    if ($item['member_id'] > 0 && strtotime($item['end_time']) < $dayTime) {
                        $item['state_str'] = '<span style="color: red">(已过期)</span>';
                    } else {
                        $item['state_str'] = '';
                    }
                } else {
                    if (strtotime($item['coupon_end_time']) < $dayTime) {
                        $item['state_str'] = '<span style="color: red">(已过期)</span>';
                    } else {
                        $item['state_str'] = '';
                    }
                }
                return $item;
            });

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    public function frozen()
    {
        $id = input('id');
        $coupon_id = input('coupon_id');
        $coupon = model('Common/CouponMember')->where('id', $id)->find();

        if (!$coupon) {
            $this->error('记录不存在');
        }

        if ($coupon['state'] == 2) {
            $this->success('冻结成功', url('index'));
        }

        if (model('common/CouponMember')->where('id', $id)->update(['state' => 2])) {
            if($coupon_id){
                $this->success('冻结成功', url('Coupon/coupon', ['id'=> $coupon_id]));
            }else{
                $this->success('冻结成功', url('Coupon/index'));
            }
        } else {
            $this->error('冻结失败');
        }
    }
    public function thaw()
    {
        $id = input('id');
        $coupon_id = input('coupon_id');
        $coupon = model('common/CouponMember')->where('id', $id)->find();

        if (!$coupon) {
            $this->error('记录不存在');
        }

        if ($coupon['state'] == 1) {
            $this->success('解冻成功', url('index'));
        }

        if (model('Common/CouponMember')->where('id', $id)->update(['state' => 1])) {
            if($coupon_id){
                $this->success('解冻成功', url('Coupon/coupon', ['id'=> $coupon_id]));
            }else{
                $this->success('解冻成功', url('Coupon/index'));
            }
        } else {
            $this->error('解冻失败');
        }
    }

    public function approved()
    {
        $data = input();
        $state = $data['state'];
        $ids = explode(',', $data['ids']);
        unset($data['ids']);
        if (empty($state)) {
            $this->error("系统错误");
        }
        if (!in_array($state, [1, 2])) {
            $this->error('状态错误');
        }
        if (empty($ids)) {
            $this->error("请选择记录");
        }
        $data = model('common/CouponMember')->whereIn('id', $ids)->column('id');
        if ($data) {
            if (model('common/CouponMember')->whereIn('id', $data)->update(['state' => $state])) {

                $this->success("审核成功");
            } else {
                $this->error('审核失败');
            }
        }

    }
}
