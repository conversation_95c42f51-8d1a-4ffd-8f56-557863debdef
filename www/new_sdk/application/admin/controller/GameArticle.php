<?php
/**
 * 游戏文章管理
 * Created by PhpStorm.
 * User: cqingt
 * Date: 2018/6/28
 * Time: 10:59
 */
namespace app\admin\controller;

class GameArticle extends Admin
{
    protected $model;

    public function _initialize()
    {
        $this->model = model('Common/GameArticle');
    }

    public function index()
    {
        $title = $this->request->param('title', '', 'trim');
        $isShow = $this->request->param('is_show');
        $isEject = $this->request->param('is_eject');
        $condition = [];

        if (! empty($title)) {
            $condition['title'] = ['LIKE', '%' . $title . '%'];
        }

        if (is_numeric($isShow)) {
            $condition['is_show'] = (int)$isShow;
        }

        if (is_numeric($isEject)) {
            $condition['is_eject'] = (int)$isEject;
        }

        $articles = $this->model
            ->field(['id', 'title', 'is_show', 'show_time', 'create_time' , 'is_eject'])
            ->where($condition)
            ->order('id', 'desc')
            ->paginate(8, false, ['query' => $condition]);

        $this->assign('list', $articles);
        $this->assign('page', $articles->render());

        return $this->fetch();
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = [
                'title'     => input('title', '', 'trim'),
                'is_show'   => input('is_show'),
                'is_eject'   => input('is_eject'),
                'show_time' => input('show_time', 0, 'strtotime'),
                'content'   => input('content')
            ];

            $result = $this->validate($data, [
                ['title', 'require', '标题不能为空'],
                ['is_show', 'require|integer', '请选择文章状态|必须为整数'],
                ['is_eject', 'require|integer', '请选择文章状态|必须为整数'],
                ['show_time', 'require', '显示时间不能为空'],
                ['content', 'require', '内容不能为空'],
            ]);

            if (true !== $result) {

                $this->error($result);
            } else {
                $data['is_show'] = (int)$data['is_show'];
                if ($this->model->save($data)) {
                    $this->success('文章新增成功', 'gameArticle/index');
                } else {
                    $this->error($this->model->getError());
                }
            }
        }

        $this->assign('showTime', date('Y-m-d H:i:s'));
        return $this->fetch();
    }

    public function edit($id)
    {
        $article = $this->model->find($id);

        if (empty($article)) {
            $this->error('文章不存在');
        }

        if ($this->request->isPost()) {
            $data = [
                'title'     => input('title', '', 'trim'),
                'is_show'   => input('is_show'),
                'is_eject'   => input('is_eject'),
                'show_time' => input('show_time', 0, 'strtotime'),
                'content'   => input('content')
            ];

            $result = $this->validate($data, [
                ['title', 'require', '标题不能为空'],
                ['is_show', 'require|integer', '请选择文章状态|必须为整数'],
                ['is_eject', 'require|integer', '请选择文章状态|必须为整数'],
                ['show_time', 'require', '显示时间不能为空'],
                ['content', 'require', '内容不能为空'],
            ]);

            if (true !== $result) {
                $this->error($result);
            } else {
                $data['is_show'] = (int)$data['is_show'];
                if ($this->model->update($data, ['id' => $id])) {
                    // 删除游戏公告已读记录
                    model('GameArticleRead')->delRead($id);
                    $this->success('文章修改成功', 'gameArticle/index');
                } else {
                    $this->error($this->model->getError());
                }
            }
        }

        $this->assign('data', $article);
        return $this->fetch();
    }

    public function del($id)
    {
        $article = $this->model->find($id);

        if (empty($article)) {
            $this->error('文章不存在');
        }

        if ($this->model->where(['id' => $id])->delete()) {
            // 删除游戏公告已读记录
            model('GameArticleRead')->delRead($id);
            $this->success('删除成功', 'gameArticle/index');
        } else {
            $this->error($this->model->getError());
        }
    }

    // 游戏推荐
    public function recommend()
    {
        $article = $this->model->getRecommend();

        $this->assign('data', $article);

        return $this->fetch();
    }
}