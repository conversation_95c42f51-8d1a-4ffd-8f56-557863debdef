<?php


namespace app\admin\controller;


use think\Db;
use think\Request;

class GiftApply extends Admin
{
    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
    }


    public function index(Request $request)
    {
        $param = $request->get();
        $query = Db::table("cy_props")->alias("a");
        if (!empty($param['game_id'])) {
            $query->where("a.game_id", $param['game_id']);
        }
        $query->field("a.id,b.name as game_name,a.prop_id,a.name as gift_name,a.create_time,a.content,c.name as channel_name")
            ->join("cy_game b", "b.id = a.game_id")
            ->join("nw_channel c", "c.id = a.admin_id");
        $list = $query->where("a.type", 0)->order("a.id desc")->paginate(20);
        $game_list = $this->getGameList();
        $this->assign("game_list", $game_list);
        $this->assign("list", $list);
        $this->assign("page", $list->render());
        return $this->fetch();
    }

    /**
     * 获取所有游戏
     */
    public function getGameList()
    {
        $game_list = Db::table("cy_game")->where("status", 1)->order("id desc")->field("name,id")->select();
        $result = [];
        foreach ($game_list as $game) {
            $result[$game["id"]] = $game;
        }
        return $result ?: [];
    }

    /**
     * 编辑页面
     */
    public function edit()
    {
        if ($this->request->isPost()) {
            $id = $this->request->post("id");
            $prop_id = $this->request->post("prop_id");
            if (empty($prop_id)) {
                $this->error("礼包编号不能为空");
            }
            $li = [
                "prop_id" => $prop_id,
                "update_time" => time()
            ];
            $update_id = Db::table("cy_props")->where("id", $id)->update($li);
            if ($update_id) {
                $this->success("操作成功", url("index"));
            } else {
                $this->error("操作失败");
            }
        } else {
            $this->assign("id", $this->request->param("id", 0));
            return $this->fetch();
        }
    }
    
    /***
     * 删除
     */
    public function del($id)
    {
        if (empty($id)) {
            $this->error("参数错误");
        }
        $del_id = Db::table("cy_props")->where("id", $id)->delete();
        if ($del_id) {
            $this->success("删除成功");
        } else {
            $this->error("删除失败");
        }
    }		

}
