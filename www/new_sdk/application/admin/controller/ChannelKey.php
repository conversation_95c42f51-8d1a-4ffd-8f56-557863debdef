<?php
/**
 * 渠道密钥管理控制器
 */

namespace app\admin\controller;

use app\common\model\ChannelCa;
use app\common\model\Department;

class ChannelKey extends Admin
{
    
    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();
    }
    
    /**
     * 游戏列表
     */
    public function index()
    {
        $channelCaModel = new ChannelCa;
        $departmentModel= new Department;
        
        $where = [];
        
        //游戏名称
        if(input('request.channel_id')!='')
        {
            $where['c.ca_channel_id'] = input('request.channel_id',0,'intval');
        }
        
        //查询参数
        $param = input('get.');
                                
        $list = $channelCaModel->alias('c')
                                ->join('cy_department d','c.ca_channel_id=d.id','left')
                                ->field('c.*,d.name as channel_name')
                                ->where($where)
                                ->order('c.ca_id desc')->paginate(10,false, array('query' => $param));
                                
        $departmentList = $departmentModel->field('id,name')->where(['flag'=>['in','2,3']])->select();
                                
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('departmentList', $departmentList);
      
        return $this->fetch('list');
    }
    
    /**
     *  删除信息
     */
    public function delete() {
        
        $ca_id = input('ca_id', 0, 'intval');
        
        if(empty($ca_id))
            $this->error('删除记录的ID不能为空');
        
        $channelCaModel = new ChannelCa;
            
        // 判断当前秘钥对是否已被禁用
        $info = $channelCaModel->where(['ca_id'=>$ca_id])->find();
        if(empty($info)) {
           $this->error('记录不存在');
        }
        elseif($info['ca_enable_status'] != 0) {
           $this->error('请先禁用该密钥对');
        }
            
        if($channelCaModel->where(['ca_id'=>$ca_id])->delete()){
            $this->success('删除成功');
        }else{
            $this->error('删除失败');
        }
    }
    
    /**
     * 新增
     * @return mixed|string
     */
    public function create()
    {
        if (request()->isPost()) {
            
            $data = ['ca_channel_id' 	=> input('post.ca_channel_id'),
                    'ca_enable_status'  => input('post.ca_enable_status'),
                    'ca_private_key'    => input('post.ca_private_key','','trim'),
                    'ca_public_key'     => input('post.ca_public_key','','trim'),
                    'ca_realname'       => input('post.ca_realname','','trim'),
                    'ca_mobile'         => input('post.ca_mobile','','trim'),
                    'ca_mail'           => input('post.ca_mail','','trim'),
            ];
            
            $result = $this->validate($data,
                [
                    ['ca_channel_id', 'require|integer', '请选择渠道|渠道ID必须为整型'],
                    ['ca_enable_status', 'require|integer', '秘钥是否启用不能为空|秘钥是否启用必须为整型'],
                    ['ca_private_key', 'require|isValidRsaKey:'.$data['ca_public_key'], '私钥不能为空|不是有效的密钥对'],
                    ['ca_public_key', 'require', '公钥不能为空'],
                    ['ca_realname', 'require|max:50', '联系人姓名不能为空|联系人姓名最大不能超过50个字符'],
                    ['ca_mobile', 'require|mobile', '联系人手机不能为空|联系人手机格式不正确'],
                    ['ca_mail', 'email', '联系人邮箱格式不正确'],
                ]);
            
            if (true !== $result) {
                $this->error($result);
            }
            
            $data['ca_create_time']         = time();
            $data['ca_encryption_key_type'] = 'RSA';
            
            $channelCaModel = new ChannelCa;
            
            if($channelCaModel->insert($data))
                $this->success('新增成功','ChannelKey/index');
            else
                $this->error('新增失败');
        }
        
        $departmentModel= new Department;
        
        $departmentList = $departmentModel->field('id,name')->where(['flag'=>['in','2,3']])->select();
        
        $this->assign('department_list', $departmentList);
        
        return $this->fetch('new');
    }
    
    
    /**
     * 编辑
     * @return mixed|string
     */
    public function edit()
    {
        $ca_id = input('ca_id',0,'intval');
        
        if(empty($ca_id))
            $this->error('ID不能为空');
        
        $channelCaModel = new ChannelCa;
        
        $info = $channelCaModel->where(['ca_id'=>$ca_id])->find();
        
        if(empty($info))
            $this->error('秘钥信息不存在');
        
        if (request()->isPost()) {
            
            $data = [
                'ca_enable_status'  => input('post.ca_enable_status'),
                'ca_realname'       => input('post.ca_realname','','trim'),
                'ca_mobile'         => input('post.ca_mobile','','trim'),
                'ca_mail'           => input('post.ca_mail','','trim'),
            ];
            
            $result = $this->validate($data,
                [
                    ['ca_enable_status', 'require|integer', '秘钥是否启用不能为空|秘钥是否启用必须为整型'],
                    ['ca_realname', 'require|max:50', '联系人姓名不能为空|联系人姓名最大不能超过50个字符'],
                    ['ca_mobile', 'require|mobile', '联系人手机不能为空|联系人手机格式不正确'],
                    ['ca_mail', 'email', '联系人邮箱格式不正确'],
                ]);
            
            if (true !== $result) {
                $this->error($result);
            }
            
            if($channelCaModel->where(['ca_id'=>$ca_id])->update($data))
                $this->success('编辑成功','ChannelKey/index');
            else
                $this->error('编辑失败');
        }
        
        $departmentModel= new Department;
        
        $departmentList = $departmentModel->field('id,name')->where(['flag'=>['in','2,3']])->select();
        
        $this->assign('department_list', $departmentList);
        $this->assign('info', $info);
        
        return $this->fetch('edit');
    }
}
