<?php
/**
 * 游戏管理控制器
 */

namespace app\admin\controller;

use app\common\library\FileUpload;
use think\Db;
use think\Exception;

class VipInfo extends Admin
{
    protected $gameModel;
    protected $gameList;

    // todo 显示的新闻类型
    private $_type
        = array(
            6 => '活动' ,
            7 => '更新' ,
            8 => '新闻' ,
            9 => '充值活动'
        );

    private $_typeList = ['6','7','8','9'];

    // 游戏等级
    protected $gameLevel = ['S' , 'A1' , 'A2' , 'B1' , 'B2' , 'B3' , 'B4' , 'C' , 'D'];

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的初始化方法
     */
    protected function _initialize()
    {
        parent::_initialize();

        //$this->gameModel = new GameModel;
        $this->gameList = $gameList = model('Common/Game')->getAllByCondition('id,name');
    }

    /**
     * 资讯总汇
     */
    public function index()
    {
        $gameid    = input('gameid' , '' , 'filterAndTrimInput');
        $condition = [];
        $count     = [];
        $today     = date('Ymd');
        $mapStr    = '';
        if ($gameid) {
            $condition['a.gameid'] = $gameid;
            $mapStr = "(a.gameid = $gameid and a.type = 0 and a.isdelete=0 and a.sercertype=2)";
        }else{
            $mapStr = "(a.type = 0 and a.isdelete=0 and a.sercertype=2)";
        }
        $count = [
            'new'  => 0 ,
            'test' => 0 ,
            'vip'  => 0 ,
            'news' => 0 ,
        ];

        //开服列表
        $serverNewList = model('ServerInfo')
            ->alias('a')
            ->join('cy_game b' , 'a.gameid = b.id')
            ->where(['a.type' => 0 , 'a.isdelete' => 0 , 'a.sertime' => ['gt' , NOW_TIMESTAMP]])
            ->where($condition)
            ->whereOr($mapStr)
            ->field('a.*,b.name as gameName')
            ->order('a.sercertype asc, a.sertime asc')
            ->limit(5)
            ->select();
        if (count($serverNewList) == 5) {
            $count['new'] = 5;
        }
        foreach ($serverNewList as &$newlist) {
            if ($newlist['sercertype'] == 1){
                if (date('Ymd') == date('Ymd' , $newlist['sertime'])) {
                    $newlist['color']   = '#ff6600';
                    $newlist['sertime'] = '今天' . date("H:i" , $newlist['sertime']);
                }else {
                    $newlist['sertime'] = date("Y-m-d H:i:s" , $newlist['sertime']);
                }
            }elseif ($newlist['sercertype'] == 2) {
                $newlist['sertime'] = '长期有效';
            }
        }

        //开测列表
        $serverTestList = model('ServerInfo')->alias('a')->join('cy_game b' , 'a.gameid = b.id')
            ->where(['a.type' => 1 , 'a.isdelete' => 0 , 'a.sertime' => ['gt' , NOW_TIMESTAMP]])
            ->where($condition)
            ->field('a.*,b.name as gameName')
            ->order('a.sertime asc')
            ->limit(5)
            ->select();
        if (count($serverTestList) == 5) {
            $count['test'] = 5;
        }

        $where['endtime']  = array('gt' , NOW_TIMESTAMP);
        $where['total']    = array('gt' , 0);
        $where['isdelete'] = 0;
        foreach ($serverTestList as &$testlist) {
            $where['gameid'] = $testlist['gameid'];
            $result          = model('Libaoinfo')->where($where)->select();
            if ($result) {
                foreach ($result as $data) {
                    if ($data['total'] > $data['used']) {
                        $testlist['gift'] = 1;
                        break;
                    }
                }
            }
            if ($today == date('Ymd' , $testlist['sertime'])) {
                $testlist['color']   = '#ff6600';
                $testlist['sertime'] = '今天' . date("H:i" , $testlist['sertime']);
            } else {
                $testlist['sertime'] = date("Y-m-d H:i:s" , $testlist['sertime']);
            }
        }

        //VIP价格列表
        $vipPriceList = model('GameVipPrice')->alias('a')->join('cy_game b' , 'a.gameid = b.id')
            ->where($condition)->field('a.*,b.name as gameName')
            ->order('a.online_time desc')->limit(5)->select();
        if (count($vipPriceList) == 5) {
            $count['vip'] = 5;
        }

        //新闻资讯列表
        $condition['isdelete'] = 0;
        $condition['type']     = ['in' , $this->_typeList];
        $newsInfoList          = model('Article')->alias('a')->where($condition)->order('a.zhiding desc,a.create_time desc')->limit(5)->select();
        foreach ($newsInfoList as &$newslist) {
            $newslist['new'] = '';
            if ($today == date('Ymd' , $newslist['create_time'])) {
                $newslist['new'] = '1';
            }
        }
        if (count($newsInfoList) == 5) {
            $count['news'] = 5;
        }

        // 数据绑定
        $this->assign('type_list' , $this->_type);
        $this->assign('gamelist' , $this->gameList);
        $this->assign('count' , $count);
        $this->assign('serverNewList' , $serverNewList);
        $this->assign('serverTestList' , $serverTestList);
        $this->assign('vipPriceList' , $vipPriceList);
        $this->assign('newsInfoList' , $newsInfoList);
        return $this->fetch('index');
    }

    /**
     * 开服列表
     */
    public function servernewlist()
    {
        $gameid = input('gameid' , '' , 'filterAndTrimInput');
        $start  = input('start' , strtotime('today') , 'strtotime'); // 默认时间
        $end    = input('end' , 0 , 'strtotime');

        $condition = [];
        $mapStr = '';
        if ( !empty($gameid)) {
            $condition['gameid'] = $gameid;
            $mapStr = "(gameid = $gameid and type = 0 and isdelete=0 and sercertype=2)";
        }else{
            $mapStr = "(type = 0 and isdelete=0 and sercertype=2)";
        }


        $time_condition = makeTimeCondition(input('start') , input('end'));
        if ($time_condition && 0 < count($time_condition)) {
            $condition['sertime'] = $time_condition;
        }

        $end = $end ? $end + 24 * 3600 - 1 : 0; // 查询日期当天
        $runable = true;
        if ($start && $end) {
            $condition['sertime'] = ['BETWEEN' , [$start , $end]];
            if ($start > $end){
                $runable = false;
            }

        } else if ($start) {
            $condition['sertime'] = ['EGT' , $start];
        } else if ($end) {
            $condition['sertime'] = ['ELT' , $end];
        }

        $condition['type']     = 0;
        $condition['isdelete'] = 0;

        if (!$runable){
            $mapStr = '';
        }

        $list = model('ServerInfo')
            ->field('*')
            ->where($condition)
            ->whereOr($mapStr)
            ->order('sercertype asc, sertime asc')
            ->paginate(10, false, array('query' => input('get.')))
            ->each(function($newlist, $key){
                if ($newlist['sercertype'] == 1){
                    if (date('Ymd') == date('Ymd' , $newlist['sertime'])) {
                        $newlist['color']   = '#ff6600';
                        $newlist['sertime'] = '今天' . date("H:i" , $newlist['sertime']);
                    }else {
                        $newlist['sertime'] = date("Y-m-d H:i:s" , $newlist['sertime']);
                    }
                }elseif ($newlist['sercertype'] == 2) {
                    $newlist['sertime'] = '长期有效';
                }
                return $newlist;
            });

        $this->assign('page' , $list->render());
        $this->assign('gamelist' , $this->gameList);
        $this->assign('list' , $list);
//        $this->assign('count' , count($listData));
        $this->assign('start' , $start ? date('Y-m-d' , $start) : '');
        return $this->fetch('serverNewList');
    }

    /**
     * vip价格表
     */
    public function vipPriceList()
    {
        $gameid    = input('gameid' , '' , 'filterAndTrimInput');
        $condition = [];
        if ($gameid) {
            $condition['gameid'] = $gameid;
        }
        $time_condition = makeTimeCondition(input('start') , input('end'));
        if ($time_condition && 0 < count($time_condition)) {
            $condition['online_time'] = $time_condition;
        }

        $list = model('GameVipPrice')
            ->order('online_time desc')->where($condition)
            ->paginate(10 , false , ['query' => $condition]);

        $this->assign('list' , $list);
        $this->assign('gamelist' , $this->gameList);
        $this->assign('page' , $list->render());
        return $this->fetch('vipPriceList');
    }

    /**
     * 新闻列表
     */
    public function newsInfo()
    {
        $condition = [];
        $title     = input('title' , '' , 'filterAndTrimInput');
        $gameid    = input('gameid' , '' , 'intval');
        $type      = input('type' , '' , 'intval');
        if ($title) {
            $condition['title'] = array('LIKE' , '%' . $title . '%');
        }
        $time_condition = makeTimeCondition(input('start'), input('end'));
        if ( $time_condition && 0 < count($time_condition) ) {
            $condition['create_time'] = $time_condition;
        }
        $condition['isdelete'] = 0;
        $condition['type'] = ['in',$this->_typeList];
        if($type){
            $condition['type'] = $type;
        }
        if($gameid){
            $condition['gameid'] = $gameid;
        }

        $newsInfoList = model('Article')->table('cy_article a,cy_content c')
            ->field('a.*,c.content')
            ->where('a.id=c.id')
            ->where($condition)->order('zhiding desc,create_time desc')->paginate(20, false, array('query' => input('get.')))
            ->each(function($row, $key){
                $today     = date('Ymd');
                //$row['isNew'] = (bool)($row['create_time'] > $today);
                $row['content'] = mb_substr(str_replace("　　", '', strip_tags($row['content'])),0,100,'UTF-8') ."...";
                if ($today == date('Ymd' , $row['create_time'])) {
                    $row['new'] = '1';
                }else{
                    $row['new'] = '0';
                }

                return $row;
            });
        /*$list = $newsInfoList->toArray();
        foreach ($list['data'] as &$row){
            $row['isNew'] = (bool)($row['create_time'] > $today);
            $row['content'] = mb_substr(str_replace("　　", '', strip_tags($row['content'])),0,100,'UTF-8') ."...";
        }*/

        $this->assign('page',$newsInfoList->render());
        $this->assign('type_list', $this->_type);
        $this->assign('gamelist',$this->gameList);
        $this->assign('list',$newsInfoList);


//        dump($newsInfoList);
        return $this->fetch('newsInfo');
    }

    /**
     * 新闻详情页
     */
    public function detailNews(){
        $condition = [];
        $id = input('id','','filterAndTrimInput');
        $condition['id'] = $id;
        $content = model('Content')->where($condition)->value('content');

        $condition['isdelete'] = 0;
        $data = model('Article')->where($condition)->find();
        $data['content'] = $content;

        $next = model('Article')->where(['id'=>($id-1),'type'=>['in',$this->_typeList]])->find();
        $last = model('Article')->where(['id'=>($id+1),'type'=>['in',$this->_typeList]])->find();

        $contact = model('Article')->where(
            ['gameid'=>$data['gameid'],
             'type'=>['in',$this->_typeList],
             'id'=>['not in',[$id]
             ]])
            ->limit(6)->order('create_time desc')->select();

        foreach ($contact as &$info){
            $info['title2'] = $info['title'];
            if(strlen($info['title']) > 20){
                $info['title'] = mb_substr(str_replace("　　", '', strip_tags($info['title'])),0,12,'UTF-8') ."...";
            }
        }

        $this->assign('contact',$contact);
        $this->assign('data',$data);
        $this->assign('last',$last);
        $this->assign('next',$next);
        $this->assign('content',$content);
        return $this->fetch('detailNews');
    }

    /**
     * 开测表
     */
    public function serverTestList(){
        $gameId = input('gameid', '', 'intval');
        $start  = input('start', strtotime('today'), 'strtotime'); // 默认时间
        $end    = input('end', 0, 'strtotime');

        $condition = [];
        if ( !empty($gameId)) {
            $condition['gameid'] = $gameId;
        }

        $condition['type'] = 1; // 开服
        $condition['isdelete'] = 0; // 未删除的

        $runable = true;
        if ($start && $end) {
            $condition['sertime'] = ['BETWEEN', [$start, $end]];
            if ($start > $end){
                $runable = false;
            }
        } else if ($start) {
            $condition['sertime'] = ['EGT', $start];
        } else if ($end) {
            $condition['sertime'] = ['ELT', $end];
        }

        if (!$runable){
            $mapStr = '';
        }

        $list = model('ServerInfo')
            ->field('*')
            ->where($condition)
            ->order('sertime ASC')
            ->paginate(10, false, array('query' => input('get.')))
            ->each(function($item, $key){
                $item['sertime'] = $this->_getPrettyTime($item['sertime']);
                return $item;
            });

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('gamelist' , $this->gameList);
        return $this->fetch('serverTest');

    }

    // 格式化时间
    protected function _getPrettyTime($time)
    {
        $today    = strtotime('today');
        $tomorrow = strtotime('tomorrow');
        $prefix   = '';

        if ($time >= $today && $time < $tomorrow ) {
            $prefix = '今天 ';
        } else if ($time >= $tomorrow && $time < $tomorrow + 24 * 3600) {
            $prefix = '明天 ';
        }

        if ($prefix) {
            return '<span style="color: rgb(235, 140, 63)">' . $prefix . date('H:i', $time) .'</span>';
        }

        return date('Y-m-d H:i', $time);
    }


}
