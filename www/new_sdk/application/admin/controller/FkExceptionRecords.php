<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Request;

class FkExceptionRecords extends Controller
{
    /**
     * 异常记录列表页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $page = input('get.page', 1);
            $limit = input('get.limit', 20);
            $where = [];
            
            // 搜索条件
            $exceptionType = input('get.exception_type', '');
            if ($exceptionType) {
                $where['exception_type'] = $exceptionType;
            }
            
            $exceptionValue = input('get.exception_value', '');
            if ($exceptionValue) {
                $where['exception_value'] = ['like', "%{$exceptionValue}%"];
            }
            
            $exceptionContent = input('get.exception_content', '');
            if ($exceptionContent) {
                $where['exception_content'] = ['like', "%{$exceptionContent}%"];
            }
            
            $startTime = input('get.start_time', '');
            $endTime = input('get.end_time', '');
            if ($startTime && $endTime) {
                $where['created_at'] = ['between', [$startTime, $endTime]];
            } elseif ($startTime) {
                $where['created_at'] = ['>=', $startTime];
            } elseif ($endTime) {
                $where['created_at'] = ['<=', $endTime];
            }
            
            $count = Db::name('fk_exception_records')->where($where)->count();
            $data = Db::name('fk_exception_records')
                ->where($where)
                ->page($page, $limit)
                ->order('record_id desc')
                ->select();
            
            // 计算剩余有效时间
            foreach ($data as &$item) {
                $item['remaining_days'] = getRemainingDays($item['validity_expire_time']);
                if(!empty($item['validity_expire_time'])){
                    $item['validity_expire_time'] = date("Y-m-d H:i:s", $item['validity_expire_time']);
                }
            }

            return json([
                'code' => 200,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }
        
        return $this->fetch();
    }
    
    /**
     * 查看详情
     */
    public function detail($id)
    {
        $info = Db::name('fk_exception_records')->where('record_id', $id)->find();
        if (!$info) {
            $this->error('异常记录不存在');
        }
        
        // 计算剩余有效时间
        $remainingTime = $info['validity_expire_time'] - time();
        $info['remaining_time'] = $remainingTime > 0 ? $remainingTime : 0;
        $info['is_expired'] = $remainingTime <= 0 ? 1 : 0;
        
        $this->assign('info', $info);
        return $this->fetch();
    }
    
    /**
     * 处理异常记录
     */
    public function handle($id = null)
    {
        if ($this->request->isGet() && $id) {
            $info = Db::name('fk_exception_records')->where('record_id', $id)->find();
            if (!$info) {
                return json(['code' => -100, 'msg' => '异常记录不存在']);
            }
            return json(['code' => 200, 'data' => $info]);
        }
        
        if ($this->request->isPost()) {
            $record_id = input('post.record_id');
            $handle_type = input('post.handle_type');
            $handle_remark = input('post.handle_remark', '');
            
            if (!$record_id || !$handle_type) {
                return json(['code' => -100, 'msg' => '参数错误']);
            }
            
            // 获取异常记录信息
            $record = Db::name('fk_exception_records')->where('record_id', $record_id)->find();
            if (!$record) {
                return json(['code' => -100, 'msg' => '异常记录不存在']);
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 根据处理方式执行不同操作
                if ($handle_type == 'add_blacklist' || $handle_type == 'add_whitelist') {
                    $list_type = $handle_type == 'add_blacklist' ? 'black' : 'white';
                    
                    // 检查是否已存在
                    $exists = Db::name('fk_roster_lists')->where([
                        'list_type' => $list_type,
                        'object_type' => $record['exception_type'],
                        'object_value' => $record['exception_value']
                    ])->find();
                    
                    if (!$exists) {
                        Db::name('fk_roster_lists')->insert([
                            'list_type' => $list_type,
                            'object_type' => $record['exception_type'],
                            'object_value' => $record['exception_value'],
                            'remark' => '来自异常记录处理：' . $handle_remark
                        ]);
                    }
                }
                
                // 更新异常记录状态（可以添加一个handle_status字段）
                // Db::name('fk_exception_records')->where('record_id', $record_id)->update(['handle_status' => 1]);
                
                Db::commit();
                return json(['code' => 200, 'msg' => '处理成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => -100, 'msg' => '处理失败：' . $e->getMessage()]);
            }
        }
    }
    
    /**
     * 删除异常记录
     */
    public function delete()
    {
        $id = input('post.id');
        if (!$id) {
            return json(['code' => -100, 'msg' => '参数错误']);
        }
        
        try {
            Db::name('fk_exception_records')->where('record_id', $id)->delete();
            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => -100, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 导出数据
     */
    public function export()
    {
        $where = [];
        
        // 搜索条件
        $exceptionType = input('get.exception_type', '');
        if ($exceptionType) {
            $where['exception_type'] = $exceptionType;
        }
        
        $exceptionValue = input('get.exception_value', '');
        if ($exceptionValue) {
            $where['exception_value'] = ['like', "%{$exceptionValue}%"];
        }
        
        $data = Db::name('fk_exception_records')
            ->where($where)
            ->order('record_id desc')
            ->limit(10000)
            ->select();
            
        // 这里实现CSV导出逻辑
        // ...
        
        return json(['code' => 200, 'msg' => '导出成功']);
    }
} 