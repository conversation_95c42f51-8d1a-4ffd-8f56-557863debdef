<?php
/**
 * 留存统计
 * Created by PhpStorm.
 * User: hhhhh
 * Date: 2019/1/8
 * Time: 18:02
 */

namespace app\admin\controller;


use app\service\RetaineService;
use think\Controller;
use think\Db;
use think\Exception;
use think\Model;

class RetainePlatformChannel extends Controller
{
    /**
     * 留存表-游戏-新增玩家
     * @return string
     */
    public function reg_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //当天注册账号
        $mebmers = $retaineService->getMembers($day);


        $list = $retaineService->regNum($day, $mebmers,0,1,1);

        $retaineService->insertOrUpdatePlatform($list, 2, $day, 'reg_num');

        return 'ok';

    }

    /**
     * 留存表-游戏-新增角色
     * @return string
     */
    public function role_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //当天新增角色
        $list = $retaineService->getFirstRoleMember($day,0,1,1);

        $retaineService->insertOrUpdatePlatform($list, 2, $day, 'role_num');

        return 'ok';

    }

    /**
     * 留存表-游戏-活跃玩家
     * @return string
     */
    public function act_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //当天活跃玩家
        $list = $retaineService->activeMember($day, 'act_num',0,1,[],1);

        $retaineService->insertOrUpdatePlatform($list, 2, $day, 'act_num');

        return 'ok';

    }

    /**
     * 留存表-游戏-充值金额-付费人数
     * @return string
     */
    public function recharge_num()
    {

        $day = input('day', '');
        if (!$day) {
            $day = date('Y-m-d', strtotime('-1 day'));
        }

        $retaineService = new RetaineService();

        //充值金额
        $list = $retaineService->rechargeMoney($day,0,1,1);

        $retaineService->insertOrUpdatePlatform($list, 2, $day, 'recharge_num');

        $retaineService->insertOrUpdatePlatform($list, 2, $day, 'pay_num');

        return 'ok';

    }

    private function getTime($day, $num)
    {
        return date('Y-m-d', strtotime($day) - 86400 * $num);
    }


}
