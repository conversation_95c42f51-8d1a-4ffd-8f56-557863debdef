<?php
/**
 * 充值记录管理控制器
 *
 */

namespace app\admin\controller;

use app\common\library\MakeReportGo;
use app\common\model\PaySupconModel;
use think\Db;
use app\common\library\MakeReport;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use think\Exception;

class Pay extends Admin
{
    private   $condition = [];
    protected $start;
    protected $end;
    protected $noCheckAuth = ['getChannel'];
    private   $gameIds = [];     //海外游戏id

    protected function _initialize()
    {
        parent::_initialize();

        $this->gameList  = $gameList = model('Common/Game')->getAllByCondition('id,name');
        $tmpSelfGameList = model('Common/Game')->getAllByCondition('id,name', [],'','self');
        $selfGameList = array();
        foreach ($tmpSelfGameList as $game) {
            $selfGameList[ $game['id']] = $game;
        }
        $this->selfGameList  = $selfGameList;
        $this->gameIds = config('haiwai_game_ids_arr');
    }


    /**
     * 前置方法(参数获取)
     */
    protected function _beforeGetParams()
    {
        
        $start          = $this->request->param('start');
        $end            = $this->request->param('end');
        $channel_id     = $this->request->param('channel_id', 0, 'intval');
        $top_channel_id = $this->request->param('parent_channel_id', 0, 'intval');
        $bplus_channel_id = $this->request->param('bplus_channel_id', 0, 'intval');
        $gameid         = $this->request->param('gameid', 0, 'trim');
        $status         = $this->request->param('status');
        // $cp_status         = $this->request->param('cp_status');
        $orderid        = $this->request->param('orderid', '', 'trim');
        $username       = $this->request->param('username', '', 'trim');
        // $paytype        = $this->request->param('paytype', '', 'trim');
        $servername     = $this->request->param('servername', '', 'trim');
        $roleid     = $this->request->param('roleid', '', 'trim');
        $rolename     = $this->request->param('rolename', '', 'trim');
        $condition = [];

        if (!empty($gameid)) {
            $gameids = explode(',', $gameid);
            if(count($gameids) > 1){
                $gameid = ['in', $gameids];
            }
            $condition['p.gameid'] = $gameid;
        }
        if (!empty($orderid)) {
            $condition['p.orderid'] = $orderid;
        }
        // if (!empty($paytype)) {
        //     if ($paytype == "wx") {
        //         $condition['p.paytype'] = ['IN', ['wxpay-h5', 'ybzf_wxmp_h5', 'coin-wxpay-h5', 'coin-ybzf_wxmp_h5', 'mix-wxpay-h5', 'mix-ybzf_wxmp_h5']];
        //     } else if($paytype == "zfb") {
        //         $condition['p.paytype'] = ['IN', ['zfb', 'old-zfb-wap', 'coin-zfb', 'old-zfb-wap', 'mix-zfb', 'mix-old-zfb-wap']];
        //     } else if($paytype == "coin") {
        //         $condition['p.paytype'] = ['IN', ['coinpay', 'coin-zfb', 'coin-old-zfb-wap', 'coin-wxpay-h5', 'coin-ybzf_wxmp_h5']];
        //     } else if($paytype == "coupon") {
        //         $condition['p.paytype'] = ['IN', ['coupon', 'mix-zfb', 'mix-old-zfb-wap', 'mix-wxpay-h5', 'mix-ybzf_wxmp_h5']];
        //     }
        // }

        // if (!empty($paytype)) {
        //     $condition['p.paytype'] = $paytype;
        // }

        if (is_numeric($status)) {
            $condition['p.status'] = (int)$status;
        }
        // if (!empty($cp_status)) {
        //     $condition['paycpinfo.status'] = $cp_status;
        // }

        if (!empty($username)) {
            //  $condition['p.username'] = ['LIKE', '%' . $username . '%'];
            //改成精确查询
            $condition['p.username'] = $username;
        }
        if (!empty($servername)) {
            $condition['p.servername'] = $servername;
        }
        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));

        if( $business != -1){
            if($bplus_channel_id){
                if(!in_array($bplus_channel_id,$business)){
                    $bplus_channel_id = -1;
                }
            }else{
                if($business && $business != -2){
                    $bplus_channel_id = implode(',',$business);
                }else{
                    $bplus_channel_id = -1;
                }
            }

        }

        //联盟/公会/渠道有选择时判断
        if(!empty($channel_id) || !empty($top_channel_id) || !empty($bplus_channel_id)){
            if(!empty($channel_id) && !empty($top_channel_id) && !empty($bplus_channel_id)){
                unset($condition['p.channel_id']);

                $bChannelIds = get_child_channel_arr($top_channel_id);
                array_push($bChannelIds,$top_channel_id);
                $bplusChannelIds = get_child_channel_arr($bplus_channel_id);
                $bplusChannelIds = array_merge($bplusChannelIds,explode(',',$bplus_channel_id));
                $channelIds = array_intersect($bChannelIds,$bplusChannelIds,array($channel_id));
                if($channelIds){
                    $condition['p.channel_id'] =  ['in', $channelIds];

                }
                else{
                    $condition['p.channel_id'] = -1;
                }
            }
            else if(!empty($top_channel_id) && !empty($bplus_channel_id)){
                $bChannelIds = get_child_channel_arr($top_channel_id);
                array_push($bChannelIds,$top_channel_id);

                $bplusChannelIds = get_child_channel_arr($bplus_channel_id);
                $bplusChannelIds = array_merge($bplusChannelIds,explode(',',$bplus_channel_id));

                $channelIds = array_intersect($bChannelIds,$bplusChannelIds);

                if($channelIds){
                    $condition['p.channel_id'] =  ['in', $channelIds];
                }
                else{
                    $condition['p.channel_id'] = -1;
                }
            }
            elseif (!empty($channel_id) && !empty($bplus_channel_id)) {

                $bplusChannelIds = get_child_channel_arr($bplus_channel_id);

                $bplusChannelIds = array_merge($bplusChannelIds,explode(',',$bplus_channel_id));
                $channelIds = array_intersect($bplusChannelIds,array($channel_id));

                if($channelIds){
                    $condition['p.channel_id'] =  ['in', $channelIds];
                }
                else{
                    $condition['p.channel_id'] = -1;
                }
            }
            elseif (!empty($channel_id) && !empty($top_channel_id)) {

                $bChannelIds = get_child_channel_arr($top_channel_id);
                array_push($bChannelIds,$top_channel_id);

                $channelIds = array_intersect($bChannelIds,array($channel_id));

                if($channelIds){
                    $condition['p.channel_id'] =  ['in', $channelIds];
                }
                else{
                    $condition['p.channel_id'] = -1;
                }
            }
            elseif (!empty($top_channel_id)) {

                $channelIds = get_child_channel_arr($top_channel_id);
                array_push($channelIds,$top_channel_id);

                $condition['p.channel_id'] = ['in',$channelIds];
            }
            elseif (!empty($bplus_channel_id)) {

                $channelIds = get_child_channel_arr($bplus_channel_id);
                $channelIds = array_merge($channelIds,explode(',',$bplus_channel_id));
                $condition['p.channel_id'] = ['in',$channelIds];
            }
            elseif (!empty($channel_id)) {
                $condition['p.channel_id'] = $channel_id;
            }
        }

        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition['p.pay_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition['p.pay_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition['p.pay_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
            // $start = date('Y-m-d', time());
            // $end   = date('Y-m-d', time());
            //
            // $condition['p.create_time'] = [
            //     ['>=', strtotime($start)],
            //     ['<=', strtotime($end . ' 23:59:59')],
            // ];
        }
        // if ($username=='' && $orderid==''){
        //     if(strtotime($end.' 23:59:59')-strtotime($start)>31*24*3600)
        //     {
        //         $this->error('单次查询日期的最长跨度为31天');
        //     }
        // }

        if (!empty($roleid)) {
            $condition['p.roleid'] = $roleid;
        }
        if (!empty($rolename)) {
            $condition['p.rolename'] = $rolename;
        }
        
        $this->start = $start;
        $this->end   = $end;
        $this->setCondition($condition);
    }

    /**
     * 充值记录管理(抓取)
     *
     * @return mixed|string
     */
    public function index()
    {
        $this->_beforeGetParams();
        $condition = $this->getCondition();

        //充值总金额
        $totalAmount = $todayTotalAmount = $lastDayTotalAmount =0;

        $PayModel = model('Common/Pay');
        $paytype = $this->request->param('paytype', '', 'trim');
        $pageSize = input('page_size', 15);

        if (!empty($paytype)) {
            if ($paytype == "wx") {
                $paytype = [
                    "wxpay-h5", "coin-wxpay-h5", "mix-wxpay-h5",
                    "ybzf_wxmp_h5", "coin-ybzf_wxmp_h5", "mix-ybzf_wxmp_h5",
                    "qzl_wx_h5", "coin-qzl_wx_h5", "mix-qzl_wx_h5",
                    'xty_wx_h5', "coin-xty_wx_h5", "mix-xty_wx_h5",
                    'qmf_wxmp_h5', "coin-qmf_wxmp_h5", "mix-qmf_wxmp_h5",
                    'yyyb_wx_h5', "coin-yyyb_wx_h5", "mix-yyyb_wx_h5",
                    "dh_wx"
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            } else if($paytype == "zfb") {
                $paytype = [
                    "zfb", "coin-zfb", "mix-zfb",
                    "old-zfb-wap", "coin-old-zfb-wap", "mix-old-zfb-wap",
                    'ldys_zfb_h5', "coin-ldys_zfb_h5", "mix-ldys_zfb_h5",
                    "xty_ali_h5", "coin-xty_ali_h5", "mix-xty_ali_h5",
                    'yyyb_ali_h5', "coin-yyyb_ali_h5", "mix-yyyb_ali_h5",
                    "dh_zfb"
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            } else if($paytype == "coin") {
                $paytype = [
                    "coinpay",
                    "coin-zfb", "coin-old-zfb-wap",
                    "coin-ldys_zfb_h5", "coin-xty_ali_h5", "coin-yyyb_ali_h5",
                    "coin-qzl_wx_h5", "coin-ldys_wx_h5", "coin-yyyb_wx_h5", "coin-ybzf_wxmp_h5", "coin-xty_wx_h5",
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            } else if($paytype == "coupon") {
                $paytype = [
                    "coupon",
                    "mix-zfb", "mix-old-zfb-wap",
                    "mix-ldys_zfb_h5", "mix-xty_ali_h5", "mix-yyyb_ali_h5",
                    "mix-qzl_wx_h5", "mix-ldys_wx_h5", "mix-yyyb_wx_h5", "mix-ybzf_wxmp_h5", "mix-xty_wx_h5",
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            }
        }

        $cp_status         = $this->request->param('cp_status'); // 游戏通知状态:0（通知失败）1（通知成功）
        if (is_numeric($cp_status)) {
            $condition['paycpinfo.status'] = (int)$cp_status;
        }
        $gh_status = input('gh_status', 0); // 是否聚合用户：0=否、1=是
        if ($gh_status<>'') {
            $condition['channel.gh_status'] = $gh_status;
        }

        // http://admin.7dgame.cn/Pay/index.html?start=2024-07-09&end=2024-07-09&gameid=&servername=&status=&cp_status=&paytype=&orderid=&username=&channel_id=&parent_channel_id=&bplus_channel_id=&page=&page_size=100&gh_status=1
        $list = $PayModel->alias('p')
            ->join('nw_channel channel','p.channel_id=channel.id')
            ->join('cy_paycpinfo paycpinfo','p.orderid=paycpinfo.orderid','left')
            ->field('p.id,p.orderid,p.gameid,p.username,p.userid,p.amount,p.real_amount,p.real_ptb,p.roleid,p.rolelevel,p.rolename,p.serverid,p.servername,p.status,p.paytype,p.create_time,p.channel_id,p.productname,p.recheck_status,p.attach,p.real_coin,p.coupon_member_id,p.coupon_amount,paycpinfo.status as cp_status, p.pay_amount, FROM_UNIXTIME(p.pay_time, "%Y-%m-%d %H:%i:%s") as pay_time, p.serverid, p.servername')
            ->where($condition)
            // ->whereNotIn('gameid',$this->gameIds) // 排除海外游戏
            ->order('pay_time desc')
            ->paginate($pageSize, false, ['query' => input('get.')]);
        $parent_list  = model('Common/Channel')->getParentChannel(); // 获取所有父类渠道

        if ($list->total()>0) {
            $data      = $list->toArray()['data'];
            $userIdArr = $paytype = $gameIdArr = $channelIdArr = $ttbhistoryArr = [];
            foreach ($data as $v) {
                $userIdArr[]    = $v['userid'];
                $paytype[]      = $v['paytype'];
                $gameIdArr[]    = $v['gameid'];
                $channelIdArr[] = $v['channel_id'];
            }
            //关联数据查询
            $gameNameArr = model('Common/Game')->whereIn('id', array_unique($gameIdArr))->column('name', 'id');
            $regTimeArr  = model('Common/Members')->whereIn('id', array_unique($userIdArr))->column('reg_time', 'id');
            foreach ($list as $key => $item) {
                $default = '--';

                $list[ $key ]['game_name']      = isset($gameNameArr[ $item['gameid'] ]) ? $gameNameArr[ $item['gameid'] ] : $default;
                $list[ $key ]['reg_time']       = isset($regTimeArr[ $item['userid'] ]) ? date("Y-m-d H:i:s", $regTimeArr[ $item['userid'] ]) : $default;

                $channel = get_top_second_channel_name($list[ $key ]['channel_id']);

                $list[ $key ]['channel_name']   = $channel['channel_name'];
                $list[ $key ]['second_channel'] = $channel['second_name'];
                $list[ $key ]['top_channel']    = $channel['top_name'];
                $list[ $key ]['union_name']     = get_union_channel($list[ $key ]['channel_id'])['name'];
            }

            //支付成功 或者支付查询条件未选择时
            if(input('status')==1 || input('status')==''){
                //充值完成总金额
                $totalAmount = $PayModel->alias('p')
                    ->join('nw_channel channel','p.channel_id=channel.id')
                    ->join('cy_paycpinfo paycpinfo','p.orderid=paycpinfo.orderid','left')
                    ->field('sum(pay_amount) as total_amount,paycpinfo.status as cp_status')
                    ->where($condition)->whereNotIn('gameid',$this->gameIds)->where('p.status=1')->find()['total_amount'];
            }
        }

        $where = array();
        $where['p.create_time'] = [
            ['>=', strtotime(date('Y-m-d'))],
            ['<=', strtotime(date('Y-m-d') . ' 23:59:59')],
        ];
        unset($condition["p.create_time"]);

        $todayTotalAmount = floatval(model('Pay')->alias("p")
            ->join('nw_channel channel','p.channel_id=channel.id')
            ->join('cy_paycpinfo paycpinfo','p.orderid=paycpinfo.orderid','left')
            ->where($condition)
            // ->whereNotIn('gameid',$this->gameIds)
            ->field('sum(pay_amount) as total_amount,paycpinfo.status as cp_status')->where('p.status=1')->where($where)->find()['total_amount']);

        $where = array();
        $where['p.create_time'] = [
            ['>=', strtotime(date("Y-m-d",strtotime("-1 day")))],
            ['<', strtotime(date('Y-m-d'))],
        ];
        $lastDayTotalAmount = floatval(model('Pay')
            ->alias("p")->where($condition)
            ->join('nw_channel channel','p.channel_id=channel.id')
            ->join('cy_paycpinfo paycpinfo','p.orderid=paycpinfo.orderid','left')
            // ->whereNotIn('gameid',$this->gameIds)
            ->field('sum(pay_amount) as total_amount,paycpinfo.status as cp_status')->where('p.status=1')->where($where)->find()['total_amount']);

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('paytype', config('paytype'));
        $this->assign('new_pay_type', config('new_pay_type'));
        $this->assign('game_list', $this->selfGameList);
        $this->assign('channel_id', $this->request->get('channel_id'));          //查询条件中的渠道ID
        $this->assign('parent_list', $parent_list);          //顶级渠道列表
        $this->assign('start', $this->start);
        $this->assign('end', $this->end);
        $this->assign('totalAmount', $totalAmount);
        $this->assign('todayTotalAmount', $todayTotalAmount);
        $this->assign('lastDayTotalAmount', $lastDayTotalAmount);

        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        $channelWhere = [];
        if( $business != -1){
            if($business && $business != -2){
                $channelWhere['id'] = ['in',$business];
            }else{
                $channelWhere['id'] = -1;
            }
        }

        $uniom = model('Channel')->where(['level'=>0])->where($channelWhere)->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表

        return $this->fetch();
    }

    // 充值记录管理展示
    public function list()
    {
        $this->_beforeGetParams();
        $condition = $this->getCondition();

        //充值总金额
        $totalAmount = $todayTotalAmount = $lastDayTotalAmount = 0;

        $PayModel = model('Common/Pay');
        $paytype = $this->request->param('paytype', '', 'trim');
        $pageSize = input('page_size', 15);

        if (!empty($paytype)) {
            if ($paytype == "wx") {
                $paytype = [
                    "wxpay-h5", "coin-wxpay-h5", "mix-wxpay-h5",
                    "ybzf_wxmp_h5", "coin-ybzf_wxmp_h5", "mix-ybzf_wxmp_h5",
                    "qzl_wx_h5", "coin-qzl_wx_h5", "mix-qzl_wx_h5",
                    'xty_wx_h5', "coin-xty_wx_h5", "mix-xty_wx_h5",
                    'qmf_wxmp_h5', "coin-qmf_wxmp_h5", "mix-qmf_wxmp_h5",
                    'yyyb_wx_h5', "coin-yyyb_wx_h5", "mix-yyyb_wx_h5",
                    "dh_wx"
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            } else if ($paytype == "zfb") {
                $paytype = [
                    "zfb", "coin-zfb", "mix-zfb",
                    "old-zfb-wap", "coin-old-zfb-wap", "mix-old-zfb-wap",
                    'ldys_zfb_h5', "coin-ldys_zfb_h5", "mix-ldys_zfb_h5",
                    "xty_ali_h5", "coin-xty_ali_h5", "mix-xty_ali_h5",
                    'yyyb_ali_h5', "coin-yyyb_ali_h5", "mix-yyyb_ali_h5",
                    "dh_zfb"
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            } else if ($paytype == "coin") {
                $paytype = [
                    "coinpay",
                    "coin-zfb", "coin-old-zfb-wap",
                    "coin-ldys_zfb_h5", "coin-xty_ali_h5", "coin-yyyb_ali_h5",
                    "coin-qzl_wx_h5", "coin-ldys_wx_h5", "coin-yyyb_wx_h5", "coin-ybzf_wxmp_h5", "coin-xty_wx_h5",
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            } else if ($paytype == "coupon") {
                $paytype = [
                    "coupon",
                    "mix-zfb", "mix-old-zfb-wap",
                    "mix-ldys_zfb_h5", "mix-xty_ali_h5", "mix-yyyb_ali_h5",
                    "mix-qzl_wx_h5", "mix-ldys_wx_h5", "mix-yyyb_wx_h5", "mix-ybzf_wxmp_h5", "mix-xty_wx_h5",
                ];
                $condition['p.paytype'] = ['IN', $paytype];
            }
        }

        $cp_status = $this->request->param('cp_status');
        if (is_numeric($cp_status)) {
            $condition['paycpinfo.status'] = (int)$cp_status;
        }
        $gh_status = input('gh_status', 0); // 是否聚合用户：0=否、1=是
        if ($gh_status <> '') {
            $condition['channel.gh_status'] = $gh_status;
        }

        // http://admin.7dgame.cn/Pay/index.html?start=2024-07-09&end=2024-07-09&gameid=&servername=&status=&cp_status=&paytype=&orderid=&username=gao1126&channel_id=&parent_channel_id=&bplus_channel_id=&page=
        // dump($condition);
        $list = $PayModel->alias('p')
            ->join('nw_channel channel', 'p.channel_id=channel.id')
            ->join('cy_paycpinfo paycpinfo', 'p.orderid=paycpinfo.orderid', 'left')
            ->field('p.id,p.orderid,p.gameid,p.username,p.userid,p.amount,p.real_amount,p.real_ptb,p.roleid,p.rolelevel,p.rolename,p.serverid,p.servername,p.status,p.paytype,p.create_time,p.channel_id,p.productname,p.recheck_status,p.attach,p.real_coin,p.coupon_member_id,p.coupon_amount,paycpinfo.status as cp_status, p.pay_amount, FROM_UNIXTIME(p.pay_time, "%Y-%m-%d %H:%i:%s") as pay_time, FROM_UNIXTIME(p.pay_time, "%Y-%m-%d %H:%i:%s") as pay_time')
            ->where($condition)
            ->whereNotIn('gameid', $this->gameIds)
            ->order('id desc')
            ->paginate($pageSize, false, ['query' => input('get.')]);
        $parent_list = model('Common/Channel')->getParentChannel();

        if ($list->total() > 0) {
            $data = $list->toArray()['data'];
            $userIdArr = $paytype = $gameIdArr = $channelIdArr = $ttbhistoryArr = [];
            foreach ($data as $v) {
                $userIdArr[] = $v['userid'];
                $paytype[] = $v['paytype'];
                $gameIdArr[] = $v['gameid'];
                $channelIdArr[] = $v['channel_id'];
            }
            //关联数据查询
            $gameNameArr = model('Common/Game')->whereIn('id', $gameIdArr)->column('name', 'id');
            $regTimeArr = model('Common/Members')->whereIn('id', array_unique($userIdArr))->column('reg_time', 'id');
            foreach ($list as $key => $item) {
                $default = '--';

                $list[$key]['game_name'] = isset($gameNameArr[$item['gameid']]) ? $gameNameArr[$item['gameid']] : $default;
                $list[$key]['reg_time'] = isset($regTimeArr[$item['userid']]) ? date("Y-m-d H:i:s", $regTimeArr[$item['userid']]) : $default;

                $channel = get_top_second_channel_name($list[$key]['channel_id']);

                $list[$key]['channel_name'] = $channel['channel_name'];
                $list[$key]['second_channel'] = $channel['second_name'];
                $list[$key]['top_channel'] = $channel['top_name'];
                $list[$key]['union_name'] = get_union_channel($list[$key]['channel_id'])['name'];
            }

            //支付成功 或者支付查询条件未选择时
            if (input('status') == 1 || input('status') == '') {
                //充值完成总金额
                $totalAmount = $PayModel->alias('p')
                    ->join('nw_channel channel', 'p.channel_id=channel.id')
                    ->join('cy_paycpinfo paycpinfo', 'p.orderid=paycpinfo.orderid', 'left')
                    ->field('sum(pay_amount) as total_amount,paycpinfo.status as cp_status')
                    ->where($condition)->whereNotIn('gameid', $this->gameIds)->where('p.status=1')->find()['total_amount'];
            }
        }

        $where = array();
        $where['p.create_time'] = [
            ['>=', strtotime(date('Y-m-d'))],
            ['<=', strtotime(date('Y-m-d') . ' 23:59:59')],
        ];
        unset($condition["p.create_time"]);

        $todayTotalAmount = floatval(model('Pay')->alias("p")
            ->join('nw_channel channel', 'p.channel_id=channel.id')
            ->join('cy_paycpinfo paycpinfo', 'p.orderid=paycpinfo.orderid', 'left')
            ->where($condition)->whereNotIn('gameid', $this->gameIds)
            ->field('sum(pay_amount) as total_amount,paycpinfo.status as cp_status')->where('p.status=1')->where($where)->find()['total_amount']);

        $where = array();
        $where['p.create_time'] = [
            ['>=', strtotime(date("Y-m-d", strtotime("-1 day")))],
            ['<', strtotime(date('Y-m-d'))],
        ];
        $lastDayTotalAmount = floatval(model('Pay')
            ->alias("p")->where($condition)
            ->join('nw_channel channel', 'p.channel_id=channel.id')
            ->join('cy_paycpinfo paycpinfo', 'p.orderid=paycpinfo.orderid', 'left')
            ->whereNotIn('gameid', $this->gameIds)
            ->field('sum(pay_amount) as total_amount,paycpinfo.status as cp_status')->where('p.status=1')->where($where)->find()['total_amount']);

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('paytype', config('paytype'));
        $this->assign('new_pay_type', config('new_pay_type'));
        $this->assign('game_list', $this->selfGameList);
        $this->assign('channel_id', $this->request->get('channel_id'));          //查询条件中的渠道ID
        $this->assign('parent_list', $parent_list);                              //顶级渠道列表
        $this->assign('start', $this->start);
        $this->assign('end', $this->end);
        $this->assign('totalAmount', $totalAmount);
        $this->assign('todayTotalAmount', $todayTotalAmount);
        $this->assign('lastDayTotalAmount', $lastDayTotalAmount);

        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        $channelWhere = [];
        if ($business != -1) {
            if ($business && $business != -2) {
                $channelWhere['id'] = ['in', $business];
            } else {
                $channelWhere['id'] = -1;
            }
        }

        $uniom = model('Channel')->where(['level' => 0])->where($channelWhere)->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表

        return $this->fetch();
    }


    /**
     * 前置方法(参数获取)
     */
    protected function _beforeChannelPayGetParams()
    {
        $start          = $this->request->param('start');
        $end            = $this->request->param('end');
        $b_channel_id     = $this->request->param('b_channel_id', 0, 'intval');
        $bplus_channel_id = $this->request->param('bplus_channel_id', 0, 'intval');
        $gameid         = $this->request->param('gameid', 0, 'intval');

        $condition = [];
        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));

        if( $business != -1){
            if($bplus_channel_id){
                if(!in_array($bplus_channel_id,$business)){
                    $bplus_channel_id = [-1];
                }
            }else{
                if($business && $business != -2){
                    $bplus_channel_id = implode(',',$business);
                }else{
                    $bplus_channel_id = [-1];
                }
            }

        }
        if (!empty($gameid)) {
            $condition['gameid'] = $gameid;
        }

        if(!empty($b_channel_id) && !empty($bplus_channel_id)){
            unset($condition['channel_id']);

            $bChannelIds = get_child_channel_arr($b_channel_id);
            array_push($bChannelIds,$b_channel_id);

            $bplusChannelIds = get_child_channel_arr($bplus_channel_id);
            array_push($bplusChannelIds,$bplus_channel_id);

            $channelIds = array_intersect($bChannelIds,$bplusChannelIds);
            //	var_dump($channelIds);
            $condition['channel_id'] = ['in', $channelIds];
        }
        elseif (!empty($b_channel_id)) {
            unset($condition['channel_id']); // 如果同时输入渠道名称和顶级渠道，出于性能考虑，只查询渠道名称这个条件

            $channelIds = get_child_channel_arr($b_channel_id);
            array_push($channelIds,$b_channel_id);

            $condition['channel_id'] = ['in',$channelIds];

        }
        //顶级渠道存到pay表中 存在渠道改变时候 需要定期刷表的情况后续开发
        elseif (!empty($bplus_channel_id)) {
            unset($condition['channel_id']); // 如果同时输入渠道名称和顶级渠道，出于性能考虑，只查询渠道名称这个条件

            $channelIds = get_child_channel_arr($bplus_channel_id);
            array_push($channelIds,$bplus_channel_id);

            $condition['channel_id'] = ['in',$channelIds];
        }

        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {
            $condition['create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        } //开始时间不为空时
        elseif ($start != '') {
            $condition['create_time'] = ['>=', strtotime($start)];
        } //结束时间不为空时
        elseif ($end != '') {
            $condition['create_time'] = ['<=', strtotime($end . ' 23:59:59')];
        } else {
            $start = date('Y-m-d', time());
            $end   = date('Y-m-d', time());

            $condition['create_time'] = [
                ['>=', strtotime($start)],
                ['<=', strtotime($end . ' 23:59:59')],
            ];
        }

        /*
        if(strtotime($end.' 23:59:59')-strtotime($start)>31*24*3600){
            $this->error('单次查询日期的最长跨度为31天');
        }
		*/

        $this->start = $start;
        $this->end   = $end;
        $this->setCondition($condition);
    }
    /**
     * 推广员数据
     * @return mixed|string
     */
    public function channelPay()
    {
        $this->_beforeChannelPayGetParams();
        $condition = $this->getCondition();
        $regCondition = $condition;
        $condition['status'] = 1;
        if(isset($regCondition['gameid'])){
            $regCondition['game_id'] = $regCondition['gameid'];
            unset($regCondition['gameid']);
        }

        $page_size = input('page_size', 20);
        $PayModel = model('Common/Pay');
        //充值完成总金额
        $totalAmount = 0;
        $totalRegCnt = 0;

        $regSql = model('MemberGameServer')->field('game_id,channel_id,0 as total_pay_amount,count(*) as total_reg_cnt')->where($regCondition)->group('game_id,channel_id')->buildSql();
        $unionSql = model('Pay')->field('gameid as game_id,channel_id as channel_id,sum(pay_amount) as total_pay_amount,0 as total_reg_cnt')->where($condition)->group('gameid,channel_id')->union([$regSql])->buildSql();
        $unionSqlNew = Db::table($unionSql . ' bb')
            ->field('bb.game_id game_id,bb.channel_id channel_id,sum(bb.total_pay_amount) as total_pay_amount,sum(bb.total_reg_cnt) as total_reg_cnt')
            ->group('bb.game_id,bb.channel_id')
            ->buildSql();
        $list = Db::table($unionSqlNew . ' cc')
            ->order('total_pay_amount desc,total_reg_cnt desc')
            // ->order('game_id desc,channel_id desc')
            ->paginate($page_size, false, ['query' => input('get.')]);
        // echo Db::table($unionSql . ' bb')->getLastSql() . "----sql-------<br>";

        $listData = array();
        if ($list->total()>0) {
            //关联数据查询
            foreach ($list as $key => $item) {
                $channel = get_top_second_channel_name($list[ $key ]['channel_id']);
                $item['game_name']			= get_game_name($list[ $key ]['game_id']);
                $item['channel_name']		= $channel['channel_name'];
                $item['second_channel']		= $channel['second_name'];
                $item['top_channel']		= $channel['top_name'];
                $item['bplus_channel_name'] = get_union_channel($list[ $key ]['channel_id'])['name'];

                $listData[] = $item;
            }

            //充值完成总金额
            $totalAmount = $PayModel->field('sum(pay_amount) as total_amount')->where($condition)->find()['total_amount'];

            //玩家新增
            $totalRegCnt = model('MemberGameServer')->field('count(*) as total_reg_cnt')->where($regCondition)->find()['total_reg_cnt'];
        }

        $this->assign('list', $list);
        $this->assign('listData', $listData);
        $this->assign('page', $list->render());

        $this->assign('game_list', $this->selfGameList);
        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        $channelWhere = [];
        if( $business != -1){
            if($business && $business != -2){
                $channelWhere['id'] = ['in',$business];
            }else{
                $channelWhere['id'] = -1;
            }
        }

        $uniom = model('Channel')->where(['level'=>0])->where($channelWhere)->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表

        $this->assign('start', $this->start);
        $this->assign('end', $this->end);
        $this->assign('totalAmount', $totalAmount);
        $this->assign('totalRegCnt', $totalRegCnt);

        return $this->fetch();
    }


    /**
     * 推广员数据报表-下载
     */
    public function channelPayDownload()
    {
        if (request()->isAjax()) {

            $this->_beforeChannelPayGetParams();
            $condition = $this->getCondition();

            $regCondition = $condition;
            $condition['status'] = 1;
            if(isset($regCondition['gameid'])){
                $regCondition['game_id'] = $regCondition['gameid'];
                unset($regCondition['gameid']);
            }


            $regSql = model('MemberGameServer')->field('game_id,channel_id,0 as total_pay_amount,count(*) as total_reg_cnt')->where($regCondition)->group('game_id,channel_id')->buildSql();
            $unionSql = model('Pay')->field('gameid as game_id,channel_id as channel_id,sum(pay_amount) as total_pay_amount,0 as total_reg_cnt')->where($condition)->group('gameid,channel_id')->union([$regSql])->buildSql();
            $unionSqlNew = Db::table($unionSql . ' bb')
                ->field('bb.game_id game_id,bb.channel_id channel_id,sum(bb.total_pay_amount) as total_pay_amount,sum(bb.total_reg_cnt) as total_reg_cnt')
                ->group('bb.game_id,bb.channel_id')
                ->buildSql();
            $sql = Db::table($unionSqlNew . ' cc')
                ->order('game_id desc,channel_id desc')
                ->fetchSql(true)->select();


            // $PayModel = model('Common/Pay');
            // $regSql = model('MemberGameServer')->field('game_id,channel_id,0 as total_pay_amount,count(*) as total_reg_cnt')->where($regCondition)->group('game_id,channel_id')->buildSql();
            // $unionSql = model('Pay')->field('gameid as game_id,channel_id as channel_id,sum(amount) as total_pay_amount,0 as total_reg_cnt')->where($condition)->group('gameid,channel_id')->union([$regSql])->buildSql();
            // $sql = Db::table($unionSql.' bb')
            //     ->field('bb.game_id game_id,bb.channel_id channel_id,sum(bb.total_pay_amount) as total_pay_amount,sum(bb.total_reg_cnt) as total_reg_cnt')
            //     ->group('bb.game_id,bb.channel_id')
            //     ->order('total_pay_amount desc,total_reg_cnt desc')
            //     ->fetchSql(true)->select();

            if ((new MakeReportGo())->addTask('admin.channelPay', $sql, session_id())) {
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }
            else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }
        else{
            $this->error('非法请求');
        }
    }

    /**
     * 充值记录管理页-查看玩家账号
     */
    public function showUsername()
    {
        $userid = input('userid');
        $game_id= input('game_id');

        if(empty($userid)){

            $this->error('玩家ID不能为空');
        }

        $username= model('Common/Members')->where(['id'=>$userid])->value('username');

        if(!empty($username)){

            //充值记录管理页
            if(!empty($game_id)){

                $game_name = model('Common/Game')->where(['id'=>$game_id])->value('name');

                $this->insertLog($this->current_node,'查看账号：'.$username.'，关联游戏：'.$game_name,81);
            }else{
                $this->insertLog($this->current_node,'查看账号：'.$username,82);
            }

            $this->result($username,1);
        }
        else{
            $this->error('玩家信息不存在');
        }
    }

    /**
     * ajax获取非聚合渠道列表
     *
     */
    public function ajaxGetNoComplexChannel()
    {
        $channel_list = model('Common/Channel')->getAllByCondition('id,name',['flag'=>['in','1,2,3']]);

        echo json_encode($channel_list);
//        exit;
    }

    /**
     * ajax获取渠道列表
     */
    public function getChannel()
    {
        $channel_list = model('Common/Channel')->getChannleList();
        echo json_encode($channel_list);
        //   exit;
    }

    /**
     * 报表-下载
     */
    public function download()
    {
        if (request()->isAjax()) {
            $makeReportGo = new MakeReportGo();
            $auth       = new \app\common\logic\Auth();

            $is_show = input('is_show');

            if($is_show>(int)$auth->check(session('ADMIN_ID'), 'admin/pay/rechargeDowmExcel'))
            {
                $this->error('您没有访问权限！');
            }

            $this->_beforeGetParams();
            $condition = $this->getCondition();

            $cp_status = $this->request->param('cp_status');
            $paytype = $this->request->param('paytype', '', 'trim');

            if (!empty($paytype)) {
                if ($paytype == "wx") {
                    $condition['p.paytype'] = ['IN', [
                        'wxpay-h5', 'coin-wxpay-h5', 'mix-wxpay-h5',
                        'ybzf_wxmp_h5', 'coin-ybzf_wxmp_h5', 'mix-ybzf_wxmp_h5',
                        'qzl_wx_h5', 'coin-qzl_wx_h5', 'mix-qzl_wx_h5',
                        'xty_wx_h5', 'coin-xty_wx_h5', 'mix-xty_wx_h5',
                        'qmf_wxmp_h5', 'coin-qmf_wxmp_h5', 'mix-qmf_wxmp_h5',
                        'yyyb_wx_h5', 'coin-yyyb_wx_h5', 'mix-yyyb_wx_h5',
                    ]];
                } else if($paytype == "zfb") {
                    $condition['p.paytype'] = ['IN', [
                        'zfb', 'coin-zfb', 'mix-zfb',
                        'old-zfb-wap', 'coin-old-zfb-wap', 'mix-old-zfb-wap',
                        'xty_ali_h5', 'coin-xty_ali_h5', 'mix-xty_ali_h5',
                        'ldys_zfb', 'coin-ldys_zfb', 'mix-ldys_zfb',
                        'yyyb_ali_h5', 'coin-yyyb_ali_h5', 'mix-yyyb_ali_h5',
                    ]];
                } else if($paytype == "coin") {
                    $condition['p.paytype'] = ['IN', [
                        'coinpay',
                        "coin-zfb", "coin-old-zfb-wap",
                        "coin-ldys_zfb_h5", "coin-xty_ali_h5", "coin-yyyb_ali_h5",
                        "coin-qzl_wx_h5", "coin-ldys_wx_h5", "coin-yyyb_wx_h5", "coin-ybzf_wxmp_h5", "coin-xty_wx_h5", "coin-qmf_wxmp_h5",
                    ]];
                } else if($paytype == "coupon") {
                    $condition['p.paytype'] = ['IN', [
                        'coupon',
                        "mix-zfb", "mix-old-zfb-wap",
                        "mix-ldys_zfb_h5", "mix-xty_ali_h5", "mix-yyyb_ali_h5",
                        "mix-qzl_wx_h5", "mix-ldys_wx_h5", "mix-yyyb_wx_h5", "mix-ybzf_wxmp_h5", "mix-xty_wx_h5", "mix-qmf_wxmp_h5",
                    ]];
                }
            }

            if (is_numeric($cp_status)) {
                $condition['paycpinfo.status'] = (int)$cp_status;
            }

            if (!$condition) {
                $this->error('导出需要至少一个查询条件！');
            }
            $is_haiwei = $this->request->param('is_haiwei', 0);
            $sql = Db::table('cy_pay p')
                ->join('cy_members m', 'm.id=p.userid', 'left')
                ->join('nw_game_channel_divide_settle_det_pay d', 'p.settle_id=d.settle_id and p.orderid = d.orderid', 'left')
                ->join('cy_paycpinfo paycpinfo', 'p.orderid=paycpinfo.orderid', 'left')
                ->field('p.orderid,p.gameid,p.username,p.userid,p.amount,p.pay_amount,p.real_amount,p.real_ptb,p.real_ptb_amount,p.roleid,p.rolelevel,p.rolename,p.productname,p.serverid,p.servername,p.status,p.paytype,p.create_time,p.channel_id,p.attach,m.reg_time,p.settle_id,d.divide_amt,d.ratio,p.real_coin,p.coupon_member_id,p.coupon_amount,paycpinfo.status as cp_status')
                ->where($condition)
                ->where(function ($query) use ($is_haiwei) {
                    if ($is_haiwei == 1) {
                        $query->whereIn('p.gameid', $this->gameIds);
                    }else{
                        $query->whereNotIn('p.gameid',$this->gameIds);
                    }
                })
                ->order('p.create_time desc')
                ->fetchSql(true)->select();

            //            if($makeReport->addTask('payList',$sql,session_id(),['is_show'=>boolval($is_show)])){
            if ($makeReportGo->addTask('payList', $sql, session_id(), ['is_show' => boolval($is_show)])) {
                //高级报表下载
                if ($is_show == 1) {
                    $this->insertLog($this->current_node, $this->getDownloadLogContent(), 83);
                }

                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }
        else{
            $this->error('非法请求');
        }
    }

    /**
     *  高级报表下载时的，操作日志内容
     */
    private function getDownloadLogContent(){

        $start          = $this->request->param('start');
        $end            = $this->request->param('end');
        $gameid         = $this->request->param('gameid', 0, 'intval');
        $username       = $this->request->param('username', '', 'trim');

        $content = '';

        //开始时间和结束时间不为空时
        if ($start != '' && $end != '') {

            $content.= '时间：'.$start.'~'.$end;
        } //开始时间不为空时
        elseif ($start != '') {
            $content.= '时间：'.$start.'~';
        } //结束时间不为空时
        elseif ($end != '') {

            $content.= '时间：'.'~'.$end;
        } else {
            $start = date('Y-m-d', time());
            $end   = date('Y-m-d', time());

            $content.= '时间：'.$start.'~'.$end;
        }


        if (!empty($gameid)) {

            $content.='，游戏：'.model('Common/Game')->where(['id'=>$gameid])->value('name');
        }
        else{
            $content.='，游戏：全部游戏';
        }


        if (!empty($username)) {

            $content.='，玩家账号：'.$username;
        }
        else{
            $content.='，玩家账号：';
        }

        return $content;
    }

    public function setCondition($condition)
    {
        return $this->condition = $condition;
    }

    public function getCondition()
    {
        return $this->condition;
    }


    /**
     * 充值用户列表
     */
    public function user()
    {
        $this->_beforeGetParams();
        $condition = $this->getCondition();
        $PayModel  = model('Common/Pay');

        /*
        // 如果没有选择条件，那么默认显示当天的数据
        if (! count($condition)) {
            $start_time               = $this->request->get('start', date('Y-m-d'));
            $condition['create_time'] = makeTimeCondition($start_time);
        }
        */
        
        $order = '';
        $amount_total = input('amount_total', 'desc');
        if($amount_total){
            $order = 'amount_total '.$amount_total;
        }
        
        $condition['status'] = 1; //只统计充值成功的
        $sql = $PayModel->alias('p')
            ->where($condition)
            ->field('username,userid,sum(amount) as amount_total ,count(1) as pay_total,max(id) as max_id, gameid, p.roleid, p.rolename')
            ->group('username')
            ->order($order);
        if(request()->isAjax() && input('down') == true){
            $makeReportGo = new MakeReportGo();
            $auth = new \app\common\logic\Auth();
            if(!$auth->check(session('ADMIN_ID'), 'admin/pay/userPayDowmExcel'))
            {
                $this->error('您没有访问权限!!');
            }
            
            $downSql = $sql->fetchSql(true)->select();
            if ($makeReportGo->addTask('userPayList', $downSql, session_id())) {
                $this->success('报表生成的任务已经提交, 报表生成完成后，会及时通知您，请耐心稍等');
            }else{
                $this->error('报表生成任务不可重复提交，如遇到无法导出情况，建议修改查询条件解除当前状态，提交重新生成报表任务！');
            }
        }
        
        $list = $sql->paginate(20, false, ['query' => input('get.')]);
        $idArr = array_column($list->toArray()['data'], 'max_id');
        
        $rencentPayArr = $PayModel->where('id', 'in', $idArr)->column('amount', 'id');
        foreach ($list as $key => $value) {
            if(!empty($value['max_id'])){
                $list[ $key ]['recent_pay'] = $rencentPayArr[ $value['max_id'] ];
            }else{
                $list[ $key ]['recent_pay'] = 0;
            }
        }

        $this->assign('game_list_by_ids', $this->selfGameList);
        $this->assign('game_list', $this->gameList);
        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('start', $this->start);
        $this->assign('end', $this->end);
        return $this->fetch();
    }
 
    
    /**
     *充值规则 列表
     */
    public function ruleList()
    {
        $condition = [];
        $gameid    = $this->request->param('gameid', 0, 'intval');
        if (!empty($gameid)) {
            $condition['game_id'] = $gameid;
        }
        $list      = DB::table('cy_payrule')->where($condition)->order('create_time desc')->paginate(10);
        $gameidArr = [];
        foreach ($list as $v) {
            $gameidArr[] = $v['game_id'];
        }
        $payTypelist = DB::table('cy_paytype')->column('paytype,payname');

        $game_name = model('game')->whereIn('id', array_unique($gameidArr))->column('id,name');
        $this->assign('paytype_list', $payTypelist);
        $this->assign('game_list', $this->selfGameList);
        $this->assign('list', $list);
        $this->assign('game_name', $game_name);
        $this->assign('page', $list->render());
        return $this->fetch('rulelist');
    }

    /**
     *充值规则添加
     */
    public function ruleAdd()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (true !== ($res = $this->validate($data, 'payrule'))) {
                $this->error($res);
            }
            $payruleModel = model('Common/Payrule');

            $data['create_time'] = NOW_TIMESTAMP;

            if ($payruleModel->save($data)) {
                $this->success('添加成功', url('ruleList'));
            }
            $this->error($payruleModel->getError() ?: '添加失败');
        }

        $payTypelist = DB::table('cy_paytype')->field('paytype,payname')->select();

        $this->assign('paytype_list', $payTypelist);
        $this->assign('game_list', $this->selfGameList);
        return $this->fetch();
    }

    /**
     *充值规则修改
     */
    public function ruleEdit()
    {
        $id           = $this->request->param('id', '', 'intval');
        $payruleModel = model('Common/Payrule');

        if (empty($id)) {
            $this->error('参数错误!');
        }
        if (!$data = $payruleModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (true !== ($res = $this->validate($data, 'payrule'))) {
                $this->error($res);
            }
            if ($payruleModel->save($data, ['id' => $id]) !== false) {
                $this->success('修改成功', url('ruleList'));
            }
            $this->error($payruleModel->getError() ?: '修改失败');
        }

        $payTypelist = DB::table('cy_paytype')->field('paytype,payname')->select();

        $this->assign('paytype_list', $payTypelist);
        $this->assign('game_list', $this->selfGameList);
        $this->assign('data', $data);
        return $this->fetch();
    }

    /**
     * 充值规则删除
     */
    public function ruleDelete()
    {
        $id = $this->request->param('id', '', 'intval');
        if (empty($id)) {
            $this->error('参数错误!');
        }
        $payruleModel = model('Common/Payrule');
        if (!$payruleModel->find($id)) {
            $this->error('参数错误，不存在该数据');
        }
        if ($payruleModel->where('id', '=', $id)->delete()) {
            $this->success('删除成功', url('ruleList'));
        }
        $error = $payruleModel->getError();
        $this->error($error ?: '删除失败');
    }

    /**
     * 平台币补单记录
     * @throws \think\exception\DbException
     */
    public function ptblist()
    {

        $where = [];
        $order = 'create_time desc';
        $orderid     = $this->request->get('orderid', '', 'trim');
        $username     = $this->request->get('username', '', 'trim');
        $cname     = $this->request->get('cname', '', 'trim');
        if (isset($orderid) && !empty($orderid)) {
            $where['a.orderid'] = $orderid;
        }
        if (isset($username) && !empty($username)) {
            $where['m.username'] = $username;
        }

        if (isset($cname) && !empty($cname)) {
            $where['c.username'] = $cname;
        }

        $list = Db::name('nw_channel_member_pay')->alias('a')
            ->join('nw_channel_admin c', 'c.id = a.channel_id','left')
            ->join('nw_channel cc', 'cc.id = a.qd_id','left')
            ->join('cy_members m', 'm.id = a.member_id','left')
            ->field('a.*,c.username,m.username as uname,cc.name as ccname')
            ->where($where)
            ->order($order)
            ->paginate();

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        return $this->fetch();
    }

    /**
     * 补单通知CP
     *
     */
    public function ptbreCallback()
    {
        if($this->request->isPost()){

            $orderid = input('post.orderid','','trim');

            if(empty($orderid)){
                $this->error('订单号不能为空');
            }

            $payCpInfoModel = model('Common/PayCpinfo');

            $where['orderid'] = $orderid;

            $paycpInfo = $payCpInfoModel->field('id,fcallbackurl,params,payflag,status')->where($where)->find();

//            if(empty($paycpInfo)){
//                $this->error('订单通知回调记录不存在');
//            }
//            elseif($paycpInfo['status']!=1){
//                $this->error('订单已完成');
//            }

            $guzzle = new Client();

            //将字符串转换成数组
            parse_str($paycpInfo['params'], $param);

            $pay = Db::name('cy_pay')->where(['orderid'=>$orderid])->find();
            $userid = $pay['userid'];//用户
            $user_amount = $pay['amount'];//用户金额
            $members = Db::name('cy_members')->where(['id'=>$userid])->find();//用户表
            $chal_id = $members['channel_id'];//用户属于渠道

            $pyb = Db::name('nw_channel_admin')->where(['id'=>$chal_id])->find();//找到渠道   3519
            $pyb_channel_admin =  Db::name('nw_channel')->where(['id'=>$pyb['channel_id']])->find();//在找到所属渠道   3544
            $pyb_channel_z =  Db::name('nw_channel')->where(['id'=>$pyb_channel_admin['parent_id']])->find();//找到渠道 3543
            $pyb_channel_cz =  Db::name('nw_channel_admin')->where(['channel_id'=>$pyb_channel_z['id']])->find();//找到渠道 3543
            if($pyb_channel_cz['pyb'] < $user_amount)
            {
                $this->error("订单通知失败,平台币不足： ");
            }
            $data_info['pyb'] = $pyb_channel_cz['pyb'] - $user_amount;
            Db::name('nw_channel_admin')->where(['id'=>$pyb_channel_cz['id']])->update($data_info);
            $data_infos['total_pay_amount'] = $members['total_pay_amount'] + $user_amount;
            Db::name('cy_members')->where(['id'=>$userid])->update($data_infos);
            $data_pay['status'] = 1;
            $data_pay['payflag'] = 1;
            Db::name('cy_paycpinfo')->where(['orderid'=>$orderid])->update($data_pay);

            $ds = [
                'orderid'   => $orderid,
                'qd_id' => $chal_id,
                'user_amount' => $user_amount,
                'channel_id' => $pyb_channel_cz['id'],
                'member_id' => $userid,
                'create_time' => time()
            ];
            Db::name('nw_channel_member_pay')->insertGetId($ds);

            try {
                $response = $guzzle->request('post', $paycpInfo['fcallbackurl'], ['form_params' => $param, 'timeout' => 10]);

                //通知成功
                if (0 == strcasecmp($response->getBody(), 'success')) {

                    $payCpInfoModel->save(['status' => 1, 'update_time' => NOW_TIMESTAMP], ['id' => $paycpInfo['id']]);

                    $this->success('订单通知成功',url('ptblist'));
                }
                else{
                    $this->error('订单通知已发送给对方，但对方未返回[success]信息');
                }

            } catch (GuzzleException $e) {

                $this->error("订单通知失败： " . $e->getMessage());
            }

        }

        return $this->fetch('ptbre_callback');
    }



    public function reCallback()
    {
        if($this->request->isPost()){

            $orderid = input('post.orderid','','trim');

            if(empty($orderid)){
                $this->error('订单号不能为空');
            }

            $payCpInfoModel = model('Common/PayCpinfo');

            $where['orderid'] = $orderid;

            $paycpInfo = $payCpInfoModel->field('id,fcallbackurl,params,payflag')->where($where)->find();

            if(empty($paycpInfo)){
                $this->error('订单通知回调记录不存在');
            }
            elseif($paycpInfo['payflag']!=1){
                $this->error('订单未完成支付或支付失败');
            }

            $guzzle = new Client();

            //将字符串转换成数组
            parse_str($paycpInfo['params'], $param);

            try {
                $response = $guzzle->request('post', $paycpInfo['fcallbackurl'], ['form_params' => $param, 'timeout' => 10]);
                log_message("[ADMIN-CP补单-SUCCESS]: " . 'orderid：' . $orderid . " url: " . $paycpInfo['fcallbackurl'] . " - params:" . json_encode($paycpInfo['params']) . " - result:" . $response->getBody()->getContents(), 'log', LOG_PATH . 'callBacklog/');

                //通知成功
                if (0 == strcasecmp($response->getBody(), 'success')) {
                    $payCpInfoModel->save(['status' => 1, 'update_time' => NOW_TIMESTAMP], ['id' => $paycpInfo['id']]);
                    $this->success('订单通知成功');
                }else{
                    $this->error('订单通知已发送给对方，但对方未返回[success]信息');
                }

            } catch (GuzzleException $e) {
                log_message("[ADMIN-CP补单-ERROR]: ".'orderid：' . $orderid." url: ".$paycpInfo['fcallbackurl']." - params:". json_encode($paycpInfo['params']), 'log', LOG_PATH . 'callBacklog/');
                $this->error("订单通知失败： " . $e->getMessage());
            }
        }

        return $this->fetch('re_callback');
    }


    /**
     * ajax 获取cp订单号
     */
    public function showAttach(){
        $order_id = input('post.orderid', '', 'filterAndTrimInput');     // 订单号
        $this->_showAttach($order_id);
    }

    /**
     * 查看订单的attach信息
     * @param $orderid string 订单号
     */
    private function _showAttach($orderid) {
        $runnable = true;
        $msg      = '';
        $status   = false;
        if(empty($orderid)) {
            $runnable = false;
            $msg      = '参数错误';
        }
        if($runnable) {
            $result = model('Pay')
                ->where(['orderid'=>$orderid])
                ->field('attach')
                ->find();
            if(empty($result)) {
                $runnable = false;
                $msg      = '订单不存在';
            }
        }
        // 查看attach
        if($runnable) {
            $status = 1;
            $result['attach'] = 'attach='.$result['attach'];
            parse_str($result['attach'],$arr);
            $msg = $arr['attach']; // 变量由上一个函数解析出来的
        }
        echo json_encode(['code'=>$status,'msg'=>$msg]);
    }


    /**
     * 充值记录管理
     * @return mixed|string
     */
    public function indexs()
    {
        $this->_beforeGetParams();
        $condition = $this->getCondition();

        //充值总金额
        $totalAmount = $todayTotalAmount = $lastDayTotalAmount =0;

        $PayModel = model('Common/Pay');
        $cp_status         = $this->request->param('cp_status');
        $where = [];
        if (is_numeric($cp_status)) {
            $where['paycpinfo.status'] = (int)$cp_status;
        }
        $list = $PayModel->alias('p')->field('p.id,p.orderid,p.gameid,p.username,p.userid,p.amount,p.real_amount,p.real_ptb,p.roleid,p.rolelevel,p.rolename,p.serverid,p.servername,p.status,p.paytype,p.create_time,p.channel_id,p.productname,p.recheck_status,p.attach,paycpinfo.status as cp_status,p.real_coin,p.coupon_member_id,p.coupon_amount')
            ->join('cy_paycpinfo paycpinfo','p.orderid=paycpinfo.orderid','left')
            ->where($condition)
            ->where($where)
            ->whereIn('gameid',$this->gameIds)
            ->order('create_time desc')
            ->paginate(10, false, ['query' => input('get.')]);
        $parent_list  = model('Common/Channel')->getParentChannel();

        if ($list->total()>0) {
            $data      = $list->toArray()['data'];
            $userIdArr = $paytype = $gameIdArr = $channelIdArr = $ttbhistoryArr = [];
            foreach ($data as $v) {
                $userIdArr[]    = $v['userid'];
                $paytype[]      = $v['paytype'];
                $gameIdArr[]    = $v['gameid'];
                $channelIdArr[] = $v['channel_id'];
            }
            //关联数据查询
            $gameNameArr = model('Common/Game')->whereIn('id', $gameIdArr)->column('name', 'id');
            $regTimeArr  = model('Common/Members')->whereIn('id', array_unique($userIdArr))->column('reg_time', 'id');
            foreach ($list as $key => $item) {
                $default = '--';

                $list[ $key ]['game_name']      = isset($gameNameArr[ $item['gameid'] ]) ? $gameNameArr[ $item['gameid'] ] : $default;
                $list[ $key ]['reg_time']       = isset($regTimeArr[ $item['userid'] ]) ? date("Y-m-d H:i:s", $regTimeArr[ $item['userid'] ]) : $default;

                $channel = get_top_second_channel_name($list[ $key ]['channel_id']);

                $list[ $key ]['channel_name']   = $channel['channel_name'];
                $list[ $key ]['second_channel'] = $channel['second_name'];
                $list[ $key ]['top_channel']    = $channel['top_name'];
                $list[ $key ]['union_name']     = get_union_channel($list[ $key ]['channel_id'])['name'];
            }

            //支付成功 或者支付查询条件未选择时
            if(input('status')==1 || input('status')==''){
                //充值完成总金额
                $totalAmount = $PayModel->alias('p')->field('sum(amount) as total_amount')->where($condition)->whereIn('gameid',$this->gameIds)->where('status=1')->find()['total_amount'];
            }
        }

        $where = array();
        $where['p.create_time'] = [
            ['>=', strtotime(date('Y-m-d'))],
            ['<=', strtotime(date('Y-m-d') . ' 23:59:59')],
        ];
        $where['status'] = 1;
        unset($condition["p.create_time"]);

        $todayTotalAmount = floatval(model('Pay')->alias("p")->where($condition)->whereIn('gameid',$this->gameIds)->field('sum(amount) as total_amount')->where($where)->find()['total_amount']);

        $where = array();
        $where['create_time'] = [
            ['>=', strtotime(date("Y-m-d",strtotime("-1 day")))],
            ['<', strtotime(date('Y-m-d'))],
        ];
        $where['status'] = 1;
        $lastDayTotalAmount = floatval(model('Pay')->alias("p")->where($condition)->whereIn('gameid',$this->gameIds)->field('sum(amount) as total_amount')->where($where)->find()['total_amount']);

        $this->assign('list', $list);
        $this->assign('page', $list->render());
        $this->assign('paytype', config('paytype'));
        $this->assign('game_list', $this->selfGameList);
        $this->assign('channel_id', $this->request->get('channel_id'));          //查询条件中的渠道ID
        $this->assign('parent_list', $parent_list);          //顶级渠道列表
        $this->assign('start', $this->start);
        $this->assign('end', $this->end);
        $this->assign('totalAmount', $totalAmount);
        $this->assign('todayTotalAmount', $todayTotalAmount);
        $this->assign('lastDayTotalAmount', $lastDayTotalAmount);

        $business = model('common/Business')->getChannelIds(session('ADMIN_ID'));
        $channelWhere = [];
        if( $business != -1){
            if($business && $business != -2){
                $channelWhere['id'] = ['in',$business];
            }else{
                $channelWhere['id'] = -1;
            }
        }

        $uniom = model('Channel')->where(['level'=>0])->where($channelWhere)->field('id,name')->select();
        $this->assign('uniom', $uniom);          //顶级渠道列表

        return $this->fetch();
    }

    // 支付中控
    public function paySupcon()
    {
        // ## 支付方式列表
        $paySupconList = (new \app\common\model\PaySupconModel())->select()->toArray();
        $newPaySupconList = [
            'wx_h5' => [
                "id" => 1,
                "pay_type" => "微信支付-H5",
                "pay_channel" => "",
                "mark" => "wx_h5",
                "type" => 1,
                "probability" => "",
                "description" => "-",
                "parentId" => null,
                "cascade" => "all",
                "children" => [],
            ],
            // 'wx_app' => [],
            'ali_h5' => [
                "id" => 3,
                "pay_type" => "支付宝-H5",
                "pay_channel" => "",
                "mark" => "ali_h5",
                "type" => 1,
                "probability" => "",
                "description" => "-",
                "parentId" => null,
                "cascade" => "all",
                "children" => [],
            ],
            'ali_app' => [
                "id" => 4,
                "pay_type" => "支付宝-APP",
                "pay_channel" => "",
                "mark" => "ali_app",
                "type" => 1,
                "probability" => "",
                "description" => "-",
                "parentId" => null,
                "cascade" => "all",
                "children" => [],
            ],
            'coinpay' => [
                "id" => 5,
                "pay_type" => "平台币",
                "pay_channel" => "",
                "mark" => "coinpay",
                "type" => 1,
                "probability" => "",
                "description" => "-",
                "parentId" => null,
                "cascade" => "all",
                "children" => [],
            ]
        ];
        foreach ($paySupconList as $key => $value) {
            $parentId = 1;
            switch ($value['pay_scene']) {
                case "wx_h5":
                    $parentId = 1;
                    break;
                case "ali_h5":
                    $parentId = 3;
                    break;
                case "ali_app":
                    $parentId = 4;
                    break;
                case "coinpay":
                    $parentId = 5;
                    break;
            }

            $game_ids = $value['game_ids'];
            if ($value['game_ids'] != 'all') {
                $game_ids = explode(',', substr($value['game_ids'], 1, -1));
            }
            $sdk_versions = $value['sdk_versions'];
            if ($value['sdk_versions'] != 'all') {
                $sdk_versions = explode(',', substr($value['sdk_versions'], 1, -1));
            }
            $children = [
                "id" => $value['id'],
                "pay_type" => "",
                "pay_channel" => $value['paytype_name'],
                "mark" => $value['pay_mark'],
                "type" => 0,
                "probability" => $value['probability'],
                "parentId" => $parentId,
                "description" => "-",
                "isParent" => false,
                "game_ids" => $game_ids,
                "sdk_versions" => $sdk_versions,
                "status" => $value['status'],
            ];
            $newPaySupconList[$value['pay_scene']]['children'][] = $children;
        }
        if ($newPaySupconList) {
            $newPaySupconList = array_values($newPaySupconList);
        }
        $this->assign('paySupconList', $newPaySupconList);

        // ## 游戏列表
        $gameList = (new \app\common\model\Game())->where(['status' => 1])->order('id desc')->field(['id', 'name', 'type'])->select()->toArray();
        $newGameList = [
            "android" => [
                "id" => "",
                "title" => "Android",
                "field" => "android",
                "checked" => false,
                "spread" => false,
                "children" => []
            ],
            "ios" => [
                "id" => "",
                "title" => "IOS",
                "field" => "ios",
                "checked" => false,
                "spread" => false,
                "children" => []
            ],
            "h5" => [
                "id" => "",
                "title" => "H5",
                "field" => "h5",
                "checked" => false,
                "spread" => false,
                "children" => []
            ],
        ];
        foreach ($gameList as $key => $value) {
            $children = [
                "id" => $value['id'],
                "field" => $value['id'],
                "title" => $value['name'],
                "checked" => false,
                "href" => "",
            ];
            
            // 游戏类型：1=ios、2=安卓、3=h5
            if ($value['type'] == 2) {
                $newGameList['android']['children'][] = $children;
            } else if ($value['type'] == 3) {
                $newGameList['h5']['children'][] = $children;
            } else if ($value['type'] == 1) {
                $newGameList['ios']['children'][] = $children;
            }
        }
        if ($newGameList) {
            $newGameList = array_values($newGameList);
        }
        $this->assign('gameList', $newGameList);

        // ## SDK版本
        $versionList = (new \app\common\model\AndroidSdk())->field(['id', 'version', 'num'])->order('id desc')->select()->toArray();
        $newVersionList = [
            "version" => [
                "id" => "",
                "title" => "SDK 版本",
                "field" => "version",
                "checked" => false,
                "spread" => true,
                "children" => []
            ],
        ];
        foreach ($versionList as $key => $value) {
            $children = [
                "id" => $value['id'],
                "field" => $value['num'],
                "title" => $value['version'],
                "checked" => false,
                "href" => "",
            ];
            $newVersionList['version']['children'][] = $children;
        }
        if ($newVersionList) {
            $newVersionList = array_values($newVersionList);
        }
        // dump($newVersionList);
        $this->assign('versionList', $newVersionList);

        return $this->fetch();
    }

    // 处理绑定
    public function saveSupcon()
    {
        try {
            $parame = $this->request->only(['game_ids', 'sdk_versions', 'probability']);
            $id = input('id');
            $result = Db::table('cy_pay_supcon')->where(['id' => $id])->update($parame);
            
            if ($result) {
                echo json_encode(['code' => 200, 'msg' => 'success', 'data' => $result]);
                return;
            }
        } catch (Exception $e) {
            echo json_encode(['code' => -110, 'msg' => 'error:' . $e->getMessage(), 'data' => []]);
            return;
        }
        echo json_encode(['code' => -100, 'msg' => 'error', 'data' => []]);
    }
    
    // 处理状态
    public function saveSupconStatus()
    {
        $parame = $this->request->only(['status']);
        $id = input('id');
        if(empty($id) || empty($id)){
            echo json_encode(['code' => -100, 'msg' => '参数有误！', 'data' => []]);
            return;
        }
        $supconInfo = Db::table('cy_pay_supcon')->where(['id' => $id])->find();
        try {
            $result = Db::table('cy_pay_supcon')->where(['id' => $id])->update($parame);
            if ($result) {
                $this->insertLog($this->current_node, '支付场景：'.$supconInfo['pay_scene'].' - 支付渠道：' . $supconInfo['paytype_name'] . ' - 状态：' . ($parame['status'] == 0 ? '关' : '开'), 569);
                echo json_encode(['code' => 200, 'msg' => 'success', 'data' => $result]);
                return;
            }
        } catch (Exception $e) {
            echo json_encode(['code' => -110, 'msg' => 'error:' . $e->getMessage(), 'data' => []]);
            return;
        }
        echo json_encode(['code' => -100, 'msg' => 'error', 'data' => []]);
    }

    // 处理权重
    public function saveProbability()
    {
        try {
            $parame = $this->request->post();
            if (!$parame) {
                echo json_encode(['code' => -100, 'msg' => 'error: 缺少必要参数！', 'data' => []]);
            }
            $result = (new \app\common\model\PaySupconModel())->isUpdate()->saveAll($parame['formData']);
            if ($result) {
                echo json_encode(['code' => 200, 'msg' => 'success', 'data' => []]);
                return;
            }
        } catch (Exception $e) {
            echo json_encode(['code' => -110, 'msg' => 'error:' . $e->getMessage(), 'data' => []]);
            return;
        }
        echo json_encode(['code' => -100, 'msg' => 'error', 'data' => []]);
    }
}
