<?php
/**
 * Copyright (C) 2018 Baidu, Inc. All Rights Reserved.
 */

namespace app\admin\controller;


use think\Db;

class Log extends Admin
{
    protected function _initialize()
    {
        parent::_initialize();
    }


    /**
     * 登录日志
     * @return mixedo
     */
    public function login()
    {
		$type = input('type', '1','intval');

        $condition = [];
        $username  = $this->request->get('username', '', 'trim');
        !empty($username) && $condition['l.username'] = ['like', "%{$username}%"];

		$condition['l.type'] = $type;

        $loginlogModel = model('Common/loginlog');
		if($type==1){
			$loginlogList  = $loginlogModel->alias('l')
				->join('cy_admin a', 'a.id=l.admin_id', 'left')
				->field('l.*, a.create_time time2')
				->where($condition)->order('l.create_time desc')
				->paginate(10, false, array('query' => input('get.')));
		}
		else{
			$loginlogList  = $loginlogModel->alias('l')
				->join('nw_channel_admin a', 'a.id=l.admin_id', 'left')
				->field('l.*, a.create_time time2')
				->where($condition)->order('l.create_time desc')
				->paginate(10, false, array('query' => input('get.')));
		}

        $this->assign('list', $loginlogList);
        $this->assign('page', $loginlogList->render());
        return $this->fetch();
    }


    /**
     * 操作日志
     * @return mixedo
     */
    public function operate()
    {
        $condition      = [];
        $parent_node_id = input('get.parent_node', 0, 'intval'); // 顶级菜单
        $child_node_id  = input('get.child_node', 0, 'intval'); // 子菜单
        // $parent_node    = config('operatelog.parent_node');
        // $child_node     = config('operatelog.child_node');
        
        // ## 菜单数据 ##
        // 顶级菜单
        $parent_node = Db::table('nw_admin_auth_rule')->where(['parent_id' => 0, 'status' => 1])->column('id,name');
        $parent_node_ids = array_keys($parent_node);
        // 子菜单
        $child_node_list = Db::table('nw_admin_auth_rule')->where(['parent_id' => ['in', $parent_node_ids], 'status' => 1])->column('id,name,parent_id');
        $parent_node_parent_ids = [];
        $child_node = [];
        foreach ($child_node_list as $k => &$v) {
            $parent_node_parent_ids[$v['id']] = $v['parent_id'];
            $v['parent_name'] = $parent_node[ $v['parent_id'] ];
            $child_node[$v['parent_id']][$v['id']] = $v;
        }
        
        
        if (!empty($child_node_id)) {
            $condition['o.type'] = $child_node_id;
        } elseif (!empty($parent_node_id)) {
            $condition['o.type'] = ['in', array_keys($child_node[$parent_node_parent_ids[$parent_node_id]])];
        }
        $username = $this->request->get('username', '', 'trim');
        !empty($username) && $condition['o.username'] = ['like', "%{$username}%"];
        $condition['a.type'] = input('type', '1');

        $operatelogModel = model('Common/operatelog');
        $loginlogList    = $operatelogModel
            ->alias('o')
            ->join('cy_admin a', 'a.id=o.admin_id', 'left')
            ->field('o.*, a.type as type2')
            ->where($condition)->order('o.create_time desc')->paginate(10, false, array('query' => input('get.')));
        $data            = $loginlogList->toArray()['data'];
        
        // 老菜单数据
        $child_node_old = config('operatelog.child_node');
        $parent_node_old = config('operatelog.parent_node');
        $parent_node_map = $child_node_map = [];
        foreach ($child_node_old as $k => $node) {
            foreach ($node as $id => $v) {
                $child_node_map[ $id ]  = $v;
                $parent_node_map[ $id ] = $k;
            }
        }
        
        foreach ($data as $key => &$v) {
            if (!empty($parent_node[$v['type']])){
                $v['child_node_name'] = '-';
                $v['parent_node_name'] = $parent_node[$v['type']];
            } elseif (!empty($child_node[$v['type']])){
                $v['child_node_name'] = $child_node_list[$parent_node_parent_ids[$v['type']]]['name'];
                $v['parent_node_name'] = $child_node_list[$parent_node_parent_ids[$v['type']]]['parent_name'];
            } else {
                $v['child_node_name']  = isset($child_node_map[ $v['type'] ]) ? $child_node_map[ $v['type'] ] : '';
                $v['parent_node_name'] = isset($parent_node_map[ $v['type'] ]) ? $parent_node_old[ $parent_node_map[ $v['type'] ] ] : '';
            }
        }
        
        $this->assign('list', $data);
        $this->assign('total', $loginlogList->total());     //总记录数
        $this->assign('parent_node', $parent_node);
        $this->assign('parent_node_id', $parent_node_id);
        $this->assign('child_node_id', $child_node_id);
        $this->assign('page', $loginlogList->render());
        return $this->fetch();
    }

    public function getChildNode()
    {
        $parent_id = input('get.parent_id', 0, 'intval');
        if (empty($parent_id)) {
            $this->success('ok', '', []);
        }
        
        // 子菜单
        $child_node_list = Db::table('nw_admin_auth_rule')->where(['parent_id' => $parent_id, 'status' => 1])->column('id,name');
        $this->success('ok', '', $child_node_list);
    }
}