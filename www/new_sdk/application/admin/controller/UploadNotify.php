<?php

/**
 * 打包回调地址
 *
 */

namespace app\admin\controller;

use app\common\logic\SubPackage as SubChannel;
use app\common\logic\Websocket;

class UploadNotify
{
    public function __construct()
    {
        // log_message('<EMAIL>:' . json_encode(input()), 'log', LOG_PATH . 'admin/');
    }
    // 分包 通知
    public function index()
    {
        $channel = new SubChannel();
        echo $channel->uploadPackageNotify();
    }

    /**
     * excel 通知
     *
     *  请求范例：http://admin.7dgames.cn/upload_notify/makeExcelComplete?action=payList&adminid=1&file_path=https://static.7dgames.cn/excel_dev/20240806/充值记录管理导出_20240806144943_1.csv&time=1722926977
     *
     * @return void
     */
    public function makeExcelComplete()
    {
        $runnable = true;

        $adminid = input('adminid', '', 'trim');

        $encrypt = input('encrypt');
        $time = input("time");
        $file_path = input('file_path');
        $action = input("action");

        // $checkToken = md5($adminid . $time . $file_path . $action . 'aoyou88!!!');
        // // token 校验 md5(game_id+channel_id+adminid+'aoyou88!!!')     md5(234354aoyou88!!!)
        // if ($checkToken != $encrypt) {
        //     $runnable = false;
        //     $msg = 'token校验失败!';
        // }

        if (empty($adminid)) {
            $runnable = false;
            $msg = '用户id缺失!';
        }
        if (empty($time)) {
            $runnable = false;
            $msg = 'time缺失!';
        }
        if (empty($file_path)) {
            $runnable = false;
            $msg = 'file_path缺失!';
        }
        if (empty($action)) {
            $runnable = false;
            $msg = 'action缺失!';
        }

        // TODO 调用消息系统接口，websocket告知用户打包完成
        try {
            if ($runnable) {
                $str = '{"error_code":0,"data":{"action":"makeExcelComplete","data":{"file_path":"' . $file_path . '","task_time":"' . $time . '", "file_type":"' . $action . '"}}}';
                // $str = '{"error_code":0,"data":{"action":"makeExcelComplete","data":{"file_path":"https://downreport.46yx.com/excel/20220909/充值记录管理_20220909155614.xlsx","task_time":1662710169,"file_type":"payList"}}}';
                // \think\Log::record('WS:', $adminid . ' - ' . $str);

                $ws = new Websocket();
                $ws->sayToUid($adminid, $str);
                echo 'success';
                exit();
            }
        } catch (\Exception $e) {
            echo "error = " . " - " . $e->getFile() . ":" . $e->getLine();
            exit();
        }
        echo 'fail = ' . $msg;
        exit();
    }
}