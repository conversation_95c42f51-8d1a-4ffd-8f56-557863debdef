<?php
use think\Env;

/**
 * 检查权限
 * @param $userId  int        要检查权限的用户 ID
 * @param $name string|array  需要验证的规则列表,支持逗号分隔的权限规则或索引数组
 * @param $relation string    如果为 'or' 表示满足任一条规则即通过验证;如果为 'and'则表示需满足所有规则才能通过验证
 * @return boolean            通过验证返回true;失败返回false
 */
function cmf_auth_check($userId, $name = null, $relation = 'or')
{
    if (empty($userId)) {
        return false;
    }

    if ($userId == 1) {
        return true;
    }

    $authObj = new \app\common\logic\Auth();
    if (empty($name)) {
        $request = request();
        $module = $request->module();
        $controller = $request->controller();
        $action = $request->action();
        $name = strtolower($module . "/" . $controller . "/" . $action);
    }
    return $authObj->check($userId, $name, $relation);
}

/**
 * 获取当前登录的管理员ID
 * @return int
 */
function mg_get_current_admin_id()
{
    return session('ADMIN_ID');
}

/**
 * 获取游戏名称
 */
function get_game_nickname($id)
{
    $info = \Think\Db::name('cy_game')->field('name')->where(['id' => $id])->find();
    return $info['name'];
}


/**
 * 获取当前渠道名称
 */
function get_current_channel_name($id)
{
    $info = \Think\Db::name('nw_channel')->field('name')->where(['id' => $id])->find();
    return $info['name'];
}


/**
 * 根据权限是否显示UI
 *
 * @param bool $show
 * @param string $rule_name
 * @return bool
 */
function authUI($rule_name = '', $show = false)
{
    return $show ?: cmf_auth_check(session('ADMIN_ID'), $rule_name);
}

/**
 * 获取渠道
 */
function get_channel_arr($channelId)
{
    $result = [];
    $level = 1;
    $channelModel = \think\Db::name('nw_channel');

    while ($info = $channelModel->field('name,parent_id')->where(['id' => $channelId])->find()) {

        $info['level'] = $level;
        $result[] = $info;
        if ($info['parent_id'] == 0) {
            return array_reverse($result);
        }

        $channelId = $info['parent_id'];
        $level++;
    }
}

/**
 * AES加密
 * @param string $str 需加密的字符串
 * @param string $key 秘钥
 *
 * @return string
 */
function aesEncode($str, $key)
{
    $td = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', MCRYPT_MODE_CBC, '');
    $ivStr = strtoupper(md5($key));
    $iv = substr($ivStr, 0, 16);
    mcrypt_generic_init($td, $key, $iv);
    $b = mcrypt_generic($td, padPkcs5($str, 16));
    mcrypt_generic_deinit($td);
    mcrypt_module_close($td);

    return bin2hex($b);
}

/**
 * AES解密
 * @param string $str 需解密的字符串
 * @param string $key 秘钥
 *
 * @return string
 */
function aesDecode($str, $key)
{
    $td = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', MCRYPT_MODE_CBC, '');
    $ivStr = strtoupper(md5($key));
    $iv = substr($ivStr, 0, 16);
    mcrypt_generic_init($td, $key, $iv);
    $a = unpadPkcs5(mdecrypt_generic($td, hex2bin($str)));
    mcrypt_generic_deinit($td);
    mcrypt_module_close($td);

    return $a;
}

function padPkcs5($text, $blocksize)
{
    $pad = $blocksize - (strlen($text) % $blocksize);
    return $text . str_repeat(chr($pad), $pad);
}

function unpadPkcs5($text)
{
    $pad = ord($text{strlen($text) - 1});
    if ($pad > strlen($text)) {
        return false;
    }
    if (strspn($text, chr($pad), strlen($text) - $pad) != $pad) {
        return false;
    }

    return substr($text, 0, -1 * $pad);
}

/**
 * 当传递的value为空时，返回设置的默认值
 * @param mixed $value
 * @param mixed $default
 * @return mixed
 */
function setDefault($value, $default = '---')
{
    if (empty($value)) {
        return $default;
    } else {
        return $value;
    }
}

/**
 * 获取一级渠道列表
 */
function getChannelByTop()
{
    $parent_id = config('TOP_CHANNEL_ID');
    $channel_list = model('Common/Channel')->where('parent_id', "{$parent_id}")->select();
    $result = [];
    foreach ($channel_list as $v) {
        $result[$v['id']] = $v['name'];
    }
    return $result;
}

/**
 * 获取日期查询条件
 * @param int $start_time 开始时间
 * @param int $end_time 结束时间
 * @param bool $isdefault 是否默认今天日期
 * @return
 */
function getTimeCondition($start_time, $end_time, $isdefault = true)
{

    $time = [];
    //开始时间和结束时间不为空时
    if ($start_time != '' && $end_time != '') {
        $time = [
            ['>=', strtotime($start_time)],
            ['<=', strtotime($end_time . ' 23:59:59')],
        ];
    } //开始时间不为空时
    elseif ($start_time != '') {
        $time = ['>=', strtotime($start_time)];
    } //结束时间不为空时
    elseif ($end_time != '') {
        $time = ['<=', strtotime($end_time . ' 23:59:59')];
    } else {

        if ($isdefault) {
            $start_time = $end_time = date('Y-m-d', time());

            $time = [
                ['>=', strtotime($start_time)],
                ['<=', strtotime($end_time . ' 23:59:59')],
            ];
        }
    }

    return $time;
}


/**
 * 获取日期查询条件
 * @param int $start_time 开始时间
 * @param int $end_time 结束时间
 * @param bool $isdefault 是否默认今天日期
 * @return
 */
function getDateTimeCondition($start_time, $end_time, $isdefault = true)
{

    $time = [];
    //开始时间和结束时间不为空时
    if ($start_time != '' && $end_time != '') {
        $time = [
            ['>=', $start_time],
            ['<=', $end_time],
        ];
    } //开始时间不为空时
    elseif ($start_time != '') {
        $time = ['>=', $start_time];
    } //结束时间不为空时
    elseif ($end_time != '') {
        $time = ['<=', $end_time];
    } else {
        if ($isdefault) {
            $start_time = $end_time = date('Y-m-d', time());
            $time = [
                ['>=',$start_time],
                ['<=', $end_time],
            ];
        }
    }

    return $time;
}

// 获取env配置参数
function getEnvs($name, $default = ''){
    return Env::get($name, $default);
}

/**
 * 数组键增加前缀
 *
 * @param array  $array 处理的数组
 * @param string $prefix 新增的前缀
 *
 * @return array
 */
function addPrefixToKeys(array $array, string $prefix): array {
    // 为所有键添加前缀
    $prefixedKeys = array_map(function($key) use ($prefix) {
        return $prefix . $key;
    }, array_keys($array));
    
    // 使用新键名和原值创建新数组
    return array_combine($prefixedKeys, $array);
}

/**
 * 获取未来时间戳与当前时间之间的剩余天数
 *
 * @param int $futureTimestamp 目标时间戳（秒）
 * @return int|string 剩余天数；如果时间已过或无效，返回空字符串
 */
function getRemainingDays(int $futureTimestamp) {
    // 当前时间戳
    $now = time();

    // 判断目标时间是否早于当前时间
    if ($futureTimestamp <= $now) {
        return "-";
    }

    // 计算剩余秒数，并换算为天数（向上取整）
    $remainingSeconds = $futureTimestamp - $now;
    $remainingDays = (int)ceil($remainingSeconds / (60 * 60 * 24));

    return $remainingDays;
}

