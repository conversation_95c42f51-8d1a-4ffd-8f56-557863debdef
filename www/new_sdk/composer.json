{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=7.0", "topthink/framework": "~5.0.0", "topthink/think-queue": "1.1.4", "phpmailer/phpmailer": "v6.0.3", "topthink/think-captcha": "v1.*", "guzzlehttp/guzzle": "^6.3", "overtrue/pinyin": "~3.0", "workerman/gatewayclient": "v3.0.10", "monolog/monolog": "1.23.0", "guzzlehttp/psr7": "1.4.2", "guzzlehttp/promises": "v1.3.1", "psr/log": "1.0.2", "topthink/think-helper": "v1.0.6", "huaweicloud/huaweicloud-sdk-php": "3.0.56-rc", "hashids/hashids": "^4.1", "ext-openssl": "*"}, "autoload": {"psr-4": {"app\\": "application"}}, "extra": {"think-path": "thinkphp"}, "config": {"preferred-install": "dist", "allow-plugins": {"topthink/think-installer": true}}}