-- 风控系统数据库表结构
-- MySQL版本: 8.0.28
-- 创建时间: 2024

-- 创建数据库（如果需要）
CREATE DATABASE IF NOT EXISTS risk_control DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE risk_control;

-- 1. 规则表
DROP TABLE IF EXISTS `fk_rules`;
CREATE TABLE `fk_rules` (
    `rule_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
    `rule_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则名',
    `rule_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则编码(标识)',
    `exception_object_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常对象数据名：ip/imei/account/id_card/phone',
    `logic_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '逻辑类型：eq/gt/lt',
    `logic_value` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '逻辑值',
    `rule_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则类型：frequency/statistics',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1=启用、2=关闭',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`rule_id`),
    UNIQUE KEY `uk_rule_code` (`rule_code`),
    KEY `idx_status` (`status`),
    KEY `idx_exception_object_type` (`exception_object_type`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='规则表';

-- 2. 策略表
DROP TABLE IF EXISTS `fk_strategies`;
CREATE TABLE `fk_strategies` (
    `strategy_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '策略ID',
    `strategy_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '策略名',
    `strategy_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '策略编码',
    `execution_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '执行类型：realtime/scheduled',
    `weight` int(11) NOT NULL DEFAULT '0' COMMENT '权重（执行优先级）',
    `action` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理动作：exception/ban',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1=启用、2=禁用',
    `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`strategy_id` DESC) USING BTREE,
    UNIQUE KEY `uk_strategy_code` (`strategy_code`),
    KEY `idx_status` (`status`),
    KEY `idx_weight` (`weight` DESC),
    KEY `idx_type_status` (`execution_type`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='策略表';

-- 3. 策略详情表
DROP TABLE IF EXISTS `fk_strategy_details`;
CREATE TABLE `fk_strategy_details` (
    `detail_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '策略详情ID',
    `strategy_id` int(11) unsigned NOT NULL COMMENT '策略ID',
    `rule_id` int(11) unsigned NOT NULL COMMENT '规则ID',
    `exception_degree` decimal(5,2) NOT NULL COMMENT '异常程度（百分比）',
    `exception_validity_period` int(11) NOT NULL COMMENT '异常有效期（秒）',
    `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`detail_id`),
    KEY `idx_strategy_id` (`strategy_id`),
    KEY `idx_rule_id` (`rule_id`),
    CONSTRAINT `fk_strategy_details_rule_id` FOREIGN KEY (`rule_id`) REFERENCES `fk_rules` (`rule_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_strategy_details_strategy` FOREIGN KEY (`strategy_id`) REFERENCES `fk_strategies` (`strategy_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='策略详情表';

-- 4. 异常对象表
DROP TABLE IF EXISTS `fk_exception_objects`;
CREATE TABLE `fk_exception_objects` (
    `object_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '异常对象ID',
    `exception_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常类型：ip/imei/account/id_card/phone',
    `exception_value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常对象值',
    `exception_degree_total` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '异常程度（汇总）',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1=正常、2=异常、3=自动封禁、4=手动封禁',
    `exception_log` json NOT NULL COMMENT '异常记录',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`object_id`),
    UNIQUE KEY `uk_type_value` (`exception_type`,`exception_value`),
    KEY `idx_status` (`status`),
    KEY `idx_exception_type` (`exception_type`),
    KEY `idx_exception_value` (`exception_value`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='异常对象表';

-- 5. 异常记录表
DROP TABLE IF EXISTS `fk_exception_records`;
CREATE TABLE `fk_exception_records` (
    `record_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '异常记录ID',
    `rule_id` int(11) NOT NULL COMMENT '规则ID',
    `exception_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常类型：ip/imei/account/id_card/phone',
    `exception_value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常对象值',
    `exception_degree` decimal(5,2) NOT NULL COMMENT '异常程度（百分比）',
    `exception_content` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常内容（异常规则名）',
    `validity_duration` int(11) NOT NULL COMMENT '异常有效期(秒数，用于倒计时)',
    `validity_expire_time` bigint(20) NOT NULL COMMENT '异常有效期(到期时间，时间戳)',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=正常、2=过期',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `rule_bak_json` json DEFAULT NULL COMMENT '规则备份',
    PRIMARY KEY (`record_id`),
    KEY `idx_type_value` (`exception_type`,`exception_value`),
    KEY `idx_expire_time` (`validity_expire_time`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='异常记录表';

-- 6. 名单表
DROP TABLE IF EXISTS `fk_roster_lists`;
CREATE TABLE `fk_roster_lists` (
    `roster_lists_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '名单ID',
    `list_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名单类型：black=黑名单/white=白名单/normal=正常数据',
    `object_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对象类型：ip/imei/user_name/id_card',
    `object_value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型值',
    `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    PRIMARY KEY (`roster_lists_id`) USING BTREE,
    UNIQUE KEY `uk_type_object_value` (`list_type`,`object_type`,`object_value`),
    KEY `idx_list_type` (`list_type`),
    KEY `idx_object_type` (`object_type`),
    KEY `idx_object_value` (`object_value`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='名单表';
