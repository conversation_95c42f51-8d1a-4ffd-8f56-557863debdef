# 风控系统使用说明

## 一、系统概述

本风控系统基于ThinkPHP 5.0框架和LayUI前端框架开发，用于管理和配置风险控制规则、策略，监控异常对象和异常记录，并支持黑白名单管理。

## 二、数据库配置

1. 在MySQL 8.0.28中执行 `risk_control_tables.sql` 文件创建数据库表
2. 在ThinkPHP配置文件中配置数据库连接信息

## 三、功能模块

### 1. 风控规则管理 (FkRules)
- **访问路径**: `/admin/fk_rules/index`
- **功能**: 
  - 查看规则列表
  - 添加新规则
  - 编辑规则
  - 删除规则
  - 批量删除

### 2. 风控策略管理 (FkStrategies)
- **访问路径**: `/admin/fk_strategies/index`
- **功能**:
  - 查看策略列表
  - 添加新策略
  - 编辑策略
  - 删除策略
  - 配置策略规则

### 3. 策略详情管理 (FkStrategyDetails)
- **访问路径**: `/admin/fk_strategy_details/index`
- **功能**:
  - 查看策略详情列表
  - 添加策略详情（关联策略和规则）
  - 编辑策略详情
  - 删除策略详情

### 4. 异常对象管理 (FkExceptionObjects)
- **访问路径**: `/admin/fk_exception_objects/index`
- **功能**:
  - 查看异常对象列表
  - 添加异常对象
  - 编辑异常对象
  - 删除异常对象
  - 按对象类型、对象值、状态搜索
  - 更新对象状态

### 5. 异常记录管理 (FkExceptionRecords)
- **访问路径**: `/admin/fk_exception_records/index`
- **功能**:
  - 查看异常记录列表
  - 处理异常记录（加入黑/白名单或忽略）
  - 删除记录
  - 按对象类型、对象值、处理状态搜索
  - 查看记录详情

### 6. 名单管理 (FkLists)
- **访问路径**: `/admin/fk_roster_lists/index`
- **功能**:
  - 查看黑白名单列表
  - 添加单个名单记录
  - 编辑名单记录
  - 删除名单记录
  - **批量导入功能**（支持一次导入多个对象）
  - 设置过期时间
  - 按名单类型、对象类型、对象值搜索

## 四、菜单配置

在后台管理系统中添加以下菜单：

```php
// 一级菜单：风控管理
[
    'name' => '风控管理',
    'icon' => 'layui-icon-auz',
    'url' => '',
    'children' => [
        [
            'name' => '规则管理',
            'url' => '/admin/fk_rules/index',
            'icon' => 'layui-icon-template-1'
        ],
        [
            'name' => '策略管理',
            'url' => '/admin/fk_strategies/index',
            'icon' => 'layui-icon-senior'
        ],
        [
            'name' => '策略详情',
            'url' => '/admin/fk_strategy_details/index',
            'icon' => 'layui-icon-form'
        ],
        [
            'name' => '异常对象',
            'url' => '/admin/fk_exception_objects/index',
            'icon' => 'layui-icon-face-surprised'
        ],
        [
            'name' => '异常记录',
            'url' => '/admin/fk_exception_records/index',
            'icon' => 'layui-icon-log'
        ],
        [
            'name' => '名单管理',
            'url' => '/admin/fk_roster_lists/index',
            'icon' => 'layui-icon-list'
        ]
    ]
]
```

## 五、使用流程

### 1. 配置规则
1. 进入"规则管理"页面
2. 点击"添加规则"按钮
3. 填写规则信息：
   - 规则名称：如"IP访问频率规则"
   - 规则编码：如"RULE_IP_FREQ_001"
   - 异常对象类型：选择监控的数据类型
   - 逻辑类型和逻辑值：设置触发条件
   - 规则类型：选择频率或统计
   - 状态：启用或关闭

### 2. 配置策略
1. 进入"策略管理"页面
2. 点击"添加策略"按钮
3. 填写策略信息：
   - 策略名称：如"高频访问策略"
   - 策略编码：如"STRATEGY_HIGH_FREQ_001"
   - 执行类型：实时或定时
   - 权重：执行优先级
   - 处理动作：异常或封禁
   - 状态：启用或禁用

### 3. 关联策略和规则
方式一：通过策略管理
1. 在策略列表中点击"配置规则"按钮
2. 在弹出页面中点击"添加规则"
3. 选择规则并设置异常程度和有效期

方式二：通过策略详情管理
1. 进入"策略详情"页面
2. 点击"添加策略详情"
3. 选择策略和规则，设置相关参数

### 4. 监控异常
- 异常对象：查看所有被标记为异常的对象
- 异常记录：查看详细的异常触发记录

### 5. 名单管理
单个添加：
- 点击"添加"按钮，填写名单信息
- 选择名单类型（黑名单/白名单）
- 选择对象类型和填写对象值
- 可选填写原因和过期时间

批量导入：
1. 点击"批量导入"按钮
2. 选择名单类型（黑名单/白名单）
3. 选择对象类型
4. 在文本框中输入对象值，一行一个
5. 填写原因说明（可选）
6. 点击"立即导入"

处理异常记录：
- 在异常记录列表中点击"处理"
- 选择处理方式：加入黑名单、加入白名单或忽略
- 填写处理备注

## 六、注意事项

1. 规则编码和策略编码必须唯一
2. 删除策略会同时删除相关的策略详情
3. 异常程度为百分比值，范围0-100
4. 有效期为秒数，0表示永久有效
5. 权重值越大，策略优先级越高
6. 批量导入时会自动过滤空行和重复记录
7. 批量导入最多支持一次导入1000条记录
8. 名单过期时间为空表示永不过期

## 七、扩展功能

系统预留了以下扩展功能接口：
- CSV数据导入导出
- 异常记录统计分析
- 实时监控面板
- 策略执行日志

如需实现这些功能，请联系开发人员进行定制开发。

## 八、风控查询逻辑类

### 1. 功能概述

风控查询逻辑类（RiskControlService）是一个公共的服务类，用于处理风控相关的业务逻辑。该类负责接收风控事件数据，执行风控策略，记录异常数据，并进行相应的处理。

### 2. 事件数据结构

```json
{
    "event_type": "login",     // 事件类型：login（登录）、register（注册）
    "member_id": "1001",       // 用户ID
    "user_name": "wen_001",    // 用户名
    "ip": "127.0.0.1",        // IP地址
    "imei": "00000000-1645-d5b5-ffff-ffffef05ac4a-wen", // 设备标识
    "timestamp": "1749021885", // 事件时间戳（Unix秒级）
    // 可选扩展字段（注册时）
    "duration": 10,           // 注册耗时
    "phone": "13121776520",   // 手机号
    "id_card": "110101199001011234" // 身份证号
}
```

### 3. 处理流程

1. **数据请求处理**
   - 接收风控事件数据
   - 数据格式验证
   - 数据预处理

2. **策略处理**
   - 过滤名单表数据（黑名单/白名单）
   - 根据事件类型匹配相关策略
   - 执行策略规则判断

3. **异常数据处理**
   - 记录异常数据到异常记录表
   - 更新异常对象表
   - 计算异常程度

4. **异常有效期处理**
   - 清理过期的异常记录
   - 更新异常对象的异常程度
   - 移除已过期的异常标记

5. **封禁处理**
   - 检查异常对象是否达到封禁标准
   - 执行封禁操作
   - 更新黑名单数据

6. **异常程度判断**
   - 查询关联对象（IP/IMEI/用户ID/身份证）的异常情况
   - 计算综合异常程度
   - 返回风控处理结果

### 4. 使用示例

```php
// 控制器中使用示例
public function login()
{
    $eventData = [
        'event_type' => 'login',
        'member_id' => '1001',
        'user_name' => 'wen_001',
        'ip' => '127.0.0.1',
        'imei' => '00000000-1645-d5b5-ffff-ffffef05ac4a-wen',
        'timestamp' => time()
    ];
    
    $riskControl = new RiskControlService();
    $result = $riskControl->processEvent($eventData);
    
    if ($result['is_blocked']) {
        return json(['code' => 403, 'msg' => '账号已被封禁']);
    }
    
    if ($result['is_risky']) {
        // 处理风险情况
        return json(['code' => 200, 'msg' => '需要验证']);
    }
    
    // 正常处理
    return json(['code' => 200, 'msg' => 'success']);
}
```

### 5. 注意事项

1. 风控查询逻辑类需要配合数据库表结构使用
2. 建议在应用层做数据缓存，提高查询效率
3. 异常程度计算需要考虑多个维度的权重
4. 封禁操作需要谨慎处理，建议增加人工审核机制
5. 定期清理过期数据，保持系统性能
6. 建议增加风控日志记录，方便问题排查

### 6. 扩展建议

1. 添加风控数据统计功能
2. 实现风控规则动态配置
3. 增加机器学习模型支持
4. 添加风控数据可视化展示
5. 实现风控策略A/B测试功能 